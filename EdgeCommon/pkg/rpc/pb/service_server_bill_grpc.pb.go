// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_bill.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerBillService_CountAllServerBills_FullMethodName = "/pb.ServerBillService/countAllServerBills"
	ServerBillService_ListServerBills_FullMethodName     = "/pb.ServerBillService/listServerBills"
)

// ServerBillServiceClient is the client API for ServerBillService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerBillServiceClient interface {
	// 查询服务账单数量
	CountAllServerBills(ctx context.Context, in *CountAllServerBillsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查询服务账单列表
	ListServerBills(ctx context.Context, in *ListServerBillsRequest, opts ...grpc.CallOption) (*ListServerBillsResponse, error)
}

type serverBillServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerBillServiceClient(cc grpc.ClientConnInterface) ServerBillServiceClient {
	return &serverBillServiceClient{cc}
}

func (c *serverBillServiceClient) CountAllServerBills(ctx context.Context, in *CountAllServerBillsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerBillService_CountAllServerBills_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverBillServiceClient) ListServerBills(ctx context.Context, in *ListServerBillsRequest, opts ...grpc.CallOption) (*ListServerBillsResponse, error) {
	out := new(ListServerBillsResponse)
	err := c.cc.Invoke(ctx, ServerBillService_ListServerBills_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerBillServiceServer is the server API for ServerBillService service.
// All implementations should embed UnimplementedServerBillServiceServer
// for forward compatibility
type ServerBillServiceServer interface {
	// 查询服务账单数量
	CountAllServerBills(context.Context, *CountAllServerBillsRequest) (*RPCCountResponse, error)
	// 查询服务账单列表
	ListServerBills(context.Context, *ListServerBillsRequest) (*ListServerBillsResponse, error)
}

// UnimplementedServerBillServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerBillServiceServer struct {
}

func (UnimplementedServerBillServiceServer) CountAllServerBills(context.Context, *CountAllServerBillsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllServerBills not implemented")
}
func (UnimplementedServerBillServiceServer) ListServerBills(context.Context, *ListServerBillsRequest) (*ListServerBillsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServerBills not implemented")
}

// UnsafeServerBillServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerBillServiceServer will
// result in compilation errors.
type UnsafeServerBillServiceServer interface {
	mustEmbedUnimplementedServerBillServiceServer()
}

func RegisterServerBillServiceServer(s grpc.ServiceRegistrar, srv ServerBillServiceServer) {
	s.RegisterService(&ServerBillService_ServiceDesc, srv)
}

func _ServerBillService_CountAllServerBills_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllServerBillsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBillServiceServer).CountAllServerBills(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBillService_CountAllServerBills_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBillServiceServer).CountAllServerBills(ctx, req.(*CountAllServerBillsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerBillService_ListServerBills_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServerBillsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBillServiceServer).ListServerBills(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBillService_ListServerBills_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBillServiceServer).ListServerBills(ctx, req.(*ListServerBillsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerBillService_ServiceDesc is the grpc.ServiceDesc for ServerBillService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerBillService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerBillService",
	HandlerType: (*ServerBillServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countAllServerBills",
			Handler:    _ServerBillService_CountAllServerBills_Handler,
		},
		{
			MethodName: "listServerBills",
			Handler:    _ServerBillService_ListServerBills_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_bill.proto",
}
