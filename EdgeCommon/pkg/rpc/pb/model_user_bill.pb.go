// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_bill.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserBill struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	User          *User                  `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	TypeName      string                 `protobuf:"bytes,4,opt,name=typeName,proto3" json:"typeName,omitempty"`
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Amount        float64                `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	Month         string                 `protobuf:"bytes,7,opt,name=month,proto3" json:"month,omitempty"`
	IsPaid        bool                   `protobuf:"varint,8,opt,name=isPaid,proto3" json:"isPaid,omitempty"`
	PaidAt        int64                  `protobuf:"varint,9,opt,name=paidAt,proto3" json:"paidAt,omitempty"`
	Code          string                 `protobuf:"bytes,10,opt,name=code,proto3" json:"code,omitempty"`
	CanPay        bool                   `protobuf:"varint,11,opt,name=canPay,proto3" json:"canPay,omitempty"`
	DayFrom       string                 `protobuf:"bytes,12,opt,name=dayFrom,proto3" json:"dayFrom,omitempty"`
	DayTo         string                 `protobuf:"bytes,13,opt,name=dayTo,proto3" json:"dayTo,omitempty"`
	PricePeriod   string                 `protobuf:"bytes,14,opt,name=pricePeriod,proto3" json:"pricePeriod,omitempty"`
	IsOverdue     bool                   `protobuf:"varint,15,opt,name=isOverdue,proto3" json:"isOverdue,omitempty"` // 是否已逾期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserBill) Reset() {
	*x = UserBill{}
	mi := &file_models_model_user_bill_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBill) ProtoMessage() {}

func (x *UserBill) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_bill_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBill.ProtoReflect.Descriptor instead.
func (*UserBill) Descriptor() ([]byte, []int) {
	return file_models_model_user_bill_proto_rawDescGZIP(), []int{0}
}

func (x *UserBill) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserBill) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserBill) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserBill) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *UserBill) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserBill) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UserBill) GetMonth() string {
	if x != nil {
		return x.Month
	}
	return ""
}

func (x *UserBill) GetIsPaid() bool {
	if x != nil {
		return x.IsPaid
	}
	return false
}

func (x *UserBill) GetPaidAt() int64 {
	if x != nil {
		return x.PaidAt
	}
	return 0
}

func (x *UserBill) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserBill) GetCanPay() bool {
	if x != nil {
		return x.CanPay
	}
	return false
}

func (x *UserBill) GetDayFrom() string {
	if x != nil {
		return x.DayFrom
	}
	return ""
}

func (x *UserBill) GetDayTo() string {
	if x != nil {
		return x.DayTo
	}
	return ""
}

func (x *UserBill) GetPricePeriod() string {
	if x != nil {
		return x.PricePeriod
	}
	return ""
}

func (x *UserBill) GetIsOverdue() bool {
	if x != nil {
		return x.IsOverdue
	}
	return false
}

var File_models_model_user_bill_proto protoreflect.FileDescriptor

const file_models_model_user_bill_proto_rawDesc = "" +
	"\n" +
	"\x1cmodels/model_user_bill.proto\x12\x02pb\x1a\x17models/model_user.proto\"\x84\x03\n" +
	"\bUserBill\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1c\n" +
	"\x04user\x18\x02 \x01(\v2\b.pb.UserR\x04user\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x1a\n" +
	"\btypeName\x18\x04 \x01(\tR\btypeName\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x16\n" +
	"\x06amount\x18\x06 \x01(\x01R\x06amount\x12\x14\n" +
	"\x05month\x18\a \x01(\tR\x05month\x12\x16\n" +
	"\x06isPaid\x18\b \x01(\bR\x06isPaid\x12\x16\n" +
	"\x06paidAt\x18\t \x01(\x03R\x06paidAt\x12\x12\n" +
	"\x04code\x18\n" +
	" \x01(\tR\x04code\x12\x16\n" +
	"\x06canPay\x18\v \x01(\bR\x06canPay\x12\x18\n" +
	"\adayFrom\x18\f \x01(\tR\adayFrom\x12\x14\n" +
	"\x05dayTo\x18\r \x01(\tR\x05dayTo\x12 \n" +
	"\vpricePeriod\x18\x0e \x01(\tR\vpricePeriod\x12\x1c\n" +
	"\tisOverdue\x18\x0f \x01(\bR\tisOverdueB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_bill_proto_rawDescOnce sync.Once
	file_models_model_user_bill_proto_rawDescData []byte
)

func file_models_model_user_bill_proto_rawDescGZIP() []byte {
	file_models_model_user_bill_proto_rawDescOnce.Do(func() {
		file_models_model_user_bill_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_bill_proto_rawDesc), len(file_models_model_user_bill_proto_rawDesc)))
	})
	return file_models_model_user_bill_proto_rawDescData
}

var file_models_model_user_bill_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_bill_proto_goTypes = []any{
	(*UserBill)(nil), // 0: pb.UserBill
	(*User)(nil),     // 1: pb.User
}
var file_models_model_user_bill_proto_depIdxs = []int32{
	1, // 0: pb.UserBill.user:type_name -> pb.User
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_user_bill_proto_init() }
func file_models_model_user_bill_proto_init() {
	if File_models_model_user_bill_proto != nil {
		return
	}
	file_models_model_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_bill_proto_rawDesc), len(file_models_model_user_bill_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_bill_proto_goTypes,
		DependencyIndexes: file_models_model_user_bill_proto_depIdxs,
		MessageInfos:      file_models_model_user_bill_proto_msgTypes,
	}.Build()
	File_models_model_user_bill_proto = out.File
	file_models_model_user_bill_proto_goTypes = nil
	file_models_model_user_bill_proto_depIdxs = nil
}
