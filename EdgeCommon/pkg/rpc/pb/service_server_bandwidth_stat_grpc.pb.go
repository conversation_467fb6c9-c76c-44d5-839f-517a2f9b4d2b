// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_bandwidth_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerBandwidthStatService_UploadServerBandwidthStats_FullMethodName               = "/pb.ServerBandwidthStatService/uploadServerBandwidthStats"
	ServerBandwidthStatService_FindServerBandwidthStats_FullMethodName                 = "/pb.ServerBandwidthStatService/findServerBandwidthStats"
	ServerBandwidthStatService_FindHourlyServerBandwidthStats_FullMethodName           = "/pb.ServerBandwidthStatService/findHourlyServerBandwidthStats"
	ServerBandwidthStatService_FindDailyServerBandwidthStats_FullMethodName            = "/pb.ServerBandwidthStatService/findDailyServerBandwidthStats"
	ServerBandwidthStatService_FindDailyServerBandwidthStatsBetweenDays_FullMethodName = "/pb.ServerBandwidthStatService/findDailyServerBandwidthStatsBetweenDays"
)

// ServerBandwidthStatServiceClient is the client API for ServerBandwidthStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerBandwidthStatServiceClient interface {
	// 上传带宽统计
	UploadServerBandwidthStats(ctx context.Context, in *UploadServerBandwidthStatsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取服务的峰值带宽
	FindServerBandwidthStats(ctx context.Context, in *FindServerBandwidthStatsRequest, opts ...grpc.CallOption) (*FindServerBandwidthStatsResponse, error)
	// 获取最近N小时峰值带宽
	FindHourlyServerBandwidthStats(ctx context.Context, in *FindHourlyServerBandwidthStatsRequest, opts ...grpc.CallOption) (*FindHourlyServerBandwidthStatsResponse, error)
	// 获取最近N天峰值带宽
	FindDailyServerBandwidthStats(ctx context.Context, in *FindDailyServerBandwidthStatsRequest, opts ...grpc.CallOption) (*FindDailyServerBandwidthStatsResponse, error)
	// 读取日期段内的带宽数据
	FindDailyServerBandwidthStatsBetweenDays(ctx context.Context, in *FindDailyServerBandwidthStatsBetweenDaysRequest, opts ...grpc.CallOption) (*FindDailyServerBandwidthStatsBetweenDaysResponse, error)
}

type serverBandwidthStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerBandwidthStatServiceClient(cc grpc.ClientConnInterface) ServerBandwidthStatServiceClient {
	return &serverBandwidthStatServiceClient{cc}
}

func (c *serverBandwidthStatServiceClient) UploadServerBandwidthStats(ctx context.Context, in *UploadServerBandwidthStatsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerBandwidthStatService_UploadServerBandwidthStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverBandwidthStatServiceClient) FindServerBandwidthStats(ctx context.Context, in *FindServerBandwidthStatsRequest, opts ...grpc.CallOption) (*FindServerBandwidthStatsResponse, error) {
	out := new(FindServerBandwidthStatsResponse)
	err := c.cc.Invoke(ctx, ServerBandwidthStatService_FindServerBandwidthStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverBandwidthStatServiceClient) FindHourlyServerBandwidthStats(ctx context.Context, in *FindHourlyServerBandwidthStatsRequest, opts ...grpc.CallOption) (*FindHourlyServerBandwidthStatsResponse, error) {
	out := new(FindHourlyServerBandwidthStatsResponse)
	err := c.cc.Invoke(ctx, ServerBandwidthStatService_FindHourlyServerBandwidthStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverBandwidthStatServiceClient) FindDailyServerBandwidthStats(ctx context.Context, in *FindDailyServerBandwidthStatsRequest, opts ...grpc.CallOption) (*FindDailyServerBandwidthStatsResponse, error) {
	out := new(FindDailyServerBandwidthStatsResponse)
	err := c.cc.Invoke(ctx, ServerBandwidthStatService_FindDailyServerBandwidthStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverBandwidthStatServiceClient) FindDailyServerBandwidthStatsBetweenDays(ctx context.Context, in *FindDailyServerBandwidthStatsBetweenDaysRequest, opts ...grpc.CallOption) (*FindDailyServerBandwidthStatsBetweenDaysResponse, error) {
	out := new(FindDailyServerBandwidthStatsBetweenDaysResponse)
	err := c.cc.Invoke(ctx, ServerBandwidthStatService_FindDailyServerBandwidthStatsBetweenDays_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerBandwidthStatServiceServer is the server API for ServerBandwidthStatService service.
// All implementations should embed UnimplementedServerBandwidthStatServiceServer
// for forward compatibility
type ServerBandwidthStatServiceServer interface {
	// 上传带宽统计
	UploadServerBandwidthStats(context.Context, *UploadServerBandwidthStatsRequest) (*RPCSuccess, error)
	// 获取服务的峰值带宽
	FindServerBandwidthStats(context.Context, *FindServerBandwidthStatsRequest) (*FindServerBandwidthStatsResponse, error)
	// 获取最近N小时峰值带宽
	FindHourlyServerBandwidthStats(context.Context, *FindHourlyServerBandwidthStatsRequest) (*FindHourlyServerBandwidthStatsResponse, error)
	// 获取最近N天峰值带宽
	FindDailyServerBandwidthStats(context.Context, *FindDailyServerBandwidthStatsRequest) (*FindDailyServerBandwidthStatsResponse, error)
	// 读取日期段内的带宽数据
	FindDailyServerBandwidthStatsBetweenDays(context.Context, *FindDailyServerBandwidthStatsBetweenDaysRequest) (*FindDailyServerBandwidthStatsBetweenDaysResponse, error)
}

// UnimplementedServerBandwidthStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerBandwidthStatServiceServer struct {
}

func (UnimplementedServerBandwidthStatServiceServer) UploadServerBandwidthStats(context.Context, *UploadServerBandwidthStatsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadServerBandwidthStats not implemented")
}
func (UnimplementedServerBandwidthStatServiceServer) FindServerBandwidthStats(context.Context, *FindServerBandwidthStatsRequest) (*FindServerBandwidthStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerBandwidthStats not implemented")
}
func (UnimplementedServerBandwidthStatServiceServer) FindHourlyServerBandwidthStats(context.Context, *FindHourlyServerBandwidthStatsRequest) (*FindHourlyServerBandwidthStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHourlyServerBandwidthStats not implemented")
}
func (UnimplementedServerBandwidthStatServiceServer) FindDailyServerBandwidthStats(context.Context, *FindDailyServerBandwidthStatsRequest) (*FindDailyServerBandwidthStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDailyServerBandwidthStats not implemented")
}
func (UnimplementedServerBandwidthStatServiceServer) FindDailyServerBandwidthStatsBetweenDays(context.Context, *FindDailyServerBandwidthStatsBetweenDaysRequest) (*FindDailyServerBandwidthStatsBetweenDaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDailyServerBandwidthStatsBetweenDays not implemented")
}

// UnsafeServerBandwidthStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerBandwidthStatServiceServer will
// result in compilation errors.
type UnsafeServerBandwidthStatServiceServer interface {
	mustEmbedUnimplementedServerBandwidthStatServiceServer()
}

func RegisterServerBandwidthStatServiceServer(s grpc.ServiceRegistrar, srv ServerBandwidthStatServiceServer) {
	s.RegisterService(&ServerBandwidthStatService_ServiceDesc, srv)
}

func _ServerBandwidthStatService_UploadServerBandwidthStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadServerBandwidthStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBandwidthStatServiceServer).UploadServerBandwidthStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBandwidthStatService_UploadServerBandwidthStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBandwidthStatServiceServer).UploadServerBandwidthStats(ctx, req.(*UploadServerBandwidthStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerBandwidthStatService_FindServerBandwidthStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerBandwidthStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBandwidthStatServiceServer).FindServerBandwidthStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBandwidthStatService_FindServerBandwidthStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBandwidthStatServiceServer).FindServerBandwidthStats(ctx, req.(*FindServerBandwidthStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerBandwidthStatService_FindHourlyServerBandwidthStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHourlyServerBandwidthStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBandwidthStatServiceServer).FindHourlyServerBandwidthStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBandwidthStatService_FindHourlyServerBandwidthStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBandwidthStatServiceServer).FindHourlyServerBandwidthStats(ctx, req.(*FindHourlyServerBandwidthStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerBandwidthStatService_FindDailyServerBandwidthStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindDailyServerBandwidthStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBandwidthStatServiceServer).FindDailyServerBandwidthStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBandwidthStatService_FindDailyServerBandwidthStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBandwidthStatServiceServer).FindDailyServerBandwidthStats(ctx, req.(*FindDailyServerBandwidthStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerBandwidthStatService_FindDailyServerBandwidthStatsBetweenDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindDailyServerBandwidthStatsBetweenDaysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerBandwidthStatServiceServer).FindDailyServerBandwidthStatsBetweenDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerBandwidthStatService_FindDailyServerBandwidthStatsBetweenDays_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerBandwidthStatServiceServer).FindDailyServerBandwidthStatsBetweenDays(ctx, req.(*FindDailyServerBandwidthStatsBetweenDaysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerBandwidthStatService_ServiceDesc is the grpc.ServiceDesc for ServerBandwidthStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerBandwidthStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerBandwidthStatService",
	HandlerType: (*ServerBandwidthStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "uploadServerBandwidthStats",
			Handler:    _ServerBandwidthStatService_UploadServerBandwidthStats_Handler,
		},
		{
			MethodName: "findServerBandwidthStats",
			Handler:    _ServerBandwidthStatService_FindServerBandwidthStats_Handler,
		},
		{
			MethodName: "findHourlyServerBandwidthStats",
			Handler:    _ServerBandwidthStatService_FindHourlyServerBandwidthStats_Handler,
		},
		{
			MethodName: "findDailyServerBandwidthStats",
			Handler:    _ServerBandwidthStatService_FindDailyServerBandwidthStats_Handler,
		},
		{
			MethodName: "findDailyServerBandwidthStatsBetweenDays",
			Handler:    _ServerBandwidthStatService_FindDailyServerBandwidthStatsBetweenDays_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_bandwidth_stat.proto",
}
