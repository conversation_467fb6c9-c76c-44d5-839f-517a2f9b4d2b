// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_identity.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserIdentity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgType       string                 `protobuf:"bytes,2,opt,name=orgType,proto3" json:"orgType,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	RealName      string                 `protobuf:"bytes,4,opt,name=realName,proto3" json:"realName,omitempty"`
	Number        string                 `protobuf:"bytes,5,opt,name=number,proto3" json:"number,omitempty"`
	FileIds       []int64                `protobuf:"varint,6,rep,packed,name=fileIds,proto3" json:"fileIds,omitempty"`
	Status        string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,8,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,9,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	SubmittedAt   int64                  `protobuf:"varint,10,opt,name=submittedAt,proto3" json:"submittedAt,omitempty"`
	RejectedAt    int64                  `protobuf:"varint,11,opt,name=rejectedAt,proto3" json:"rejectedAt,omitempty"`
	VerifiedAt    int64                  `protobuf:"varint,12,opt,name=verifiedAt,proto3" json:"verifiedAt,omitempty"`
	RejectReason  string                 `protobuf:"bytes,13,opt,name=rejectReason,proto3" json:"rejectReason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdentity) Reset() {
	*x = UserIdentity{}
	mi := &file_models_model_user_identity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdentity) ProtoMessage() {}

func (x *UserIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_identity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdentity.ProtoReflect.Descriptor instead.
func (*UserIdentity) Descriptor() ([]byte, []int) {
	return file_models_model_user_identity_proto_rawDescGZIP(), []int{0}
}

func (x *UserIdentity) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserIdentity) GetOrgType() string {
	if x != nil {
		return x.OrgType
	}
	return ""
}

func (x *UserIdentity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserIdentity) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UserIdentity) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *UserIdentity) GetFileIds() []int64 {
	if x != nil {
		return x.FileIds
	}
	return nil
}

func (x *UserIdentity) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserIdentity) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserIdentity) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *UserIdentity) GetSubmittedAt() int64 {
	if x != nil {
		return x.SubmittedAt
	}
	return 0
}

func (x *UserIdentity) GetRejectedAt() int64 {
	if x != nil {
		return x.RejectedAt
	}
	return 0
}

func (x *UserIdentity) GetVerifiedAt() int64 {
	if x != nil {
		return x.VerifiedAt
	}
	return 0
}

func (x *UserIdentity) GetRejectReason() string {
	if x != nil {
		return x.RejectReason
	}
	return ""
}

var File_models_model_user_identity_proto protoreflect.FileDescriptor

const file_models_model_user_identity_proto_rawDesc = "" +
	"\n" +
	" models/model_user_identity.proto\x12\x02pb\"\xf4\x02\n" +
	"\fUserIdentity\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\aorgType\x18\x02 \x01(\tR\aorgType\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x1a\n" +
	"\brealName\x18\x04 \x01(\tR\brealName\x12\x16\n" +
	"\x06number\x18\x05 \x01(\tR\x06number\x12\x18\n" +
	"\afileIds\x18\x06 \x03(\x03R\afileIds\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\x12\x1c\n" +
	"\tcreatedAt\x18\b \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\t \x01(\x03R\tupdatedAt\x12 \n" +
	"\vsubmittedAt\x18\n" +
	" \x01(\x03R\vsubmittedAt\x12\x1e\n" +
	"\n" +
	"rejectedAt\x18\v \x01(\x03R\n" +
	"rejectedAt\x12\x1e\n" +
	"\n" +
	"verifiedAt\x18\f \x01(\x03R\n" +
	"verifiedAt\x12\"\n" +
	"\frejectReason\x18\r \x01(\tR\frejectReasonB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_identity_proto_rawDescOnce sync.Once
	file_models_model_user_identity_proto_rawDescData []byte
)

func file_models_model_user_identity_proto_rawDescGZIP() []byte {
	file_models_model_user_identity_proto_rawDescOnce.Do(func() {
		file_models_model_user_identity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_identity_proto_rawDesc), len(file_models_model_user_identity_proto_rawDesc)))
	})
	return file_models_model_user_identity_proto_rawDescData
}

var file_models_model_user_identity_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_identity_proto_goTypes = []any{
	(*UserIdentity)(nil), // 0: pb.UserIdentity
}
var file_models_model_user_identity_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_user_identity_proto_init() }
func file_models_model_user_identity_proto_init() {
	if File_models_model_user_identity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_identity_proto_rawDesc), len(file_models_model_user_identity_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_identity_proto_goTypes,
		DependencyIndexes: file_models_model_user_identity_proto_depIdxs,
		MessageInfos:      file_models_model_user_identity_proto_msgTypes,
	}.Build()
	File_models_model_user_identity_proto = out.File
	file_models_model_user_identity_proto_goTypes = nil
	file_models_model_user_identity_proto_depIdxs = nil
}
