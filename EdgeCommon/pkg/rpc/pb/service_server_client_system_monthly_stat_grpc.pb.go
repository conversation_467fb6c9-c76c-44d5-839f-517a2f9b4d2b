// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_client_system_monthly_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerClientSystemMonthlyStatService_FindTopServerClientSystemMonthlyStats_FullMethodName = "/pb.ServerClientSystemMonthlyStatService/findTopServerClientSystemMonthlyStats"
)

// ServerClientSystemMonthlyStatServiceClient is the client API for ServerClientSystemMonthlyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerClientSystemMonthlyStatServiceClient interface {
	// 查找前N个操作系统
	FindTopServerClientSystemMonthlyStats(ctx context.Context, in *FindTopServerClientSystemMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerClientSystemMonthlyStatsResponse, error)
}

type serverClientSystemMonthlyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerClientSystemMonthlyStatServiceClient(cc grpc.ClientConnInterface) ServerClientSystemMonthlyStatServiceClient {
	return &serverClientSystemMonthlyStatServiceClient{cc}
}

func (c *serverClientSystemMonthlyStatServiceClient) FindTopServerClientSystemMonthlyStats(ctx context.Context, in *FindTopServerClientSystemMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerClientSystemMonthlyStatsResponse, error) {
	out := new(FindTopServerClientSystemMonthlyStatsResponse)
	err := c.cc.Invoke(ctx, ServerClientSystemMonthlyStatService_FindTopServerClientSystemMonthlyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerClientSystemMonthlyStatServiceServer is the server API for ServerClientSystemMonthlyStatService service.
// All implementations should embed UnimplementedServerClientSystemMonthlyStatServiceServer
// for forward compatibility
type ServerClientSystemMonthlyStatServiceServer interface {
	// 查找前N个操作系统
	FindTopServerClientSystemMonthlyStats(context.Context, *FindTopServerClientSystemMonthlyStatsRequest) (*FindTopServerClientSystemMonthlyStatsResponse, error)
}

// UnimplementedServerClientSystemMonthlyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerClientSystemMonthlyStatServiceServer struct {
}

func (UnimplementedServerClientSystemMonthlyStatServiceServer) FindTopServerClientSystemMonthlyStats(context.Context, *FindTopServerClientSystemMonthlyStatsRequest) (*FindTopServerClientSystemMonthlyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTopServerClientSystemMonthlyStats not implemented")
}

// UnsafeServerClientSystemMonthlyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerClientSystemMonthlyStatServiceServer will
// result in compilation errors.
type UnsafeServerClientSystemMonthlyStatServiceServer interface {
	mustEmbedUnimplementedServerClientSystemMonthlyStatServiceServer()
}

func RegisterServerClientSystemMonthlyStatServiceServer(s grpc.ServiceRegistrar, srv ServerClientSystemMonthlyStatServiceServer) {
	s.RegisterService(&ServerClientSystemMonthlyStatService_ServiceDesc, srv)
}

func _ServerClientSystemMonthlyStatService_FindTopServerClientSystemMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTopServerClientSystemMonthlyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerClientSystemMonthlyStatServiceServer).FindTopServerClientSystemMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerClientSystemMonthlyStatService_FindTopServerClientSystemMonthlyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerClientSystemMonthlyStatServiceServer).FindTopServerClientSystemMonthlyStats(ctx, req.(*FindTopServerClientSystemMonthlyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerClientSystemMonthlyStatService_ServiceDesc is the grpc.ServiceDesc for ServerClientSystemMonthlyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerClientSystemMonthlyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerClientSystemMonthlyStatService",
	HandlerType: (*ServerClientSystemMonthlyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findTopServerClientSystemMonthlyStats",
			Handler:    _ServerClientSystemMonthlyStatService_FindTopServerClientSystemMonthlyStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_client_system_monthly_stat.proto",
}
