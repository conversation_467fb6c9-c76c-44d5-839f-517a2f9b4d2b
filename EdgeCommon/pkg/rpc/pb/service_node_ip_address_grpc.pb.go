// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_ip_address.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeIPAddressService_CreateNodeIPAddress_FullMethodName                     = "/pb.NodeIPAddressService/createNodeIPAddress"
	NodeIPAddressService_CreateNodeIPAddresses_FullMethodName                   = "/pb.NodeIPAddressService/createNodeIPAddresses"
	NodeIPAddressService_UpdateNodeIPAddress_FullMethodName                     = "/pb.NodeIPAddressService/updateNodeIPAddress"
	NodeIPAddressService_UpdateNodeIPAddressNodeId_FullMethodName               = "/pb.NodeIPAddressService/updateNodeIPAddressNodeId"
	NodeIPAddressService_DisableNodeIPAddress_FullMethodName                    = "/pb.NodeIPAddressService/disableNodeIPAddress"
	NodeIPAddressService_DisableAllNodeIPAddressesWithNodeId_FullMethodName     = "/pb.NodeIPAddressService/disableAllNodeIPAddressesWithNodeId"
	NodeIPAddressService_FindEnabledNodeIPAddress_FullMethodName                = "/pb.NodeIPAddressService/findEnabledNodeIPAddress"
	NodeIPAddressService_FindAllEnabledNodeIPAddressesWithNodeId_FullMethodName = "/pb.NodeIPAddressService/findAllEnabledNodeIPAddressesWithNodeId"
	NodeIPAddressService_CountAllEnabledNodeIPAddresses_FullMethodName          = "/pb.NodeIPAddressService/countAllEnabledNodeIPAddresses"
	NodeIPAddressService_ListEnabledNodeIPAddresses_FullMethodName              = "/pb.NodeIPAddressService/listEnabledNodeIPAddresses"
	NodeIPAddressService_UpdateNodeIPAddressIsUp_FullMethodName                 = "/pb.NodeIPAddressService/updateNodeIPAddressIsUp"
	NodeIPAddressService_RestoreNodeIPAddressBackupIP_FullMethodName            = "/pb.NodeIPAddressService/restoreNodeIPAddressBackupIP"
)

// NodeIPAddressServiceClient is the client API for NodeIPAddressService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeIPAddressServiceClient interface {
	// 创建IP地址
	CreateNodeIPAddress(ctx context.Context, in *CreateNodeIPAddressRequest, opts ...grpc.CallOption) (*CreateNodeIPAddressResponse, error)
	// 批量创建IP地址
	CreateNodeIPAddresses(ctx context.Context, in *CreateNodeIPAddressesRequest, opts ...grpc.CallOption) (*CreateNodeIPAddressesResponse, error)
	// 修改IP地址
	UpdateNodeIPAddress(ctx context.Context, in *UpdateNodeIPAddressRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改IP地址所属节点
	UpdateNodeIPAddressNodeId(ctx context.Context, in *UpdateNodeIPAddressNodeIdRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 禁用单个IP地址
	DisableNodeIPAddress(ctx context.Context, in *DisableNodeIPAddressRequest, opts ...grpc.CallOption) (*DisableNodeIPAddressResponse, error)
	// 禁用节点的所有IP地址
	DisableAllNodeIPAddressesWithNodeId(ctx context.Context, in *DisableAllNodeIPAddressesWithNodeIdRequest, opts ...grpc.CallOption) (*DisableAllNodeIPAddressesWithNodeIdResponse, error)
	// 查找单个IP地址
	FindEnabledNodeIPAddress(ctx context.Context, in *FindEnabledNodeIPAddressRequest, opts ...grpc.CallOption) (*FindEnabledNodeIPAddressResponse, error)
	// 查找节点的所有地址
	FindAllEnabledNodeIPAddressesWithNodeId(ctx context.Context, in *FindAllEnabledNodeIPAddressesWithNodeIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeIPAddressesWithNodeIdResponse, error)
	// 计算IP地址数量
	CountAllEnabledNodeIPAddresses(ctx context.Context, in *CountAllEnabledNodeIPAddressesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页IP地址
	ListEnabledNodeIPAddresses(ctx context.Context, in *ListEnabledNodeIPAddressesRequest, opts ...grpc.CallOption) (*ListEnabledNodeIPAddressesResponse, error)
	// 设置上下线状态
	UpdateNodeIPAddressIsUp(ctx context.Context, in *UpdateNodeIPAddressIsUpRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 还原备用IP状态
	RestoreNodeIPAddressBackupIP(ctx context.Context, in *RestoreNodeIPAddressBackupIPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeIPAddressServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeIPAddressServiceClient(cc grpc.ClientConnInterface) NodeIPAddressServiceClient {
	return &nodeIPAddressServiceClient{cc}
}

func (c *nodeIPAddressServiceClient) CreateNodeIPAddress(ctx context.Context, in *CreateNodeIPAddressRequest, opts ...grpc.CallOption) (*CreateNodeIPAddressResponse, error) {
	out := new(CreateNodeIPAddressResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_CreateNodeIPAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) CreateNodeIPAddresses(ctx context.Context, in *CreateNodeIPAddressesRequest, opts ...grpc.CallOption) (*CreateNodeIPAddressesResponse, error) {
	out := new(CreateNodeIPAddressesResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_CreateNodeIPAddresses_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) UpdateNodeIPAddress(ctx context.Context, in *UpdateNodeIPAddressRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressService_UpdateNodeIPAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) UpdateNodeIPAddressNodeId(ctx context.Context, in *UpdateNodeIPAddressNodeIdRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressService_UpdateNodeIPAddressNodeId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) DisableNodeIPAddress(ctx context.Context, in *DisableNodeIPAddressRequest, opts ...grpc.CallOption) (*DisableNodeIPAddressResponse, error) {
	out := new(DisableNodeIPAddressResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_DisableNodeIPAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) DisableAllNodeIPAddressesWithNodeId(ctx context.Context, in *DisableAllNodeIPAddressesWithNodeIdRequest, opts ...grpc.CallOption) (*DisableAllNodeIPAddressesWithNodeIdResponse, error) {
	out := new(DisableAllNodeIPAddressesWithNodeIdResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_DisableAllNodeIPAddressesWithNodeId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) FindEnabledNodeIPAddress(ctx context.Context, in *FindEnabledNodeIPAddressRequest, opts ...grpc.CallOption) (*FindEnabledNodeIPAddressResponse, error) {
	out := new(FindEnabledNodeIPAddressResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_FindEnabledNodeIPAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) FindAllEnabledNodeIPAddressesWithNodeId(ctx context.Context, in *FindAllEnabledNodeIPAddressesWithNodeIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeIPAddressesWithNodeIdResponse, error) {
	out := new(FindAllEnabledNodeIPAddressesWithNodeIdResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_FindAllEnabledNodeIPAddressesWithNodeId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) CountAllEnabledNodeIPAddresses(ctx context.Context, in *CountAllEnabledNodeIPAddressesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_CountAllEnabledNodeIPAddresses_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) ListEnabledNodeIPAddresses(ctx context.Context, in *ListEnabledNodeIPAddressesRequest, opts ...grpc.CallOption) (*ListEnabledNodeIPAddressesResponse, error) {
	out := new(ListEnabledNodeIPAddressesResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressService_ListEnabledNodeIPAddresses_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) UpdateNodeIPAddressIsUp(ctx context.Context, in *UpdateNodeIPAddressIsUpRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressService_UpdateNodeIPAddressIsUp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressServiceClient) RestoreNodeIPAddressBackupIP(ctx context.Context, in *RestoreNodeIPAddressBackupIPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressService_RestoreNodeIPAddressBackupIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeIPAddressServiceServer is the server API for NodeIPAddressService service.
// All implementations should embed UnimplementedNodeIPAddressServiceServer
// for forward compatibility
type NodeIPAddressServiceServer interface {
	// 创建IP地址
	CreateNodeIPAddress(context.Context, *CreateNodeIPAddressRequest) (*CreateNodeIPAddressResponse, error)
	// 批量创建IP地址
	CreateNodeIPAddresses(context.Context, *CreateNodeIPAddressesRequest) (*CreateNodeIPAddressesResponse, error)
	// 修改IP地址
	UpdateNodeIPAddress(context.Context, *UpdateNodeIPAddressRequest) (*RPCSuccess, error)
	// 修改IP地址所属节点
	UpdateNodeIPAddressNodeId(context.Context, *UpdateNodeIPAddressNodeIdRequest) (*RPCSuccess, error)
	// 禁用单个IP地址
	DisableNodeIPAddress(context.Context, *DisableNodeIPAddressRequest) (*DisableNodeIPAddressResponse, error)
	// 禁用节点的所有IP地址
	DisableAllNodeIPAddressesWithNodeId(context.Context, *DisableAllNodeIPAddressesWithNodeIdRequest) (*DisableAllNodeIPAddressesWithNodeIdResponse, error)
	// 查找单个IP地址
	FindEnabledNodeIPAddress(context.Context, *FindEnabledNodeIPAddressRequest) (*FindEnabledNodeIPAddressResponse, error)
	// 查找节点的所有地址
	FindAllEnabledNodeIPAddressesWithNodeId(context.Context, *FindAllEnabledNodeIPAddressesWithNodeIdRequest) (*FindAllEnabledNodeIPAddressesWithNodeIdResponse, error)
	// 计算IP地址数量
	CountAllEnabledNodeIPAddresses(context.Context, *CountAllEnabledNodeIPAddressesRequest) (*RPCCountResponse, error)
	// 列出单页IP地址
	ListEnabledNodeIPAddresses(context.Context, *ListEnabledNodeIPAddressesRequest) (*ListEnabledNodeIPAddressesResponse, error)
	// 设置上下线状态
	UpdateNodeIPAddressIsUp(context.Context, *UpdateNodeIPAddressIsUpRequest) (*RPCSuccess, error)
	// 还原备用IP状态
	RestoreNodeIPAddressBackupIP(context.Context, *RestoreNodeIPAddressBackupIPRequest) (*RPCSuccess, error)
}

// UnimplementedNodeIPAddressServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeIPAddressServiceServer struct {
}

func (UnimplementedNodeIPAddressServiceServer) CreateNodeIPAddress(context.Context, *CreateNodeIPAddressRequest) (*CreateNodeIPAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeIPAddress not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) CreateNodeIPAddresses(context.Context, *CreateNodeIPAddressesRequest) (*CreateNodeIPAddressesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeIPAddresses not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) UpdateNodeIPAddress(context.Context, *UpdateNodeIPAddressRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeIPAddress not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) UpdateNodeIPAddressNodeId(context.Context, *UpdateNodeIPAddressNodeIdRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeIPAddressNodeId not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) DisableNodeIPAddress(context.Context, *DisableNodeIPAddressRequest) (*DisableNodeIPAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableNodeIPAddress not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) DisableAllNodeIPAddressesWithNodeId(context.Context, *DisableAllNodeIPAddressesWithNodeIdRequest) (*DisableAllNodeIPAddressesWithNodeIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableAllNodeIPAddressesWithNodeId not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) FindEnabledNodeIPAddress(context.Context, *FindEnabledNodeIPAddressRequest) (*FindEnabledNodeIPAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeIPAddress not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) FindAllEnabledNodeIPAddressesWithNodeId(context.Context, *FindAllEnabledNodeIPAddressesWithNodeIdRequest) (*FindAllEnabledNodeIPAddressesWithNodeIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeIPAddressesWithNodeId not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) CountAllEnabledNodeIPAddresses(context.Context, *CountAllEnabledNodeIPAddressesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeIPAddresses not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) ListEnabledNodeIPAddresses(context.Context, *ListEnabledNodeIPAddressesRequest) (*ListEnabledNodeIPAddressesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledNodeIPAddresses not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) UpdateNodeIPAddressIsUp(context.Context, *UpdateNodeIPAddressIsUpRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeIPAddressIsUp not implemented")
}
func (UnimplementedNodeIPAddressServiceServer) RestoreNodeIPAddressBackupIP(context.Context, *RestoreNodeIPAddressBackupIPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestoreNodeIPAddressBackupIP not implemented")
}

// UnsafeNodeIPAddressServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeIPAddressServiceServer will
// result in compilation errors.
type UnsafeNodeIPAddressServiceServer interface {
	mustEmbedUnimplementedNodeIPAddressServiceServer()
}

func RegisterNodeIPAddressServiceServer(s grpc.ServiceRegistrar, srv NodeIPAddressServiceServer) {
	s.RegisterService(&NodeIPAddressService_ServiceDesc, srv)
}

func _NodeIPAddressService_CreateNodeIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).CreateNodeIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_CreateNodeIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).CreateNodeIPAddress(ctx, req.(*CreateNodeIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_CreateNodeIPAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeIPAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).CreateNodeIPAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_CreateNodeIPAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).CreateNodeIPAddresses(ctx, req.(*CreateNodeIPAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_UpdateNodeIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).UpdateNodeIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_UpdateNodeIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).UpdateNodeIPAddress(ctx, req.(*UpdateNodeIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_UpdateNodeIPAddressNodeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeIPAddressNodeIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).UpdateNodeIPAddressNodeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_UpdateNodeIPAddressNodeId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).UpdateNodeIPAddressNodeId(ctx, req.(*UpdateNodeIPAddressNodeIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_DisableNodeIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableNodeIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).DisableNodeIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_DisableNodeIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).DisableNodeIPAddress(ctx, req.(*DisableNodeIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_DisableAllNodeIPAddressesWithNodeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableAllNodeIPAddressesWithNodeIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).DisableAllNodeIPAddressesWithNodeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_DisableAllNodeIPAddressesWithNodeId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).DisableAllNodeIPAddressesWithNodeId(ctx, req.(*DisableAllNodeIPAddressesWithNodeIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_FindEnabledNodeIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).FindEnabledNodeIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_FindEnabledNodeIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).FindEnabledNodeIPAddress(ctx, req.(*FindEnabledNodeIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_FindAllEnabledNodeIPAddressesWithNodeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeIPAddressesWithNodeIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).FindAllEnabledNodeIPAddressesWithNodeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_FindAllEnabledNodeIPAddressesWithNodeId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).FindAllEnabledNodeIPAddressesWithNodeId(ctx, req.(*FindAllEnabledNodeIPAddressesWithNodeIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_CountAllEnabledNodeIPAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeIPAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).CountAllEnabledNodeIPAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_CountAllEnabledNodeIPAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).CountAllEnabledNodeIPAddresses(ctx, req.(*CountAllEnabledNodeIPAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_ListEnabledNodeIPAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledNodeIPAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).ListEnabledNodeIPAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_ListEnabledNodeIPAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).ListEnabledNodeIPAddresses(ctx, req.(*ListEnabledNodeIPAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_UpdateNodeIPAddressIsUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeIPAddressIsUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).UpdateNodeIPAddressIsUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_UpdateNodeIPAddressIsUp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).UpdateNodeIPAddressIsUp(ctx, req.(*UpdateNodeIPAddressIsUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressService_RestoreNodeIPAddressBackupIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestoreNodeIPAddressBackupIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressServiceServer).RestoreNodeIPAddressBackupIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressService_RestoreNodeIPAddressBackupIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressServiceServer).RestoreNodeIPAddressBackupIP(ctx, req.(*RestoreNodeIPAddressBackupIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeIPAddressService_ServiceDesc is the grpc.ServiceDesc for NodeIPAddressService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeIPAddressService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeIPAddressService",
	HandlerType: (*NodeIPAddressServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeIPAddress",
			Handler:    _NodeIPAddressService_CreateNodeIPAddress_Handler,
		},
		{
			MethodName: "createNodeIPAddresses",
			Handler:    _NodeIPAddressService_CreateNodeIPAddresses_Handler,
		},
		{
			MethodName: "updateNodeIPAddress",
			Handler:    _NodeIPAddressService_UpdateNodeIPAddress_Handler,
		},
		{
			MethodName: "updateNodeIPAddressNodeId",
			Handler:    _NodeIPAddressService_UpdateNodeIPAddressNodeId_Handler,
		},
		{
			MethodName: "disableNodeIPAddress",
			Handler:    _NodeIPAddressService_DisableNodeIPAddress_Handler,
		},
		{
			MethodName: "disableAllNodeIPAddressesWithNodeId",
			Handler:    _NodeIPAddressService_DisableAllNodeIPAddressesWithNodeId_Handler,
		},
		{
			MethodName: "findEnabledNodeIPAddress",
			Handler:    _NodeIPAddressService_FindEnabledNodeIPAddress_Handler,
		},
		{
			MethodName: "findAllEnabledNodeIPAddressesWithNodeId",
			Handler:    _NodeIPAddressService_FindAllEnabledNodeIPAddressesWithNodeId_Handler,
		},
		{
			MethodName: "countAllEnabledNodeIPAddresses",
			Handler:    _NodeIPAddressService_CountAllEnabledNodeIPAddresses_Handler,
		},
		{
			MethodName: "listEnabledNodeIPAddresses",
			Handler:    _NodeIPAddressService_ListEnabledNodeIPAddresses_Handler,
		},
		{
			MethodName: "updateNodeIPAddressIsUp",
			Handler:    _NodeIPAddressService_UpdateNodeIPAddressIsUp_Handler,
		},
		{
			MethodName: "restoreNodeIPAddressBackupIP",
			Handler:    _NodeIPAddressService_RestoreNodeIPAddressBackupIP_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_ip_address.proto",
}
