// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_ticket.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 工单
type UserTicket struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CategoryId          int64                  `protobuf:"varint,2,opt,name=categoryId,proto3" json:"categoryId,omitempty"`
	UserId              int64                  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	Subject             string                 `protobuf:"bytes,4,opt,name=subject,proto3" json:"subject,omitempty"`
	Body                string                 `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
	Status              string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt           int64                  `protobuf:"varint,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	LastLogAt           int64                  `protobuf:"varint,8,opt,name=lastLogAt,proto3" json:"lastLogAt,omitempty"`
	UserTicketCategory  *UserTicketCategory    `protobuf:"bytes,30,opt,name=userTicketCategory,proto3" json:"userTicketCategory,omitempty"`
	User                *User                  `protobuf:"bytes,31,opt,name=user,proto3" json:"user,omitempty"`
	LatestUserTicketLog *UserTicketLog         `protobuf:"bytes,32,opt,name=latestUserTicketLog,proto3" json:"latestUserTicketLog,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UserTicket) Reset() {
	*x = UserTicket{}
	mi := &file_models_model_user_ticket_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTicket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTicket) ProtoMessage() {}

func (x *UserTicket) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_ticket_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTicket.ProtoReflect.Descriptor instead.
func (*UserTicket) Descriptor() ([]byte, []int) {
	return file_models_model_user_ticket_proto_rawDescGZIP(), []int{0}
}

func (x *UserTicket) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserTicket) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UserTicket) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserTicket) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *UserTicket) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *UserTicket) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserTicket) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserTicket) GetLastLogAt() int64 {
	if x != nil {
		return x.LastLogAt
	}
	return 0
}

func (x *UserTicket) GetUserTicketCategory() *UserTicketCategory {
	if x != nil {
		return x.UserTicketCategory
	}
	return nil
}

func (x *UserTicket) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserTicket) GetLatestUserTicketLog() *UserTicketLog {
	if x != nil {
		return x.LatestUserTicketLog
	}
	return nil
}

var File_models_model_user_ticket_proto protoreflect.FileDescriptor

const file_models_model_user_ticket_proto_rawDesc = "" +
	"\n" +
	"\x1emodels/model_user_ticket.proto\x12\x02pb\x1a'models/model_user_ticket_category.proto\x1a\x17models/model_user.proto\x1a\"models/model_user_ticket_log.proto\"\x81\x03\n" +
	"\n" +
	"UserTicket\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"categoryId\x18\x02 \x01(\x03R\n" +
	"categoryId\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\x03R\x06userId\x12\x18\n" +
	"\asubject\x18\x04 \x01(\tR\asubject\x12\x12\n" +
	"\x04body\x18\x05 \x01(\tR\x04body\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\x12\x1c\n" +
	"\tcreatedAt\x18\a \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tlastLogAt\x18\b \x01(\x03R\tlastLogAt\x12F\n" +
	"\x12userTicketCategory\x18\x1e \x01(\v2\x16.pb.UserTicketCategoryR\x12userTicketCategory\x12\x1c\n" +
	"\x04user\x18\x1f \x01(\v2\b.pb.UserR\x04user\x12C\n" +
	"\x13latestUserTicketLog\x18  \x01(\v2\x11.pb.UserTicketLogR\x13latestUserTicketLogB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_ticket_proto_rawDescOnce sync.Once
	file_models_model_user_ticket_proto_rawDescData []byte
)

func file_models_model_user_ticket_proto_rawDescGZIP() []byte {
	file_models_model_user_ticket_proto_rawDescOnce.Do(func() {
		file_models_model_user_ticket_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_ticket_proto_rawDesc), len(file_models_model_user_ticket_proto_rawDesc)))
	})
	return file_models_model_user_ticket_proto_rawDescData
}

var file_models_model_user_ticket_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_ticket_proto_goTypes = []any{
	(*UserTicket)(nil),         // 0: pb.UserTicket
	(*UserTicketCategory)(nil), // 1: pb.UserTicketCategory
	(*User)(nil),               // 2: pb.User
	(*UserTicketLog)(nil),      // 3: pb.UserTicketLog
}
var file_models_model_user_ticket_proto_depIdxs = []int32{
	1, // 0: pb.UserTicket.userTicketCategory:type_name -> pb.UserTicketCategory
	2, // 1: pb.UserTicket.user:type_name -> pb.User
	3, // 2: pb.UserTicket.latestUserTicketLog:type_name -> pb.UserTicketLog
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_models_model_user_ticket_proto_init() }
func file_models_model_user_ticket_proto_init() {
	if File_models_model_user_ticket_proto != nil {
		return
	}
	file_models_model_user_ticket_category_proto_init()
	file_models_model_user_proto_init()
	file_models_model_user_ticket_log_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_ticket_proto_rawDesc), len(file_models_model_user_ticket_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_ticket_proto_goTypes,
		DependencyIndexes: file_models_model_user_ticket_proto_depIdxs,
		MessageInfos:      file_models_model_user_ticket_proto_msgTypes,
	}.Build()
	File_models_model_user_ticket_proto = out.File
	file_models_model_user_ticket_proto_goTypes = nil
	file_models_model_user_ticket_proto_depIdxs = nil
}
