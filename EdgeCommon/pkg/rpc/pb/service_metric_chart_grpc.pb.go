// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_metric_chart.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MetricChartService_CreateMetricChart_FullMethodName        = "/pb.MetricChartService/createMetricChart"
	MetricChartService_UpdateMetricChart_FullMethodName        = "/pb.MetricChartService/updateMetricChart"
	MetricChartService_FindEnabledMetricChart_FullMethodName   = "/pb.MetricChartService/findEnabledMetricChart"
	MetricChartService_CountEnabledMetricCharts_FullMethodName = "/pb.MetricChartService/countEnabledMetricCharts"
	MetricChartService_ListEnabledMetricCharts_FullMethodName  = "/pb.MetricChartService/listEnabledMetricCharts"
	MetricChartService_DeleteMetricChart_FullMethodName        = "/pb.MetricChartService/deleteMetricChart"
)

// MetricChartServiceClient is the client API for MetricChartService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MetricChartServiceClient interface {
	// 创建图表
	CreateMetricChart(ctx context.Context, in *CreateMetricChartRequest, opts ...grpc.CallOption) (*CreateMetricChartResponse, error)
	// 修改图表
	UpdateMetricChart(ctx context.Context, in *UpdateMetricChartRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个图表
	FindEnabledMetricChart(ctx context.Context, in *FindEnabledMetricChartRequest, opts ...grpc.CallOption) (*FindEnabledMetricChartResponse, error)
	// 计算图表数量
	CountEnabledMetricCharts(ctx context.Context, in *CountEnabledMetricChartsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页图表
	ListEnabledMetricCharts(ctx context.Context, in *ListEnabledMetricChartsRequest, opts ...grpc.CallOption) (*ListEnabledMetricChartsResponse, error)
	// 删除图表
	DeleteMetricChart(ctx context.Context, in *DeleteMetricChartRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type metricChartServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMetricChartServiceClient(cc grpc.ClientConnInterface) MetricChartServiceClient {
	return &metricChartServiceClient{cc}
}

func (c *metricChartServiceClient) CreateMetricChart(ctx context.Context, in *CreateMetricChartRequest, opts ...grpc.CallOption) (*CreateMetricChartResponse, error) {
	out := new(CreateMetricChartResponse)
	err := c.cc.Invoke(ctx, MetricChartService_CreateMetricChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricChartServiceClient) UpdateMetricChart(ctx context.Context, in *UpdateMetricChartRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MetricChartService_UpdateMetricChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricChartServiceClient) FindEnabledMetricChart(ctx context.Context, in *FindEnabledMetricChartRequest, opts ...grpc.CallOption) (*FindEnabledMetricChartResponse, error) {
	out := new(FindEnabledMetricChartResponse)
	err := c.cc.Invoke(ctx, MetricChartService_FindEnabledMetricChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricChartServiceClient) CountEnabledMetricCharts(ctx context.Context, in *CountEnabledMetricChartsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, MetricChartService_CountEnabledMetricCharts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricChartServiceClient) ListEnabledMetricCharts(ctx context.Context, in *ListEnabledMetricChartsRequest, opts ...grpc.CallOption) (*ListEnabledMetricChartsResponse, error) {
	out := new(ListEnabledMetricChartsResponse)
	err := c.cc.Invoke(ctx, MetricChartService_ListEnabledMetricCharts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metricChartServiceClient) DeleteMetricChart(ctx context.Context, in *DeleteMetricChartRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MetricChartService_DeleteMetricChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetricChartServiceServer is the server API for MetricChartService service.
// All implementations should embed UnimplementedMetricChartServiceServer
// for forward compatibility
type MetricChartServiceServer interface {
	// 创建图表
	CreateMetricChart(context.Context, *CreateMetricChartRequest) (*CreateMetricChartResponse, error)
	// 修改图表
	UpdateMetricChart(context.Context, *UpdateMetricChartRequest) (*RPCSuccess, error)
	// 查找单个图表
	FindEnabledMetricChart(context.Context, *FindEnabledMetricChartRequest) (*FindEnabledMetricChartResponse, error)
	// 计算图表数量
	CountEnabledMetricCharts(context.Context, *CountEnabledMetricChartsRequest) (*RPCCountResponse, error)
	// 列出单页图表
	ListEnabledMetricCharts(context.Context, *ListEnabledMetricChartsRequest) (*ListEnabledMetricChartsResponse, error)
	// 删除图表
	DeleteMetricChart(context.Context, *DeleteMetricChartRequest) (*RPCSuccess, error)
}

// UnimplementedMetricChartServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMetricChartServiceServer struct {
}

func (UnimplementedMetricChartServiceServer) CreateMetricChart(context.Context, *CreateMetricChartRequest) (*CreateMetricChartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMetricChart not implemented")
}
func (UnimplementedMetricChartServiceServer) UpdateMetricChart(context.Context, *UpdateMetricChartRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMetricChart not implemented")
}
func (UnimplementedMetricChartServiceServer) FindEnabledMetricChart(context.Context, *FindEnabledMetricChartRequest) (*FindEnabledMetricChartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledMetricChart not implemented")
}
func (UnimplementedMetricChartServiceServer) CountEnabledMetricCharts(context.Context, *CountEnabledMetricChartsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountEnabledMetricCharts not implemented")
}
func (UnimplementedMetricChartServiceServer) ListEnabledMetricCharts(context.Context, *ListEnabledMetricChartsRequest) (*ListEnabledMetricChartsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledMetricCharts not implemented")
}
func (UnimplementedMetricChartServiceServer) DeleteMetricChart(context.Context, *DeleteMetricChartRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMetricChart not implemented")
}

// UnsafeMetricChartServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetricChartServiceServer will
// result in compilation errors.
type UnsafeMetricChartServiceServer interface {
	mustEmbedUnimplementedMetricChartServiceServer()
}

func RegisterMetricChartServiceServer(s grpc.ServiceRegistrar, srv MetricChartServiceServer) {
	s.RegisterService(&MetricChartService_ServiceDesc, srv)
}

func _MetricChartService_CreateMetricChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMetricChartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricChartServiceServer).CreateMetricChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricChartService_CreateMetricChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricChartServiceServer).CreateMetricChart(ctx, req.(*CreateMetricChartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricChartService_UpdateMetricChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMetricChartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricChartServiceServer).UpdateMetricChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricChartService_UpdateMetricChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricChartServiceServer).UpdateMetricChart(ctx, req.(*UpdateMetricChartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricChartService_FindEnabledMetricChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledMetricChartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricChartServiceServer).FindEnabledMetricChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricChartService_FindEnabledMetricChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricChartServiceServer).FindEnabledMetricChart(ctx, req.(*FindEnabledMetricChartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricChartService_CountEnabledMetricCharts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountEnabledMetricChartsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricChartServiceServer).CountEnabledMetricCharts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricChartService_CountEnabledMetricCharts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricChartServiceServer).CountEnabledMetricCharts(ctx, req.(*CountEnabledMetricChartsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricChartService_ListEnabledMetricCharts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledMetricChartsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricChartServiceServer).ListEnabledMetricCharts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricChartService_ListEnabledMetricCharts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricChartServiceServer).ListEnabledMetricCharts(ctx, req.(*ListEnabledMetricChartsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetricChartService_DeleteMetricChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMetricChartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricChartServiceServer).DeleteMetricChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricChartService_DeleteMetricChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricChartServiceServer).DeleteMetricChart(ctx, req.(*DeleteMetricChartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MetricChartService_ServiceDesc is the grpc.ServiceDesc for MetricChartService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetricChartService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MetricChartService",
	HandlerType: (*MetricChartServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createMetricChart",
			Handler:    _MetricChartService_CreateMetricChart_Handler,
		},
		{
			MethodName: "updateMetricChart",
			Handler:    _MetricChartService_UpdateMetricChart_Handler,
		},
		{
			MethodName: "findEnabledMetricChart",
			Handler:    _MetricChartService_FindEnabledMetricChart_Handler,
		},
		{
			MethodName: "countEnabledMetricCharts",
			Handler:    _MetricChartService_CountEnabledMetricCharts_Handler,
		},
		{
			MethodName: "listEnabledMetricCharts",
			Handler:    _MetricChartService_ListEnabledMetricCharts_Handler,
		},
		{
			MethodName: "deleteMetricChart",
			Handler:    _MetricChartService_DeleteMetricChart_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_metric_chart.proto",
}
