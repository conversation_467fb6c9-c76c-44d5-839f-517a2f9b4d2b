// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_node.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSNodeService_FindAllNSNodesWithNSClusterId_FullMethodName         = "/pb.NSNodeService/findAllNSNodesWithNSClusterId"
	NSNodeService_CountAllNSNodes_FullMethodName                       = "/pb.NSNodeService/countAllNSNodes"
	NSNodeService_CountAllNSNodesMatch_FullMethodName                  = "/pb.NSNodeService/countAllNSNodesMatch"
	NSNodeService_ListNSNodesMatch_FullMethodName                      = "/pb.NSNodeService/listNSNodesMatch"
	NSNodeService_CountAllUpgradeNSNodesWithNSClusterId_FullMethodName = "/pb.NSNodeService/countAllUpgradeNSNodesWithNSClusterId"
	NSNodeService_CreateNSNode_FullMethodName                          = "/pb.NSNodeService/createNSNode"
	NSNodeService_DeleteNSNode_FullMethodName                          = "/pb.NSNodeService/deleteNSNode"
	NSNodeService_FindNSNode_FullMethodName                            = "/pb.NSNodeService/findNSNode"
	NSNodeService_UpdateNSNode_FullMethodName                          = "/pb.NSNodeService/updateNSNode"
	NSNodeService_InstallNSNode_FullMethodName                         = "/pb.NSNodeService/installNSNode"
	NSNodeService_FindNSNodeInstallStatus_FullMethodName               = "/pb.NSNodeService/findNSNodeInstallStatus"
	NSNodeService_UpdateNSNodeIsInstalled_FullMethodName               = "/pb.NSNodeService/updateNSNodeIsInstalled"
	NSNodeService_UpdateNSNodeStatus_FullMethodName                    = "/pb.NSNodeService/updateNSNodeStatus"
	NSNodeService_FindCurrentNSNodeConfig_FullMethodName               = "/pb.NSNodeService/findCurrentNSNodeConfig"
	NSNodeService_CheckNSNodeLatestVersion_FullMethodName              = "/pb.NSNodeService/checkNSNodeLatestVersion"
	NSNodeService_FindLatestNSNodeVersion_FullMethodName               = "/pb.NSNodeService/findLatestNSNodeVersion"
	NSNodeService_DownloadNSNodeInstallationFile_FullMethodName        = "/pb.NSNodeService/downloadNSNodeInstallationFile"
	NSNodeService_NsNodeStream_FullMethodName                          = "/pb.NSNodeService/nsNodeStream"
	NSNodeService_SendCommandToNSNode_FullMethodName                   = "/pb.NSNodeService/sendCommandToNSNode"
	NSNodeService_UpdateNSNodeConnectedAPINodes_FullMethodName         = "/pb.NSNodeService/updateNSNodeConnectedAPINodes"
	NSNodeService_UpdateNSNodeLogin_FullMethodName                     = "/pb.NSNodeService/updateNSNodeLogin"
	NSNodeService_StartNSNode_FullMethodName                           = "/pb.NSNodeService/startNSNode"
	NSNodeService_StopNSNode_FullMethodName                            = "/pb.NSNodeService/stopNSNode"
	NSNodeService_FindNSNodeDDoSProtection_FullMethodName              = "/pb.NSNodeService/findNSNodeDDoSProtection"
	NSNodeService_UpdateNSNodeDDoSProtection_FullMethodName            = "/pb.NSNodeService/updateNSNodeDDoSProtection"
	NSNodeService_FindNSNodeAPIConfig_FullMethodName                   = "/pb.NSNodeService/findNSNodeAPIConfig"
	NSNodeService_UpdateNSNodeAPIConfig_FullMethodName                 = "/pb.NSNodeService/updateNSNodeAPIConfig"
)

// NSNodeServiceClient is the client API for NSNodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSNodeServiceClient interface {
	// 根据集群查找所有NS节点
	FindAllNSNodesWithNSClusterId(ctx context.Context, in *FindAllNSNodesWithNSClusterIdRequest, opts ...grpc.CallOption) (*FindAllNSNodesWithNSClusterIdResponse, error)
	// 所有可用的NS节点数量
	CountAllNSNodes(ctx context.Context, in *CountAllNSNodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算匹配的NS节点数量
	CountAllNSNodesMatch(ctx context.Context, in *CountAllNSNodesMatchRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页NS节点
	ListNSNodesMatch(ctx context.Context, in *ListNSNodesMatchRequest, opts ...grpc.CallOption) (*ListNSNodesMatchResponse, error)
	// 计算需要升级的NS节点数量
	CountAllUpgradeNSNodesWithNSClusterId(ctx context.Context, in *CountAllUpgradeNSNodesWithNSClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 创建NS节点
	CreateNSNode(ctx context.Context, in *CreateNSNodeRequest, opts ...grpc.CallOption) (*CreateNSNodeResponse, error)
	// 删除NS节点
	DeleteNSNode(ctx context.Context, in *DeleteNSNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取单个NS节点信息
	FindNSNode(ctx context.Context, in *FindNSNodeRequest, opts ...grpc.CallOption) (*FindNSNodeResponse, error)
	// 修改NS节点
	UpdateNSNode(ctx context.Context, in *UpdateNSNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 安装NS节点
	InstallNSNode(ctx context.Context, in *InstallNSNodeRequest, opts ...grpc.CallOption) (*InstallNSNodeResponse, error)
	// 读取NS节点安装状态
	FindNSNodeInstallStatus(ctx context.Context, in *FindNSNodeInstallStatusRequest, opts ...grpc.CallOption) (*FindNSNodeInstallStatusResponse, error)
	// 修改NS节点安装状态
	UpdateNSNodeIsInstalled(ctx context.Context, in *UpdateNSNodeIsInstalledRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更新NS节点状态
	UpdateNSNodeStatus(ctx context.Context, in *UpdateNSNodeStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取当前NS节点信息
	FindCurrentNSNodeConfig(ctx context.Context, in *FindCurrentNSNodeConfigRequest, opts ...grpc.CallOption) (*FindCurrentNSNodeConfigResponse, error)
	// 检查NS节点新版本
	CheckNSNodeLatestVersion(ctx context.Context, in *CheckNSNodeLatestVersionRequest, opts ...grpc.CallOption) (*CheckNSNodeLatestVersionResponse, error)
	// 获取NS节点最新版本
	FindLatestNSNodeVersion(ctx context.Context, in *FindLatestNSNodeVersionRequest, opts ...grpc.CallOption) (*FindLatestNSNodeVersionResponse, error)
	// 下载最新NS节点安装文件
	DownloadNSNodeInstallationFile(ctx context.Context, in *DownloadNSNodeInstallationFileRequest, opts ...grpc.CallOption) (*DownloadNSNodeInstallationFileResponse, error)
	// NS节点stream
	NsNodeStream(ctx context.Context, opts ...grpc.CallOption) (NSNodeService_NsNodeStreamClient, error)
	// 向NS节点发送命令
	SendCommandToNSNode(ctx context.Context, in *NSNodeStreamMessage, opts ...grpc.CallOption) (*NSNodeStreamMessage, error)
	// 更改NS节点连接的API节点信息
	UpdateNSNodeConnectedAPINodes(ctx context.Context, in *UpdateNSNodeConnectedAPINodesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改NS节点登录信息
	UpdateNSNodeLogin(ctx context.Context, in *UpdateNSNodeLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 启动NS节点
	StartNSNode(ctx context.Context, in *StartNSNodeRequest, opts ...grpc.CallOption) (*StartNSNodeResponse, error)
	// 停止NS节点
	StopNSNode(ctx context.Context, in *StopNSNodeRequest, opts ...grpc.CallOption) (*StopNSNodeResponse, error)
	// 获取NS节点的DDoS设置
	FindNSNodeDDoSProtection(ctx context.Context, in *FindNSNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNSNodeDDoSProtectionResponse, error)
	// 修改NS节点的DDoS设置
	UpdateNSNodeDDoSProtection(ctx context.Context, in *UpdateNSNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个节点的API相关配置
	FindNSNodeAPIConfig(ctx context.Context, in *FindNSNodeAPIConfigRequest, opts ...grpc.CallOption) (*FindNSNodeAPIConfigResponse, error)
	// 修改某个节点的API相关配置
	UpdateNSNodeAPIConfig(ctx context.Context, in *UpdateNSNodeAPIConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nSNodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSNodeServiceClient(cc grpc.ClientConnInterface) NSNodeServiceClient {
	return &nSNodeServiceClient{cc}
}

func (c *nSNodeServiceClient) FindAllNSNodesWithNSClusterId(ctx context.Context, in *FindAllNSNodesWithNSClusterIdRequest, opts ...grpc.CallOption) (*FindAllNSNodesWithNSClusterIdResponse, error) {
	out := new(FindAllNSNodesWithNSClusterIdResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindAllNSNodesWithNSClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) CountAllNSNodes(ctx context.Context, in *CountAllNSNodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSNodeService_CountAllNSNodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) CountAllNSNodesMatch(ctx context.Context, in *CountAllNSNodesMatchRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSNodeService_CountAllNSNodesMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) ListNSNodesMatch(ctx context.Context, in *ListNSNodesMatchRequest, opts ...grpc.CallOption) (*ListNSNodesMatchResponse, error) {
	out := new(ListNSNodesMatchResponse)
	err := c.cc.Invoke(ctx, NSNodeService_ListNSNodesMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) CountAllUpgradeNSNodesWithNSClusterId(ctx context.Context, in *CountAllUpgradeNSNodesWithNSClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSNodeService_CountAllUpgradeNSNodesWithNSClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) CreateNSNode(ctx context.Context, in *CreateNSNodeRequest, opts ...grpc.CallOption) (*CreateNSNodeResponse, error) {
	out := new(CreateNSNodeResponse)
	err := c.cc.Invoke(ctx, NSNodeService_CreateNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) DeleteNSNode(ctx context.Context, in *DeleteNSNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_DeleteNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) FindNSNode(ctx context.Context, in *FindNSNodeRequest, opts ...grpc.CallOption) (*FindNSNodeResponse, error) {
	out := new(FindNSNodeResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNode(ctx context.Context, in *UpdateNSNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) InstallNSNode(ctx context.Context, in *InstallNSNodeRequest, opts ...grpc.CallOption) (*InstallNSNodeResponse, error) {
	out := new(InstallNSNodeResponse)
	err := c.cc.Invoke(ctx, NSNodeService_InstallNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) FindNSNodeInstallStatus(ctx context.Context, in *FindNSNodeInstallStatusRequest, opts ...grpc.CallOption) (*FindNSNodeInstallStatusResponse, error) {
	out := new(FindNSNodeInstallStatusResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindNSNodeInstallStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNodeIsInstalled(ctx context.Context, in *UpdateNSNodeIsInstalledRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNodeIsInstalled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNodeStatus(ctx context.Context, in *UpdateNSNodeStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNodeStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) FindCurrentNSNodeConfig(ctx context.Context, in *FindCurrentNSNodeConfigRequest, opts ...grpc.CallOption) (*FindCurrentNSNodeConfigResponse, error) {
	out := new(FindCurrentNSNodeConfigResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindCurrentNSNodeConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) CheckNSNodeLatestVersion(ctx context.Context, in *CheckNSNodeLatestVersionRequest, opts ...grpc.CallOption) (*CheckNSNodeLatestVersionResponse, error) {
	out := new(CheckNSNodeLatestVersionResponse)
	err := c.cc.Invoke(ctx, NSNodeService_CheckNSNodeLatestVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) FindLatestNSNodeVersion(ctx context.Context, in *FindLatestNSNodeVersionRequest, opts ...grpc.CallOption) (*FindLatestNSNodeVersionResponse, error) {
	out := new(FindLatestNSNodeVersionResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindLatestNSNodeVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) DownloadNSNodeInstallationFile(ctx context.Context, in *DownloadNSNodeInstallationFileRequest, opts ...grpc.CallOption) (*DownloadNSNodeInstallationFileResponse, error) {
	out := new(DownloadNSNodeInstallationFileResponse)
	err := c.cc.Invoke(ctx, NSNodeService_DownloadNSNodeInstallationFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) NsNodeStream(ctx context.Context, opts ...grpc.CallOption) (NSNodeService_NsNodeStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &NSNodeService_ServiceDesc.Streams[0], NSNodeService_NsNodeStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &nSNodeServiceNsNodeStreamClient{stream}
	return x, nil
}

type NSNodeService_NsNodeStreamClient interface {
	Send(*NSNodeStreamMessage) error
	Recv() (*NSNodeStreamMessage, error)
	grpc.ClientStream
}

type nSNodeServiceNsNodeStreamClient struct {
	grpc.ClientStream
}

func (x *nSNodeServiceNsNodeStreamClient) Send(m *NSNodeStreamMessage) error {
	return x.ClientStream.SendMsg(m)
}

func (x *nSNodeServiceNsNodeStreamClient) Recv() (*NSNodeStreamMessage, error) {
	m := new(NSNodeStreamMessage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *nSNodeServiceClient) SendCommandToNSNode(ctx context.Context, in *NSNodeStreamMessage, opts ...grpc.CallOption) (*NSNodeStreamMessage, error) {
	out := new(NSNodeStreamMessage)
	err := c.cc.Invoke(ctx, NSNodeService_SendCommandToNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNodeConnectedAPINodes(ctx context.Context, in *UpdateNSNodeConnectedAPINodesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNodeConnectedAPINodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNodeLogin(ctx context.Context, in *UpdateNSNodeLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNodeLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) StartNSNode(ctx context.Context, in *StartNSNodeRequest, opts ...grpc.CallOption) (*StartNSNodeResponse, error) {
	out := new(StartNSNodeResponse)
	err := c.cc.Invoke(ctx, NSNodeService_StartNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) StopNSNode(ctx context.Context, in *StopNSNodeRequest, opts ...grpc.CallOption) (*StopNSNodeResponse, error) {
	out := new(StopNSNodeResponse)
	err := c.cc.Invoke(ctx, NSNodeService_StopNSNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) FindNSNodeDDoSProtection(ctx context.Context, in *FindNSNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNSNodeDDoSProtectionResponse, error) {
	out := new(FindNSNodeDDoSProtectionResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindNSNodeDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNodeDDoSProtection(ctx context.Context, in *UpdateNSNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNodeDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) FindNSNodeAPIConfig(ctx context.Context, in *FindNSNodeAPIConfigRequest, opts ...grpc.CallOption) (*FindNSNodeAPIConfigResponse, error) {
	out := new(FindNSNodeAPIConfigResponse)
	err := c.cc.Invoke(ctx, NSNodeService_FindNSNodeAPIConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSNodeServiceClient) UpdateNSNodeAPIConfig(ctx context.Context, in *UpdateNSNodeAPIConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSNodeService_UpdateNSNodeAPIConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSNodeServiceServer is the server API for NSNodeService service.
// All implementations should embed UnimplementedNSNodeServiceServer
// for forward compatibility
type NSNodeServiceServer interface {
	// 根据集群查找所有NS节点
	FindAllNSNodesWithNSClusterId(context.Context, *FindAllNSNodesWithNSClusterIdRequest) (*FindAllNSNodesWithNSClusterIdResponse, error)
	// 所有可用的NS节点数量
	CountAllNSNodes(context.Context, *CountAllNSNodesRequest) (*RPCCountResponse, error)
	// 计算匹配的NS节点数量
	CountAllNSNodesMatch(context.Context, *CountAllNSNodesMatchRequest) (*RPCCountResponse, error)
	// 列出单页NS节点
	ListNSNodesMatch(context.Context, *ListNSNodesMatchRequest) (*ListNSNodesMatchResponse, error)
	// 计算需要升级的NS节点数量
	CountAllUpgradeNSNodesWithNSClusterId(context.Context, *CountAllUpgradeNSNodesWithNSClusterIdRequest) (*RPCCountResponse, error)
	// 创建NS节点
	CreateNSNode(context.Context, *CreateNSNodeRequest) (*CreateNSNodeResponse, error)
	// 删除NS节点
	DeleteNSNode(context.Context, *DeleteNSNodeRequest) (*RPCSuccess, error)
	// 获取单个NS节点信息
	FindNSNode(context.Context, *FindNSNodeRequest) (*FindNSNodeResponse, error)
	// 修改NS节点
	UpdateNSNode(context.Context, *UpdateNSNodeRequest) (*RPCSuccess, error)
	// 安装NS节点
	InstallNSNode(context.Context, *InstallNSNodeRequest) (*InstallNSNodeResponse, error)
	// 读取NS节点安装状态
	FindNSNodeInstallStatus(context.Context, *FindNSNodeInstallStatusRequest) (*FindNSNodeInstallStatusResponse, error)
	// 修改NS节点安装状态
	UpdateNSNodeIsInstalled(context.Context, *UpdateNSNodeIsInstalledRequest) (*RPCSuccess, error)
	// 更新NS节点状态
	UpdateNSNodeStatus(context.Context, *UpdateNSNodeStatusRequest) (*RPCSuccess, error)
	// 获取当前NS节点信息
	FindCurrentNSNodeConfig(context.Context, *FindCurrentNSNodeConfigRequest) (*FindCurrentNSNodeConfigResponse, error)
	// 检查NS节点新版本
	CheckNSNodeLatestVersion(context.Context, *CheckNSNodeLatestVersionRequest) (*CheckNSNodeLatestVersionResponse, error)
	// 获取NS节点最新版本
	FindLatestNSNodeVersion(context.Context, *FindLatestNSNodeVersionRequest) (*FindLatestNSNodeVersionResponse, error)
	// 下载最新NS节点安装文件
	DownloadNSNodeInstallationFile(context.Context, *DownloadNSNodeInstallationFileRequest) (*DownloadNSNodeInstallationFileResponse, error)
	// NS节点stream
	NsNodeStream(NSNodeService_NsNodeStreamServer) error
	// 向NS节点发送命令
	SendCommandToNSNode(context.Context, *NSNodeStreamMessage) (*NSNodeStreamMessage, error)
	// 更改NS节点连接的API节点信息
	UpdateNSNodeConnectedAPINodes(context.Context, *UpdateNSNodeConnectedAPINodesRequest) (*RPCSuccess, error)
	// 修改NS节点登录信息
	UpdateNSNodeLogin(context.Context, *UpdateNSNodeLoginRequest) (*RPCSuccess, error)
	// 启动NS节点
	StartNSNode(context.Context, *StartNSNodeRequest) (*StartNSNodeResponse, error)
	// 停止NS节点
	StopNSNode(context.Context, *StopNSNodeRequest) (*StopNSNodeResponse, error)
	// 获取NS节点的DDoS设置
	FindNSNodeDDoSProtection(context.Context, *FindNSNodeDDoSProtectionRequest) (*FindNSNodeDDoSProtectionResponse, error)
	// 修改NS节点的DDoS设置
	UpdateNSNodeDDoSProtection(context.Context, *UpdateNSNodeDDoSProtectionRequest) (*RPCSuccess, error)
	// 查找单个节点的API相关配置
	FindNSNodeAPIConfig(context.Context, *FindNSNodeAPIConfigRequest) (*FindNSNodeAPIConfigResponse, error)
	// 修改某个节点的API相关配置
	UpdateNSNodeAPIConfig(context.Context, *UpdateNSNodeAPIConfigRequest) (*RPCSuccess, error)
}

// UnimplementedNSNodeServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSNodeServiceServer struct {
}

func (UnimplementedNSNodeServiceServer) FindAllNSNodesWithNSClusterId(context.Context, *FindAllNSNodesWithNSClusterIdRequest) (*FindAllNSNodesWithNSClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNSNodesWithNSClusterId not implemented")
}
func (UnimplementedNSNodeServiceServer) CountAllNSNodes(context.Context, *CountAllNSNodesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSNodes not implemented")
}
func (UnimplementedNSNodeServiceServer) CountAllNSNodesMatch(context.Context, *CountAllNSNodesMatchRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSNodesMatch not implemented")
}
func (UnimplementedNSNodeServiceServer) ListNSNodesMatch(context.Context, *ListNSNodesMatchRequest) (*ListNSNodesMatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSNodesMatch not implemented")
}
func (UnimplementedNSNodeServiceServer) CountAllUpgradeNSNodesWithNSClusterId(context.Context, *CountAllUpgradeNSNodesWithNSClusterIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllUpgradeNSNodesWithNSClusterId not implemented")
}
func (UnimplementedNSNodeServiceServer) CreateNSNode(context.Context, *CreateNSNodeRequest) (*CreateNSNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) DeleteNSNode(context.Context, *DeleteNSNodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) FindNSNode(context.Context, *FindNSNodeRequest) (*FindNSNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNode(context.Context, *UpdateNSNodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) InstallNSNode(context.Context, *InstallNSNodeRequest) (*InstallNSNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstallNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) FindNSNodeInstallStatus(context.Context, *FindNSNodeInstallStatusRequest) (*FindNSNodeInstallStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSNodeInstallStatus not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNodeIsInstalled(context.Context, *UpdateNSNodeIsInstalledRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNodeIsInstalled not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNodeStatus(context.Context, *UpdateNSNodeStatusRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNodeStatus not implemented")
}
func (UnimplementedNSNodeServiceServer) FindCurrentNSNodeConfig(context.Context, *FindCurrentNSNodeConfigRequest) (*FindCurrentNSNodeConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCurrentNSNodeConfig not implemented")
}
func (UnimplementedNSNodeServiceServer) CheckNSNodeLatestVersion(context.Context, *CheckNSNodeLatestVersionRequest) (*CheckNSNodeLatestVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckNSNodeLatestVersion not implemented")
}
func (UnimplementedNSNodeServiceServer) FindLatestNSNodeVersion(context.Context, *FindLatestNSNodeVersionRequest) (*FindLatestNSNodeVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLatestNSNodeVersion not implemented")
}
func (UnimplementedNSNodeServiceServer) DownloadNSNodeInstallationFile(context.Context, *DownloadNSNodeInstallationFileRequest) (*DownloadNSNodeInstallationFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadNSNodeInstallationFile not implemented")
}
func (UnimplementedNSNodeServiceServer) NsNodeStream(NSNodeService_NsNodeStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method NsNodeStream not implemented")
}
func (UnimplementedNSNodeServiceServer) SendCommandToNSNode(context.Context, *NSNodeStreamMessage) (*NSNodeStreamMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCommandToNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNodeConnectedAPINodes(context.Context, *UpdateNSNodeConnectedAPINodesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNodeConnectedAPINodes not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNodeLogin(context.Context, *UpdateNSNodeLoginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNodeLogin not implemented")
}
func (UnimplementedNSNodeServiceServer) StartNSNode(context.Context, *StartNSNodeRequest) (*StartNSNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) StopNSNode(context.Context, *StopNSNodeRequest) (*StopNSNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopNSNode not implemented")
}
func (UnimplementedNSNodeServiceServer) FindNSNodeDDoSProtection(context.Context, *FindNSNodeDDoSProtectionRequest) (*FindNSNodeDDoSProtectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSNodeDDoSProtection not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNodeDDoSProtection(context.Context, *UpdateNSNodeDDoSProtectionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNodeDDoSProtection not implemented")
}
func (UnimplementedNSNodeServiceServer) FindNSNodeAPIConfig(context.Context, *FindNSNodeAPIConfigRequest) (*FindNSNodeAPIConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSNodeAPIConfig not implemented")
}
func (UnimplementedNSNodeServiceServer) UpdateNSNodeAPIConfig(context.Context, *UpdateNSNodeAPIConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSNodeAPIConfig not implemented")
}

// UnsafeNSNodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSNodeServiceServer will
// result in compilation errors.
type UnsafeNSNodeServiceServer interface {
	mustEmbedUnimplementedNSNodeServiceServer()
}

func RegisterNSNodeServiceServer(s grpc.ServiceRegistrar, srv NSNodeServiceServer) {
	s.RegisterService(&NSNodeService_ServiceDesc, srv)
}

func _NSNodeService_FindAllNSNodesWithNSClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNSNodesWithNSClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindAllNSNodesWithNSClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindAllNSNodesWithNSClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindAllNSNodesWithNSClusterId(ctx, req.(*FindAllNSNodesWithNSClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_CountAllNSNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSNodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).CountAllNSNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_CountAllNSNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).CountAllNSNodes(ctx, req.(*CountAllNSNodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_CountAllNSNodesMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSNodesMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).CountAllNSNodesMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_CountAllNSNodesMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).CountAllNSNodesMatch(ctx, req.(*CountAllNSNodesMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_ListNSNodesMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSNodesMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).ListNSNodesMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_ListNSNodesMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).ListNSNodesMatch(ctx, req.(*ListNSNodesMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_CountAllUpgradeNSNodesWithNSClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllUpgradeNSNodesWithNSClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).CountAllUpgradeNSNodesWithNSClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_CountAllUpgradeNSNodesWithNSClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).CountAllUpgradeNSNodesWithNSClusterId(ctx, req.(*CountAllUpgradeNSNodesWithNSClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_CreateNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).CreateNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_CreateNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).CreateNSNode(ctx, req.(*CreateNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_DeleteNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).DeleteNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_DeleteNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).DeleteNSNode(ctx, req.(*DeleteNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_FindNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindNSNode(ctx, req.(*FindNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNode(ctx, req.(*UpdateNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_InstallNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).InstallNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_InstallNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).InstallNSNode(ctx, req.(*InstallNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_FindNSNodeInstallStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSNodeInstallStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindNSNodeInstallStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindNSNodeInstallStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindNSNodeInstallStatus(ctx, req.(*FindNSNodeInstallStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNodeIsInstalled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeIsInstalledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNodeIsInstalled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNodeIsInstalled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNodeIsInstalled(ctx, req.(*UpdateNSNodeIsInstalledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNodeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNodeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNodeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNodeStatus(ctx, req.(*UpdateNSNodeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_FindCurrentNSNodeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCurrentNSNodeConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindCurrentNSNodeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindCurrentNSNodeConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindCurrentNSNodeConfig(ctx, req.(*FindCurrentNSNodeConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_CheckNSNodeLatestVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckNSNodeLatestVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).CheckNSNodeLatestVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_CheckNSNodeLatestVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).CheckNSNodeLatestVersion(ctx, req.(*CheckNSNodeLatestVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_FindLatestNSNodeVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLatestNSNodeVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindLatestNSNodeVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindLatestNSNodeVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindLatestNSNodeVersion(ctx, req.(*FindLatestNSNodeVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_DownloadNSNodeInstallationFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadNSNodeInstallationFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).DownloadNSNodeInstallationFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_DownloadNSNodeInstallationFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).DownloadNSNodeInstallationFile(ctx, req.(*DownloadNSNodeInstallationFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_NsNodeStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(NSNodeServiceServer).NsNodeStream(&nSNodeServiceNsNodeStreamServer{stream})
}

type NSNodeService_NsNodeStreamServer interface {
	Send(*NSNodeStreamMessage) error
	Recv() (*NSNodeStreamMessage, error)
	grpc.ServerStream
}

type nSNodeServiceNsNodeStreamServer struct {
	grpc.ServerStream
}

func (x *nSNodeServiceNsNodeStreamServer) Send(m *NSNodeStreamMessage) error {
	return x.ServerStream.SendMsg(m)
}

func (x *nSNodeServiceNsNodeStreamServer) Recv() (*NSNodeStreamMessage, error) {
	m := new(NSNodeStreamMessage)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _NSNodeService_SendCommandToNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NSNodeStreamMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).SendCommandToNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_SendCommandToNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).SendCommandToNSNode(ctx, req.(*NSNodeStreamMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNodeConnectedAPINodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeConnectedAPINodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNodeConnectedAPINodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNodeConnectedAPINodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNodeConnectedAPINodes(ctx, req.(*UpdateNSNodeConnectedAPINodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNodeLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNodeLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNodeLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNodeLogin(ctx, req.(*UpdateNSNodeLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_StartNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).StartNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_StartNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).StartNSNode(ctx, req.(*StartNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_StopNSNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopNSNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).StopNSNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_StopNSNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).StopNSNode(ctx, req.(*StopNSNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_FindNSNodeDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSNodeDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindNSNodeDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindNSNodeDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindNSNodeDDoSProtection(ctx, req.(*FindNSNodeDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNodeDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNodeDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNodeDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNodeDDoSProtection(ctx, req.(*UpdateNSNodeDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_FindNSNodeAPIConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSNodeAPIConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).FindNSNodeAPIConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_FindNSNodeAPIConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).FindNSNodeAPIConfig(ctx, req.(*FindNSNodeAPIConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSNodeService_UpdateNSNodeAPIConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSNodeAPIConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSNodeServiceServer).UpdateNSNodeAPIConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSNodeService_UpdateNSNodeAPIConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSNodeServiceServer).UpdateNSNodeAPIConfig(ctx, req.(*UpdateNSNodeAPIConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSNodeService_ServiceDesc is the grpc.ServiceDesc for NSNodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSNodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSNodeService",
	HandlerType: (*NSNodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllNSNodesWithNSClusterId",
			Handler:    _NSNodeService_FindAllNSNodesWithNSClusterId_Handler,
		},
		{
			MethodName: "countAllNSNodes",
			Handler:    _NSNodeService_CountAllNSNodes_Handler,
		},
		{
			MethodName: "countAllNSNodesMatch",
			Handler:    _NSNodeService_CountAllNSNodesMatch_Handler,
		},
		{
			MethodName: "listNSNodesMatch",
			Handler:    _NSNodeService_ListNSNodesMatch_Handler,
		},
		{
			MethodName: "countAllUpgradeNSNodesWithNSClusterId",
			Handler:    _NSNodeService_CountAllUpgradeNSNodesWithNSClusterId_Handler,
		},
		{
			MethodName: "createNSNode",
			Handler:    _NSNodeService_CreateNSNode_Handler,
		},
		{
			MethodName: "deleteNSNode",
			Handler:    _NSNodeService_DeleteNSNode_Handler,
		},
		{
			MethodName: "findNSNode",
			Handler:    _NSNodeService_FindNSNode_Handler,
		},
		{
			MethodName: "updateNSNode",
			Handler:    _NSNodeService_UpdateNSNode_Handler,
		},
		{
			MethodName: "installNSNode",
			Handler:    _NSNodeService_InstallNSNode_Handler,
		},
		{
			MethodName: "findNSNodeInstallStatus",
			Handler:    _NSNodeService_FindNSNodeInstallStatus_Handler,
		},
		{
			MethodName: "updateNSNodeIsInstalled",
			Handler:    _NSNodeService_UpdateNSNodeIsInstalled_Handler,
		},
		{
			MethodName: "updateNSNodeStatus",
			Handler:    _NSNodeService_UpdateNSNodeStatus_Handler,
		},
		{
			MethodName: "findCurrentNSNodeConfig",
			Handler:    _NSNodeService_FindCurrentNSNodeConfig_Handler,
		},
		{
			MethodName: "checkNSNodeLatestVersion",
			Handler:    _NSNodeService_CheckNSNodeLatestVersion_Handler,
		},
		{
			MethodName: "findLatestNSNodeVersion",
			Handler:    _NSNodeService_FindLatestNSNodeVersion_Handler,
		},
		{
			MethodName: "downloadNSNodeInstallationFile",
			Handler:    _NSNodeService_DownloadNSNodeInstallationFile_Handler,
		},
		{
			MethodName: "sendCommandToNSNode",
			Handler:    _NSNodeService_SendCommandToNSNode_Handler,
		},
		{
			MethodName: "updateNSNodeConnectedAPINodes",
			Handler:    _NSNodeService_UpdateNSNodeConnectedAPINodes_Handler,
		},
		{
			MethodName: "updateNSNodeLogin",
			Handler:    _NSNodeService_UpdateNSNodeLogin_Handler,
		},
		{
			MethodName: "startNSNode",
			Handler:    _NSNodeService_StartNSNode_Handler,
		},
		{
			MethodName: "stopNSNode",
			Handler:    _NSNodeService_StopNSNode_Handler,
		},
		{
			MethodName: "findNSNodeDDoSProtection",
			Handler:    _NSNodeService_FindNSNodeDDoSProtection_Handler,
		},
		{
			MethodName: "updateNSNodeDDoSProtection",
			Handler:    _NSNodeService_UpdateNSNodeDDoSProtection_Handler,
		},
		{
			MethodName: "findNSNodeAPIConfig",
			Handler:    _NSNodeService_FindNSNodeAPIConfig_Handler,
		},
		{
			MethodName: "updateNSNodeAPIConfig",
			Handler:    _NSNodeService_UpdateNSNodeAPIConfig_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "nsNodeStream",
			Handler:       _NSNodeService_NsNodeStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "service_ns_node.proto",
}
