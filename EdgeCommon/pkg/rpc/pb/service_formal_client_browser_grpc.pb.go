// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_formal_client_browser.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FormalClientBrowserService_CreateFormalClientBrowser_FullMethodName         = "/pb.FormalClientBrowserService/createFormalClientBrowser"
	FormalClientBrowserService_CountFormalClientBrowsers_FullMethodName         = "/pb.FormalClientBrowserService/countFormalClientBrowsers"
	FormalClientBrowserService_ListFormalClientBrowsers_FullMethodName          = "/pb.FormalClientBrowserService/listFormalClientBrowsers"
	FormalClientBrowserService_UpdateFormalClientBrowser_FullMethodName         = "/pb.FormalClientBrowserService/updateFormalClientBrowser"
	FormalClientBrowserService_FindFormalClientBrowserWithDataId_FullMethodName = "/pb.FormalClientBrowserService/findFormalClientBrowserWithDataId"
)

// FormalClientBrowserServiceClient is the client API for FormalClientBrowserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FormalClientBrowserServiceClient interface {
	// 创建浏览器信息
	CreateFormalClientBrowser(ctx context.Context, in *CreateFormalClientBrowserRequest, opts ...grpc.CallOption) (*CreateFormalClientBrowserResponse, error)
	// 计算浏览器信息数量
	CountFormalClientBrowsers(ctx context.Context, in *CountFormalClientBrowsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页浏览器信息
	ListFormalClientBrowsers(ctx context.Context, in *ListFormalClientBrowsersRequest, opts ...grpc.CallOption) (*ListFormalClientBrowsersResponse, error)
	// 修改浏览器信息
	UpdateFormalClientBrowser(ctx context.Context, in *UpdateFormalClientBrowserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 通过dataId查询浏览器信息
	FindFormalClientBrowserWithDataId(ctx context.Context, in *FindFormalClientBrowserWithDataIdRequest, opts ...grpc.CallOption) (*FindFormalClientBrowserWithDataIdResponse, error)
}

type formalClientBrowserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFormalClientBrowserServiceClient(cc grpc.ClientConnInterface) FormalClientBrowserServiceClient {
	return &formalClientBrowserServiceClient{cc}
}

func (c *formalClientBrowserServiceClient) CreateFormalClientBrowser(ctx context.Context, in *CreateFormalClientBrowserRequest, opts ...grpc.CallOption) (*CreateFormalClientBrowserResponse, error) {
	out := new(CreateFormalClientBrowserResponse)
	err := c.cc.Invoke(ctx, FormalClientBrowserService_CreateFormalClientBrowser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *formalClientBrowserServiceClient) CountFormalClientBrowsers(ctx context.Context, in *CountFormalClientBrowsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, FormalClientBrowserService_CountFormalClientBrowsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *formalClientBrowserServiceClient) ListFormalClientBrowsers(ctx context.Context, in *ListFormalClientBrowsersRequest, opts ...grpc.CallOption) (*ListFormalClientBrowsersResponse, error) {
	out := new(ListFormalClientBrowsersResponse)
	err := c.cc.Invoke(ctx, FormalClientBrowserService_ListFormalClientBrowsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *formalClientBrowserServiceClient) UpdateFormalClientBrowser(ctx context.Context, in *UpdateFormalClientBrowserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, FormalClientBrowserService_UpdateFormalClientBrowser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *formalClientBrowserServiceClient) FindFormalClientBrowserWithDataId(ctx context.Context, in *FindFormalClientBrowserWithDataIdRequest, opts ...grpc.CallOption) (*FindFormalClientBrowserWithDataIdResponse, error) {
	out := new(FindFormalClientBrowserWithDataIdResponse)
	err := c.cc.Invoke(ctx, FormalClientBrowserService_FindFormalClientBrowserWithDataId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FormalClientBrowserServiceServer is the server API for FormalClientBrowserService service.
// All implementations should embed UnimplementedFormalClientBrowserServiceServer
// for forward compatibility
type FormalClientBrowserServiceServer interface {
	// 创建浏览器信息
	CreateFormalClientBrowser(context.Context, *CreateFormalClientBrowserRequest) (*CreateFormalClientBrowserResponse, error)
	// 计算浏览器信息数量
	CountFormalClientBrowsers(context.Context, *CountFormalClientBrowsersRequest) (*RPCCountResponse, error)
	// 列出单页浏览器信息
	ListFormalClientBrowsers(context.Context, *ListFormalClientBrowsersRequest) (*ListFormalClientBrowsersResponse, error)
	// 修改浏览器信息
	UpdateFormalClientBrowser(context.Context, *UpdateFormalClientBrowserRequest) (*RPCSuccess, error)
	// 通过dataId查询浏览器信息
	FindFormalClientBrowserWithDataId(context.Context, *FindFormalClientBrowserWithDataIdRequest) (*FindFormalClientBrowserWithDataIdResponse, error)
}

// UnimplementedFormalClientBrowserServiceServer should be embedded to have forward compatible implementations.
type UnimplementedFormalClientBrowserServiceServer struct {
}

func (UnimplementedFormalClientBrowserServiceServer) CreateFormalClientBrowser(context.Context, *CreateFormalClientBrowserRequest) (*CreateFormalClientBrowserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFormalClientBrowser not implemented")
}
func (UnimplementedFormalClientBrowserServiceServer) CountFormalClientBrowsers(context.Context, *CountFormalClientBrowsersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountFormalClientBrowsers not implemented")
}
func (UnimplementedFormalClientBrowserServiceServer) ListFormalClientBrowsers(context.Context, *ListFormalClientBrowsersRequest) (*ListFormalClientBrowsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFormalClientBrowsers not implemented")
}
func (UnimplementedFormalClientBrowserServiceServer) UpdateFormalClientBrowser(context.Context, *UpdateFormalClientBrowserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFormalClientBrowser not implemented")
}
func (UnimplementedFormalClientBrowserServiceServer) FindFormalClientBrowserWithDataId(context.Context, *FindFormalClientBrowserWithDataIdRequest) (*FindFormalClientBrowserWithDataIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindFormalClientBrowserWithDataId not implemented")
}

// UnsafeFormalClientBrowserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FormalClientBrowserServiceServer will
// result in compilation errors.
type UnsafeFormalClientBrowserServiceServer interface {
	mustEmbedUnimplementedFormalClientBrowserServiceServer()
}

func RegisterFormalClientBrowserServiceServer(s grpc.ServiceRegistrar, srv FormalClientBrowserServiceServer) {
	s.RegisterService(&FormalClientBrowserService_ServiceDesc, srv)
}

func _FormalClientBrowserService_CreateFormalClientBrowser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFormalClientBrowserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FormalClientBrowserServiceServer).CreateFormalClientBrowser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FormalClientBrowserService_CreateFormalClientBrowser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FormalClientBrowserServiceServer).CreateFormalClientBrowser(ctx, req.(*CreateFormalClientBrowserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FormalClientBrowserService_CountFormalClientBrowsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountFormalClientBrowsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FormalClientBrowserServiceServer).CountFormalClientBrowsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FormalClientBrowserService_CountFormalClientBrowsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FormalClientBrowserServiceServer).CountFormalClientBrowsers(ctx, req.(*CountFormalClientBrowsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FormalClientBrowserService_ListFormalClientBrowsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFormalClientBrowsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FormalClientBrowserServiceServer).ListFormalClientBrowsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FormalClientBrowserService_ListFormalClientBrowsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FormalClientBrowserServiceServer).ListFormalClientBrowsers(ctx, req.(*ListFormalClientBrowsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FormalClientBrowserService_UpdateFormalClientBrowser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFormalClientBrowserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FormalClientBrowserServiceServer).UpdateFormalClientBrowser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FormalClientBrowserService_UpdateFormalClientBrowser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FormalClientBrowserServiceServer).UpdateFormalClientBrowser(ctx, req.(*UpdateFormalClientBrowserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FormalClientBrowserService_FindFormalClientBrowserWithDataId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindFormalClientBrowserWithDataIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FormalClientBrowserServiceServer).FindFormalClientBrowserWithDataId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FormalClientBrowserService_FindFormalClientBrowserWithDataId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FormalClientBrowserServiceServer).FindFormalClientBrowserWithDataId(ctx, req.(*FindFormalClientBrowserWithDataIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FormalClientBrowserService_ServiceDesc is the grpc.ServiceDesc for FormalClientBrowserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FormalClientBrowserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.FormalClientBrowserService",
	HandlerType: (*FormalClientBrowserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createFormalClientBrowser",
			Handler:    _FormalClientBrowserService_CreateFormalClientBrowser_Handler,
		},
		{
			MethodName: "countFormalClientBrowsers",
			Handler:    _FormalClientBrowserService_CountFormalClientBrowsers_Handler,
		},
		{
			MethodName: "listFormalClientBrowsers",
			Handler:    _FormalClientBrowserService_ListFormalClientBrowsers_Handler,
		},
		{
			MethodName: "updateFormalClientBrowser",
			Handler:    _FormalClientBrowserService_UpdateFormalClientBrowser_Handler,
		},
		{
			MethodName: "findFormalClientBrowserWithDataId",
			Handler:    _FormalClientBrowserService_FindFormalClientBrowserWithDataId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_formal_client_browser.proto",
}
