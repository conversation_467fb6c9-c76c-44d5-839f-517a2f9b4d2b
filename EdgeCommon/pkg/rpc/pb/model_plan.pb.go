// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_plan.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Plan struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	Id                          int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                    // 套餐ID
	IsOn                        bool                   `protobuf:"varint,2,opt,name=isOn,proto3" json:"isOn,omitempty"`                                                // 是否启用
	Name                        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                 // 套餐名称
	Description                 string                 `protobuf:"bytes,21,opt,name=description,proto3" json:"description,omitempty"`                                  // 套餐简介
	ClusterId                   int64                  `protobuf:"varint,4,opt,name=clusterId,proto3" json:"clusterId,omitempty"`                                      // 集群ID
	TrafficLimitJSON            []byte                 `protobuf:"bytes,5,opt,name=trafficLimitJSON,proto3" json:"trafficLimitJSON,omitempty"`                         // 流量限制
	BandwidthLimitPerNodeJSON   []byte                 `protobuf:"bytes,22,opt,name=bandwidthLimitPerNodeJSON,proto3" json:"bandwidthLimitPerNodeJSON,omitempty"`      // 单节点带宽限制
	HasFullFeatures             bool                   `protobuf:"varint,20,opt,name=hasFullFeatures,proto3" json:"hasFullFeatures,omitempty"`                         // 是否有所有权限
	FeaturesJSON                []byte                 `protobuf:"bytes,6,opt,name=featuresJSON,proto3" json:"featuresJSON,omitempty"`                                 // 权限列表，[code1, code2, ...]
	PriceType                   string                 `protobuf:"bytes,7,opt,name=priceType,proto3" json:"priceType,omitempty"`                                       // 价格类型：traffic, bandwidth, period
	TrafficPriceJSON            []byte                 `protobuf:"bytes,8,opt,name=trafficPriceJSON,proto3" json:"trafficPriceJSON,omitempty"`                         // 流量价格配置
	BandwidthPriceJSON          []byte                 `protobuf:"bytes,12,opt,name=bandwidthPriceJSON,proto3" json:"bandwidthPriceJSON,omitempty"`                    // 带宽价格配置
	MonthlyPrice                float64                `protobuf:"fixed64,9,opt,name=monthlyPrice,proto3" json:"monthlyPrice,omitempty"`                               // 月度价格
	SeasonallyPrice             float64                `protobuf:"fixed64,10,opt,name=seasonallyPrice,proto3" json:"seasonallyPrice,omitempty"`                        // 季度价格
	YearlyPrice                 float64                `protobuf:"fixed64,11,opt,name=yearlyPrice,proto3" json:"yearlyPrice,omitempty"`                                // 年度价格
	TotalServers                int32                  `protobuf:"varint,13,opt,name=totalServers,proto3" json:"totalServers,omitempty"`                               // 可以添加的网站数
	TotalServerNamesPerServer   int32                  `protobuf:"varint,14,opt,name=totalServerNamesPerServer,proto3" json:"totalServerNamesPerServer,omitempty"`     // 每个网站可以添加的域名数
	TotalServerNames            int32                  `protobuf:"varint,15,opt,name=totalServerNames,proto3" json:"totalServerNames,omitempty"`                       // 可以添加的域名总数
	DailyRequests               int64                  `protobuf:"varint,16,opt,name=dailyRequests,proto3" json:"dailyRequests,omitempty"`                             // 每日访问量额度
	MonthlyRequests             int64                  `protobuf:"varint,17,opt,name=monthlyRequests,proto3" json:"monthlyRequests,omitempty"`                         // 每月访问量额度
	DailyWebsocketConnections   int64                  `protobuf:"varint,18,opt,name=dailyWebsocketConnections,proto3" json:"dailyWebsocketConnections,omitempty"`     // 每日Websocket连接数额度
	MonthlyWebsocketConnections int64                  `protobuf:"varint,19,opt,name=monthlyWebsocketConnections,proto3" json:"monthlyWebsocketConnections,omitempty"` // 每月Websocket连接数额度
	MaxUploadSizeJSON           []byte                 `protobuf:"bytes,23,opt,name=maxUploadSizeJSON,proto3" json:"maxUploadSizeJSON,omitempty"`                      // 文件最大上传尺寸 @link json:size_capacity
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *Plan) Reset() {
	*x = Plan{}
	mi := &file_models_model_plan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Plan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plan) ProtoMessage() {}

func (x *Plan) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_plan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plan.ProtoReflect.Descriptor instead.
func (*Plan) Descriptor() ([]byte, []int) {
	return file_models_model_plan_proto_rawDescGZIP(), []int{0}
}

func (x *Plan) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Plan) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *Plan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Plan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Plan) GetClusterId() int64 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *Plan) GetTrafficLimitJSON() []byte {
	if x != nil {
		return x.TrafficLimitJSON
	}
	return nil
}

func (x *Plan) GetBandwidthLimitPerNodeJSON() []byte {
	if x != nil {
		return x.BandwidthLimitPerNodeJSON
	}
	return nil
}

func (x *Plan) GetHasFullFeatures() bool {
	if x != nil {
		return x.HasFullFeatures
	}
	return false
}

func (x *Plan) GetFeaturesJSON() []byte {
	if x != nil {
		return x.FeaturesJSON
	}
	return nil
}

func (x *Plan) GetPriceType() string {
	if x != nil {
		return x.PriceType
	}
	return ""
}

func (x *Plan) GetTrafficPriceJSON() []byte {
	if x != nil {
		return x.TrafficPriceJSON
	}
	return nil
}

func (x *Plan) GetBandwidthPriceJSON() []byte {
	if x != nil {
		return x.BandwidthPriceJSON
	}
	return nil
}

func (x *Plan) GetMonthlyPrice() float64 {
	if x != nil {
		return x.MonthlyPrice
	}
	return 0
}

func (x *Plan) GetSeasonallyPrice() float64 {
	if x != nil {
		return x.SeasonallyPrice
	}
	return 0
}

func (x *Plan) GetYearlyPrice() float64 {
	if x != nil {
		return x.YearlyPrice
	}
	return 0
}

func (x *Plan) GetTotalServers() int32 {
	if x != nil {
		return x.TotalServers
	}
	return 0
}

func (x *Plan) GetTotalServerNamesPerServer() int32 {
	if x != nil {
		return x.TotalServerNamesPerServer
	}
	return 0
}

func (x *Plan) GetTotalServerNames() int32 {
	if x != nil {
		return x.TotalServerNames
	}
	return 0
}

func (x *Plan) GetDailyRequests() int64 {
	if x != nil {
		return x.DailyRequests
	}
	return 0
}

func (x *Plan) GetMonthlyRequests() int64 {
	if x != nil {
		return x.MonthlyRequests
	}
	return 0
}

func (x *Plan) GetDailyWebsocketConnections() int64 {
	if x != nil {
		return x.DailyWebsocketConnections
	}
	return 0
}

func (x *Plan) GetMonthlyWebsocketConnections() int64 {
	if x != nil {
		return x.MonthlyWebsocketConnections
	}
	return 0
}

func (x *Plan) GetMaxUploadSizeJSON() []byte {
	if x != nil {
		return x.MaxUploadSizeJSON
	}
	return nil
}

var File_models_model_plan_proto protoreflect.FileDescriptor

const file_models_model_plan_proto_rawDesc = "" +
	"\n" +
	"\x17models/model_plan.proto\x12\x02pb\"\xac\a\n" +
	"\x04Plan\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04isOn\x18\x02 \x01(\bR\x04isOn\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x15 \x01(\tR\vdescription\x12\x1c\n" +
	"\tclusterId\x18\x04 \x01(\x03R\tclusterId\x12*\n" +
	"\x10trafficLimitJSON\x18\x05 \x01(\fR\x10trafficLimitJSON\x12<\n" +
	"\x19bandwidthLimitPerNodeJSON\x18\x16 \x01(\fR\x19bandwidthLimitPerNodeJSON\x12(\n" +
	"\x0fhasFullFeatures\x18\x14 \x01(\bR\x0fhasFullFeatures\x12\"\n" +
	"\ffeaturesJSON\x18\x06 \x01(\fR\ffeaturesJSON\x12\x1c\n" +
	"\tpriceType\x18\a \x01(\tR\tpriceType\x12*\n" +
	"\x10trafficPriceJSON\x18\b \x01(\fR\x10trafficPriceJSON\x12.\n" +
	"\x12bandwidthPriceJSON\x18\f \x01(\fR\x12bandwidthPriceJSON\x12\"\n" +
	"\fmonthlyPrice\x18\t \x01(\x01R\fmonthlyPrice\x12(\n" +
	"\x0fseasonallyPrice\x18\n" +
	" \x01(\x01R\x0fseasonallyPrice\x12 \n" +
	"\vyearlyPrice\x18\v \x01(\x01R\vyearlyPrice\x12\"\n" +
	"\ftotalServers\x18\r \x01(\x05R\ftotalServers\x12<\n" +
	"\x19totalServerNamesPerServer\x18\x0e \x01(\x05R\x19totalServerNamesPerServer\x12*\n" +
	"\x10totalServerNames\x18\x0f \x01(\x05R\x10totalServerNames\x12$\n" +
	"\rdailyRequests\x18\x10 \x01(\x03R\rdailyRequests\x12(\n" +
	"\x0fmonthlyRequests\x18\x11 \x01(\x03R\x0fmonthlyRequests\x12<\n" +
	"\x19dailyWebsocketConnections\x18\x12 \x01(\x03R\x19dailyWebsocketConnections\x12@\n" +
	"\x1bmonthlyWebsocketConnections\x18\x13 \x01(\x03R\x1bmonthlyWebsocketConnections\x12,\n" +
	"\x11maxUploadSizeJSON\x18\x17 \x01(\fR\x11maxUploadSizeJSONB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_plan_proto_rawDescOnce sync.Once
	file_models_model_plan_proto_rawDescData []byte
)

func file_models_model_plan_proto_rawDescGZIP() []byte {
	file_models_model_plan_proto_rawDescOnce.Do(func() {
		file_models_model_plan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_plan_proto_rawDesc), len(file_models_model_plan_proto_rawDesc)))
	})
	return file_models_model_plan_proto_rawDescData
}

var file_models_model_plan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_plan_proto_goTypes = []any{
	(*Plan)(nil), // 0: pb.Plan
}
var file_models_model_plan_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_plan_proto_init() }
func file_models_model_plan_proto_init() {
	if File_models_model_plan_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_plan_proto_rawDesc), len(file_models_model_plan_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_plan_proto_goTypes,
		DependencyIndexes: file_models_model_plan_proto_depIdxs,
		MessageInfos:      file_models_model_plan_proto_msgTypes,
	}.Build()
	File_models_model_plan_proto = out.File
	file_models_model_plan_proto_goTypes = nil
	file_models_model_plan_proto_depIdxs = nil
}
