// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_region_city_monthly_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerRegionCityMonthlyStatService_FindTopServerRegionCityMonthlyStats_FullMethodName = "/pb.ServerRegionCityMonthlyStatService/findTopServerRegionCityMonthlyStats"
)

// ServerRegionCityMonthlyStatServiceClient is the client API for ServerRegionCityMonthlyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerRegionCityMonthlyStatServiceClient interface {
	// 查找前N个城市
	FindTopServerRegionCityMonthlyStats(ctx context.Context, in *FindTopServerRegionCityMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerRegionCityMonthlyStatsResponse, error)
}

type serverRegionCityMonthlyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerRegionCityMonthlyStatServiceClient(cc grpc.ClientConnInterface) ServerRegionCityMonthlyStatServiceClient {
	return &serverRegionCityMonthlyStatServiceClient{cc}
}

func (c *serverRegionCityMonthlyStatServiceClient) FindTopServerRegionCityMonthlyStats(ctx context.Context, in *FindTopServerRegionCityMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerRegionCityMonthlyStatsResponse, error) {
	out := new(FindTopServerRegionCityMonthlyStatsResponse)
	err := c.cc.Invoke(ctx, ServerRegionCityMonthlyStatService_FindTopServerRegionCityMonthlyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerRegionCityMonthlyStatServiceServer is the server API for ServerRegionCityMonthlyStatService service.
// All implementations should embed UnimplementedServerRegionCityMonthlyStatServiceServer
// for forward compatibility
type ServerRegionCityMonthlyStatServiceServer interface {
	// 查找前N个城市
	FindTopServerRegionCityMonthlyStats(context.Context, *FindTopServerRegionCityMonthlyStatsRequest) (*FindTopServerRegionCityMonthlyStatsResponse, error)
}

// UnimplementedServerRegionCityMonthlyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerRegionCityMonthlyStatServiceServer struct {
}

func (UnimplementedServerRegionCityMonthlyStatServiceServer) FindTopServerRegionCityMonthlyStats(context.Context, *FindTopServerRegionCityMonthlyStatsRequest) (*FindTopServerRegionCityMonthlyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTopServerRegionCityMonthlyStats not implemented")
}

// UnsafeServerRegionCityMonthlyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerRegionCityMonthlyStatServiceServer will
// result in compilation errors.
type UnsafeServerRegionCityMonthlyStatServiceServer interface {
	mustEmbedUnimplementedServerRegionCityMonthlyStatServiceServer()
}

func RegisterServerRegionCityMonthlyStatServiceServer(s grpc.ServiceRegistrar, srv ServerRegionCityMonthlyStatServiceServer) {
	s.RegisterService(&ServerRegionCityMonthlyStatService_ServiceDesc, srv)
}

func _ServerRegionCityMonthlyStatService_FindTopServerRegionCityMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTopServerRegionCityMonthlyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerRegionCityMonthlyStatServiceServer).FindTopServerRegionCityMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerRegionCityMonthlyStatService_FindTopServerRegionCityMonthlyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerRegionCityMonthlyStatServiceServer).FindTopServerRegionCityMonthlyStats(ctx, req.(*FindTopServerRegionCityMonthlyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerRegionCityMonthlyStatService_ServiceDesc is the grpc.ServiceDesc for ServerRegionCityMonthlyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerRegionCityMonthlyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerRegionCityMonthlyStatService",
	HandlerType: (*ServerRegionCityMonthlyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findTopServerRegionCityMonthlyStats",
			Handler:    _ServerRegionCityMonthlyStatService_FindTopServerRegionCityMonthlyStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_region_city_monthly_stat.proto",
}
