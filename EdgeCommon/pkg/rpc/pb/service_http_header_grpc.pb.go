// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_header.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPHeaderService_CreateHTTPHeader_FullMethodName            = "/pb.HTTPHeaderService/createHTTPHeader"
	HTTPHeaderService_UpdateHTTPHeader_FullMethodName            = "/pb.HTTPHeaderService/updateHTTPHeader"
	HTTPHeaderService_FindEnabledHTTPHeaderConfig_FullMethodName = "/pb.HTTPHeaderService/findEnabledHTTPHeaderConfig"
)

// HTTPHeaderServiceClient is the client API for HTTPHeaderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPHeaderServiceClient interface {
	// 创建Header
	CreateHTTPHeader(ctx context.Context, in *CreateHTTPHeaderRequest, opts ...grpc.CallOption) (*CreateHTTPHeaderResponse, error)
	// 修改Header
	UpdateHTTPHeader(ctx context.Context, in *UpdateHTTPHeaderRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找配置
	FindEnabledHTTPHeaderConfig(ctx context.Context, in *FindEnabledHTTPHeaderConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPHeaderConfigResponse, error)
}

type hTTPHeaderServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPHeaderServiceClient(cc grpc.ClientConnInterface) HTTPHeaderServiceClient {
	return &hTTPHeaderServiceClient{cc}
}

func (c *hTTPHeaderServiceClient) CreateHTTPHeader(ctx context.Context, in *CreateHTTPHeaderRequest, opts ...grpc.CallOption) (*CreateHTTPHeaderResponse, error) {
	out := new(CreateHTTPHeaderResponse)
	err := c.cc.Invoke(ctx, HTTPHeaderService_CreateHTTPHeader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPHeaderServiceClient) UpdateHTTPHeader(ctx context.Context, in *UpdateHTTPHeaderRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPHeaderService_UpdateHTTPHeader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPHeaderServiceClient) FindEnabledHTTPHeaderConfig(ctx context.Context, in *FindEnabledHTTPHeaderConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPHeaderConfigResponse, error) {
	out := new(FindEnabledHTTPHeaderConfigResponse)
	err := c.cc.Invoke(ctx, HTTPHeaderService_FindEnabledHTTPHeaderConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPHeaderServiceServer is the server API for HTTPHeaderService service.
// All implementations should embed UnimplementedHTTPHeaderServiceServer
// for forward compatibility
type HTTPHeaderServiceServer interface {
	// 创建Header
	CreateHTTPHeader(context.Context, *CreateHTTPHeaderRequest) (*CreateHTTPHeaderResponse, error)
	// 修改Header
	UpdateHTTPHeader(context.Context, *UpdateHTTPHeaderRequest) (*RPCSuccess, error)
	// 查找配置
	FindEnabledHTTPHeaderConfig(context.Context, *FindEnabledHTTPHeaderConfigRequest) (*FindEnabledHTTPHeaderConfigResponse, error)
}

// UnimplementedHTTPHeaderServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPHeaderServiceServer struct {
}

func (UnimplementedHTTPHeaderServiceServer) CreateHTTPHeader(context.Context, *CreateHTTPHeaderRequest) (*CreateHTTPHeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPHeader not implemented")
}
func (UnimplementedHTTPHeaderServiceServer) UpdateHTTPHeader(context.Context, *UpdateHTTPHeaderRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPHeader not implemented")
}
func (UnimplementedHTTPHeaderServiceServer) FindEnabledHTTPHeaderConfig(context.Context, *FindEnabledHTTPHeaderConfigRequest) (*FindEnabledHTTPHeaderConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPHeaderConfig not implemented")
}

// UnsafeHTTPHeaderServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPHeaderServiceServer will
// result in compilation errors.
type UnsafeHTTPHeaderServiceServer interface {
	mustEmbedUnimplementedHTTPHeaderServiceServer()
}

func RegisterHTTPHeaderServiceServer(s grpc.ServiceRegistrar, srv HTTPHeaderServiceServer) {
	s.RegisterService(&HTTPHeaderService_ServiceDesc, srv)
}

func _HTTPHeaderService_CreateHTTPHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPHeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPHeaderServiceServer).CreateHTTPHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPHeaderService_CreateHTTPHeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPHeaderServiceServer).CreateHTTPHeader(ctx, req.(*CreateHTTPHeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPHeaderService_UpdateHTTPHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPHeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPHeaderServiceServer).UpdateHTTPHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPHeaderService_UpdateHTTPHeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPHeaderServiceServer).UpdateHTTPHeader(ctx, req.(*UpdateHTTPHeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPHeaderService_FindEnabledHTTPHeaderConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPHeaderConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPHeaderServiceServer).FindEnabledHTTPHeaderConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPHeaderService_FindEnabledHTTPHeaderConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPHeaderServiceServer).FindEnabledHTTPHeaderConfig(ctx, req.(*FindEnabledHTTPHeaderConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPHeaderService_ServiceDesc is the grpc.ServiceDesc for HTTPHeaderService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPHeaderService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPHeaderService",
	HandlerType: (*HTTPHeaderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHTTPHeader",
			Handler:    _HTTPHeaderService_CreateHTTPHeader_Handler,
		},
		{
			MethodName: "updateHTTPHeader",
			Handler:    _HTTPHeaderService_UpdateHTTPHeader_Handler,
		},
		{
			MethodName: "findEnabledHTTPHeaderConfig",
			Handler:    _HTTPHeaderService_FindEnabledHTTPHeaderConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_header.proto",
}
