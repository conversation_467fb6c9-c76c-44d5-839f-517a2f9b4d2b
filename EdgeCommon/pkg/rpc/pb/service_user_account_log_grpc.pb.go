// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_account_log.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserAccountLogService_CountUserAccountLogs_FullMethodName = "/pb.UserAccountLogService/countUserAccountLogs"
	UserAccountLogService_ListUserAccountLogs_FullMethodName  = "/pb.UserAccountLogService/listUserAccountLogs"
)

// UserAccountLogServiceClient is the client API for UserAccountLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserAccountLogServiceClient interface {
	// 计算日志数量
	CountUserAccountLogs(ctx context.Context, in *CountUserAccountLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页日志
	ListUserAccountLogs(ctx context.Context, in *ListUserAccountLogsRequest, opts ...grpc.CallOption) (*ListUserAccountLogsResponse, error)
}

type userAccountLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserAccountLogServiceClient(cc grpc.ClientConnInterface) UserAccountLogServiceClient {
	return &userAccountLogServiceClient{cc}
}

func (c *userAccountLogServiceClient) CountUserAccountLogs(ctx context.Context, in *CountUserAccountLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserAccountLogService_CountUserAccountLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAccountLogServiceClient) ListUserAccountLogs(ctx context.Context, in *ListUserAccountLogsRequest, opts ...grpc.CallOption) (*ListUserAccountLogsResponse, error) {
	out := new(ListUserAccountLogsResponse)
	err := c.cc.Invoke(ctx, UserAccountLogService_ListUserAccountLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserAccountLogServiceServer is the server API for UserAccountLogService service.
// All implementations should embed UnimplementedUserAccountLogServiceServer
// for forward compatibility
type UserAccountLogServiceServer interface {
	// 计算日志数量
	CountUserAccountLogs(context.Context, *CountUserAccountLogsRequest) (*RPCCountResponse, error)
	// 列出单页日志
	ListUserAccountLogs(context.Context, *ListUserAccountLogsRequest) (*ListUserAccountLogsResponse, error)
}

// UnimplementedUserAccountLogServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserAccountLogServiceServer struct {
}

func (UnimplementedUserAccountLogServiceServer) CountUserAccountLogs(context.Context, *CountUserAccountLogsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserAccountLogs not implemented")
}
func (UnimplementedUserAccountLogServiceServer) ListUserAccountLogs(context.Context, *ListUserAccountLogsRequest) (*ListUserAccountLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserAccountLogs not implemented")
}

// UnsafeUserAccountLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserAccountLogServiceServer will
// result in compilation errors.
type UnsafeUserAccountLogServiceServer interface {
	mustEmbedUnimplementedUserAccountLogServiceServer()
}

func RegisterUserAccountLogServiceServer(s grpc.ServiceRegistrar, srv UserAccountLogServiceServer) {
	s.RegisterService(&UserAccountLogService_ServiceDesc, srv)
}

func _UserAccountLogService_CountUserAccountLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserAccountLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountLogServiceServer).CountUserAccountLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountLogService_CountUserAccountLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountLogServiceServer).CountUserAccountLogs(ctx, req.(*CountUserAccountLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAccountLogService_ListUserAccountLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserAccountLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountLogServiceServer).ListUserAccountLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountLogService_ListUserAccountLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountLogServiceServer).ListUserAccountLogs(ctx, req.(*ListUserAccountLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserAccountLogService_ServiceDesc is the grpc.ServiceDesc for UserAccountLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserAccountLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserAccountLogService",
	HandlerType: (*UserAccountLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countUserAccountLogs",
			Handler:    _UserAccountLogService_CountUserAccountLogs_Handler,
		},
		{
			MethodName: "listUserAccountLogs",
			Handler:    _UserAccountLogService_ListUserAccountLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_account_log.proto",
}
