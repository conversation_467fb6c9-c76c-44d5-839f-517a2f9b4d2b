// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_region_province.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 省份｜州|区域信息
type RegionProvince struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Codes           []string               `protobuf:"bytes,3,rep,name=codes,proto3" json:"codes,omitempty"`
	RegionCountryId int64                  `protobuf:"varint,4,opt,name=regionCountryId,proto3" json:"regionCountryId,omitempty"`
	CustomName      string                 `protobuf:"bytes,5,opt,name=customName,proto3" json:"customName,omitempty"`
	CustomCodes     []string               `protobuf:"bytes,6,rep,name=customCodes,proto3" json:"customCodes,omitempty"`
	DisplayName     string                 `protobuf:"bytes,7,opt,name=displayName,proto3" json:"displayName,omitempty"`
	RegionCountry   *RegionCountry         `protobuf:"bytes,30,opt,name=regionCountry,proto3" json:"regionCountry,omitempty"` // 国家|地区信息
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RegionProvince) Reset() {
	*x = RegionProvince{}
	mi := &file_models_model_region_province_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionProvince) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionProvince) ProtoMessage() {}

func (x *RegionProvince) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_region_province_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionProvince.ProtoReflect.Descriptor instead.
func (*RegionProvince) Descriptor() ([]byte, []int) {
	return file_models_model_region_province_proto_rawDescGZIP(), []int{0}
}

func (x *RegionProvince) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegionProvince) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegionProvince) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *RegionProvince) GetRegionCountryId() int64 {
	if x != nil {
		return x.RegionCountryId
	}
	return 0
}

func (x *RegionProvince) GetCustomName() string {
	if x != nil {
		return x.CustomName
	}
	return ""
}

func (x *RegionProvince) GetCustomCodes() []string {
	if x != nil {
		return x.CustomCodes
	}
	return nil
}

func (x *RegionProvince) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *RegionProvince) GetRegionCountry() *RegionCountry {
	if x != nil {
		return x.RegionCountry
	}
	return nil
}

var File_models_model_region_province_proto protoreflect.FileDescriptor

const file_models_model_region_province_proto_rawDesc = "" +
	"\n" +
	"\"models/model_region_province.proto\x12\x02pb\x1a!models/model_region_country.proto\"\x91\x02\n" +
	"\x0eRegionProvince\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05codes\x18\x03 \x03(\tR\x05codes\x12(\n" +
	"\x0fregionCountryId\x18\x04 \x01(\x03R\x0fregionCountryId\x12\x1e\n" +
	"\n" +
	"customName\x18\x05 \x01(\tR\n" +
	"customName\x12 \n" +
	"\vcustomCodes\x18\x06 \x03(\tR\vcustomCodes\x12 \n" +
	"\vdisplayName\x18\a \x01(\tR\vdisplayName\x127\n" +
	"\rregionCountry\x18\x1e \x01(\v2\x11.pb.RegionCountryR\rregionCountryB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_region_province_proto_rawDescOnce sync.Once
	file_models_model_region_province_proto_rawDescData []byte
)

func file_models_model_region_province_proto_rawDescGZIP() []byte {
	file_models_model_region_province_proto_rawDescOnce.Do(func() {
		file_models_model_region_province_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_region_province_proto_rawDesc), len(file_models_model_region_province_proto_rawDesc)))
	})
	return file_models_model_region_province_proto_rawDescData
}

var file_models_model_region_province_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_region_province_proto_goTypes = []any{
	(*RegionProvince)(nil), // 0: pb.RegionProvince
	(*RegionCountry)(nil),  // 1: pb.RegionCountry
}
var file_models_model_region_province_proto_depIdxs = []int32{
	1, // 0: pb.RegionProvince.regionCountry:type_name -> pb.RegionCountry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_region_province_proto_init() }
func file_models_model_region_province_proto_init() {
	if File_models_model_region_province_proto != nil {
		return
	}
	file_models_model_region_country_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_region_province_proto_rawDesc), len(file_models_model_region_province_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_region_province_proto_goTypes,
		DependencyIndexes: file_models_model_region_province_proto_depIdxs,
		MessageInfos:      file_models_model_region_province_proto_msgTypes,
	}.Build()
	File_models_model_region_province_proto = out.File
	file_models_model_region_province_proto_goTypes = nil
	file_models_model_region_province_proto_depIdxs = nil
}
