// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_mobile_verification.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 手机号码认证
type UserMobileVerification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 // ID
	Mobile        string                 `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`          // 手机号码
	UserId        int64                  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`         // 用户ID
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`              // 代号
	CreatedAt     int64                  `protobuf:"varint,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`   // 创建时间
	IsSent        bool                   `protobuf:"varint,6,opt,name=isSent,proto3" json:"isSent,omitempty"`         // 已发送
	IsVerified    bool                   `protobuf:"varint,7,opt,name=isVerified,proto3" json:"isVerified,omitempty"` // 已激活
	ExpiresAt     int64                  `protobuf:"varint,8,opt,name=expiresAt,proto3" json:"expiresAt,omitempty"`   // 过期时间，动态计算而来
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserMobileVerification) Reset() {
	*x = UserMobileVerification{}
	mi := &file_models_model_user_mobile_verification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserMobileVerification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserMobileVerification) ProtoMessage() {}

func (x *UserMobileVerification) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_mobile_verification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserMobileVerification.ProtoReflect.Descriptor instead.
func (*UserMobileVerification) Descriptor() ([]byte, []int) {
	return file_models_model_user_mobile_verification_proto_rawDescGZIP(), []int{0}
}

func (x *UserMobileVerification) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserMobileVerification) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserMobileVerification) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserMobileVerification) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserMobileVerification) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserMobileVerification) GetIsSent() bool {
	if x != nil {
		return x.IsSent
	}
	return false
}

func (x *UserMobileVerification) GetIsVerified() bool {
	if x != nil {
		return x.IsVerified
	}
	return false
}

func (x *UserMobileVerification) GetExpiresAt() int64 {
	if x != nil {
		return x.ExpiresAt
	}
	return 0
}

var File_models_model_user_mobile_verification_proto protoreflect.FileDescriptor

const file_models_model_user_mobile_verification_proto_rawDesc = "" +
	"\n" +
	"+models/model_user_mobile_verification.proto\x12\x02pb\"\xe0\x01\n" +
	"\x16UserMobileVerification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06mobile\x18\x02 \x01(\tR\x06mobile\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\x03R\x06userId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x1c\n" +
	"\tcreatedAt\x18\x05 \x01(\x03R\tcreatedAt\x12\x16\n" +
	"\x06isSent\x18\x06 \x01(\bR\x06isSent\x12\x1e\n" +
	"\n" +
	"isVerified\x18\a \x01(\bR\n" +
	"isVerified\x12\x1c\n" +
	"\texpiresAt\x18\b \x01(\x03R\texpiresAtB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_mobile_verification_proto_rawDescOnce sync.Once
	file_models_model_user_mobile_verification_proto_rawDescData []byte
)

func file_models_model_user_mobile_verification_proto_rawDescGZIP() []byte {
	file_models_model_user_mobile_verification_proto_rawDescOnce.Do(func() {
		file_models_model_user_mobile_verification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_mobile_verification_proto_rawDesc), len(file_models_model_user_mobile_verification_proto_rawDesc)))
	})
	return file_models_model_user_mobile_verification_proto_rawDescData
}

var file_models_model_user_mobile_verification_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_mobile_verification_proto_goTypes = []any{
	(*UserMobileVerification)(nil), // 0: pb.UserMobileVerification
}
var file_models_model_user_mobile_verification_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_user_mobile_verification_proto_init() }
func file_models_model_user_mobile_verification_proto_init() {
	if File_models_model_user_mobile_verification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_mobile_verification_proto_rawDesc), len(file_models_model_user_mobile_verification_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_mobile_verification_proto_goTypes,
		DependencyIndexes: file_models_model_user_mobile_verification_proto_depIdxs,
		MessageInfos:      file_models_model_user_mobile_verification_proto_msgTypes,
	}.Build()
	File_models_model_user_mobile_verification_proto = out.File
	file_models_model_user_mobile_verification_proto_goTypes = nil
	file_models_model_user_mobile_verification_proto_depIdxs = nil
}
