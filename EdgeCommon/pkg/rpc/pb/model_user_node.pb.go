// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_node.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserNode struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOn            bool                   `protobuf:"varint,2,opt,name=isOn,proto3" json:"isOn,omitempty"`
	UniqueId        string                 `protobuf:"bytes,3,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
	Secret          string                 `protobuf:"bytes,4,opt,name=secret,proto3" json:"secret,omitempty"`
	Name            string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Description     string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	HttpJSON        []byte                 `protobuf:"bytes,7,opt,name=httpJSON,proto3" json:"httpJSON,omitempty"`
	HttpsJSON       []byte                 `protobuf:"bytes,8,opt,name=httpsJSON,proto3" json:"httpsJSON,omitempty"`
	AccessAddrsJSON []byte                 `protobuf:"bytes,9,opt,name=accessAddrsJSON,proto3" json:"accessAddrsJSON,omitempty"`
	AccessAddrs     []string               `protobuf:"bytes,10,rep,name=accessAddrs,proto3" json:"accessAddrs,omitempty"`
	StatusJSON      []byte                 `protobuf:"bytes,11,opt,name=statusJSON,proto3" json:"statusJSON,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UserNode) Reset() {
	*x = UserNode{}
	mi := &file_models_model_user_node_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNode) ProtoMessage() {}

func (x *UserNode) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_node_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNode.ProtoReflect.Descriptor instead.
func (*UserNode) Descriptor() ([]byte, []int) {
	return file_models_model_user_node_proto_rawDescGZIP(), []int{0}
}

func (x *UserNode) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserNode) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *UserNode) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

func (x *UserNode) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *UserNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserNode) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserNode) GetHttpJSON() []byte {
	if x != nil {
		return x.HttpJSON
	}
	return nil
}

func (x *UserNode) GetHttpsJSON() []byte {
	if x != nil {
		return x.HttpsJSON
	}
	return nil
}

func (x *UserNode) GetAccessAddrsJSON() []byte {
	if x != nil {
		return x.AccessAddrsJSON
	}
	return nil
}

func (x *UserNode) GetAccessAddrs() []string {
	if x != nil {
		return x.AccessAddrs
	}
	return nil
}

func (x *UserNode) GetStatusJSON() []byte {
	if x != nil {
		return x.StatusJSON
	}
	return nil
}

var File_models_model_user_node_proto protoreflect.FileDescriptor

const file_models_model_user_node_proto_rawDesc = "" +
	"\n" +
	"\x1cmodels/model_user_node.proto\x12\x02pb\"\xbe\x02\n" +
	"\bUserNode\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04isOn\x18\x02 \x01(\bR\x04isOn\x12\x1a\n" +
	"\buniqueId\x18\x03 \x01(\tR\buniqueId\x12\x16\n" +
	"\x06secret\x18\x04 \x01(\tR\x06secret\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x1a\n" +
	"\bhttpJSON\x18\a \x01(\fR\bhttpJSON\x12\x1c\n" +
	"\thttpsJSON\x18\b \x01(\fR\thttpsJSON\x12(\n" +
	"\x0faccessAddrsJSON\x18\t \x01(\fR\x0faccessAddrsJSON\x12 \n" +
	"\vaccessAddrs\x18\n" +
	" \x03(\tR\vaccessAddrs\x12\x1e\n" +
	"\n" +
	"statusJSON\x18\v \x01(\fR\n" +
	"statusJSONB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_node_proto_rawDescOnce sync.Once
	file_models_model_user_node_proto_rawDescData []byte
)

func file_models_model_user_node_proto_rawDescGZIP() []byte {
	file_models_model_user_node_proto_rawDescOnce.Do(func() {
		file_models_model_user_node_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_node_proto_rawDesc), len(file_models_model_user_node_proto_rawDesc)))
	})
	return file_models_model_user_node_proto_rawDescData
}

var file_models_model_user_node_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_node_proto_goTypes = []any{
	(*UserNode)(nil), // 0: pb.UserNode
}
var file_models_model_user_node_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_user_node_proto_init() }
func file_models_model_user_node_proto_init() {
	if File_models_model_user_node_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_node_proto_rawDesc), len(file_models_model_user_node_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_node_proto_goTypes,
		DependencyIndexes: file_models_model_user_node_proto_depIdxs,
		MessageInfos:      file_models_model_user_node_proto_msgTypes,
	}.Build()
	File_models_model_user_node_proto = out.File
	file_models_model_user_node_proto_goTypes = nil
	file_models_model_user_node_proto_depIdxs = nil
}
