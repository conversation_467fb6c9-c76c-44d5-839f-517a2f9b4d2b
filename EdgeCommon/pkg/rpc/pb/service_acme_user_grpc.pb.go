// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_acme_user.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ACMEUserService_CreateACMEUser_FullMethodName      = "/pb.ACMEUserService/createACMEUser"
	ACMEUserService_UpdateACMEUser_FullMethodName      = "/pb.ACMEUserService/updateACMEUser"
	ACMEUserService_DeleteACMEUser_FullMethodName      = "/pb.ACMEUserService/deleteACMEUser"
	ACMEUserService_CountACMEUsers_FullMethodName      = "/pb.ACMEUserService/countACMEUsers"
	ACMEUserService_ListACMEUsers_FullMethodName       = "/pb.ACMEUserService/listACMEUsers"
	ACMEUserService_FindEnabledACMEUser_FullMethodName = "/pb.ACMEUserService/findEnabledACMEUser"
	ACMEUserService_FindAllACMEUsers_FullMethodName    = "/pb.ACMEUserService/findAllACMEUsers"
)

// ACMEUserServiceClient is the client API for ACMEUserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ACMEUserServiceClient interface {
	// 创建用户
	CreateACMEUser(ctx context.Context, in *CreateACMEUserRequest, opts ...grpc.CallOption) (*CreateACMEUserResponse, error)
	// 修改用户
	UpdateACMEUser(ctx context.Context, in *UpdateACMEUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除用户
	DeleteACMEUser(ctx context.Context, in *DeleteACMEUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算用户数量
	CountACMEUsers(ctx context.Context, in *CountAcmeUsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页用户
	ListACMEUsers(ctx context.Context, in *ListACMEUsersRequest, opts ...grpc.CallOption) (*ListACMEUsersResponse, error)
	// 查找单个用户
	FindEnabledACMEUser(ctx context.Context, in *FindEnabledACMEUserRequest, opts ...grpc.CallOption) (*FindEnabledACMEUserResponse, error)
	// 查找所有用户
	FindAllACMEUsers(ctx context.Context, in *FindAllACMEUsersRequest, opts ...grpc.CallOption) (*FindAllACMEUsersResponse, error)
}

type aCMEUserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewACMEUserServiceClient(cc grpc.ClientConnInterface) ACMEUserServiceClient {
	return &aCMEUserServiceClient{cc}
}

func (c *aCMEUserServiceClient) CreateACMEUser(ctx context.Context, in *CreateACMEUserRequest, opts ...grpc.CallOption) (*CreateACMEUserResponse, error) {
	out := new(CreateACMEUserResponse)
	err := c.cc.Invoke(ctx, ACMEUserService_CreateACMEUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEUserServiceClient) UpdateACMEUser(ctx context.Context, in *UpdateACMEUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ACMEUserService_UpdateACMEUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEUserServiceClient) DeleteACMEUser(ctx context.Context, in *DeleteACMEUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ACMEUserService_DeleteACMEUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEUserServiceClient) CountACMEUsers(ctx context.Context, in *CountAcmeUsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ACMEUserService_CountACMEUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEUserServiceClient) ListACMEUsers(ctx context.Context, in *ListACMEUsersRequest, opts ...grpc.CallOption) (*ListACMEUsersResponse, error) {
	out := new(ListACMEUsersResponse)
	err := c.cc.Invoke(ctx, ACMEUserService_ListACMEUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEUserServiceClient) FindEnabledACMEUser(ctx context.Context, in *FindEnabledACMEUserRequest, opts ...grpc.CallOption) (*FindEnabledACMEUserResponse, error) {
	out := new(FindEnabledACMEUserResponse)
	err := c.cc.Invoke(ctx, ACMEUserService_FindEnabledACMEUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEUserServiceClient) FindAllACMEUsers(ctx context.Context, in *FindAllACMEUsersRequest, opts ...grpc.CallOption) (*FindAllACMEUsersResponse, error) {
	out := new(FindAllACMEUsersResponse)
	err := c.cc.Invoke(ctx, ACMEUserService_FindAllACMEUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ACMEUserServiceServer is the server API for ACMEUserService service.
// All implementations should embed UnimplementedACMEUserServiceServer
// for forward compatibility
type ACMEUserServiceServer interface {
	// 创建用户
	CreateACMEUser(context.Context, *CreateACMEUserRequest) (*CreateACMEUserResponse, error)
	// 修改用户
	UpdateACMEUser(context.Context, *UpdateACMEUserRequest) (*RPCSuccess, error)
	// 删除用户
	DeleteACMEUser(context.Context, *DeleteACMEUserRequest) (*RPCSuccess, error)
	// 计算用户数量
	CountACMEUsers(context.Context, *CountAcmeUsersRequest) (*RPCCountResponse, error)
	// 列出单页用户
	ListACMEUsers(context.Context, *ListACMEUsersRequest) (*ListACMEUsersResponse, error)
	// 查找单个用户
	FindEnabledACMEUser(context.Context, *FindEnabledACMEUserRequest) (*FindEnabledACMEUserResponse, error)
	// 查找所有用户
	FindAllACMEUsers(context.Context, *FindAllACMEUsersRequest) (*FindAllACMEUsersResponse, error)
}

// UnimplementedACMEUserServiceServer should be embedded to have forward compatible implementations.
type UnimplementedACMEUserServiceServer struct {
}

func (UnimplementedACMEUserServiceServer) CreateACMEUser(context.Context, *CreateACMEUserRequest) (*CreateACMEUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateACMEUser not implemented")
}
func (UnimplementedACMEUserServiceServer) UpdateACMEUser(context.Context, *UpdateACMEUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateACMEUser not implemented")
}
func (UnimplementedACMEUserServiceServer) DeleteACMEUser(context.Context, *DeleteACMEUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteACMEUser not implemented")
}
func (UnimplementedACMEUserServiceServer) CountACMEUsers(context.Context, *CountAcmeUsersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountACMEUsers not implemented")
}
func (UnimplementedACMEUserServiceServer) ListACMEUsers(context.Context, *ListACMEUsersRequest) (*ListACMEUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListACMEUsers not implemented")
}
func (UnimplementedACMEUserServiceServer) FindEnabledACMEUser(context.Context, *FindEnabledACMEUserRequest) (*FindEnabledACMEUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledACMEUser not implemented")
}
func (UnimplementedACMEUserServiceServer) FindAllACMEUsers(context.Context, *FindAllACMEUsersRequest) (*FindAllACMEUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllACMEUsers not implemented")
}

// UnsafeACMEUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ACMEUserServiceServer will
// result in compilation errors.
type UnsafeACMEUserServiceServer interface {
	mustEmbedUnimplementedACMEUserServiceServer()
}

func RegisterACMEUserServiceServer(s grpc.ServiceRegistrar, srv ACMEUserServiceServer) {
	s.RegisterService(&ACMEUserService_ServiceDesc, srv)
}

func _ACMEUserService_CreateACMEUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateACMEUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).CreateACMEUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_CreateACMEUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).CreateACMEUser(ctx, req.(*CreateACMEUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEUserService_UpdateACMEUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateACMEUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).UpdateACMEUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_UpdateACMEUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).UpdateACMEUser(ctx, req.(*UpdateACMEUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEUserService_DeleteACMEUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteACMEUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).DeleteACMEUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_DeleteACMEUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).DeleteACMEUser(ctx, req.(*DeleteACMEUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEUserService_CountACMEUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAcmeUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).CountACMEUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_CountACMEUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).CountACMEUsers(ctx, req.(*CountAcmeUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEUserService_ListACMEUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListACMEUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).ListACMEUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_ListACMEUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).ListACMEUsers(ctx, req.(*ListACMEUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEUserService_FindEnabledACMEUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledACMEUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).FindEnabledACMEUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_FindEnabledACMEUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).FindEnabledACMEUser(ctx, req.(*FindEnabledACMEUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEUserService_FindAllACMEUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllACMEUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEUserServiceServer).FindAllACMEUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEUserService_FindAllACMEUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEUserServiceServer).FindAllACMEUsers(ctx, req.(*FindAllACMEUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ACMEUserService_ServiceDesc is the grpc.ServiceDesc for ACMEUserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ACMEUserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ACMEUserService",
	HandlerType: (*ACMEUserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createACMEUser",
			Handler:    _ACMEUserService_CreateACMEUser_Handler,
		},
		{
			MethodName: "updateACMEUser",
			Handler:    _ACMEUserService_UpdateACMEUser_Handler,
		},
		{
			MethodName: "deleteACMEUser",
			Handler:    _ACMEUserService_DeleteACMEUser_Handler,
		},
		{
			MethodName: "countACMEUsers",
			Handler:    _ACMEUserService_CountACMEUsers_Handler,
		},
		{
			MethodName: "listACMEUsers",
			Handler:    _ACMEUserService_ListACMEUsers_Handler,
		},
		{
			MethodName: "findEnabledACMEUser",
			Handler:    _ACMEUserService_FindEnabledACMEUser_Handler,
		},
		{
			MethodName: "findAllACMEUsers",
			Handler:    _ACMEUserService_FindAllACMEUsers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_acme_user.proto",
}
