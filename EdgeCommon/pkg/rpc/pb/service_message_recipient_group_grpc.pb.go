// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_message_recipient_group.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageRecipientGroupService_CreateMessageRecipientGroup_FullMethodName          = "/pb.MessageRecipientGroupService/createMessageRecipientGroup"
	MessageRecipientGroupService_UpdateMessageRecipientGroup_FullMethodName          = "/pb.MessageRecipientGroupService/updateMessageRecipientGroup"
	MessageRecipientGroupService_FindAllEnabledMessageRecipientGroups_FullMethodName = "/pb.MessageRecipientGroupService/findAllEnabledMessageRecipientGroups"
	MessageRecipientGroupService_DeleteMessageRecipientGroup_FullMethodName          = "/pb.MessageRecipientGroupService/deleteMessageRecipientGroup"
	MessageRecipientGroupService_FindEnabledMessageRecipientGroup_FullMethodName     = "/pb.MessageRecipientGroupService/findEnabledMessageRecipientGroup"
)

// MessageRecipientGroupServiceClient is the client API for MessageRecipientGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageRecipientGroupServiceClient interface {
	// 创建分组
	CreateMessageRecipientGroup(ctx context.Context, in *CreateMessageRecipientGroupRequest, opts ...grpc.CallOption) (*CreateMessageRecipientGroupResponse, error)
	// 修改分组
	UpdateMessageRecipientGroup(ctx context.Context, in *UpdateMessageRecipientGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找所有可用的分组
	FindAllEnabledMessageRecipientGroups(ctx context.Context, in *FindAllEnabledMessageRecipientGroupsRequest, opts ...grpc.CallOption) (*FindAllEnabledMessageRecipientGroupsResponse, error)
	// 删除分组
	DeleteMessageRecipientGroup(ctx context.Context, in *DeleteMessageRecipientGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个分组信息
	FindEnabledMessageRecipientGroup(ctx context.Context, in *FindEnabledMessageRecipientGroupRequest, opts ...grpc.CallOption) (*FindEnabledMessageRecipientGroupResponse, error)
}

type messageRecipientGroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageRecipientGroupServiceClient(cc grpc.ClientConnInterface) MessageRecipientGroupServiceClient {
	return &messageRecipientGroupServiceClient{cc}
}

func (c *messageRecipientGroupServiceClient) CreateMessageRecipientGroup(ctx context.Context, in *CreateMessageRecipientGroupRequest, opts ...grpc.CallOption) (*CreateMessageRecipientGroupResponse, error) {
	out := new(CreateMessageRecipientGroupResponse)
	err := c.cc.Invoke(ctx, MessageRecipientGroupService_CreateMessageRecipientGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientGroupServiceClient) UpdateMessageRecipientGroup(ctx context.Context, in *UpdateMessageRecipientGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageRecipientGroupService_UpdateMessageRecipientGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientGroupServiceClient) FindAllEnabledMessageRecipientGroups(ctx context.Context, in *FindAllEnabledMessageRecipientGroupsRequest, opts ...grpc.CallOption) (*FindAllEnabledMessageRecipientGroupsResponse, error) {
	out := new(FindAllEnabledMessageRecipientGroupsResponse)
	err := c.cc.Invoke(ctx, MessageRecipientGroupService_FindAllEnabledMessageRecipientGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientGroupServiceClient) DeleteMessageRecipientGroup(ctx context.Context, in *DeleteMessageRecipientGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageRecipientGroupService_DeleteMessageRecipientGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientGroupServiceClient) FindEnabledMessageRecipientGroup(ctx context.Context, in *FindEnabledMessageRecipientGroupRequest, opts ...grpc.CallOption) (*FindEnabledMessageRecipientGroupResponse, error) {
	out := new(FindEnabledMessageRecipientGroupResponse)
	err := c.cc.Invoke(ctx, MessageRecipientGroupService_FindEnabledMessageRecipientGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageRecipientGroupServiceServer is the server API for MessageRecipientGroupService service.
// All implementations should embed UnimplementedMessageRecipientGroupServiceServer
// for forward compatibility
type MessageRecipientGroupServiceServer interface {
	// 创建分组
	CreateMessageRecipientGroup(context.Context, *CreateMessageRecipientGroupRequest) (*CreateMessageRecipientGroupResponse, error)
	// 修改分组
	UpdateMessageRecipientGroup(context.Context, *UpdateMessageRecipientGroupRequest) (*RPCSuccess, error)
	// 查找所有可用的分组
	FindAllEnabledMessageRecipientGroups(context.Context, *FindAllEnabledMessageRecipientGroupsRequest) (*FindAllEnabledMessageRecipientGroupsResponse, error)
	// 删除分组
	DeleteMessageRecipientGroup(context.Context, *DeleteMessageRecipientGroupRequest) (*RPCSuccess, error)
	// 查找单个分组信息
	FindEnabledMessageRecipientGroup(context.Context, *FindEnabledMessageRecipientGroupRequest) (*FindEnabledMessageRecipientGroupResponse, error)
}

// UnimplementedMessageRecipientGroupServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMessageRecipientGroupServiceServer struct {
}

func (UnimplementedMessageRecipientGroupServiceServer) CreateMessageRecipientGroup(context.Context, *CreateMessageRecipientGroupRequest) (*CreateMessageRecipientGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMessageRecipientGroup not implemented")
}
func (UnimplementedMessageRecipientGroupServiceServer) UpdateMessageRecipientGroup(context.Context, *UpdateMessageRecipientGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMessageRecipientGroup not implemented")
}
func (UnimplementedMessageRecipientGroupServiceServer) FindAllEnabledMessageRecipientGroups(context.Context, *FindAllEnabledMessageRecipientGroupsRequest) (*FindAllEnabledMessageRecipientGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledMessageRecipientGroups not implemented")
}
func (UnimplementedMessageRecipientGroupServiceServer) DeleteMessageRecipientGroup(context.Context, *DeleteMessageRecipientGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMessageRecipientGroup not implemented")
}
func (UnimplementedMessageRecipientGroupServiceServer) FindEnabledMessageRecipientGroup(context.Context, *FindEnabledMessageRecipientGroupRequest) (*FindEnabledMessageRecipientGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledMessageRecipientGroup not implemented")
}

// UnsafeMessageRecipientGroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageRecipientGroupServiceServer will
// result in compilation errors.
type UnsafeMessageRecipientGroupServiceServer interface {
	mustEmbedUnimplementedMessageRecipientGroupServiceServer()
}

func RegisterMessageRecipientGroupServiceServer(s grpc.ServiceRegistrar, srv MessageRecipientGroupServiceServer) {
	s.RegisterService(&MessageRecipientGroupService_ServiceDesc, srv)
}

func _MessageRecipientGroupService_CreateMessageRecipientGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMessageRecipientGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientGroupServiceServer).CreateMessageRecipientGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientGroupService_CreateMessageRecipientGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientGroupServiceServer).CreateMessageRecipientGroup(ctx, req.(*CreateMessageRecipientGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientGroupService_UpdateMessageRecipientGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMessageRecipientGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientGroupServiceServer).UpdateMessageRecipientGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientGroupService_UpdateMessageRecipientGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientGroupServiceServer).UpdateMessageRecipientGroup(ctx, req.(*UpdateMessageRecipientGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientGroupService_FindAllEnabledMessageRecipientGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledMessageRecipientGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientGroupServiceServer).FindAllEnabledMessageRecipientGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientGroupService_FindAllEnabledMessageRecipientGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientGroupServiceServer).FindAllEnabledMessageRecipientGroups(ctx, req.(*FindAllEnabledMessageRecipientGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientGroupService_DeleteMessageRecipientGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMessageRecipientGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientGroupServiceServer).DeleteMessageRecipientGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientGroupService_DeleteMessageRecipientGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientGroupServiceServer).DeleteMessageRecipientGroup(ctx, req.(*DeleteMessageRecipientGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientGroupService_FindEnabledMessageRecipientGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledMessageRecipientGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientGroupServiceServer).FindEnabledMessageRecipientGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientGroupService_FindEnabledMessageRecipientGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientGroupServiceServer).FindEnabledMessageRecipientGroup(ctx, req.(*FindEnabledMessageRecipientGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageRecipientGroupService_ServiceDesc is the grpc.ServiceDesc for MessageRecipientGroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageRecipientGroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MessageRecipientGroupService",
	HandlerType: (*MessageRecipientGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createMessageRecipientGroup",
			Handler:    _MessageRecipientGroupService_CreateMessageRecipientGroup_Handler,
		},
		{
			MethodName: "updateMessageRecipientGroup",
			Handler:    _MessageRecipientGroupService_UpdateMessageRecipientGroup_Handler,
		},
		{
			MethodName: "findAllEnabledMessageRecipientGroups",
			Handler:    _MessageRecipientGroupService_FindAllEnabledMessageRecipientGroups_Handler,
		},
		{
			MethodName: "deleteMessageRecipientGroup",
			Handler:    _MessageRecipientGroupService_DeleteMessageRecipientGroup_Handler,
		},
		{
			MethodName: "findEnabledMessageRecipientGroup",
			Handler:    _MessageRecipientGroupService_FindEnabledMessageRecipientGroup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_message_recipient_group.proto",
}
