// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_route.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSRouteService_CreateNSRoute_FullMethodName                     = "/pb.NSRouteService/createNSRoute"
	NSRouteService_UpdateNSRoute_FullMethodName                     = "/pb.NSRouteService/updateNSRoute"
	NSRouteService_DeleteNSRoute_FullMethodName                     = "/pb.NSRouteService/deleteNSRoute"
	NSRouteService_FindNSRoute_FullMethodName                       = "/pb.NSRouteService/findNSRoute"
	NSRouteService_CountAllNSRoutes_FullMethodName                  = "/pb.NSRouteService/countAllNSRoutes"
	NSRouteService_FindAllNSRoutes_FullMethodName                   = "/pb.NSRouteService/findAllNSRoutes"
	NSRouteService_FindAllPublicNSRoutes_FullMethodName             = "/pb.NSRouteService/findAllPublicNSRoutes"
	NSRouteService_UpdateNSRouteOrders_FullMethodName               = "/pb.NSRouteService/updateNSRouteOrders"
	NSRouteService_ListNSRoutesAfterVersion_FullMethodName          = "/pb.NSRouteService/listNSRoutesAfterVersion"
	NSRouteService_FindAllDefaultWorldRegionRoutes_FullMethodName   = "/pb.NSRouteService/findAllDefaultWorldRegionRoutes"
	NSRouteService_FindAllDefaultChinaProvinceRoutes_FullMethodName = "/pb.NSRouteService/findAllDefaultChinaProvinceRoutes"
	NSRouteService_FindAllDefaultISPRoutes_FullMethodName           = "/pb.NSRouteService/findAllDefaultISPRoutes"
	NSRouteService_FindAllAgentNSRoutes_FullMethodName              = "/pb.NSRouteService/findAllAgentNSRoutes"
)

// NSRouteServiceClient is the client API for NSRouteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSRouteServiceClient interface {
	// 创建自定义线路
	CreateNSRoute(ctx context.Context, in *CreateNSRouteRequest, opts ...grpc.CallOption) (*CreateNSRouteResponse, error)
	// 修改自定义线路
	UpdateNSRoute(ctx context.Context, in *UpdateNSRouteRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除自定义线路
	DeleteNSRoute(ctx context.Context, in *DeleteNSRouteRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取单个自定义路线信息
	FindNSRoute(ctx context.Context, in *FindNSRouteRequest, opts ...grpc.CallOption) (*FindNSRouteResponse, error)
	// 查询自定义线路数量
	CountAllNSRoutes(ctx context.Context, in *CountAllNSRoutesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 读取所有自定义线路
	FindAllNSRoutes(ctx context.Context, in *FindAllNSRoutesRequest, opts ...grpc.CallOption) (*FindAllNSRoutesResponse, error)
	// 读取所有公用的自定义线路
	// 目前只允许读取系统管理员设置的公用自定义线路
	FindAllPublicNSRoutes(ctx context.Context, in *FindAllPublicRoutesRequest, opts ...grpc.CallOption) (*FindAllPublicRoutesResponse, error)
	// 设置自定义线路排序
	UpdateNSRouteOrders(ctx context.Context, in *UpdateNSRouteOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 根据版本列出一组自定义线路
	ListNSRoutesAfterVersion(ctx context.Context, in *ListNSRoutesAfterVersionRequest, opts ...grpc.CallOption) (*ListNSRoutesAfterVersionResponse, error)
	// 查找默认的世界区域线路
	FindAllDefaultWorldRegionRoutes(ctx context.Context, in *FindAllDefaultWorldRegionRoutesRequest, opts ...grpc.CallOption) (*FindAllDefaultWorldRegionRoutesResponse, error)
	// 查找默认的中国省份线路
	FindAllDefaultChinaProvinceRoutes(ctx context.Context, in *FindAllDefaultChinaProvinceRoutesRequest, opts ...grpc.CallOption) (*FindAllDefaultChinaProvinceRoutesResponse, error)
	// 查找默认的ISP线路
	FindAllDefaultISPRoutes(ctx context.Context, in *FindAllDefaultISPRoutesRequest, opts ...grpc.CallOption) (*FindAllDefaultISPRoutesResponse, error)
	// 查找默认的搜索引擎线路
	FindAllAgentNSRoutes(ctx context.Context, in *FindAllAgentNSRoutesRequest, opts ...grpc.CallOption) (*FindAllAgentNSRoutesResponse, error)
}

type nSRouteServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSRouteServiceClient(cc grpc.ClientConnInterface) NSRouteServiceClient {
	return &nSRouteServiceClient{cc}
}

func (c *nSRouteServiceClient) CreateNSRoute(ctx context.Context, in *CreateNSRouteRequest, opts ...grpc.CallOption) (*CreateNSRouteResponse, error) {
	out := new(CreateNSRouteResponse)
	err := c.cc.Invoke(ctx, NSRouteService_CreateNSRoute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) UpdateNSRoute(ctx context.Context, in *UpdateNSRouteRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRouteService_UpdateNSRoute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) DeleteNSRoute(ctx context.Context, in *DeleteNSRouteRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRouteService_DeleteNSRoute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindNSRoute(ctx context.Context, in *FindNSRouteRequest, opts ...grpc.CallOption) (*FindNSRouteResponse, error) {
	out := new(FindNSRouteResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindNSRoute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) CountAllNSRoutes(ctx context.Context, in *CountAllNSRoutesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSRouteService_CountAllNSRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindAllNSRoutes(ctx context.Context, in *FindAllNSRoutesRequest, opts ...grpc.CallOption) (*FindAllNSRoutesResponse, error) {
	out := new(FindAllNSRoutesResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindAllNSRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindAllPublicNSRoutes(ctx context.Context, in *FindAllPublicRoutesRequest, opts ...grpc.CallOption) (*FindAllPublicRoutesResponse, error) {
	out := new(FindAllPublicRoutesResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindAllPublicNSRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) UpdateNSRouteOrders(ctx context.Context, in *UpdateNSRouteOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRouteService_UpdateNSRouteOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) ListNSRoutesAfterVersion(ctx context.Context, in *ListNSRoutesAfterVersionRequest, opts ...grpc.CallOption) (*ListNSRoutesAfterVersionResponse, error) {
	out := new(ListNSRoutesAfterVersionResponse)
	err := c.cc.Invoke(ctx, NSRouteService_ListNSRoutesAfterVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindAllDefaultWorldRegionRoutes(ctx context.Context, in *FindAllDefaultWorldRegionRoutesRequest, opts ...grpc.CallOption) (*FindAllDefaultWorldRegionRoutesResponse, error) {
	out := new(FindAllDefaultWorldRegionRoutesResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindAllDefaultWorldRegionRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindAllDefaultChinaProvinceRoutes(ctx context.Context, in *FindAllDefaultChinaProvinceRoutesRequest, opts ...grpc.CallOption) (*FindAllDefaultChinaProvinceRoutesResponse, error) {
	out := new(FindAllDefaultChinaProvinceRoutesResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindAllDefaultChinaProvinceRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindAllDefaultISPRoutes(ctx context.Context, in *FindAllDefaultISPRoutesRequest, opts ...grpc.CallOption) (*FindAllDefaultISPRoutesResponse, error) {
	out := new(FindAllDefaultISPRoutesResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindAllDefaultISPRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteServiceClient) FindAllAgentNSRoutes(ctx context.Context, in *FindAllAgentNSRoutesRequest, opts ...grpc.CallOption) (*FindAllAgentNSRoutesResponse, error) {
	out := new(FindAllAgentNSRoutesResponse)
	err := c.cc.Invoke(ctx, NSRouteService_FindAllAgentNSRoutes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSRouteServiceServer is the server API for NSRouteService service.
// All implementations should embed UnimplementedNSRouteServiceServer
// for forward compatibility
type NSRouteServiceServer interface {
	// 创建自定义线路
	CreateNSRoute(context.Context, *CreateNSRouteRequest) (*CreateNSRouteResponse, error)
	// 修改自定义线路
	UpdateNSRoute(context.Context, *UpdateNSRouteRequest) (*RPCSuccess, error)
	// 删除自定义线路
	DeleteNSRoute(context.Context, *DeleteNSRouteRequest) (*RPCSuccess, error)
	// 获取单个自定义路线信息
	FindNSRoute(context.Context, *FindNSRouteRequest) (*FindNSRouteResponse, error)
	// 查询自定义线路数量
	CountAllNSRoutes(context.Context, *CountAllNSRoutesRequest) (*RPCCountResponse, error)
	// 读取所有自定义线路
	FindAllNSRoutes(context.Context, *FindAllNSRoutesRequest) (*FindAllNSRoutesResponse, error)
	// 读取所有公用的自定义线路
	// 目前只允许读取系统管理员设置的公用自定义线路
	FindAllPublicNSRoutes(context.Context, *FindAllPublicRoutesRequest) (*FindAllPublicRoutesResponse, error)
	// 设置自定义线路排序
	UpdateNSRouteOrders(context.Context, *UpdateNSRouteOrdersRequest) (*RPCSuccess, error)
	// 根据版本列出一组自定义线路
	ListNSRoutesAfterVersion(context.Context, *ListNSRoutesAfterVersionRequest) (*ListNSRoutesAfterVersionResponse, error)
	// 查找默认的世界区域线路
	FindAllDefaultWorldRegionRoutes(context.Context, *FindAllDefaultWorldRegionRoutesRequest) (*FindAllDefaultWorldRegionRoutesResponse, error)
	// 查找默认的中国省份线路
	FindAllDefaultChinaProvinceRoutes(context.Context, *FindAllDefaultChinaProvinceRoutesRequest) (*FindAllDefaultChinaProvinceRoutesResponse, error)
	// 查找默认的ISP线路
	FindAllDefaultISPRoutes(context.Context, *FindAllDefaultISPRoutesRequest) (*FindAllDefaultISPRoutesResponse, error)
	// 查找默认的搜索引擎线路
	FindAllAgentNSRoutes(context.Context, *FindAllAgentNSRoutesRequest) (*FindAllAgentNSRoutesResponse, error)
}

// UnimplementedNSRouteServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSRouteServiceServer struct {
}

func (UnimplementedNSRouteServiceServer) CreateNSRoute(context.Context, *CreateNSRouteRequest) (*CreateNSRouteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSRoute not implemented")
}
func (UnimplementedNSRouteServiceServer) UpdateNSRoute(context.Context, *UpdateNSRouteRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRoute not implemented")
}
func (UnimplementedNSRouteServiceServer) DeleteNSRoute(context.Context, *DeleteNSRouteRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSRoute not implemented")
}
func (UnimplementedNSRouteServiceServer) FindNSRoute(context.Context, *FindNSRouteRequest) (*FindNSRouteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSRoute not implemented")
}
func (UnimplementedNSRouteServiceServer) CountAllNSRoutes(context.Context, *CountAllNSRoutesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSRoutes not implemented")
}
func (UnimplementedNSRouteServiceServer) FindAllNSRoutes(context.Context, *FindAllNSRoutesRequest) (*FindAllNSRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNSRoutes not implemented")
}
func (UnimplementedNSRouteServiceServer) FindAllPublicNSRoutes(context.Context, *FindAllPublicRoutesRequest) (*FindAllPublicRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllPublicNSRoutes not implemented")
}
func (UnimplementedNSRouteServiceServer) UpdateNSRouteOrders(context.Context, *UpdateNSRouteOrdersRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRouteOrders not implemented")
}
func (UnimplementedNSRouteServiceServer) ListNSRoutesAfterVersion(context.Context, *ListNSRoutesAfterVersionRequest) (*ListNSRoutesAfterVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSRoutesAfterVersion not implemented")
}
func (UnimplementedNSRouteServiceServer) FindAllDefaultWorldRegionRoutes(context.Context, *FindAllDefaultWorldRegionRoutesRequest) (*FindAllDefaultWorldRegionRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllDefaultWorldRegionRoutes not implemented")
}
func (UnimplementedNSRouteServiceServer) FindAllDefaultChinaProvinceRoutes(context.Context, *FindAllDefaultChinaProvinceRoutesRequest) (*FindAllDefaultChinaProvinceRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllDefaultChinaProvinceRoutes not implemented")
}
func (UnimplementedNSRouteServiceServer) FindAllDefaultISPRoutes(context.Context, *FindAllDefaultISPRoutesRequest) (*FindAllDefaultISPRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllDefaultISPRoutes not implemented")
}
func (UnimplementedNSRouteServiceServer) FindAllAgentNSRoutes(context.Context, *FindAllAgentNSRoutesRequest) (*FindAllAgentNSRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAgentNSRoutes not implemented")
}

// UnsafeNSRouteServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSRouteServiceServer will
// result in compilation errors.
type UnsafeNSRouteServiceServer interface {
	mustEmbedUnimplementedNSRouteServiceServer()
}

func RegisterNSRouteServiceServer(s grpc.ServiceRegistrar, srv NSRouteServiceServer) {
	s.RegisterService(&NSRouteService_ServiceDesc, srv)
}

func _NSRouteService_CreateNSRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSRouteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).CreateNSRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_CreateNSRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).CreateNSRoute(ctx, req.(*CreateNSRouteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_UpdateNSRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRouteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).UpdateNSRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_UpdateNSRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).UpdateNSRoute(ctx, req.(*UpdateNSRouteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_DeleteNSRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSRouteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).DeleteNSRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_DeleteNSRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).DeleteNSRoute(ctx, req.(*DeleteNSRouteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindNSRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSRouteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindNSRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindNSRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindNSRoute(ctx, req.(*FindNSRouteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_CountAllNSRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).CountAllNSRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_CountAllNSRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).CountAllNSRoutes(ctx, req.(*CountAllNSRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindAllNSRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNSRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindAllNSRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindAllNSRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindAllNSRoutes(ctx, req.(*FindAllNSRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindAllPublicNSRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllPublicRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindAllPublicNSRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindAllPublicNSRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindAllPublicNSRoutes(ctx, req.(*FindAllPublicRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_UpdateNSRouteOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRouteOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).UpdateNSRouteOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_UpdateNSRouteOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).UpdateNSRouteOrders(ctx, req.(*UpdateNSRouteOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_ListNSRoutesAfterVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSRoutesAfterVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).ListNSRoutesAfterVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_ListNSRoutesAfterVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).ListNSRoutesAfterVersion(ctx, req.(*ListNSRoutesAfterVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindAllDefaultWorldRegionRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllDefaultWorldRegionRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindAllDefaultWorldRegionRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindAllDefaultWorldRegionRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindAllDefaultWorldRegionRoutes(ctx, req.(*FindAllDefaultWorldRegionRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindAllDefaultChinaProvinceRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllDefaultChinaProvinceRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindAllDefaultChinaProvinceRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindAllDefaultChinaProvinceRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindAllDefaultChinaProvinceRoutes(ctx, req.(*FindAllDefaultChinaProvinceRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindAllDefaultISPRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllDefaultISPRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindAllDefaultISPRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindAllDefaultISPRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindAllDefaultISPRoutes(ctx, req.(*FindAllDefaultISPRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteService_FindAllAgentNSRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAgentNSRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteServiceServer).FindAllAgentNSRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteService_FindAllAgentNSRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteServiceServer).FindAllAgentNSRoutes(ctx, req.(*FindAllAgentNSRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSRouteService_ServiceDesc is the grpc.ServiceDesc for NSRouteService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSRouteService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSRouteService",
	HandlerType: (*NSRouteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSRoute",
			Handler:    _NSRouteService_CreateNSRoute_Handler,
		},
		{
			MethodName: "updateNSRoute",
			Handler:    _NSRouteService_UpdateNSRoute_Handler,
		},
		{
			MethodName: "deleteNSRoute",
			Handler:    _NSRouteService_DeleteNSRoute_Handler,
		},
		{
			MethodName: "findNSRoute",
			Handler:    _NSRouteService_FindNSRoute_Handler,
		},
		{
			MethodName: "countAllNSRoutes",
			Handler:    _NSRouteService_CountAllNSRoutes_Handler,
		},
		{
			MethodName: "findAllNSRoutes",
			Handler:    _NSRouteService_FindAllNSRoutes_Handler,
		},
		{
			MethodName: "findAllPublicNSRoutes",
			Handler:    _NSRouteService_FindAllPublicNSRoutes_Handler,
		},
		{
			MethodName: "updateNSRouteOrders",
			Handler:    _NSRouteService_UpdateNSRouteOrders_Handler,
		},
		{
			MethodName: "listNSRoutesAfterVersion",
			Handler:    _NSRouteService_ListNSRoutesAfterVersion_Handler,
		},
		{
			MethodName: "findAllDefaultWorldRegionRoutes",
			Handler:    _NSRouteService_FindAllDefaultWorldRegionRoutes_Handler,
		},
		{
			MethodName: "findAllDefaultChinaProvinceRoutes",
			Handler:    _NSRouteService_FindAllDefaultChinaProvinceRoutes_Handler,
		},
		{
			MethodName: "findAllDefaultISPRoutes",
			Handler:    _NSRouteService_FindAllDefaultISPRoutes_Handler,
		},
		{
			MethodName: "findAllAgentNSRoutes",
			Handler:    _NSRouteService_FindAllAgentNSRoutes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_route.proto",
}
