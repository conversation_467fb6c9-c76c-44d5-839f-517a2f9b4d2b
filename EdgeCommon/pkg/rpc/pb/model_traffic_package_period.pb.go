// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_traffic_package_period.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 流量包有效期
type TrafficPackagePeriod struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOn          bool                   `protobuf:"varint,2,opt,name=isOn,proto3" json:"isOn,omitempty"`
	Count         int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Unit          string                 `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	Months        int32                  `protobuf:"varint,5,opt,name=months,proto3" json:"months,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrafficPackagePeriod) Reset() {
	*x = TrafficPackagePeriod{}
	mi := &file_models_model_traffic_package_period_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficPackagePeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficPackagePeriod) ProtoMessage() {}

func (x *TrafficPackagePeriod) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_traffic_package_period_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficPackagePeriod.ProtoReflect.Descriptor instead.
func (*TrafficPackagePeriod) Descriptor() ([]byte, []int) {
	return file_models_model_traffic_package_period_proto_rawDescGZIP(), []int{0}
}

func (x *TrafficPackagePeriod) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrafficPackagePeriod) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *TrafficPackagePeriod) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TrafficPackagePeriod) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *TrafficPackagePeriod) GetMonths() int32 {
	if x != nil {
		return x.Months
	}
	return 0
}

var File_models_model_traffic_package_period_proto protoreflect.FileDescriptor

const file_models_model_traffic_package_period_proto_rawDesc = "" +
	"\n" +
	")models/model_traffic_package_period.proto\x12\x02pb\"|\n" +
	"\x14TrafficPackagePeriod\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04isOn\x18\x02 \x01(\bR\x04isOn\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\x12\x12\n" +
	"\x04unit\x18\x04 \x01(\tR\x04unit\x12\x16\n" +
	"\x06months\x18\x05 \x01(\x05R\x06monthsB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_traffic_package_period_proto_rawDescOnce sync.Once
	file_models_model_traffic_package_period_proto_rawDescData []byte
)

func file_models_model_traffic_package_period_proto_rawDescGZIP() []byte {
	file_models_model_traffic_package_period_proto_rawDescOnce.Do(func() {
		file_models_model_traffic_package_period_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_traffic_package_period_proto_rawDesc), len(file_models_model_traffic_package_period_proto_rawDesc)))
	})
	return file_models_model_traffic_package_period_proto_rawDescData
}

var file_models_model_traffic_package_period_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_traffic_package_period_proto_goTypes = []any{
	(*TrafficPackagePeriod)(nil), // 0: pb.TrafficPackagePeriod
}
var file_models_model_traffic_package_period_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_traffic_package_period_proto_init() }
func file_models_model_traffic_package_period_proto_init() {
	if File_models_model_traffic_package_period_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_traffic_package_period_proto_rawDesc), len(file_models_model_traffic_package_period_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_traffic_package_period_proto_goTypes,
		DependencyIndexes: file_models_model_traffic_package_period_proto_depIdxs,
		MessageInfos:      file_models_model_traffic_package_period_proto_msgTypes,
	}.Build()
	File_models_model_traffic_package_period_proto = out.File
	file_models_model_traffic_package_period_proto_goTypes = nil
	file_models_model_traffic_package_period_proto_depIdxs = nil
}
