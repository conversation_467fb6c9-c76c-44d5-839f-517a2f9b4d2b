// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_traffic_package_price.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TrafficPackagePriceService_UpdateTrafficPackagePrice_FullMethodName   = "/pb.TrafficPackagePriceService/updateTrafficPackagePrice"
	TrafficPackagePriceService_FindTrafficPackagePrice_FullMethodName     = "/pb.TrafficPackagePriceService/findTrafficPackagePrice"
	TrafficPackagePriceService_CountTrafficPackagePrices_FullMethodName   = "/pb.TrafficPackagePriceService/countTrafficPackagePrices"
	TrafficPackagePriceService_FindTrafficPackagePrices_FullMethodName    = "/pb.TrafficPackagePriceService/findTrafficPackagePrices"
	TrafficPackagePriceService_FindAllTrafficPackagePrices_FullMethodName = "/pb.TrafficPackagePriceService/findAllTrafficPackagePrices"
)

// TrafficPackagePriceServiceClient is the client API for TrafficPackagePriceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TrafficPackagePriceServiceClient interface {
	// 设置流量包价格
	UpdateTrafficPackagePrice(ctx context.Context, in *UpdateTrafficPackagePriceRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取单个流量包具体价格
	FindTrafficPackagePrice(ctx context.Context, in *FindTrafficPackagePriceRequest, opts ...grpc.CallOption) (*FindTrafficPackagePriceResponse, error)
	// 计算流量包价格项数量
	CountTrafficPackagePrices(ctx context.Context, in *CountTrafficPackagePricesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找流量包价格
	FindTrafficPackagePrices(ctx context.Context, in *FindTrafficPackagePricesRequest, opts ...grpc.CallOption) (*FindTrafficPackagePricesResponse, error)
	// 查找所有流量包价格
	FindAllTrafficPackagePrices(ctx context.Context, in *FindAllTrafficPackagePricesRequest, opts ...grpc.CallOption) (*FindAllTrafficPackagePricesResponse, error)
}

type trafficPackagePriceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTrafficPackagePriceServiceClient(cc grpc.ClientConnInterface) TrafficPackagePriceServiceClient {
	return &trafficPackagePriceServiceClient{cc}
}

func (c *trafficPackagePriceServiceClient) UpdateTrafficPackagePrice(ctx context.Context, in *UpdateTrafficPackagePriceRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, TrafficPackagePriceService_UpdateTrafficPackagePrice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePriceServiceClient) FindTrafficPackagePrice(ctx context.Context, in *FindTrafficPackagePriceRequest, opts ...grpc.CallOption) (*FindTrafficPackagePriceResponse, error) {
	out := new(FindTrafficPackagePriceResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePriceService_FindTrafficPackagePrice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePriceServiceClient) CountTrafficPackagePrices(ctx context.Context, in *CountTrafficPackagePricesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePriceService_CountTrafficPackagePrices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePriceServiceClient) FindTrafficPackagePrices(ctx context.Context, in *FindTrafficPackagePricesRequest, opts ...grpc.CallOption) (*FindTrafficPackagePricesResponse, error) {
	out := new(FindTrafficPackagePricesResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePriceService_FindTrafficPackagePrices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePriceServiceClient) FindAllTrafficPackagePrices(ctx context.Context, in *FindAllTrafficPackagePricesRequest, opts ...grpc.CallOption) (*FindAllTrafficPackagePricesResponse, error) {
	out := new(FindAllTrafficPackagePricesResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePriceService_FindAllTrafficPackagePrices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TrafficPackagePriceServiceServer is the server API for TrafficPackagePriceService service.
// All implementations should embed UnimplementedTrafficPackagePriceServiceServer
// for forward compatibility
type TrafficPackagePriceServiceServer interface {
	// 设置流量包价格
	UpdateTrafficPackagePrice(context.Context, *UpdateTrafficPackagePriceRequest) (*RPCSuccess, error)
	// 获取单个流量包具体价格
	FindTrafficPackagePrice(context.Context, *FindTrafficPackagePriceRequest) (*FindTrafficPackagePriceResponse, error)
	// 计算流量包价格项数量
	CountTrafficPackagePrices(context.Context, *CountTrafficPackagePricesRequest) (*RPCCountResponse, error)
	// 查找流量包价格
	FindTrafficPackagePrices(context.Context, *FindTrafficPackagePricesRequest) (*FindTrafficPackagePricesResponse, error)
	// 查找所有流量包价格
	FindAllTrafficPackagePrices(context.Context, *FindAllTrafficPackagePricesRequest) (*FindAllTrafficPackagePricesResponse, error)
}

// UnimplementedTrafficPackagePriceServiceServer should be embedded to have forward compatible implementations.
type UnimplementedTrafficPackagePriceServiceServer struct {
}

func (UnimplementedTrafficPackagePriceServiceServer) UpdateTrafficPackagePrice(context.Context, *UpdateTrafficPackagePriceRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTrafficPackagePrice not implemented")
}
func (UnimplementedTrafficPackagePriceServiceServer) FindTrafficPackagePrice(context.Context, *FindTrafficPackagePriceRequest) (*FindTrafficPackagePriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTrafficPackagePrice not implemented")
}
func (UnimplementedTrafficPackagePriceServiceServer) CountTrafficPackagePrices(context.Context, *CountTrafficPackagePricesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountTrafficPackagePrices not implemented")
}
func (UnimplementedTrafficPackagePriceServiceServer) FindTrafficPackagePrices(context.Context, *FindTrafficPackagePricesRequest) (*FindTrafficPackagePricesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTrafficPackagePrices not implemented")
}
func (UnimplementedTrafficPackagePriceServiceServer) FindAllTrafficPackagePrices(context.Context, *FindAllTrafficPackagePricesRequest) (*FindAllTrafficPackagePricesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllTrafficPackagePrices not implemented")
}

// UnsafeTrafficPackagePriceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TrafficPackagePriceServiceServer will
// result in compilation errors.
type UnsafeTrafficPackagePriceServiceServer interface {
	mustEmbedUnimplementedTrafficPackagePriceServiceServer()
}

func RegisterTrafficPackagePriceServiceServer(s grpc.ServiceRegistrar, srv TrafficPackagePriceServiceServer) {
	s.RegisterService(&TrafficPackagePriceService_ServiceDesc, srv)
}

func _TrafficPackagePriceService_UpdateTrafficPackagePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTrafficPackagePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePriceServiceServer).UpdateTrafficPackagePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePriceService_UpdateTrafficPackagePrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePriceServiceServer).UpdateTrafficPackagePrice(ctx, req.(*UpdateTrafficPackagePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePriceService_FindTrafficPackagePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTrafficPackagePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePriceServiceServer).FindTrafficPackagePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePriceService_FindTrafficPackagePrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePriceServiceServer).FindTrafficPackagePrice(ctx, req.(*FindTrafficPackagePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePriceService_CountTrafficPackagePrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountTrafficPackagePricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePriceServiceServer).CountTrafficPackagePrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePriceService_CountTrafficPackagePrices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePriceServiceServer).CountTrafficPackagePrices(ctx, req.(*CountTrafficPackagePricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePriceService_FindTrafficPackagePrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTrafficPackagePricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePriceServiceServer).FindTrafficPackagePrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePriceService_FindTrafficPackagePrices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePriceServiceServer).FindTrafficPackagePrices(ctx, req.(*FindTrafficPackagePricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePriceService_FindAllTrafficPackagePrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllTrafficPackagePricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePriceServiceServer).FindAllTrafficPackagePrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePriceService_FindAllTrafficPackagePrices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePriceServiceServer).FindAllTrafficPackagePrices(ctx, req.(*FindAllTrafficPackagePricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TrafficPackagePriceService_ServiceDesc is the grpc.ServiceDesc for TrafficPackagePriceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TrafficPackagePriceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TrafficPackagePriceService",
	HandlerType: (*TrafficPackagePriceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "updateTrafficPackagePrice",
			Handler:    _TrafficPackagePriceService_UpdateTrafficPackagePrice_Handler,
		},
		{
			MethodName: "findTrafficPackagePrice",
			Handler:    _TrafficPackagePriceService_FindTrafficPackagePrice_Handler,
		},
		{
			MethodName: "countTrafficPackagePrices",
			Handler:    _TrafficPackagePriceService_CountTrafficPackagePrices_Handler,
		},
		{
			MethodName: "findTrafficPackagePrices",
			Handler:    _TrafficPackagePriceService_FindTrafficPackagePrices_Handler,
		},
		{
			MethodName: "findAllTrafficPackagePrices",
			Handler:    _TrafficPackagePriceService_FindAllTrafficPackagePrices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_traffic_package_price.proto",
}
