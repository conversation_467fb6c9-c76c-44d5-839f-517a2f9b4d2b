// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_script.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserScriptService_FindUserScript_FullMethodName        = "/pb.UserScriptService/findUserScript"
	UserScriptService_FindUserScriptWithMD5_FullMethodName = "/pb.UserScriptService/findUserScriptWithMD5"
	UserScriptService_CountUserScripts_FullMethodName      = "/pb.UserScriptService/countUserScripts"
	UserScriptService_ListUserScripts_FullMethodName       = "/pb.UserScriptService/listUserScripts"
	UserScriptService_PassUserScript_FullMethodName        = "/pb.UserScriptService/passUserScript"
	UserScriptService_RejectUserScript_FullMethodName      = "/pb.UserScriptService/rejectUserScript"
)

// UserScriptServiceClient is the client API for UserScriptService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserScriptServiceClient interface {
	// 查找单个用户脚本信息
	FindUserScript(ctx context.Context, in *FindUserScriptRequest, opts ...grpc.CallOption) (*FindUserScriptResponse, error)
	// 根据代码MD5查找脚本
	FindUserScriptWithMD5(ctx context.Context, in *FindUserScriptWithMD5Request, opts ...grpc.CallOption) (*FindUserScriptWithMD5Response, error)
	// 计算用户脚本数量
	CountUserScripts(ctx context.Context, in *CountUserScriptsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页用户脚本
	ListUserScripts(ctx context.Context, in *ListUserScriptsRequest, opts ...grpc.CallOption) (*ListUserScriptsResponse, error)
	// 审核并通过用户脚本
	PassUserScript(ctx context.Context, in *PassUserScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 审核并驳回用户脚本
	RejectUserScript(ctx context.Context, in *RejectUserScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type userScriptServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserScriptServiceClient(cc grpc.ClientConnInterface) UserScriptServiceClient {
	return &userScriptServiceClient{cc}
}

func (c *userScriptServiceClient) FindUserScript(ctx context.Context, in *FindUserScriptRequest, opts ...grpc.CallOption) (*FindUserScriptResponse, error) {
	out := new(FindUserScriptResponse)
	err := c.cc.Invoke(ctx, UserScriptService_FindUserScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScriptServiceClient) FindUserScriptWithMD5(ctx context.Context, in *FindUserScriptWithMD5Request, opts ...grpc.CallOption) (*FindUserScriptWithMD5Response, error) {
	out := new(FindUserScriptWithMD5Response)
	err := c.cc.Invoke(ctx, UserScriptService_FindUserScriptWithMD5_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScriptServiceClient) CountUserScripts(ctx context.Context, in *CountUserScriptsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserScriptService_CountUserScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScriptServiceClient) ListUserScripts(ctx context.Context, in *ListUserScriptsRequest, opts ...grpc.CallOption) (*ListUserScriptsResponse, error) {
	out := new(ListUserScriptsResponse)
	err := c.cc.Invoke(ctx, UserScriptService_ListUserScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScriptServiceClient) PassUserScript(ctx context.Context, in *PassUserScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserScriptService_PassUserScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScriptServiceClient) RejectUserScript(ctx context.Context, in *RejectUserScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserScriptService_RejectUserScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserScriptServiceServer is the server API for UserScriptService service.
// All implementations should embed UnimplementedUserScriptServiceServer
// for forward compatibility
type UserScriptServiceServer interface {
	// 查找单个用户脚本信息
	FindUserScript(context.Context, *FindUserScriptRequest) (*FindUserScriptResponse, error)
	// 根据代码MD5查找脚本
	FindUserScriptWithMD5(context.Context, *FindUserScriptWithMD5Request) (*FindUserScriptWithMD5Response, error)
	// 计算用户脚本数量
	CountUserScripts(context.Context, *CountUserScriptsRequest) (*RPCCountResponse, error)
	// 列出单页用户脚本
	ListUserScripts(context.Context, *ListUserScriptsRequest) (*ListUserScriptsResponse, error)
	// 审核并通过用户脚本
	PassUserScript(context.Context, *PassUserScriptRequest) (*RPCSuccess, error)
	// 审核并驳回用户脚本
	RejectUserScript(context.Context, *RejectUserScriptRequest) (*RPCSuccess, error)
}

// UnimplementedUserScriptServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserScriptServiceServer struct {
}

func (UnimplementedUserScriptServiceServer) FindUserScript(context.Context, *FindUserScriptRequest) (*FindUserScriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserScript not implemented")
}
func (UnimplementedUserScriptServiceServer) FindUserScriptWithMD5(context.Context, *FindUserScriptWithMD5Request) (*FindUserScriptWithMD5Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserScriptWithMD5 not implemented")
}
func (UnimplementedUserScriptServiceServer) CountUserScripts(context.Context, *CountUserScriptsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserScripts not implemented")
}
func (UnimplementedUserScriptServiceServer) ListUserScripts(context.Context, *ListUserScriptsRequest) (*ListUserScriptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserScripts not implemented")
}
func (UnimplementedUserScriptServiceServer) PassUserScript(context.Context, *PassUserScriptRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PassUserScript not implemented")
}
func (UnimplementedUserScriptServiceServer) RejectUserScript(context.Context, *RejectUserScriptRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectUserScript not implemented")
}

// UnsafeUserScriptServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserScriptServiceServer will
// result in compilation errors.
type UnsafeUserScriptServiceServer interface {
	mustEmbedUnimplementedUserScriptServiceServer()
}

func RegisterUserScriptServiceServer(s grpc.ServiceRegistrar, srv UserScriptServiceServer) {
	s.RegisterService(&UserScriptService_ServiceDesc, srv)
}

func _UserScriptService_FindUserScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScriptServiceServer).FindUserScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserScriptService_FindUserScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScriptServiceServer).FindUserScript(ctx, req.(*FindUserScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScriptService_FindUserScriptWithMD5_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserScriptWithMD5Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScriptServiceServer).FindUserScriptWithMD5(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserScriptService_FindUserScriptWithMD5_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScriptServiceServer).FindUserScriptWithMD5(ctx, req.(*FindUserScriptWithMD5Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScriptService_CountUserScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScriptServiceServer).CountUserScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserScriptService_CountUserScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScriptServiceServer).CountUserScripts(ctx, req.(*CountUserScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScriptService_ListUserScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScriptServiceServer).ListUserScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserScriptService_ListUserScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScriptServiceServer).ListUserScripts(ctx, req.(*ListUserScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScriptService_PassUserScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PassUserScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScriptServiceServer).PassUserScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserScriptService_PassUserScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScriptServiceServer).PassUserScript(ctx, req.(*PassUserScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScriptService_RejectUserScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectUserScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScriptServiceServer).RejectUserScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserScriptService_RejectUserScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScriptServiceServer).RejectUserScript(ctx, req.(*RejectUserScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserScriptService_ServiceDesc is the grpc.ServiceDesc for UserScriptService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserScriptService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserScriptService",
	HandlerType: (*UserScriptServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findUserScript",
			Handler:    _UserScriptService_FindUserScript_Handler,
		},
		{
			MethodName: "findUserScriptWithMD5",
			Handler:    _UserScriptService_FindUserScriptWithMD5_Handler,
		},
		{
			MethodName: "countUserScripts",
			Handler:    _UserScriptService_CountUserScripts_Handler,
		},
		{
			MethodName: "listUserScripts",
			Handler:    _UserScriptService_ListUserScripts_Handler,
		},
		{
			MethodName: "passUserScript",
			Handler:    _UserScriptService_PassUserScript_Handler,
		},
		{
			MethodName: "rejectUserScript",
			Handler:    _UserScriptService_RejectUserScript_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_script.proto",
}
