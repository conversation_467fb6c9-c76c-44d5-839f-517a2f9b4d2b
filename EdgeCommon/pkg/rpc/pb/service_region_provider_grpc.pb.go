// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_region_provider.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RegionProviderService_FindAllEnabledRegionProviders_FullMethodName = "/pb.RegionProviderService/findAllEnabledRegionProviders"
	RegionProviderService_FindEnabledRegionProvider_FullMethodName     = "/pb.RegionProviderService/findEnabledRegionProvider"
	RegionProviderService_FindAllRegionProviders_FullMethodName        = "/pb.RegionProviderService/findAllRegionProviders"
	RegionProviderService_FindRegionProvider_FullMethodName            = "/pb.RegionProviderService/findRegionProvider"
	RegionProviderService_UpdateRegionProviderCustom_FullMethodName    = "/pb.RegionProviderService/updateRegionProviderCustom"
)

// RegionProviderServiceClient is the client API for RegionProviderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RegionProviderServiceClient interface {
	// Deprecated: Do not use.
	// 查找所有ISP
	FindAllEnabledRegionProviders(ctx context.Context, in *FindAllEnabledRegionProvidersRequest, opts ...grpc.CallOption) (*FindAllEnabledRegionProvidersResponse, error)
	// Deprecated: Do not use.
	// 查找单个ISP信息
	FindEnabledRegionProvider(ctx context.Context, in *FindEnabledRegionProviderRequest, opts ...grpc.CallOption) (*FindEnabledRegionProviderResponse, error)
	// 查找所有ISP
	FindAllRegionProviders(ctx context.Context, in *FindAllRegionProvidersRequest, opts ...grpc.CallOption) (*FindAllRegionProvidersResponse, error)
	// 查找单个ISP信息
	FindRegionProvider(ctx context.Context, in *FindRegionProviderRequest, opts ...grpc.CallOption) (*FindRegionProviderResponse, error)
	// 修改ISP定制信息
	UpdateRegionProviderCustom(ctx context.Context, in *UpdateRegionProviderCustomRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type regionProviderServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRegionProviderServiceClient(cc grpc.ClientConnInterface) RegionProviderServiceClient {
	return &regionProviderServiceClient{cc}
}

// Deprecated: Do not use.
func (c *regionProviderServiceClient) FindAllEnabledRegionProviders(ctx context.Context, in *FindAllEnabledRegionProvidersRequest, opts ...grpc.CallOption) (*FindAllEnabledRegionProvidersResponse, error) {
	out := new(FindAllEnabledRegionProvidersResponse)
	err := c.cc.Invoke(ctx, RegionProviderService_FindAllEnabledRegionProviders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *regionProviderServiceClient) FindEnabledRegionProvider(ctx context.Context, in *FindEnabledRegionProviderRequest, opts ...grpc.CallOption) (*FindEnabledRegionProviderResponse, error) {
	out := new(FindEnabledRegionProviderResponse)
	err := c.cc.Invoke(ctx, RegionProviderService_FindEnabledRegionProvider_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionProviderServiceClient) FindAllRegionProviders(ctx context.Context, in *FindAllRegionProvidersRequest, opts ...grpc.CallOption) (*FindAllRegionProvidersResponse, error) {
	out := new(FindAllRegionProvidersResponse)
	err := c.cc.Invoke(ctx, RegionProviderService_FindAllRegionProviders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionProviderServiceClient) FindRegionProvider(ctx context.Context, in *FindRegionProviderRequest, opts ...grpc.CallOption) (*FindRegionProviderResponse, error) {
	out := new(FindRegionProviderResponse)
	err := c.cc.Invoke(ctx, RegionProviderService_FindRegionProvider_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionProviderServiceClient) UpdateRegionProviderCustom(ctx context.Context, in *UpdateRegionProviderCustomRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, RegionProviderService_UpdateRegionProviderCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RegionProviderServiceServer is the server API for RegionProviderService service.
// All implementations should embed UnimplementedRegionProviderServiceServer
// for forward compatibility
type RegionProviderServiceServer interface {
	// Deprecated: Do not use.
	// 查找所有ISP
	FindAllEnabledRegionProviders(context.Context, *FindAllEnabledRegionProvidersRequest) (*FindAllEnabledRegionProvidersResponse, error)
	// Deprecated: Do not use.
	// 查找单个ISP信息
	FindEnabledRegionProvider(context.Context, *FindEnabledRegionProviderRequest) (*FindEnabledRegionProviderResponse, error)
	// 查找所有ISP
	FindAllRegionProviders(context.Context, *FindAllRegionProvidersRequest) (*FindAllRegionProvidersResponse, error)
	// 查找单个ISP信息
	FindRegionProvider(context.Context, *FindRegionProviderRequest) (*FindRegionProviderResponse, error)
	// 修改ISP定制信息
	UpdateRegionProviderCustom(context.Context, *UpdateRegionProviderCustomRequest) (*RPCSuccess, error)
}

// UnimplementedRegionProviderServiceServer should be embedded to have forward compatible implementations.
type UnimplementedRegionProviderServiceServer struct {
}

func (UnimplementedRegionProviderServiceServer) FindAllEnabledRegionProviders(context.Context, *FindAllEnabledRegionProvidersRequest) (*FindAllEnabledRegionProvidersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledRegionProviders not implemented")
}
func (UnimplementedRegionProviderServiceServer) FindEnabledRegionProvider(context.Context, *FindEnabledRegionProviderRequest) (*FindEnabledRegionProviderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledRegionProvider not implemented")
}
func (UnimplementedRegionProviderServiceServer) FindAllRegionProviders(context.Context, *FindAllRegionProvidersRequest) (*FindAllRegionProvidersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllRegionProviders not implemented")
}
func (UnimplementedRegionProviderServiceServer) FindRegionProvider(context.Context, *FindRegionProviderRequest) (*FindRegionProviderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindRegionProvider not implemented")
}
func (UnimplementedRegionProviderServiceServer) UpdateRegionProviderCustom(context.Context, *UpdateRegionProviderCustomRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRegionProviderCustom not implemented")
}

// UnsafeRegionProviderServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RegionProviderServiceServer will
// result in compilation errors.
type UnsafeRegionProviderServiceServer interface {
	mustEmbedUnimplementedRegionProviderServiceServer()
}

func RegisterRegionProviderServiceServer(s grpc.ServiceRegistrar, srv RegionProviderServiceServer) {
	s.RegisterService(&RegionProviderService_ServiceDesc, srv)
}

func _RegionProviderService_FindAllEnabledRegionProviders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledRegionProvidersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionProviderServiceServer).FindAllEnabledRegionProviders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionProviderService_FindAllEnabledRegionProviders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionProviderServiceServer).FindAllEnabledRegionProviders(ctx, req.(*FindAllEnabledRegionProvidersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionProviderService_FindEnabledRegionProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledRegionProviderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionProviderServiceServer).FindEnabledRegionProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionProviderService_FindEnabledRegionProvider_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionProviderServiceServer).FindEnabledRegionProvider(ctx, req.(*FindEnabledRegionProviderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionProviderService_FindAllRegionProviders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllRegionProvidersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionProviderServiceServer).FindAllRegionProviders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionProviderService_FindAllRegionProviders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionProviderServiceServer).FindAllRegionProviders(ctx, req.(*FindAllRegionProvidersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionProviderService_FindRegionProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindRegionProviderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionProviderServiceServer).FindRegionProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionProviderService_FindRegionProvider_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionProviderServiceServer).FindRegionProvider(ctx, req.(*FindRegionProviderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionProviderService_UpdateRegionProviderCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRegionProviderCustomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionProviderServiceServer).UpdateRegionProviderCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionProviderService_UpdateRegionProviderCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionProviderServiceServer).UpdateRegionProviderCustom(ctx, req.(*UpdateRegionProviderCustomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RegionProviderService_ServiceDesc is the grpc.ServiceDesc for RegionProviderService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RegionProviderService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.RegionProviderService",
	HandlerType: (*RegionProviderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllEnabledRegionProviders",
			Handler:    _RegionProviderService_FindAllEnabledRegionProviders_Handler,
		},
		{
			MethodName: "findEnabledRegionProvider",
			Handler:    _RegionProviderService_FindEnabledRegionProvider_Handler,
		},
		{
			MethodName: "findAllRegionProviders",
			Handler:    _RegionProviderService_FindAllRegionProviders_Handler,
		},
		{
			MethodName: "findRegionProvider",
			Handler:    _RegionProviderService_FindRegionProvider_Handler,
		},
		{
			MethodName: "updateRegionProviderCustom",
			Handler:    _RegionProviderService_UpdateRegionProviderCustom_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_region_provider.proto",
}
