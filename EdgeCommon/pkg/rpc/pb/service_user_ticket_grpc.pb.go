// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_ticket.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserTicketService_CreateUserTicket_FullMethodName = "/pb.UserTicketService/createUserTicket"
	UserTicketService_UpdateUserTicket_FullMethodName = "/pb.UserTicketService/updateUserTicket"
	UserTicketService_DeleteUserTicket_FullMethodName = "/pb.UserTicketService/deleteUserTicket"
	UserTicketService_CountUserTickets_FullMethodName = "/pb.UserTicketService/countUserTickets"
	UserTicketService_ListUserTickets_FullMethodName  = "/pb.UserTicketService/listUserTickets"
	UserTicketService_FindUserTicket_FullMethodName   = "/pb.UserTicketService/findUserTicket"
)

// UserTicketServiceClient is the client API for UserTicketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserTicketServiceClient interface {
	// 创建工单
	CreateUserTicket(ctx context.Context, in *CreateUserTicketRequest, opts ...grpc.CallOption) (*CreateUserTicketResponse, error)
	// 修改工单
	UpdateUserTicket(ctx context.Context, in *UpdateUserTicketRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除工单
	DeleteUserTicket(ctx context.Context, in *DeleteUserTicketRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算工单数量
	CountUserTickets(ctx context.Context, in *CountUserTicketsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页工单
	ListUserTickets(ctx context.Context, in *ListUserTicketsRequest, opts ...grpc.CallOption) (*ListUserTicketsResponse, error)
	// 查找单个工单
	FindUserTicket(ctx context.Context, in *FindUserTicketRequest, opts ...grpc.CallOption) (*FindUserTicketResponse, error)
}

type userTicketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserTicketServiceClient(cc grpc.ClientConnInterface) UserTicketServiceClient {
	return &userTicketServiceClient{cc}
}

func (c *userTicketServiceClient) CreateUserTicket(ctx context.Context, in *CreateUserTicketRequest, opts ...grpc.CallOption) (*CreateUserTicketResponse, error) {
	out := new(CreateUserTicketResponse)
	err := c.cc.Invoke(ctx, UserTicketService_CreateUserTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketServiceClient) UpdateUserTicket(ctx context.Context, in *UpdateUserTicketRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserTicketService_UpdateUserTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketServiceClient) DeleteUserTicket(ctx context.Context, in *DeleteUserTicketRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserTicketService_DeleteUserTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketServiceClient) CountUserTickets(ctx context.Context, in *CountUserTicketsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserTicketService_CountUserTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketServiceClient) ListUserTickets(ctx context.Context, in *ListUserTicketsRequest, opts ...grpc.CallOption) (*ListUserTicketsResponse, error) {
	out := new(ListUserTicketsResponse)
	err := c.cc.Invoke(ctx, UserTicketService_ListUserTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketServiceClient) FindUserTicket(ctx context.Context, in *FindUserTicketRequest, opts ...grpc.CallOption) (*FindUserTicketResponse, error) {
	out := new(FindUserTicketResponse)
	err := c.cc.Invoke(ctx, UserTicketService_FindUserTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTicketServiceServer is the server API for UserTicketService service.
// All implementations should embed UnimplementedUserTicketServiceServer
// for forward compatibility
type UserTicketServiceServer interface {
	// 创建工单
	CreateUserTicket(context.Context, *CreateUserTicketRequest) (*CreateUserTicketResponse, error)
	// 修改工单
	UpdateUserTicket(context.Context, *UpdateUserTicketRequest) (*RPCSuccess, error)
	// 删除工单
	DeleteUserTicket(context.Context, *DeleteUserTicketRequest) (*RPCSuccess, error)
	// 计算工单数量
	CountUserTickets(context.Context, *CountUserTicketsRequest) (*RPCCountResponse, error)
	// 列出单页工单
	ListUserTickets(context.Context, *ListUserTicketsRequest) (*ListUserTicketsResponse, error)
	// 查找单个工单
	FindUserTicket(context.Context, *FindUserTicketRequest) (*FindUserTicketResponse, error)
}

// UnimplementedUserTicketServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserTicketServiceServer struct {
}

func (UnimplementedUserTicketServiceServer) CreateUserTicket(context.Context, *CreateUserTicketRequest) (*CreateUserTicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserTicket not implemented")
}
func (UnimplementedUserTicketServiceServer) UpdateUserTicket(context.Context, *UpdateUserTicketRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserTicket not implemented")
}
func (UnimplementedUserTicketServiceServer) DeleteUserTicket(context.Context, *DeleteUserTicketRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserTicket not implemented")
}
func (UnimplementedUserTicketServiceServer) CountUserTickets(context.Context, *CountUserTicketsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserTickets not implemented")
}
func (UnimplementedUserTicketServiceServer) ListUserTickets(context.Context, *ListUserTicketsRequest) (*ListUserTicketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserTickets not implemented")
}
func (UnimplementedUserTicketServiceServer) FindUserTicket(context.Context, *FindUserTicketRequest) (*FindUserTicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserTicket not implemented")
}

// UnsafeUserTicketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserTicketServiceServer will
// result in compilation errors.
type UnsafeUserTicketServiceServer interface {
	mustEmbedUnimplementedUserTicketServiceServer()
}

func RegisterUserTicketServiceServer(s grpc.ServiceRegistrar, srv UserTicketServiceServer) {
	s.RegisterService(&UserTicketService_ServiceDesc, srv)
}

func _UserTicketService_CreateUserTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketServiceServer).CreateUserTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketService_CreateUserTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketServiceServer).CreateUserTicket(ctx, req.(*CreateUserTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketService_UpdateUserTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketServiceServer).UpdateUserTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketService_UpdateUserTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketServiceServer).UpdateUserTicket(ctx, req.(*UpdateUserTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketService_DeleteUserTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketServiceServer).DeleteUserTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketService_DeleteUserTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketServiceServer).DeleteUserTicket(ctx, req.(*DeleteUserTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketService_CountUserTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketServiceServer).CountUserTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketService_CountUserTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketServiceServer).CountUserTickets(ctx, req.(*CountUserTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketService_ListUserTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketServiceServer).ListUserTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketService_ListUserTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketServiceServer).ListUserTickets(ctx, req.(*ListUserTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketService_FindUserTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketServiceServer).FindUserTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketService_FindUserTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketServiceServer).FindUserTicket(ctx, req.(*FindUserTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserTicketService_ServiceDesc is the grpc.ServiceDesc for UserTicketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserTicketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserTicketService",
	HandlerType: (*UserTicketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createUserTicket",
			Handler:    _UserTicketService_CreateUserTicket_Handler,
		},
		{
			MethodName: "updateUserTicket",
			Handler:    _UserTicketService_UpdateUserTicket_Handler,
		},
		{
			MethodName: "deleteUserTicket",
			Handler:    _UserTicketService_DeleteUserTicket_Handler,
		},
		{
			MethodName: "countUserTickets",
			Handler:    _UserTicketService_CountUserTickets_Handler,
		},
		{
			MethodName: "listUserTickets",
			Handler:    _UserTicketService_ListUserTickets_Handler,
		},
		{
			MethodName: "findUserTicket",
			Handler:    _UserTicketService_FindUserTicket_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_ticket.proto",
}
