// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_client_browser_monthly_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerClientBrowserMonthlyStatService_FindTopServerClientBrowserMonthlyStats_FullMethodName = "/pb.ServerClientBrowserMonthlyStatService/findTopServerClientBrowserMonthlyStats"
)

// ServerClientBrowserMonthlyStatServiceClient is the client API for ServerClientBrowserMonthlyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerClientBrowserMonthlyStatServiceClient interface {
	// 查找前N个浏览器
	FindTopServerClientBrowserMonthlyStats(ctx context.Context, in *FindTopServerClientBrowserMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerClientBrowserMonthlyStatsResponse, error)
}

type serverClientBrowserMonthlyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerClientBrowserMonthlyStatServiceClient(cc grpc.ClientConnInterface) ServerClientBrowserMonthlyStatServiceClient {
	return &serverClientBrowserMonthlyStatServiceClient{cc}
}

func (c *serverClientBrowserMonthlyStatServiceClient) FindTopServerClientBrowserMonthlyStats(ctx context.Context, in *FindTopServerClientBrowserMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerClientBrowserMonthlyStatsResponse, error) {
	out := new(FindTopServerClientBrowserMonthlyStatsResponse)
	err := c.cc.Invoke(ctx, ServerClientBrowserMonthlyStatService_FindTopServerClientBrowserMonthlyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerClientBrowserMonthlyStatServiceServer is the server API for ServerClientBrowserMonthlyStatService service.
// All implementations should embed UnimplementedServerClientBrowserMonthlyStatServiceServer
// for forward compatibility
type ServerClientBrowserMonthlyStatServiceServer interface {
	// 查找前N个浏览器
	FindTopServerClientBrowserMonthlyStats(context.Context, *FindTopServerClientBrowserMonthlyStatsRequest) (*FindTopServerClientBrowserMonthlyStatsResponse, error)
}

// UnimplementedServerClientBrowserMonthlyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerClientBrowserMonthlyStatServiceServer struct {
}

func (UnimplementedServerClientBrowserMonthlyStatServiceServer) FindTopServerClientBrowserMonthlyStats(context.Context, *FindTopServerClientBrowserMonthlyStatsRequest) (*FindTopServerClientBrowserMonthlyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTopServerClientBrowserMonthlyStats not implemented")
}

// UnsafeServerClientBrowserMonthlyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerClientBrowserMonthlyStatServiceServer will
// result in compilation errors.
type UnsafeServerClientBrowserMonthlyStatServiceServer interface {
	mustEmbedUnimplementedServerClientBrowserMonthlyStatServiceServer()
}

func RegisterServerClientBrowserMonthlyStatServiceServer(s grpc.ServiceRegistrar, srv ServerClientBrowserMonthlyStatServiceServer) {
	s.RegisterService(&ServerClientBrowserMonthlyStatService_ServiceDesc, srv)
}

func _ServerClientBrowserMonthlyStatService_FindTopServerClientBrowserMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTopServerClientBrowserMonthlyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerClientBrowserMonthlyStatServiceServer).FindTopServerClientBrowserMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerClientBrowserMonthlyStatService_FindTopServerClientBrowserMonthlyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerClientBrowserMonthlyStatServiceServer).FindTopServerClientBrowserMonthlyStats(ctx, req.(*FindTopServerClientBrowserMonthlyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerClientBrowserMonthlyStatService_ServiceDesc is the grpc.ServiceDesc for ServerClientBrowserMonthlyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerClientBrowserMonthlyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerClientBrowserMonthlyStatService",
	HandlerType: (*ServerClientBrowserMonthlyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findTopServerClientBrowserMonthlyStats",
			Handler:    _ServerClientBrowserMonthlyStatService_FindTopServerClientBrowserMonthlyStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_client_browser_monthly_stat.proto",
}
