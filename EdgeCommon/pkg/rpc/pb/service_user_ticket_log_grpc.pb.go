// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_ticket_log.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserTicketLogService_CreateUserTicketLog_FullMethodName = "/pb.UserTicketLogService/createUserTicketLog"
	UserTicketLogService_DeleteUserTicketLog_FullMethodName = "/pb.UserTicketLogService/deleteUserTicketLog"
	UserTicketLogService_CountUserTicketLogs_FullMethodName = "/pb.UserTicketLogService/countUserTicketLogs"
	UserTicketLogService_ListUserTicketLogs_FullMethodName  = "/pb.UserTicketLogService/listUserTicketLogs"
)

// UserTicketLogServiceClient is the client API for UserTicketLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserTicketLogServiceClient interface {
	// 创建日志
	CreateUserTicketLog(ctx context.Context, in *CreateUserTicketLogRequest, opts ...grpc.CallOption) (*CreateUserTicketLogResponse, error)
	// 删除日志
	DeleteUserTicketLog(ctx context.Context, in *DeleteUserTicketLogRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查询日志数量
	CountUserTicketLogs(ctx context.Context, in *CountUserTicketLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页日志
	ListUserTicketLogs(ctx context.Context, in *ListUserTicketLogsRequest, opts ...grpc.CallOption) (*ListUserTicketLogsResponse, error)
}

type userTicketLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserTicketLogServiceClient(cc grpc.ClientConnInterface) UserTicketLogServiceClient {
	return &userTicketLogServiceClient{cc}
}

func (c *userTicketLogServiceClient) CreateUserTicketLog(ctx context.Context, in *CreateUserTicketLogRequest, opts ...grpc.CallOption) (*CreateUserTicketLogResponse, error) {
	out := new(CreateUserTicketLogResponse)
	err := c.cc.Invoke(ctx, UserTicketLogService_CreateUserTicketLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketLogServiceClient) DeleteUserTicketLog(ctx context.Context, in *DeleteUserTicketLogRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserTicketLogService_DeleteUserTicketLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketLogServiceClient) CountUserTicketLogs(ctx context.Context, in *CountUserTicketLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserTicketLogService_CountUserTicketLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketLogServiceClient) ListUserTicketLogs(ctx context.Context, in *ListUserTicketLogsRequest, opts ...grpc.CallOption) (*ListUserTicketLogsResponse, error) {
	out := new(ListUserTicketLogsResponse)
	err := c.cc.Invoke(ctx, UserTicketLogService_ListUserTicketLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTicketLogServiceServer is the server API for UserTicketLogService service.
// All implementations should embed UnimplementedUserTicketLogServiceServer
// for forward compatibility
type UserTicketLogServiceServer interface {
	// 创建日志
	CreateUserTicketLog(context.Context, *CreateUserTicketLogRequest) (*CreateUserTicketLogResponse, error)
	// 删除日志
	DeleteUserTicketLog(context.Context, *DeleteUserTicketLogRequest) (*RPCSuccess, error)
	// 查询日志数量
	CountUserTicketLogs(context.Context, *CountUserTicketLogsRequest) (*RPCCountResponse, error)
	// 列出单页日志
	ListUserTicketLogs(context.Context, *ListUserTicketLogsRequest) (*ListUserTicketLogsResponse, error)
}

// UnimplementedUserTicketLogServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserTicketLogServiceServer struct {
}

func (UnimplementedUserTicketLogServiceServer) CreateUserTicketLog(context.Context, *CreateUserTicketLogRequest) (*CreateUserTicketLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserTicketLog not implemented")
}
func (UnimplementedUserTicketLogServiceServer) DeleteUserTicketLog(context.Context, *DeleteUserTicketLogRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserTicketLog not implemented")
}
func (UnimplementedUserTicketLogServiceServer) CountUserTicketLogs(context.Context, *CountUserTicketLogsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserTicketLogs not implemented")
}
func (UnimplementedUserTicketLogServiceServer) ListUserTicketLogs(context.Context, *ListUserTicketLogsRequest) (*ListUserTicketLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserTicketLogs not implemented")
}

// UnsafeUserTicketLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserTicketLogServiceServer will
// result in compilation errors.
type UnsafeUserTicketLogServiceServer interface {
	mustEmbedUnimplementedUserTicketLogServiceServer()
}

func RegisterUserTicketLogServiceServer(s grpc.ServiceRegistrar, srv UserTicketLogServiceServer) {
	s.RegisterService(&UserTicketLogService_ServiceDesc, srv)
}

func _UserTicketLogService_CreateUserTicketLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserTicketLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketLogServiceServer).CreateUserTicketLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketLogService_CreateUserTicketLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketLogServiceServer).CreateUserTicketLog(ctx, req.(*CreateUserTicketLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketLogService_DeleteUserTicketLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserTicketLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketLogServiceServer).DeleteUserTicketLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketLogService_DeleteUserTicketLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketLogServiceServer).DeleteUserTicketLog(ctx, req.(*DeleteUserTicketLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketLogService_CountUserTicketLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserTicketLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketLogServiceServer).CountUserTicketLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketLogService_CountUserTicketLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketLogServiceServer).CountUserTicketLogs(ctx, req.(*CountUserTicketLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketLogService_ListUserTicketLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserTicketLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketLogServiceServer).ListUserTicketLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketLogService_ListUserTicketLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketLogServiceServer).ListUserTicketLogs(ctx, req.(*ListUserTicketLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserTicketLogService_ServiceDesc is the grpc.ServiceDesc for UserTicketLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserTicketLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserTicketLogService",
	HandlerType: (*UserTicketLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createUserTicketLog",
			Handler:    _UserTicketLogService_CreateUserTicketLog_Handler,
		},
		{
			MethodName: "deleteUserTicketLog",
			Handler:    _UserTicketLogService_DeleteUserTicketLog_Handler,
		},
		{
			MethodName: "countUserTicketLogs",
			Handler:    _UserTicketLogService_CountUserTicketLogs_Handler,
		},
		{
			MethodName: "listUserTicketLogs",
			Handler:    _UserTicketLogService_ListUserTicketLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_ticket_log.proto",
}
