// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_report_node.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReportNodeService_CreateReportNode_FullMethodName              = "/pb.ReportNodeService/createReportNode"
	ReportNodeService_DeleteReportNode_FullMethodName              = "/pb.ReportNodeService/deleteReportNode"
	ReportNodeService_UpdateReportNode_FullMethodName              = "/pb.ReportNodeService/updateReportNode"
	ReportNodeService_CountAllEnabledReportNodes_FullMethodName    = "/pb.ReportNodeService/countAllEnabledReportNodes"
	ReportNodeService_ListEnabledReportNodes_FullMethodName        = "/pb.ReportNodeService/listEnabledReportNodes"
	ReportNodeService_FindEnabledReportNode_FullMethodName         = "/pb.ReportNodeService/findEnabledReportNode"
	ReportNodeService_ReportNodeStream_FullMethodName              = "/pb.ReportNodeService/reportNodeStream"
	ReportNodeService_UpdateReportNodeStatus_FullMethodName        = "/pb.ReportNodeService/updateReportNodeStatus"
	ReportNodeService_FindCurrentReportNodeConfig_FullMethodName   = "/pb.ReportNodeService/findCurrentReportNodeConfig"
	ReportNodeService_FindReportNodeTasks_FullMethodName           = "/pb.ReportNodeService/findReportNodeTasks"
	ReportNodeService_FindLatestReportNodeVersion_FullMethodName   = "/pb.ReportNodeService/findLatestReportNodeVersion"
	ReportNodeService_CountAllReportNodeTasks_FullMethodName       = "/pb.ReportNodeService/countAllReportNodeTasks"
	ReportNodeService_ListReportNodeTasks_FullMethodName           = "/pb.ReportNodeService/listReportNodeTasks"
	ReportNodeService_UpdateReportNodeGlobalSetting_FullMethodName = "/pb.ReportNodeService/updateReportNodeGlobalSetting"
	ReportNodeService_ReadReportNodeGlobalSetting_FullMethodName   = "/pb.ReportNodeService/readReportNodeGlobalSetting"
)

// ReportNodeServiceClient is the client API for ReportNodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportNodeServiceClient interface {
	// 添加终端
	CreateReportNode(ctx context.Context, in *CreateReportNodeRequest, opts ...grpc.CallOption) (*CreateReportNodeResponse, error)
	// 删除终端
	DeleteReportNode(ctx context.Context, in *DeleteReportNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改终端
	UpdateReportNode(ctx context.Context, in *UpdateReportNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算终端数量
	CountAllEnabledReportNodes(ctx context.Context, in *CountAllEnabledReportNodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页终端
	ListEnabledReportNodes(ctx context.Context, in *ListEnabledReportNodesRequest, opts ...grpc.CallOption) (*ListEnabledReportNodesResponse, error)
	// 查找单个终端
	FindEnabledReportNode(ctx context.Context, in *FindEnabledReportNodeRequest, opts ...grpc.CallOption) (*FindEnabledReportNodeResponse, error)
	// 终端stream
	ReportNodeStream(ctx context.Context, opts ...grpc.CallOption) (ReportNodeService_ReportNodeStreamClient, error)
	// 更新节点状态
	UpdateReportNodeStatus(ctx context.Context, in *UpdateReportNodeStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取当前节点信息
	FindCurrentReportNodeConfig(ctx context.Context, in *FindCurrentReportNodeConfigRequest, opts ...grpc.CallOption) (*FindCurrentReportNodeConfigResponse, error)
	// 读取任务
	FindReportNodeTasks(ctx context.Context, in *FindReportNodeTasksRequest, opts ...grpc.CallOption) (*FindReportNodeTasksResponse, error)
	// 取得最新的版本号
	FindLatestReportNodeVersion(ctx context.Context, in *FindLatestReportNodeVersionRequest, opts ...grpc.CallOption) (*FindLatestReportNodeVersionResponse, error)
	// 计算任务数量
	CountAllReportNodeTasks(ctx context.Context, in *CountAllReportNodeTasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页任务
	ListReportNodeTasks(ctx context.Context, in *ListReportNodeTasksRequest, opts ...grpc.CallOption) (*ListReportNodeTasksResponse, error)
	// 修改全局设置
	UpdateReportNodeGlobalSetting(ctx context.Context, in *UpdateReportNodeGlobalSetting, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取全局设置
	ReadReportNodeGlobalSetting(ctx context.Context, in *ReadReportNodeGlobalSettingRequest, opts ...grpc.CallOption) (*ReadReportNodeGlobalSettingResponse, error)
}

type reportNodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReportNodeServiceClient(cc grpc.ClientConnInterface) ReportNodeServiceClient {
	return &reportNodeServiceClient{cc}
}

func (c *reportNodeServiceClient) CreateReportNode(ctx context.Context, in *CreateReportNodeRequest, opts ...grpc.CallOption) (*CreateReportNodeResponse, error) {
	out := new(CreateReportNodeResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_CreateReportNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) DeleteReportNode(ctx context.Context, in *DeleteReportNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportNodeService_DeleteReportNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) UpdateReportNode(ctx context.Context, in *UpdateReportNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportNodeService_UpdateReportNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) CountAllEnabledReportNodes(ctx context.Context, in *CountAllEnabledReportNodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_CountAllEnabledReportNodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) ListEnabledReportNodes(ctx context.Context, in *ListEnabledReportNodesRequest, opts ...grpc.CallOption) (*ListEnabledReportNodesResponse, error) {
	out := new(ListEnabledReportNodesResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_ListEnabledReportNodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) FindEnabledReportNode(ctx context.Context, in *FindEnabledReportNodeRequest, opts ...grpc.CallOption) (*FindEnabledReportNodeResponse, error) {
	out := new(FindEnabledReportNodeResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_FindEnabledReportNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) ReportNodeStream(ctx context.Context, opts ...grpc.CallOption) (ReportNodeService_ReportNodeStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &ReportNodeService_ServiceDesc.Streams[0], ReportNodeService_ReportNodeStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &reportNodeServiceReportNodeStreamClient{stream}
	return x, nil
}

type ReportNodeService_ReportNodeStreamClient interface {
	Send(*ReportNodeStreamMessage) error
	Recv() (*ReportNodeStreamMessage, error)
	grpc.ClientStream
}

type reportNodeServiceReportNodeStreamClient struct {
	grpc.ClientStream
}

func (x *reportNodeServiceReportNodeStreamClient) Send(m *ReportNodeStreamMessage) error {
	return x.ClientStream.SendMsg(m)
}

func (x *reportNodeServiceReportNodeStreamClient) Recv() (*ReportNodeStreamMessage, error) {
	m := new(ReportNodeStreamMessage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *reportNodeServiceClient) UpdateReportNodeStatus(ctx context.Context, in *UpdateReportNodeStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportNodeService_UpdateReportNodeStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) FindCurrentReportNodeConfig(ctx context.Context, in *FindCurrentReportNodeConfigRequest, opts ...grpc.CallOption) (*FindCurrentReportNodeConfigResponse, error) {
	out := new(FindCurrentReportNodeConfigResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_FindCurrentReportNodeConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) FindReportNodeTasks(ctx context.Context, in *FindReportNodeTasksRequest, opts ...grpc.CallOption) (*FindReportNodeTasksResponse, error) {
	out := new(FindReportNodeTasksResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_FindReportNodeTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) FindLatestReportNodeVersion(ctx context.Context, in *FindLatestReportNodeVersionRequest, opts ...grpc.CallOption) (*FindLatestReportNodeVersionResponse, error) {
	out := new(FindLatestReportNodeVersionResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_FindLatestReportNodeVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) CountAllReportNodeTasks(ctx context.Context, in *CountAllReportNodeTasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_CountAllReportNodeTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) ListReportNodeTasks(ctx context.Context, in *ListReportNodeTasksRequest, opts ...grpc.CallOption) (*ListReportNodeTasksResponse, error) {
	out := new(ListReportNodeTasksResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_ListReportNodeTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) UpdateReportNodeGlobalSetting(ctx context.Context, in *UpdateReportNodeGlobalSetting, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportNodeService_UpdateReportNodeGlobalSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeServiceClient) ReadReportNodeGlobalSetting(ctx context.Context, in *ReadReportNodeGlobalSettingRequest, opts ...grpc.CallOption) (*ReadReportNodeGlobalSettingResponse, error) {
	out := new(ReadReportNodeGlobalSettingResponse)
	err := c.cc.Invoke(ctx, ReportNodeService_ReadReportNodeGlobalSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportNodeServiceServer is the server API for ReportNodeService service.
// All implementations should embed UnimplementedReportNodeServiceServer
// for forward compatibility
type ReportNodeServiceServer interface {
	// 添加终端
	CreateReportNode(context.Context, *CreateReportNodeRequest) (*CreateReportNodeResponse, error)
	// 删除终端
	DeleteReportNode(context.Context, *DeleteReportNodeRequest) (*RPCSuccess, error)
	// 修改终端
	UpdateReportNode(context.Context, *UpdateReportNodeRequest) (*RPCSuccess, error)
	// 计算终端数量
	CountAllEnabledReportNodes(context.Context, *CountAllEnabledReportNodesRequest) (*RPCCountResponse, error)
	// 列出单页终端
	ListEnabledReportNodes(context.Context, *ListEnabledReportNodesRequest) (*ListEnabledReportNodesResponse, error)
	// 查找单个终端
	FindEnabledReportNode(context.Context, *FindEnabledReportNodeRequest) (*FindEnabledReportNodeResponse, error)
	// 终端stream
	ReportNodeStream(ReportNodeService_ReportNodeStreamServer) error
	// 更新节点状态
	UpdateReportNodeStatus(context.Context, *UpdateReportNodeStatusRequest) (*RPCSuccess, error)
	// 获取当前节点信息
	FindCurrentReportNodeConfig(context.Context, *FindCurrentReportNodeConfigRequest) (*FindCurrentReportNodeConfigResponse, error)
	// 读取任务
	FindReportNodeTasks(context.Context, *FindReportNodeTasksRequest) (*FindReportNodeTasksResponse, error)
	// 取得最新的版本号
	FindLatestReportNodeVersion(context.Context, *FindLatestReportNodeVersionRequest) (*FindLatestReportNodeVersionResponse, error)
	// 计算任务数量
	CountAllReportNodeTasks(context.Context, *CountAllReportNodeTasksRequest) (*RPCCountResponse, error)
	// 列出单页任务
	ListReportNodeTasks(context.Context, *ListReportNodeTasksRequest) (*ListReportNodeTasksResponse, error)
	// 修改全局设置
	UpdateReportNodeGlobalSetting(context.Context, *UpdateReportNodeGlobalSetting) (*RPCSuccess, error)
	// 读取全局设置
	ReadReportNodeGlobalSetting(context.Context, *ReadReportNodeGlobalSettingRequest) (*ReadReportNodeGlobalSettingResponse, error)
}

// UnimplementedReportNodeServiceServer should be embedded to have forward compatible implementations.
type UnimplementedReportNodeServiceServer struct {
}

func (UnimplementedReportNodeServiceServer) CreateReportNode(context.Context, *CreateReportNodeRequest) (*CreateReportNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReportNode not implemented")
}
func (UnimplementedReportNodeServiceServer) DeleteReportNode(context.Context, *DeleteReportNodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteReportNode not implemented")
}
func (UnimplementedReportNodeServiceServer) UpdateReportNode(context.Context, *UpdateReportNodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReportNode not implemented")
}
func (UnimplementedReportNodeServiceServer) CountAllEnabledReportNodes(context.Context, *CountAllEnabledReportNodesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledReportNodes not implemented")
}
func (UnimplementedReportNodeServiceServer) ListEnabledReportNodes(context.Context, *ListEnabledReportNodesRequest) (*ListEnabledReportNodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledReportNodes not implemented")
}
func (UnimplementedReportNodeServiceServer) FindEnabledReportNode(context.Context, *FindEnabledReportNodeRequest) (*FindEnabledReportNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledReportNode not implemented")
}
func (UnimplementedReportNodeServiceServer) ReportNodeStream(ReportNodeService_ReportNodeStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method ReportNodeStream not implemented")
}
func (UnimplementedReportNodeServiceServer) UpdateReportNodeStatus(context.Context, *UpdateReportNodeStatusRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReportNodeStatus not implemented")
}
func (UnimplementedReportNodeServiceServer) FindCurrentReportNodeConfig(context.Context, *FindCurrentReportNodeConfigRequest) (*FindCurrentReportNodeConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCurrentReportNodeConfig not implemented")
}
func (UnimplementedReportNodeServiceServer) FindReportNodeTasks(context.Context, *FindReportNodeTasksRequest) (*FindReportNodeTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindReportNodeTasks not implemented")
}
func (UnimplementedReportNodeServiceServer) FindLatestReportNodeVersion(context.Context, *FindLatestReportNodeVersionRequest) (*FindLatestReportNodeVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLatestReportNodeVersion not implemented")
}
func (UnimplementedReportNodeServiceServer) CountAllReportNodeTasks(context.Context, *CountAllReportNodeTasksRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllReportNodeTasks not implemented")
}
func (UnimplementedReportNodeServiceServer) ListReportNodeTasks(context.Context, *ListReportNodeTasksRequest) (*ListReportNodeTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListReportNodeTasks not implemented")
}
func (UnimplementedReportNodeServiceServer) UpdateReportNodeGlobalSetting(context.Context, *UpdateReportNodeGlobalSetting) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReportNodeGlobalSetting not implemented")
}
func (UnimplementedReportNodeServiceServer) ReadReportNodeGlobalSetting(context.Context, *ReadReportNodeGlobalSettingRequest) (*ReadReportNodeGlobalSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadReportNodeGlobalSetting not implemented")
}

// UnsafeReportNodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportNodeServiceServer will
// result in compilation errors.
type UnsafeReportNodeServiceServer interface {
	mustEmbedUnimplementedReportNodeServiceServer()
}

func RegisterReportNodeServiceServer(s grpc.ServiceRegistrar, srv ReportNodeServiceServer) {
	s.RegisterService(&ReportNodeService_ServiceDesc, srv)
}

func _ReportNodeService_CreateReportNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReportNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).CreateReportNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_CreateReportNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).CreateReportNode(ctx, req.(*CreateReportNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_DeleteReportNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteReportNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).DeleteReportNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_DeleteReportNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).DeleteReportNode(ctx, req.(*DeleteReportNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_UpdateReportNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).UpdateReportNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_UpdateReportNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).UpdateReportNode(ctx, req.(*UpdateReportNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_CountAllEnabledReportNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledReportNodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).CountAllEnabledReportNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_CountAllEnabledReportNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).CountAllEnabledReportNodes(ctx, req.(*CountAllEnabledReportNodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_ListEnabledReportNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledReportNodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).ListEnabledReportNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_ListEnabledReportNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).ListEnabledReportNodes(ctx, req.(*ListEnabledReportNodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_FindEnabledReportNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledReportNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).FindEnabledReportNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_FindEnabledReportNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).FindEnabledReportNode(ctx, req.(*FindEnabledReportNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_ReportNodeStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ReportNodeServiceServer).ReportNodeStream(&reportNodeServiceReportNodeStreamServer{stream})
}

type ReportNodeService_ReportNodeStreamServer interface {
	Send(*ReportNodeStreamMessage) error
	Recv() (*ReportNodeStreamMessage, error)
	grpc.ServerStream
}

type reportNodeServiceReportNodeStreamServer struct {
	grpc.ServerStream
}

func (x *reportNodeServiceReportNodeStreamServer) Send(m *ReportNodeStreamMessage) error {
	return x.ServerStream.SendMsg(m)
}

func (x *reportNodeServiceReportNodeStreamServer) Recv() (*ReportNodeStreamMessage, error) {
	m := new(ReportNodeStreamMessage)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ReportNodeService_UpdateReportNodeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportNodeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).UpdateReportNodeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_UpdateReportNodeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).UpdateReportNodeStatus(ctx, req.(*UpdateReportNodeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_FindCurrentReportNodeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCurrentReportNodeConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).FindCurrentReportNodeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_FindCurrentReportNodeConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).FindCurrentReportNodeConfig(ctx, req.(*FindCurrentReportNodeConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_FindReportNodeTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindReportNodeTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).FindReportNodeTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_FindReportNodeTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).FindReportNodeTasks(ctx, req.(*FindReportNodeTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_FindLatestReportNodeVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLatestReportNodeVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).FindLatestReportNodeVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_FindLatestReportNodeVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).FindLatestReportNodeVersion(ctx, req.(*FindLatestReportNodeVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_CountAllReportNodeTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllReportNodeTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).CountAllReportNodeTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_CountAllReportNodeTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).CountAllReportNodeTasks(ctx, req.(*CountAllReportNodeTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_ListReportNodeTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListReportNodeTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).ListReportNodeTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_ListReportNodeTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).ListReportNodeTasks(ctx, req.(*ListReportNodeTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_UpdateReportNodeGlobalSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportNodeGlobalSetting)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).UpdateReportNodeGlobalSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_UpdateReportNodeGlobalSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).UpdateReportNodeGlobalSetting(ctx, req.(*UpdateReportNodeGlobalSetting))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeService_ReadReportNodeGlobalSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadReportNodeGlobalSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeServiceServer).ReadReportNodeGlobalSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeService_ReadReportNodeGlobalSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeServiceServer).ReadReportNodeGlobalSetting(ctx, req.(*ReadReportNodeGlobalSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportNodeService_ServiceDesc is the grpc.ServiceDesc for ReportNodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportNodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ReportNodeService",
	HandlerType: (*ReportNodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createReportNode",
			Handler:    _ReportNodeService_CreateReportNode_Handler,
		},
		{
			MethodName: "deleteReportNode",
			Handler:    _ReportNodeService_DeleteReportNode_Handler,
		},
		{
			MethodName: "updateReportNode",
			Handler:    _ReportNodeService_UpdateReportNode_Handler,
		},
		{
			MethodName: "countAllEnabledReportNodes",
			Handler:    _ReportNodeService_CountAllEnabledReportNodes_Handler,
		},
		{
			MethodName: "listEnabledReportNodes",
			Handler:    _ReportNodeService_ListEnabledReportNodes_Handler,
		},
		{
			MethodName: "findEnabledReportNode",
			Handler:    _ReportNodeService_FindEnabledReportNode_Handler,
		},
		{
			MethodName: "updateReportNodeStatus",
			Handler:    _ReportNodeService_UpdateReportNodeStatus_Handler,
		},
		{
			MethodName: "findCurrentReportNodeConfig",
			Handler:    _ReportNodeService_FindCurrentReportNodeConfig_Handler,
		},
		{
			MethodName: "findReportNodeTasks",
			Handler:    _ReportNodeService_FindReportNodeTasks_Handler,
		},
		{
			MethodName: "findLatestReportNodeVersion",
			Handler:    _ReportNodeService_FindLatestReportNodeVersion_Handler,
		},
		{
			MethodName: "countAllReportNodeTasks",
			Handler:    _ReportNodeService_CountAllReportNodeTasks_Handler,
		},
		{
			MethodName: "listReportNodeTasks",
			Handler:    _ReportNodeService_ListReportNodeTasks_Handler,
		},
		{
			MethodName: "updateReportNodeGlobalSetting",
			Handler:    _ReportNodeService_UpdateReportNodeGlobalSetting_Handler,
		},
		{
			MethodName: "readReportNodeGlobalSetting",
			Handler:    _ReportNodeService_ReadReportNodeGlobalSetting_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "reportNodeStream",
			Handler:       _ReportNodeService_ReportNodeStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "service_report_node.proto",
}
