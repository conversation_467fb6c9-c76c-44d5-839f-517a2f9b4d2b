// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_group.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerGroupService_CreateServerGroup_FullMethodName                            = "/pb.ServerGroupService/createServerGroup"
	ServerGroupService_UpdateServerGroup_FullMethodName                            = "/pb.ServerGroupService/updateServerGroup"
	ServerGroupService_DeleteServerGroup_FullMethodName                            = "/pb.ServerGroupService/deleteServerGroup"
	ServerGroupService_FindAllEnabledServerGroups_FullMethodName                   = "/pb.ServerGroupService/findAllEnabledServerGroups"
	ServerGroupService_UpdateServerGroupOrders_FullMethodName                      = "/pb.ServerGroupService/updateServerGroupOrders"
	ServerGroupService_FindEnabledServerGroup_FullMethodName                       = "/pb.ServerGroupService/findEnabledServerGroup"
	ServerGroupService_FindAndInitServerGroupHTTPReverseProxyConfig_FullMethodName = "/pb.ServerGroupService/findAndInitServerGroupHTTPReverseProxyConfig"
	ServerGroupService_FindAndInitServerGroupTCPReverseProxyConfig_FullMethodName  = "/pb.ServerGroupService/findAndInitServerGroupTCPReverseProxyConfig"
	ServerGroupService_FindAndInitServerGroupUDPReverseProxyConfig_FullMethodName  = "/pb.ServerGroupService/findAndInitServerGroupUDPReverseProxyConfig"
	ServerGroupService_UpdateServerGroupHTTPReverseProxy_FullMethodName            = "/pb.ServerGroupService/updateServerGroupHTTPReverseProxy"
	ServerGroupService_UpdateServerGroupTCPReverseProxy_FullMethodName             = "/pb.ServerGroupService/updateServerGroupTCPReverseProxy"
	ServerGroupService_UpdateServerGroupUDPReverseProxy_FullMethodName             = "/pb.ServerGroupService/updateServerGroupUDPReverseProxy"
	ServerGroupService_FindEnabledServerGroupConfigInfo_FullMethodName             = "/pb.ServerGroupService/findEnabledServerGroupConfigInfo"
	ServerGroupService_FindAndInitServerGroupWebConfig_FullMethodName              = "/pb.ServerGroupService/findAndInitServerGroupWebConfig"
)

// ServerGroupServiceClient is the client API for ServerGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerGroupServiceClient interface {
	// 创建分组
	CreateServerGroup(ctx context.Context, in *CreateServerGroupRequest, opts ...grpc.CallOption) (*CreateServerGroupResponse, error)
	// 修改分组
	UpdateServerGroup(ctx context.Context, in *UpdateServerGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除分组
	DeleteServerGroup(ctx context.Context, in *DeleteServerGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查询所有分组
	FindAllEnabledServerGroups(ctx context.Context, in *FindAllEnabledServerGroupsRequest, opts ...grpc.CallOption) (*FindAllEnabledServerGroupsResponse, error)
	// 修改分组排序
	UpdateServerGroupOrders(ctx context.Context, in *UpdateServerGroupOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个分组信息
	FindEnabledServerGroup(ctx context.Context, in *FindEnabledServerGroupRequest, opts ...grpc.CallOption) (*FindEnabledServerGroupResponse, error)
	// 查找HTTP反向代理设置
	FindAndInitServerGroupHTTPReverseProxyConfig(ctx context.Context, in *FindAndInitServerGroupHTTPReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupHTTPReverseProxyConfigResponse, error)
	// 查找TCP反向代理设置
	FindAndInitServerGroupTCPReverseProxyConfig(ctx context.Context, in *FindAndInitServerGroupTCPReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupTCPReverseProxyConfigResponse, error)
	// 查找UDP反向代理设置
	FindAndInitServerGroupUDPReverseProxyConfig(ctx context.Context, in *FindAndInitServerGroupUDPReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupUDPReverseProxyConfigResponse, error)
	// 修改分组的HTTP反向代理设置
	UpdateServerGroupHTTPReverseProxy(ctx context.Context, in *UpdateServerGroupHTTPReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改分组的TCP反向代理设置
	UpdateServerGroupTCPReverseProxy(ctx context.Context, in *UpdateServerGroupTCPReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改分组的UDP反向代理设置
	UpdateServerGroupUDPReverseProxy(ctx context.Context, in *UpdateServerGroupUDPReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 取得分组的配置概要信息
	FindEnabledServerGroupConfigInfo(ctx context.Context, in *FindEnabledServerGroupConfigInfoRequest, opts ...grpc.CallOption) (*FindEnabledServerGroupConfigInfoResponse, error)
	// 初始化Web设置
	FindAndInitServerGroupWebConfig(ctx context.Context, in *FindAndInitServerGroupWebConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupWebConfigResponse, error)
}

type serverGroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerGroupServiceClient(cc grpc.ClientConnInterface) ServerGroupServiceClient {
	return &serverGroupServiceClient{cc}
}

func (c *serverGroupServiceClient) CreateServerGroup(ctx context.Context, in *CreateServerGroupRequest, opts ...grpc.CallOption) (*CreateServerGroupResponse, error) {
	out := new(CreateServerGroupResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_CreateServerGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) UpdateServerGroup(ctx context.Context, in *UpdateServerGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerGroupService_UpdateServerGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) DeleteServerGroup(ctx context.Context, in *DeleteServerGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerGroupService_DeleteServerGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindAllEnabledServerGroups(ctx context.Context, in *FindAllEnabledServerGroupsRequest, opts ...grpc.CallOption) (*FindAllEnabledServerGroupsResponse, error) {
	out := new(FindAllEnabledServerGroupsResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindAllEnabledServerGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) UpdateServerGroupOrders(ctx context.Context, in *UpdateServerGroupOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerGroupService_UpdateServerGroupOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindEnabledServerGroup(ctx context.Context, in *FindEnabledServerGroupRequest, opts ...grpc.CallOption) (*FindEnabledServerGroupResponse, error) {
	out := new(FindEnabledServerGroupResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindEnabledServerGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindAndInitServerGroupHTTPReverseProxyConfig(ctx context.Context, in *FindAndInitServerGroupHTTPReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupHTTPReverseProxyConfigResponse, error) {
	out := new(FindAndInitServerGroupHTTPReverseProxyConfigResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindAndInitServerGroupHTTPReverseProxyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindAndInitServerGroupTCPReverseProxyConfig(ctx context.Context, in *FindAndInitServerGroupTCPReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupTCPReverseProxyConfigResponse, error) {
	out := new(FindAndInitServerGroupTCPReverseProxyConfigResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindAndInitServerGroupTCPReverseProxyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindAndInitServerGroupUDPReverseProxyConfig(ctx context.Context, in *FindAndInitServerGroupUDPReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupUDPReverseProxyConfigResponse, error) {
	out := new(FindAndInitServerGroupUDPReverseProxyConfigResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindAndInitServerGroupUDPReverseProxyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) UpdateServerGroupHTTPReverseProxy(ctx context.Context, in *UpdateServerGroupHTTPReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerGroupService_UpdateServerGroupHTTPReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) UpdateServerGroupTCPReverseProxy(ctx context.Context, in *UpdateServerGroupTCPReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerGroupService_UpdateServerGroupTCPReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) UpdateServerGroupUDPReverseProxy(ctx context.Context, in *UpdateServerGroupUDPReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerGroupService_UpdateServerGroupUDPReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindEnabledServerGroupConfigInfo(ctx context.Context, in *FindEnabledServerGroupConfigInfoRequest, opts ...grpc.CallOption) (*FindEnabledServerGroupConfigInfoResponse, error) {
	out := new(FindEnabledServerGroupConfigInfoResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindEnabledServerGroupConfigInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverGroupServiceClient) FindAndInitServerGroupWebConfig(ctx context.Context, in *FindAndInitServerGroupWebConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerGroupWebConfigResponse, error) {
	out := new(FindAndInitServerGroupWebConfigResponse)
	err := c.cc.Invoke(ctx, ServerGroupService_FindAndInitServerGroupWebConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerGroupServiceServer is the server API for ServerGroupService service.
// All implementations should embed UnimplementedServerGroupServiceServer
// for forward compatibility
type ServerGroupServiceServer interface {
	// 创建分组
	CreateServerGroup(context.Context, *CreateServerGroupRequest) (*CreateServerGroupResponse, error)
	// 修改分组
	UpdateServerGroup(context.Context, *UpdateServerGroupRequest) (*RPCSuccess, error)
	// 删除分组
	DeleteServerGroup(context.Context, *DeleteServerGroupRequest) (*RPCSuccess, error)
	// 查询所有分组
	FindAllEnabledServerGroups(context.Context, *FindAllEnabledServerGroupsRequest) (*FindAllEnabledServerGroupsResponse, error)
	// 修改分组排序
	UpdateServerGroupOrders(context.Context, *UpdateServerGroupOrdersRequest) (*RPCSuccess, error)
	// 查找单个分组信息
	FindEnabledServerGroup(context.Context, *FindEnabledServerGroupRequest) (*FindEnabledServerGroupResponse, error)
	// 查找HTTP反向代理设置
	FindAndInitServerGroupHTTPReverseProxyConfig(context.Context, *FindAndInitServerGroupHTTPReverseProxyConfigRequest) (*FindAndInitServerGroupHTTPReverseProxyConfigResponse, error)
	// 查找TCP反向代理设置
	FindAndInitServerGroupTCPReverseProxyConfig(context.Context, *FindAndInitServerGroupTCPReverseProxyConfigRequest) (*FindAndInitServerGroupTCPReverseProxyConfigResponse, error)
	// 查找UDP反向代理设置
	FindAndInitServerGroupUDPReverseProxyConfig(context.Context, *FindAndInitServerGroupUDPReverseProxyConfigRequest) (*FindAndInitServerGroupUDPReverseProxyConfigResponse, error)
	// 修改分组的HTTP反向代理设置
	UpdateServerGroupHTTPReverseProxy(context.Context, *UpdateServerGroupHTTPReverseProxyRequest) (*RPCSuccess, error)
	// 修改分组的TCP反向代理设置
	UpdateServerGroupTCPReverseProxy(context.Context, *UpdateServerGroupTCPReverseProxyRequest) (*RPCSuccess, error)
	// 修改分组的UDP反向代理设置
	UpdateServerGroupUDPReverseProxy(context.Context, *UpdateServerGroupUDPReverseProxyRequest) (*RPCSuccess, error)
	// 取得分组的配置概要信息
	FindEnabledServerGroupConfigInfo(context.Context, *FindEnabledServerGroupConfigInfoRequest) (*FindEnabledServerGroupConfigInfoResponse, error)
	// 初始化Web设置
	FindAndInitServerGroupWebConfig(context.Context, *FindAndInitServerGroupWebConfigRequest) (*FindAndInitServerGroupWebConfigResponse, error)
}

// UnimplementedServerGroupServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerGroupServiceServer struct {
}

func (UnimplementedServerGroupServiceServer) CreateServerGroup(context.Context, *CreateServerGroupRequest) (*CreateServerGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServerGroup not implemented")
}
func (UnimplementedServerGroupServiceServer) UpdateServerGroup(context.Context, *UpdateServerGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerGroup not implemented")
}
func (UnimplementedServerGroupServiceServer) DeleteServerGroup(context.Context, *DeleteServerGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServerGroup not implemented")
}
func (UnimplementedServerGroupServiceServer) FindAllEnabledServerGroups(context.Context, *FindAllEnabledServerGroupsRequest) (*FindAllEnabledServerGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledServerGroups not implemented")
}
func (UnimplementedServerGroupServiceServer) UpdateServerGroupOrders(context.Context, *UpdateServerGroupOrdersRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerGroupOrders not implemented")
}
func (UnimplementedServerGroupServiceServer) FindEnabledServerGroup(context.Context, *FindEnabledServerGroupRequest) (*FindEnabledServerGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerGroup not implemented")
}
func (UnimplementedServerGroupServiceServer) FindAndInitServerGroupHTTPReverseProxyConfig(context.Context, *FindAndInitServerGroupHTTPReverseProxyConfigRequest) (*FindAndInitServerGroupHTTPReverseProxyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAndInitServerGroupHTTPReverseProxyConfig not implemented")
}
func (UnimplementedServerGroupServiceServer) FindAndInitServerGroupTCPReverseProxyConfig(context.Context, *FindAndInitServerGroupTCPReverseProxyConfigRequest) (*FindAndInitServerGroupTCPReverseProxyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAndInitServerGroupTCPReverseProxyConfig not implemented")
}
func (UnimplementedServerGroupServiceServer) FindAndInitServerGroupUDPReverseProxyConfig(context.Context, *FindAndInitServerGroupUDPReverseProxyConfigRequest) (*FindAndInitServerGroupUDPReverseProxyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAndInitServerGroupUDPReverseProxyConfig not implemented")
}
func (UnimplementedServerGroupServiceServer) UpdateServerGroupHTTPReverseProxy(context.Context, *UpdateServerGroupHTTPReverseProxyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerGroupHTTPReverseProxy not implemented")
}
func (UnimplementedServerGroupServiceServer) UpdateServerGroupTCPReverseProxy(context.Context, *UpdateServerGroupTCPReverseProxyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerGroupTCPReverseProxy not implemented")
}
func (UnimplementedServerGroupServiceServer) UpdateServerGroupUDPReverseProxy(context.Context, *UpdateServerGroupUDPReverseProxyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerGroupUDPReverseProxy not implemented")
}
func (UnimplementedServerGroupServiceServer) FindEnabledServerGroupConfigInfo(context.Context, *FindEnabledServerGroupConfigInfoRequest) (*FindEnabledServerGroupConfigInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerGroupConfigInfo not implemented")
}
func (UnimplementedServerGroupServiceServer) FindAndInitServerGroupWebConfig(context.Context, *FindAndInitServerGroupWebConfigRequest) (*FindAndInitServerGroupWebConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAndInitServerGroupWebConfig not implemented")
}

// UnsafeServerGroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerGroupServiceServer will
// result in compilation errors.
type UnsafeServerGroupServiceServer interface {
	mustEmbedUnimplementedServerGroupServiceServer()
}

func RegisterServerGroupServiceServer(s grpc.ServiceRegistrar, srv ServerGroupServiceServer) {
	s.RegisterService(&ServerGroupService_ServiceDesc, srv)
}

func _ServerGroupService_CreateServerGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServerGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).CreateServerGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_CreateServerGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).CreateServerGroup(ctx, req.(*CreateServerGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_UpdateServerGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).UpdateServerGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_UpdateServerGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).UpdateServerGroup(ctx, req.(*UpdateServerGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_DeleteServerGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServerGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).DeleteServerGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_DeleteServerGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).DeleteServerGroup(ctx, req.(*DeleteServerGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindAllEnabledServerGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledServerGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindAllEnabledServerGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindAllEnabledServerGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindAllEnabledServerGroups(ctx, req.(*FindAllEnabledServerGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_UpdateServerGroupOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerGroupOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).UpdateServerGroupOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_UpdateServerGroupOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).UpdateServerGroupOrders(ctx, req.(*UpdateServerGroupOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindEnabledServerGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindEnabledServerGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindEnabledServerGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindEnabledServerGroup(ctx, req.(*FindEnabledServerGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindAndInitServerGroupHTTPReverseProxyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAndInitServerGroupHTTPReverseProxyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupHTTPReverseProxyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindAndInitServerGroupHTTPReverseProxyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupHTTPReverseProxyConfig(ctx, req.(*FindAndInitServerGroupHTTPReverseProxyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindAndInitServerGroupTCPReverseProxyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAndInitServerGroupTCPReverseProxyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupTCPReverseProxyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindAndInitServerGroupTCPReverseProxyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupTCPReverseProxyConfig(ctx, req.(*FindAndInitServerGroupTCPReverseProxyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindAndInitServerGroupUDPReverseProxyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAndInitServerGroupUDPReverseProxyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupUDPReverseProxyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindAndInitServerGroupUDPReverseProxyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupUDPReverseProxyConfig(ctx, req.(*FindAndInitServerGroupUDPReverseProxyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_UpdateServerGroupHTTPReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerGroupHTTPReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).UpdateServerGroupHTTPReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_UpdateServerGroupHTTPReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).UpdateServerGroupHTTPReverseProxy(ctx, req.(*UpdateServerGroupHTTPReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_UpdateServerGroupTCPReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerGroupTCPReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).UpdateServerGroupTCPReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_UpdateServerGroupTCPReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).UpdateServerGroupTCPReverseProxy(ctx, req.(*UpdateServerGroupTCPReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_UpdateServerGroupUDPReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerGroupUDPReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).UpdateServerGroupUDPReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_UpdateServerGroupUDPReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).UpdateServerGroupUDPReverseProxy(ctx, req.(*UpdateServerGroupUDPReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindEnabledServerGroupConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerGroupConfigInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindEnabledServerGroupConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindEnabledServerGroupConfigInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindEnabledServerGroupConfigInfo(ctx, req.(*FindEnabledServerGroupConfigInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerGroupService_FindAndInitServerGroupWebConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAndInitServerGroupWebConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupWebConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerGroupService_FindAndInitServerGroupWebConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerGroupServiceServer).FindAndInitServerGroupWebConfig(ctx, req.(*FindAndInitServerGroupWebConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerGroupService_ServiceDesc is the grpc.ServiceDesc for ServerGroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerGroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerGroupService",
	HandlerType: (*ServerGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createServerGroup",
			Handler:    _ServerGroupService_CreateServerGroup_Handler,
		},
		{
			MethodName: "updateServerGroup",
			Handler:    _ServerGroupService_UpdateServerGroup_Handler,
		},
		{
			MethodName: "deleteServerGroup",
			Handler:    _ServerGroupService_DeleteServerGroup_Handler,
		},
		{
			MethodName: "findAllEnabledServerGroups",
			Handler:    _ServerGroupService_FindAllEnabledServerGroups_Handler,
		},
		{
			MethodName: "updateServerGroupOrders",
			Handler:    _ServerGroupService_UpdateServerGroupOrders_Handler,
		},
		{
			MethodName: "findEnabledServerGroup",
			Handler:    _ServerGroupService_FindEnabledServerGroup_Handler,
		},
		{
			MethodName: "findAndInitServerGroupHTTPReverseProxyConfig",
			Handler:    _ServerGroupService_FindAndInitServerGroupHTTPReverseProxyConfig_Handler,
		},
		{
			MethodName: "findAndInitServerGroupTCPReverseProxyConfig",
			Handler:    _ServerGroupService_FindAndInitServerGroupTCPReverseProxyConfig_Handler,
		},
		{
			MethodName: "findAndInitServerGroupUDPReverseProxyConfig",
			Handler:    _ServerGroupService_FindAndInitServerGroupUDPReverseProxyConfig_Handler,
		},
		{
			MethodName: "updateServerGroupHTTPReverseProxy",
			Handler:    _ServerGroupService_UpdateServerGroupHTTPReverseProxy_Handler,
		},
		{
			MethodName: "updateServerGroupTCPReverseProxy",
			Handler:    _ServerGroupService_UpdateServerGroupTCPReverseProxy_Handler,
		},
		{
			MethodName: "updateServerGroupUDPReverseProxy",
			Handler:    _ServerGroupService_UpdateServerGroupUDPReverseProxy_Handler,
		},
		{
			MethodName: "findEnabledServerGroupConfigInfo",
			Handler:    _ServerGroupService_FindEnabledServerGroupConfigInfo_Handler,
		},
		{
			MethodName: "findAndInitServerGroupWebConfig",
			Handler:    _ServerGroupService_FindAndInitServerGroupWebConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_group.proto",
}
