// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_region_province_monthly_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerRegionProvinceMonthlyStatService_FindTopServerRegionProvinceMonthlyStats_FullMethodName = "/pb.ServerRegionProvinceMonthlyStatService/findTopServerRegionProvinceMonthlyStats"
)

// ServerRegionProvinceMonthlyStatServiceClient is the client API for ServerRegionProvinceMonthlyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerRegionProvinceMonthlyStatServiceClient interface {
	// 查找前N个省份
	FindTopServerRegionProvinceMonthlyStats(ctx context.Context, in *FindTopServerRegionProvinceMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerRegionProvinceMonthlyStatsResponse, error)
}

type serverRegionProvinceMonthlyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerRegionProvinceMonthlyStatServiceClient(cc grpc.ClientConnInterface) ServerRegionProvinceMonthlyStatServiceClient {
	return &serverRegionProvinceMonthlyStatServiceClient{cc}
}

func (c *serverRegionProvinceMonthlyStatServiceClient) FindTopServerRegionProvinceMonthlyStats(ctx context.Context, in *FindTopServerRegionProvinceMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerRegionProvinceMonthlyStatsResponse, error) {
	out := new(FindTopServerRegionProvinceMonthlyStatsResponse)
	err := c.cc.Invoke(ctx, ServerRegionProvinceMonthlyStatService_FindTopServerRegionProvinceMonthlyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerRegionProvinceMonthlyStatServiceServer is the server API for ServerRegionProvinceMonthlyStatService service.
// All implementations should embed UnimplementedServerRegionProvinceMonthlyStatServiceServer
// for forward compatibility
type ServerRegionProvinceMonthlyStatServiceServer interface {
	// 查找前N个省份
	FindTopServerRegionProvinceMonthlyStats(context.Context, *FindTopServerRegionProvinceMonthlyStatsRequest) (*FindTopServerRegionProvinceMonthlyStatsResponse, error)
}

// UnimplementedServerRegionProvinceMonthlyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerRegionProvinceMonthlyStatServiceServer struct {
}

func (UnimplementedServerRegionProvinceMonthlyStatServiceServer) FindTopServerRegionProvinceMonthlyStats(context.Context, *FindTopServerRegionProvinceMonthlyStatsRequest) (*FindTopServerRegionProvinceMonthlyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTopServerRegionProvinceMonthlyStats not implemented")
}

// UnsafeServerRegionProvinceMonthlyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerRegionProvinceMonthlyStatServiceServer will
// result in compilation errors.
type UnsafeServerRegionProvinceMonthlyStatServiceServer interface {
	mustEmbedUnimplementedServerRegionProvinceMonthlyStatServiceServer()
}

func RegisterServerRegionProvinceMonthlyStatServiceServer(s grpc.ServiceRegistrar, srv ServerRegionProvinceMonthlyStatServiceServer) {
	s.RegisterService(&ServerRegionProvinceMonthlyStatService_ServiceDesc, srv)
}

func _ServerRegionProvinceMonthlyStatService_FindTopServerRegionProvinceMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTopServerRegionProvinceMonthlyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerRegionProvinceMonthlyStatServiceServer).FindTopServerRegionProvinceMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerRegionProvinceMonthlyStatService_FindTopServerRegionProvinceMonthlyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerRegionProvinceMonthlyStatServiceServer).FindTopServerRegionProvinceMonthlyStats(ctx, req.(*FindTopServerRegionProvinceMonthlyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerRegionProvinceMonthlyStatService_ServiceDesc is the grpc.ServiceDesc for ServerRegionProvinceMonthlyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerRegionProvinceMonthlyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerRegionProvinceMonthlyStatService",
	HandlerType: (*ServerRegionProvinceMonthlyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findTopServerRegionProvinceMonthlyStats",
			Handler:    _ServerRegionProvinceMonthlyStatService_FindTopServerRegionProvinceMonthlyStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_region_province_monthly_stat.proto",
}
