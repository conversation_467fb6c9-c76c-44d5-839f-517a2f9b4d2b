// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_order.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户订单
type UserOrder struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	OrderMethodId int64                  `protobuf:"varint,5,opt,name=orderMethodId,proto3" json:"orderMethodId,omitempty"`
	Status        string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	Amount        float32                `protobuf:"fixed32,7,opt,name=amount,proto3" json:"amount,omitempty"`
	ParamsJSON    []byte                 `protobuf:"bytes,8,opt,name=paramsJSON,proto3" json:"paramsJSON,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,9,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	CancelledAt   int64                  `protobuf:"varint,10,opt,name=cancelledAt,proto3" json:"cancelledAt,omitempty"`
	FinishedAt    int64                  `protobuf:"varint,11,opt,name=finishedAt,proto3" json:"finishedAt,omitempty"`
	IsExpired     bool                   `protobuf:"varint,12,opt,name=isExpired,proto3" json:"isExpired,omitempty"`
	User          *User                  `protobuf:"bytes,30,opt,name=user,proto3" json:"user,omitempty"`
	OrderMethod   *OrderMethod           `protobuf:"bytes,31,opt,name=orderMethod,proto3" json:"orderMethod,omitempty"`
	CanPay        bool                   `protobuf:"varint,32,opt,name=canPay,proto3" json:"canPay,omitempty"` // 是否可以支付
	PayURL        string                 `protobuf:"bytes,33,opt,name=payURL,proto3" json:"payURL,omitempty"`  // 构造后的支付URL
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserOrder) Reset() {
	*x = UserOrder{}
	mi := &file_models_model_user_order_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserOrder) ProtoMessage() {}

func (x *UserOrder) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_order_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserOrder.ProtoReflect.Descriptor instead.
func (*UserOrder) Descriptor() ([]byte, []int) {
	return file_models_model_user_order_proto_rawDescGZIP(), []int{0}
}

func (x *UserOrder) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserOrder) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserOrder) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserOrder) GetOrderMethodId() int64 {
	if x != nil {
		return x.OrderMethodId
	}
	return 0
}

func (x *UserOrder) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserOrder) GetAmount() float32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UserOrder) GetParamsJSON() []byte {
	if x != nil {
		return x.ParamsJSON
	}
	return nil
}

func (x *UserOrder) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserOrder) GetCancelledAt() int64 {
	if x != nil {
		return x.CancelledAt
	}
	return 0
}

func (x *UserOrder) GetFinishedAt() int64 {
	if x != nil {
		return x.FinishedAt
	}
	return 0
}

func (x *UserOrder) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *UserOrder) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserOrder) GetOrderMethod() *OrderMethod {
	if x != nil {
		return x.OrderMethod
	}
	return nil
}

func (x *UserOrder) GetCanPay() bool {
	if x != nil {
		return x.CanPay
	}
	return false
}

func (x *UserOrder) GetPayURL() string {
	if x != nil {
		return x.PayURL
	}
	return ""
}

var File_models_model_user_order_proto protoreflect.FileDescriptor

const file_models_model_user_order_proto_rawDesc = "" +
	"\n" +
	"\x1dmodels/model_user_order.proto\x12\x02pb\x1a\x17models/model_user.proto\x1a\x1fmodels/model_order_method.proto\"\xc0\x03\n" +
	"\tUserOrder\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12$\n" +
	"\rorderMethodId\x18\x05 \x01(\x03R\rorderMethodId\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\x12\x16\n" +
	"\x06amount\x18\a \x01(\x02R\x06amount\x12\x1e\n" +
	"\n" +
	"paramsJSON\x18\b \x01(\fR\n" +
	"paramsJSON\x12\x1c\n" +
	"\tcreatedAt\x18\t \x01(\x03R\tcreatedAt\x12 \n" +
	"\vcancelledAt\x18\n" +
	" \x01(\x03R\vcancelledAt\x12\x1e\n" +
	"\n" +
	"finishedAt\x18\v \x01(\x03R\n" +
	"finishedAt\x12\x1c\n" +
	"\tisExpired\x18\f \x01(\bR\tisExpired\x12\x1c\n" +
	"\x04user\x18\x1e \x01(\v2\b.pb.UserR\x04user\x121\n" +
	"\vorderMethod\x18\x1f \x01(\v2\x0f.pb.OrderMethodR\vorderMethod\x12\x16\n" +
	"\x06canPay\x18  \x01(\bR\x06canPay\x12\x16\n" +
	"\x06payURL\x18! \x01(\tR\x06payURLB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_order_proto_rawDescOnce sync.Once
	file_models_model_user_order_proto_rawDescData []byte
)

func file_models_model_user_order_proto_rawDescGZIP() []byte {
	file_models_model_user_order_proto_rawDescOnce.Do(func() {
		file_models_model_user_order_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_order_proto_rawDesc), len(file_models_model_user_order_proto_rawDesc)))
	})
	return file_models_model_user_order_proto_rawDescData
}

var file_models_model_user_order_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_order_proto_goTypes = []any{
	(*UserOrder)(nil),   // 0: pb.UserOrder
	(*User)(nil),        // 1: pb.User
	(*OrderMethod)(nil), // 2: pb.OrderMethod
}
var file_models_model_user_order_proto_depIdxs = []int32{
	1, // 0: pb.UserOrder.user:type_name -> pb.User
	2, // 1: pb.UserOrder.orderMethod:type_name -> pb.OrderMethod
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_models_model_user_order_proto_init() }
func file_models_model_user_order_proto_init() {
	if File_models_model_user_order_proto != nil {
		return
	}
	file_models_model_user_proto_init()
	file_models_model_order_method_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_order_proto_rawDesc), len(file_models_model_user_order_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_order_proto_goTypes,
		DependencyIndexes: file_models_model_user_order_proto_depIdxs,
		MessageInfos:      file_models_model_user_order_proto_msgTypes,
	}.Build()
	File_models_model_user_order_proto = out.File
	file_models_model_user_order_proto_goTypes = nil
	file_models_model_user_order_proto_depIdxs = nil
}
