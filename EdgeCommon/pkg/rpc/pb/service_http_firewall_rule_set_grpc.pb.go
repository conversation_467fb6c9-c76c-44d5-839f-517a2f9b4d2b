// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_firewall_rule_set.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPFirewallRuleSetService_CreateOrUpdateHTTPFirewallRuleSetFromConfig_FullMethodName = "/pb.HTTPFirewallRuleSetService/createOrUpdateHTTPFirewallRuleSetFromConfig"
	HTTPFirewallRuleSetService_UpdateHTTPFirewallRuleSetIsOn_FullMethodName               = "/pb.HTTPFirewallRuleSetService/updateHTTPFirewallRuleSetIsOn"
	HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSetConfig_FullMethodName        = "/pb.HTTPFirewallRuleSetService/findEnabledHTTPFirewallRuleSetConfig"
	HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSet_FullMethodName              = "/pb.HTTPFirewallRuleSetService/findEnabledHTTPFirewallRuleSet"
)

// HTTPFirewallRuleSetServiceClient is the client API for HTTPFirewallRuleSetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPFirewallRuleSetServiceClient interface {
	// 根据配置创建或修改规则集
	CreateOrUpdateHTTPFirewallRuleSetFromConfig(ctx context.Context, in *CreateOrUpdateHTTPFirewallRuleSetFromConfigRequest, opts ...grpc.CallOption) (*CreateOrUpdateHTTPFirewallRuleSetFromConfigResponse, error)
	// 设置开启状态
	UpdateHTTPFirewallRuleSetIsOn(ctx context.Context, in *UpdateHTTPFirewallRuleSetIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找规则集配置
	FindEnabledHTTPFirewallRuleSetConfig(ctx context.Context, in *FindEnabledHTTPFirewallRuleSetConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleSetConfigResponse, error)
	// 查找规则集信息
	FindEnabledHTTPFirewallRuleSet(ctx context.Context, in *FindEnabledHTTPFirewallRuleSetRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleSetResponse, error)
}

type hTTPFirewallRuleSetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPFirewallRuleSetServiceClient(cc grpc.ClientConnInterface) HTTPFirewallRuleSetServiceClient {
	return &hTTPFirewallRuleSetServiceClient{cc}
}

func (c *hTTPFirewallRuleSetServiceClient) CreateOrUpdateHTTPFirewallRuleSetFromConfig(ctx context.Context, in *CreateOrUpdateHTTPFirewallRuleSetFromConfigRequest, opts ...grpc.CallOption) (*CreateOrUpdateHTTPFirewallRuleSetFromConfigResponse, error) {
	out := new(CreateOrUpdateHTTPFirewallRuleSetFromConfigResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleSetService_CreateOrUpdateHTTPFirewallRuleSetFromConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleSetServiceClient) UpdateHTTPFirewallRuleSetIsOn(ctx context.Context, in *UpdateHTTPFirewallRuleSetIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleSetService_UpdateHTTPFirewallRuleSetIsOn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleSetServiceClient) FindEnabledHTTPFirewallRuleSetConfig(ctx context.Context, in *FindEnabledHTTPFirewallRuleSetConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleSetConfigResponse, error) {
	out := new(FindEnabledHTTPFirewallRuleSetConfigResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSetConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleSetServiceClient) FindEnabledHTTPFirewallRuleSet(ctx context.Context, in *FindEnabledHTTPFirewallRuleSetRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleSetResponse, error) {
	out := new(FindEnabledHTTPFirewallRuleSetResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPFirewallRuleSetServiceServer is the server API for HTTPFirewallRuleSetService service.
// All implementations should embed UnimplementedHTTPFirewallRuleSetServiceServer
// for forward compatibility
type HTTPFirewallRuleSetServiceServer interface {
	// 根据配置创建或修改规则集
	CreateOrUpdateHTTPFirewallRuleSetFromConfig(context.Context, *CreateOrUpdateHTTPFirewallRuleSetFromConfigRequest) (*CreateOrUpdateHTTPFirewallRuleSetFromConfigResponse, error)
	// 设置开启状态
	UpdateHTTPFirewallRuleSetIsOn(context.Context, *UpdateHTTPFirewallRuleSetIsOnRequest) (*RPCSuccess, error)
	// 查找规则集配置
	FindEnabledHTTPFirewallRuleSetConfig(context.Context, *FindEnabledHTTPFirewallRuleSetConfigRequest) (*FindEnabledHTTPFirewallRuleSetConfigResponse, error)
	// 查找规则集信息
	FindEnabledHTTPFirewallRuleSet(context.Context, *FindEnabledHTTPFirewallRuleSetRequest) (*FindEnabledHTTPFirewallRuleSetResponse, error)
}

// UnimplementedHTTPFirewallRuleSetServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPFirewallRuleSetServiceServer struct {
}

func (UnimplementedHTTPFirewallRuleSetServiceServer) CreateOrUpdateHTTPFirewallRuleSetFromConfig(context.Context, *CreateOrUpdateHTTPFirewallRuleSetFromConfigRequest) (*CreateOrUpdateHTTPFirewallRuleSetFromConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateHTTPFirewallRuleSetFromConfig not implemented")
}
func (UnimplementedHTTPFirewallRuleSetServiceServer) UpdateHTTPFirewallRuleSetIsOn(context.Context, *UpdateHTTPFirewallRuleSetIsOnRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallRuleSetIsOn not implemented")
}
func (UnimplementedHTTPFirewallRuleSetServiceServer) FindEnabledHTTPFirewallRuleSetConfig(context.Context, *FindEnabledHTTPFirewallRuleSetConfigRequest) (*FindEnabledHTTPFirewallRuleSetConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPFirewallRuleSetConfig not implemented")
}
func (UnimplementedHTTPFirewallRuleSetServiceServer) FindEnabledHTTPFirewallRuleSet(context.Context, *FindEnabledHTTPFirewallRuleSetRequest) (*FindEnabledHTTPFirewallRuleSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPFirewallRuleSet not implemented")
}

// UnsafeHTTPFirewallRuleSetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPFirewallRuleSetServiceServer will
// result in compilation errors.
type UnsafeHTTPFirewallRuleSetServiceServer interface {
	mustEmbedUnimplementedHTTPFirewallRuleSetServiceServer()
}

func RegisterHTTPFirewallRuleSetServiceServer(s grpc.ServiceRegistrar, srv HTTPFirewallRuleSetServiceServer) {
	s.RegisterService(&HTTPFirewallRuleSetService_ServiceDesc, srv)
}

func _HTTPFirewallRuleSetService_CreateOrUpdateHTTPFirewallRuleSetFromConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateHTTPFirewallRuleSetFromConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleSetServiceServer).CreateOrUpdateHTTPFirewallRuleSetFromConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleSetService_CreateOrUpdateHTTPFirewallRuleSetFromConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleSetServiceServer).CreateOrUpdateHTTPFirewallRuleSetFromConfig(ctx, req.(*CreateOrUpdateHTTPFirewallRuleSetFromConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleSetService_UpdateHTTPFirewallRuleSetIsOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallRuleSetIsOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleSetServiceServer).UpdateHTTPFirewallRuleSetIsOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleSetService_UpdateHTTPFirewallRuleSetIsOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleSetServiceServer).UpdateHTTPFirewallRuleSetIsOn(ctx, req.(*UpdateHTTPFirewallRuleSetIsOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPFirewallRuleSetConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleSetServiceServer).FindEnabledHTTPFirewallRuleSetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleSetServiceServer).FindEnabledHTTPFirewallRuleSetConfig(ctx, req.(*FindEnabledHTTPFirewallRuleSetConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPFirewallRuleSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleSetServiceServer).FindEnabledHTTPFirewallRuleSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleSetServiceServer).FindEnabledHTTPFirewallRuleSet(ctx, req.(*FindEnabledHTTPFirewallRuleSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPFirewallRuleSetService_ServiceDesc is the grpc.ServiceDesc for HTTPFirewallRuleSetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPFirewallRuleSetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPFirewallRuleSetService",
	HandlerType: (*HTTPFirewallRuleSetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createOrUpdateHTTPFirewallRuleSetFromConfig",
			Handler:    _HTTPFirewallRuleSetService_CreateOrUpdateHTTPFirewallRuleSetFromConfig_Handler,
		},
		{
			MethodName: "updateHTTPFirewallRuleSetIsOn",
			Handler:    _HTTPFirewallRuleSetService_UpdateHTTPFirewallRuleSetIsOn_Handler,
		},
		{
			MethodName: "findEnabledHTTPFirewallRuleSetConfig",
			Handler:    _HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSetConfig_Handler,
		},
		{
			MethodName: "findEnabledHTTPFirewallRuleSet",
			Handler:    _HTTPFirewallRuleSetService_FindEnabledHTTPFirewallRuleSet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_firewall_rule_set.proto",
}
