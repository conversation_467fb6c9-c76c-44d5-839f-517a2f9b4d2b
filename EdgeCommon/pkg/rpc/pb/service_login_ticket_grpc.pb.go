// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_login_ticket.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LoginTicketService_CreateLoginTicket_FullMethodName        = "/pb.LoginTicketService/createLoginTicket"
	LoginTicketService_FindLoginTicketWithValue_FullMethodName = "/pb.LoginTicketService/findLoginTicketWithValue"
)

// LoginTicketServiceClient is the client API for LoginTicketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LoginTicketServiceClient interface {
	// 创建票据
	CreateLoginTicket(ctx context.Context, in *CreateLoginTicketRequest, opts ...grpc.CallOption) (*CreateLoginTicketResponse, error)
	// 查找票据
	// 查找成功后，会自动删除票据信息，所以票据信息只能查询一次
	FindLoginTicketWithValue(ctx context.Context, in *FindLoginTicketWithValueRequest, opts ...grpc.CallOption) (*FindLoginTicketWithValueResponse, error)
}

type loginTicketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLoginTicketServiceClient(cc grpc.ClientConnInterface) LoginTicketServiceClient {
	return &loginTicketServiceClient{cc}
}

func (c *loginTicketServiceClient) CreateLoginTicket(ctx context.Context, in *CreateLoginTicketRequest, opts ...grpc.CallOption) (*CreateLoginTicketResponse, error) {
	out := new(CreateLoginTicketResponse)
	err := c.cc.Invoke(ctx, LoginTicketService_CreateLoginTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loginTicketServiceClient) FindLoginTicketWithValue(ctx context.Context, in *FindLoginTicketWithValueRequest, opts ...grpc.CallOption) (*FindLoginTicketWithValueResponse, error) {
	out := new(FindLoginTicketWithValueResponse)
	err := c.cc.Invoke(ctx, LoginTicketService_FindLoginTicketWithValue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LoginTicketServiceServer is the server API for LoginTicketService service.
// All implementations should embed UnimplementedLoginTicketServiceServer
// for forward compatibility
type LoginTicketServiceServer interface {
	// 创建票据
	CreateLoginTicket(context.Context, *CreateLoginTicketRequest) (*CreateLoginTicketResponse, error)
	// 查找票据
	// 查找成功后，会自动删除票据信息，所以票据信息只能查询一次
	FindLoginTicketWithValue(context.Context, *FindLoginTicketWithValueRequest) (*FindLoginTicketWithValueResponse, error)
}

// UnimplementedLoginTicketServiceServer should be embedded to have forward compatible implementations.
type UnimplementedLoginTicketServiceServer struct {
}

func (UnimplementedLoginTicketServiceServer) CreateLoginTicket(context.Context, *CreateLoginTicketRequest) (*CreateLoginTicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLoginTicket not implemented")
}
func (UnimplementedLoginTicketServiceServer) FindLoginTicketWithValue(context.Context, *FindLoginTicketWithValueRequest) (*FindLoginTicketWithValueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLoginTicketWithValue not implemented")
}

// UnsafeLoginTicketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LoginTicketServiceServer will
// result in compilation errors.
type UnsafeLoginTicketServiceServer interface {
	mustEmbedUnimplementedLoginTicketServiceServer()
}

func RegisterLoginTicketServiceServer(s grpc.ServiceRegistrar, srv LoginTicketServiceServer) {
	s.RegisterService(&LoginTicketService_ServiceDesc, srv)
}

func _LoginTicketService_CreateLoginTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLoginTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoginTicketServiceServer).CreateLoginTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoginTicketService_CreateLoginTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoginTicketServiceServer).CreateLoginTicket(ctx, req.(*CreateLoginTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoginTicketService_FindLoginTicketWithValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLoginTicketWithValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoginTicketServiceServer).FindLoginTicketWithValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoginTicketService_FindLoginTicketWithValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoginTicketServiceServer).FindLoginTicketWithValue(ctx, req.(*FindLoginTicketWithValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LoginTicketService_ServiceDesc is the grpc.ServiceDesc for LoginTicketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LoginTicketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.LoginTicketService",
	HandlerType: (*LoginTicketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createLoginTicket",
			Handler:    _LoginTicketService_CreateLoginTicket_Handler,
		},
		{
			MethodName: "findLoginTicketWithValue",
			Handler:    _LoginTicketService_FindLoginTicketWithValue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_login_ticket.proto",
}
