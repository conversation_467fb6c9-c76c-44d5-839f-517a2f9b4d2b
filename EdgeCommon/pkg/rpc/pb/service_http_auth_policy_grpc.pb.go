// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_auth_policy.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPAuthPolicyService_CreateHTTPAuthPolicy_FullMethodName      = "/pb.HTTPAuthPolicyService/createHTTPAuthPolicy"
	HTTPAuthPolicyService_UpdateHTTPAuthPolicy_FullMethodName      = "/pb.HTTPAuthPolicyService/updateHTTPAuthPolicy"
	HTTPAuthPolicyService_FindEnabledHTTPAuthPolicy_FullMethodName = "/pb.HTTPAuthPolicyService/findEnabledHTTPAuthPolicy"
)

// HTTPAuthPolicyServiceClient is the client API for HTTPAuthPolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPAuthPolicyServiceClient interface {
	// 创建策略
	CreateHTTPAuthPolicy(ctx context.Context, in *CreateHTTPAuthPolicyRequest, opts ...grpc.CallOption) (*CreateHTTPAuthPolicyResponse, error)
	// 修改策略
	UpdateHTTPAuthPolicy(ctx context.Context, in *UpdateHTTPAuthPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找策略信息
	FindEnabledHTTPAuthPolicy(ctx context.Context, in *FindEnabledHTTPAuthPolicyRequest, opts ...grpc.CallOption) (*FindEnabledHTTPAuthPolicyResponse, error)
}

type hTTPAuthPolicyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPAuthPolicyServiceClient(cc grpc.ClientConnInterface) HTTPAuthPolicyServiceClient {
	return &hTTPAuthPolicyServiceClient{cc}
}

func (c *hTTPAuthPolicyServiceClient) CreateHTTPAuthPolicy(ctx context.Context, in *CreateHTTPAuthPolicyRequest, opts ...grpc.CallOption) (*CreateHTTPAuthPolicyResponse, error) {
	out := new(CreateHTTPAuthPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPAuthPolicyService_CreateHTTPAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAuthPolicyServiceClient) UpdateHTTPAuthPolicy(ctx context.Context, in *UpdateHTTPAuthPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPAuthPolicyService_UpdateHTTPAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAuthPolicyServiceClient) FindEnabledHTTPAuthPolicy(ctx context.Context, in *FindEnabledHTTPAuthPolicyRequest, opts ...grpc.CallOption) (*FindEnabledHTTPAuthPolicyResponse, error) {
	out := new(FindEnabledHTTPAuthPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPAuthPolicyService_FindEnabledHTTPAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPAuthPolicyServiceServer is the server API for HTTPAuthPolicyService service.
// All implementations should embed UnimplementedHTTPAuthPolicyServiceServer
// for forward compatibility
type HTTPAuthPolicyServiceServer interface {
	// 创建策略
	CreateHTTPAuthPolicy(context.Context, *CreateHTTPAuthPolicyRequest) (*CreateHTTPAuthPolicyResponse, error)
	// 修改策略
	UpdateHTTPAuthPolicy(context.Context, *UpdateHTTPAuthPolicyRequest) (*RPCSuccess, error)
	// 查找策略信息
	FindEnabledHTTPAuthPolicy(context.Context, *FindEnabledHTTPAuthPolicyRequest) (*FindEnabledHTTPAuthPolicyResponse, error)
}

// UnimplementedHTTPAuthPolicyServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPAuthPolicyServiceServer struct {
}

func (UnimplementedHTTPAuthPolicyServiceServer) CreateHTTPAuthPolicy(context.Context, *CreateHTTPAuthPolicyRequest) (*CreateHTTPAuthPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPAuthPolicy not implemented")
}
func (UnimplementedHTTPAuthPolicyServiceServer) UpdateHTTPAuthPolicy(context.Context, *UpdateHTTPAuthPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPAuthPolicy not implemented")
}
func (UnimplementedHTTPAuthPolicyServiceServer) FindEnabledHTTPAuthPolicy(context.Context, *FindEnabledHTTPAuthPolicyRequest) (*FindEnabledHTTPAuthPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPAuthPolicy not implemented")
}

// UnsafeHTTPAuthPolicyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPAuthPolicyServiceServer will
// result in compilation errors.
type UnsafeHTTPAuthPolicyServiceServer interface {
	mustEmbedUnimplementedHTTPAuthPolicyServiceServer()
}

func RegisterHTTPAuthPolicyServiceServer(s grpc.ServiceRegistrar, srv HTTPAuthPolicyServiceServer) {
	s.RegisterService(&HTTPAuthPolicyService_ServiceDesc, srv)
}

func _HTTPAuthPolicyService_CreateHTTPAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAuthPolicyServiceServer).CreateHTTPAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAuthPolicyService_CreateHTTPAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAuthPolicyServiceServer).CreateHTTPAuthPolicy(ctx, req.(*CreateHTTPAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAuthPolicyService_UpdateHTTPAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAuthPolicyServiceServer).UpdateHTTPAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAuthPolicyService_UpdateHTTPAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAuthPolicyServiceServer).UpdateHTTPAuthPolicy(ctx, req.(*UpdateHTTPAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAuthPolicyService_FindEnabledHTTPAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAuthPolicyServiceServer).FindEnabledHTTPAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAuthPolicyService_FindEnabledHTTPAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAuthPolicyServiceServer).FindEnabledHTTPAuthPolicy(ctx, req.(*FindEnabledHTTPAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPAuthPolicyService_ServiceDesc is the grpc.ServiceDesc for HTTPAuthPolicyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPAuthPolicyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPAuthPolicyService",
	HandlerType: (*HTTPAuthPolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHTTPAuthPolicy",
			Handler:    _HTTPAuthPolicyService_CreateHTTPAuthPolicy_Handler,
		},
		{
			MethodName: "updateHTTPAuthPolicy",
			Handler:    _HTTPAuthPolicyService_UpdateHTTPAuthPolicy_Handler,
		},
		{
			MethodName: "findEnabledHTTPAuthPolicy",
			Handler:    _HTTPAuthPolicyService_FindEnabledHTTPAuthPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_auth_policy.proto",
}
