// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server_daily_stat.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务每日统计
type ServerDailyStat struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ServerId             int64                  `protobuf:"varint,1,opt,name=serverId,proto3" json:"serverId,omitempty"`
	UserId               int64                  `protobuf:"varint,16,opt,name=userId,proto3" json:"userId,omitempty"` // 用户ID
	NodeRegionId         int64                  `protobuf:"varint,2,opt,name=nodeRegionId,proto3" json:"nodeRegionId,omitempty"`
	Bytes                int64                  `protobuf:"varint,3,opt,name=bytes,proto3" json:"bytes,omitempty"`
	CachedBytes          int64                  `protobuf:"varint,5,opt,name=cachedBytes,proto3" json:"cachedBytes,omitempty"`
	CountRequests        int64                  `protobuf:"varint,6,opt,name=countRequests,proto3" json:"countRequests,omitempty"`
	CountCachedRequests  int64                  `protobuf:"varint,7,opt,name=countCachedRequests,proto3" json:"countCachedRequests,omitempty"`
	CreatedAt            int64                  `protobuf:"varint,4,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	CountAttackRequests  int64                  `protobuf:"varint,8,opt,name=countAttackRequests,proto3" json:"countAttackRequests,omitempty"`
	AttackBytes          int64                  `protobuf:"varint,9,opt,name=attackBytes,proto3" json:"attackBytes,omitempty"`
	CheckTrafficLimiting bool                   `protobuf:"varint,10,opt,name=checkTrafficLimiting,proto3" json:"checkTrafficLimiting,omitempty"`
	PlanId               int64                  `protobuf:"varint,11,opt,name=planId,proto3" json:"planId,omitempty"` // 套餐ID
	Day                  string                 `protobuf:"bytes,12,opt,name=day,proto3" json:"day,omitempty"`        // 日期 YYYYMMDD
	Hour                 string                 `protobuf:"bytes,13,opt,name=hour,proto3" json:"hour,omitempty"`
	TimeFrom             string                 `protobuf:"bytes,14,opt,name=timeFrom,proto3" json:"timeFrom,omitempty"`
	TimeTo               string                 `protobuf:"bytes,15,opt,name=timeTo,proto3" json:"timeTo,omitempty"`
	CountIPs             int64                  `protobuf:"varint,17,opt,name=countIPs,proto3" json:"countIPs,omitempty"` // 独立IP数量
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ServerDailyStat) Reset() {
	*x = ServerDailyStat{}
	mi := &file_models_model_server_daily_stat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerDailyStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerDailyStat) ProtoMessage() {}

func (x *ServerDailyStat) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_daily_stat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerDailyStat.ProtoReflect.Descriptor instead.
func (*ServerDailyStat) Descriptor() ([]byte, []int) {
	return file_models_model_server_daily_stat_proto_rawDescGZIP(), []int{0}
}

func (x *ServerDailyStat) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ServerDailyStat) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ServerDailyStat) GetNodeRegionId() int64 {
	if x != nil {
		return x.NodeRegionId
	}
	return 0
}

func (x *ServerDailyStat) GetBytes() int64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *ServerDailyStat) GetCachedBytes() int64 {
	if x != nil {
		return x.CachedBytes
	}
	return 0
}

func (x *ServerDailyStat) GetCountRequests() int64 {
	if x != nil {
		return x.CountRequests
	}
	return 0
}

func (x *ServerDailyStat) GetCountCachedRequests() int64 {
	if x != nil {
		return x.CountCachedRequests
	}
	return 0
}

func (x *ServerDailyStat) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ServerDailyStat) GetCountAttackRequests() int64 {
	if x != nil {
		return x.CountAttackRequests
	}
	return 0
}

func (x *ServerDailyStat) GetAttackBytes() int64 {
	if x != nil {
		return x.AttackBytes
	}
	return 0
}

func (x *ServerDailyStat) GetCheckTrafficLimiting() bool {
	if x != nil {
		return x.CheckTrafficLimiting
	}
	return false
}

func (x *ServerDailyStat) GetPlanId() int64 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *ServerDailyStat) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *ServerDailyStat) GetHour() string {
	if x != nil {
		return x.Hour
	}
	return ""
}

func (x *ServerDailyStat) GetTimeFrom() string {
	if x != nil {
		return x.TimeFrom
	}
	return ""
}

func (x *ServerDailyStat) GetTimeTo() string {
	if x != nil {
		return x.TimeTo
	}
	return ""
}

func (x *ServerDailyStat) GetCountIPs() int64 {
	if x != nil {
		return x.CountIPs
	}
	return 0
}

var File_models_model_server_daily_stat_proto protoreflect.FileDescriptor

const file_models_model_server_daily_stat_proto_rawDesc = "" +
	"\n" +
	"$models/model_server_daily_stat.proto\x12\x02pb\"\xad\x04\n" +
	"\x0fServerDailyStat\x12\x1a\n" +
	"\bserverId\x18\x01 \x01(\x03R\bserverId\x12\x16\n" +
	"\x06userId\x18\x10 \x01(\x03R\x06userId\x12\"\n" +
	"\fnodeRegionId\x18\x02 \x01(\x03R\fnodeRegionId\x12\x14\n" +
	"\x05bytes\x18\x03 \x01(\x03R\x05bytes\x12 \n" +
	"\vcachedBytes\x18\x05 \x01(\x03R\vcachedBytes\x12$\n" +
	"\rcountRequests\x18\x06 \x01(\x03R\rcountRequests\x120\n" +
	"\x13countCachedRequests\x18\a \x01(\x03R\x13countCachedRequests\x12\x1c\n" +
	"\tcreatedAt\x18\x04 \x01(\x03R\tcreatedAt\x120\n" +
	"\x13countAttackRequests\x18\b \x01(\x03R\x13countAttackRequests\x12 \n" +
	"\vattackBytes\x18\t \x01(\x03R\vattackBytes\x122\n" +
	"\x14checkTrafficLimiting\x18\n" +
	" \x01(\bR\x14checkTrafficLimiting\x12\x16\n" +
	"\x06planId\x18\v \x01(\x03R\x06planId\x12\x10\n" +
	"\x03day\x18\f \x01(\tR\x03day\x12\x12\n" +
	"\x04hour\x18\r \x01(\tR\x04hour\x12\x1a\n" +
	"\btimeFrom\x18\x0e \x01(\tR\btimeFrom\x12\x16\n" +
	"\x06timeTo\x18\x0f \x01(\tR\x06timeTo\x12\x1a\n" +
	"\bcountIPs\x18\x11 \x01(\x03R\bcountIPsB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_daily_stat_proto_rawDescOnce sync.Once
	file_models_model_server_daily_stat_proto_rawDescData []byte
)

func file_models_model_server_daily_stat_proto_rawDescGZIP() []byte {
	file_models_model_server_daily_stat_proto_rawDescOnce.Do(func() {
		file_models_model_server_daily_stat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_daily_stat_proto_rawDesc), len(file_models_model_server_daily_stat_proto_rawDesc)))
	})
	return file_models_model_server_daily_stat_proto_rawDescData
}

var file_models_model_server_daily_stat_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_daily_stat_proto_goTypes = []any{
	(*ServerDailyStat)(nil), // 0: pb.ServerDailyStat
}
var file_models_model_server_daily_stat_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_server_daily_stat_proto_init() }
func file_models_model_server_daily_stat_proto_init() {
	if File_models_model_server_daily_stat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_daily_stat_proto_rawDesc), len(file_models_model_server_daily_stat_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_daily_stat_proto_goTypes,
		DependencyIndexes: file_models_model_server_daily_stat_proto_depIdxs,
		MessageInfos:      file_models_model_server_daily_stat_proto_msgTypes,
	}.Build()
	File_models_model_server_daily_stat_proto = out.File
	file_models_model_server_daily_stat_proto_goTypes = nil
	file_models_model_server_daily_stat_proto_depIdxs = nil
}
