// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server_name_auditing_result.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerNameAuditingResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsOk          bool                   `protobuf:"varint,1,opt,name=isOk,proto3" json:"isOk,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,3,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerNameAuditingResult) Reset() {
	*x = ServerNameAuditingResult{}
	mi := &file_models_model_server_name_auditing_result_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerNameAuditingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerNameAuditingResult) ProtoMessage() {}

func (x *ServerNameAuditingResult) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_name_auditing_result_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerNameAuditingResult.ProtoReflect.Descriptor instead.
func (*ServerNameAuditingResult) Descriptor() ([]byte, []int) {
	return file_models_model_server_name_auditing_result_proto_rawDescGZIP(), []int{0}
}

func (x *ServerNameAuditingResult) GetIsOk() bool {
	if x != nil {
		return x.IsOk
	}
	return false
}

func (x *ServerNameAuditingResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ServerNameAuditingResult) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

var File_models_model_server_name_auditing_result_proto protoreflect.FileDescriptor

const file_models_model_server_name_auditing_result_proto_rawDesc = "" +
	"\n" +
	".models/model_server_name_auditing_result.proto\x12\x02pb\"d\n" +
	"\x18ServerNameAuditingResult\x12\x12\n" +
	"\x04isOk\x18\x01 \x01(\bR\x04isOk\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\x12\x1c\n" +
	"\tcreatedAt\x18\x03 \x01(\x03R\tcreatedAtB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_name_auditing_result_proto_rawDescOnce sync.Once
	file_models_model_server_name_auditing_result_proto_rawDescData []byte
)

func file_models_model_server_name_auditing_result_proto_rawDescGZIP() []byte {
	file_models_model_server_name_auditing_result_proto_rawDescOnce.Do(func() {
		file_models_model_server_name_auditing_result_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_name_auditing_result_proto_rawDesc), len(file_models_model_server_name_auditing_result_proto_rawDesc)))
	})
	return file_models_model_server_name_auditing_result_proto_rawDescData
}

var file_models_model_server_name_auditing_result_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_name_auditing_result_proto_goTypes = []any{
	(*ServerNameAuditingResult)(nil), // 0: pb.ServerNameAuditingResult
}
var file_models_model_server_name_auditing_result_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_server_name_auditing_result_proto_init() }
func file_models_model_server_name_auditing_result_proto_init() {
	if File_models_model_server_name_auditing_result_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_name_auditing_result_proto_rawDesc), len(file_models_model_server_name_auditing_result_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_name_auditing_result_proto_goTypes,
		DependencyIndexes: file_models_model_server_name_auditing_result_proto_depIdxs,
		MessageInfos:      file_models_model_server_name_auditing_result_proto_msgTypes,
	}.Build()
	File_models_model_server_name_auditing_result_proto = out.File
	file_models_model_server_name_auditing_result_proto_goTypes = nil
	file_models_model_server_name_auditing_result_proto_depIdxs = nil
}
