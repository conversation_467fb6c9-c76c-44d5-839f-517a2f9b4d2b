// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_record.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSRecordService_CreateNSRecord_FullMethodName                     = "/pb.NSRecordService/createNSRecord"
	NSRecordService_CreateNSRecords_FullMethodName                    = "/pb.NSRecordService/createNSRecords"
	NSRecordService_CreateNSRecordsWithDomainNames_FullMethodName     = "/pb.NSRecordService/createNSRecordsWithDomainNames"
	NSRecordService_UpdateNSRecordsWithDomainNames_FullMethodName     = "/pb.NSRecordService/updateNSRecordsWithDomainNames"
	NSRecordService_DeleteNSRecordsWithDomainNames_FullMethodName     = "/pb.NSRecordService/deleteNSRecordsWithDomainNames"
	NSRecordService_UpdateNSRecordsIsOnWithDomainNames_FullMethodName = "/pb.NSRecordService/updateNSRecordsIsOnWithDomainNames"
	NSRecordService_ImportNSRecords_FullMethodName                    = "/pb.NSRecordService/importNSRecords"
	NSRecordService_UpdateNSRecord_FullMethodName                     = "/pb.NSRecordService/updateNSRecord"
	NSRecordService_DeleteNSRecord_FullMethodName                     = "/pb.NSRecordService/deleteNSRecord"
	NSRecordService_CountAllNSRecords_FullMethodName                  = "/pb.NSRecordService/countAllNSRecords"
	NSRecordService_CountAllNSRecordsWithName_FullMethodName          = "/pb.NSRecordService/countAllNSRecordsWithName"
	NSRecordService_ListNSRecords_FullMethodName                      = "/pb.NSRecordService/listNSRecords"
	NSRecordService_FindNSRecord_FullMethodName                       = "/pb.NSRecordService/findNSRecord"
	NSRecordService_FindNSRecordWithNameAndType_FullMethodName        = "/pb.NSRecordService/findNSRecordWithNameAndType"
	NSRecordService_FindNSRecordsWithNameAndType_FullMethodName       = "/pb.NSRecordService/findNSRecordsWithNameAndType"
	NSRecordService_ListNSRecordsAfterVersion_FullMethodName          = "/pb.NSRecordService/listNSRecordsAfterVersion"
	NSRecordService_FindNSRecordHealthCheck_FullMethodName            = "/pb.NSRecordService/findNSRecordHealthCheck"
	NSRecordService_UpdateNSRecordHealthCheck_FullMethodName          = "/pb.NSRecordService/updateNSRecordHealthCheck"
	NSRecordService_UpdateNSRecordIsUp_FullMethodName                 = "/pb.NSRecordService/updateNSRecordIsUp"
)

// NSRecordServiceClient is the client API for NSRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSRecordServiceClient interface {
	// 创建记录
	CreateNSRecord(ctx context.Context, in *CreateNSRecordRequest, opts ...grpc.CallOption) (*CreateNSRecordResponse, error)
	// 批量创建记录
	CreateNSRecords(ctx context.Context, in *CreateNSRecordsRequest, opts ...grpc.CallOption) (*CreateNSRecordsResponse, error)
	// 为一组域名批量创建记录
	CreateNSRecordsWithDomainNames(ctx context.Context, in *CreateNSRecordsWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 批量修改一组域名的一组记录
	UpdateNSRecordsWithDomainNames(ctx context.Context, in *UpdateNSRecordsWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 批量删除一组域名的一组记录
	DeleteNSRecordsWithDomainNames(ctx context.Context, in *DeleteNSRecordsWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 批量一组域名的一组记录启用状态
	UpdateNSRecordsIsOnWithDomainNames(ctx context.Context, in *UpdateNSRecordsIsOnWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 导入域名解析
	ImportNSRecords(ctx context.Context, in *ImportNSRecordsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改记录
	UpdateNSRecord(ctx context.Context, in *UpdateNSRecordRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除记录
	DeleteNSRecord(ctx context.Context, in *DeleteNSRecordRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算记录数量
	CountAllNSRecords(ctx context.Context, in *CountAllNSRecordsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查询相同记录名的记录数
	CountAllNSRecordsWithName(ctx context.Context, in *CountAllNSRecordsWithNameRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 读取单页记录
	ListNSRecords(ctx context.Context, in *ListNSRecordsRequest, opts ...grpc.CallOption) (*ListNSRecordsResponse, error)
	// 查询单个记录信息
	FindNSRecord(ctx context.Context, in *FindNSRecordRequest, opts ...grpc.CallOption) (*FindNSRecordResponse, error)
	// 使用名称和类型查询单个记录信息
	FindNSRecordWithNameAndType(ctx context.Context, in *FindNSRecordWithNameAndTypeRequest, opts ...grpc.CallOption) (*FindNSRecordWithNameAndTypeResponse, error)
	// 使用名称和类型查询多个记录信息
	FindNSRecordsWithNameAndType(ctx context.Context, in *FindNSRecordsWithNameAndTypeRequest, opts ...grpc.CallOption) (*FindNSRecordsWithNameAndTypeResponse, error)
	// 根据版本列出一组记录
	ListNSRecordsAfterVersion(ctx context.Context, in *ListNSRecordsAfterVersionRequest, opts ...grpc.CallOption) (*ListNSRecordsAfterVersionResponse, error)
	// 查询记录健康检查设置
	FindNSRecordHealthCheck(ctx context.Context, in *FindNSRecordHealthCheckRequest, opts ...grpc.CallOption) (*FindNSRecordHealthCheckResponse, error)
	// 修改记录健康检查设置
	UpdateNSRecordHealthCheck(ctx context.Context, in *UpdateNSRecordHealthCheckRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 手动修改记录在线状态
	UpdateNSRecordIsUp(ctx context.Context, in *UpdateNSRecordIsUpRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nSRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSRecordServiceClient(cc grpc.ClientConnInterface) NSRecordServiceClient {
	return &nSRecordServiceClient{cc}
}

func (c *nSRecordServiceClient) CreateNSRecord(ctx context.Context, in *CreateNSRecordRequest, opts ...grpc.CallOption) (*CreateNSRecordResponse, error) {
	out := new(CreateNSRecordResponse)
	err := c.cc.Invoke(ctx, NSRecordService_CreateNSRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) CreateNSRecords(ctx context.Context, in *CreateNSRecordsRequest, opts ...grpc.CallOption) (*CreateNSRecordsResponse, error) {
	out := new(CreateNSRecordsResponse)
	err := c.cc.Invoke(ctx, NSRecordService_CreateNSRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) CreateNSRecordsWithDomainNames(ctx context.Context, in *CreateNSRecordsWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_CreateNSRecordsWithDomainNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) UpdateNSRecordsWithDomainNames(ctx context.Context, in *UpdateNSRecordsWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_UpdateNSRecordsWithDomainNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) DeleteNSRecordsWithDomainNames(ctx context.Context, in *DeleteNSRecordsWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_DeleteNSRecordsWithDomainNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) UpdateNSRecordsIsOnWithDomainNames(ctx context.Context, in *UpdateNSRecordsIsOnWithDomainNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_UpdateNSRecordsIsOnWithDomainNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) ImportNSRecords(ctx context.Context, in *ImportNSRecordsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_ImportNSRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) UpdateNSRecord(ctx context.Context, in *UpdateNSRecordRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_UpdateNSRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) DeleteNSRecord(ctx context.Context, in *DeleteNSRecordRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_DeleteNSRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) CountAllNSRecords(ctx context.Context, in *CountAllNSRecordsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSRecordService_CountAllNSRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) CountAllNSRecordsWithName(ctx context.Context, in *CountAllNSRecordsWithNameRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSRecordService_CountAllNSRecordsWithName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) ListNSRecords(ctx context.Context, in *ListNSRecordsRequest, opts ...grpc.CallOption) (*ListNSRecordsResponse, error) {
	out := new(ListNSRecordsResponse)
	err := c.cc.Invoke(ctx, NSRecordService_ListNSRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) FindNSRecord(ctx context.Context, in *FindNSRecordRequest, opts ...grpc.CallOption) (*FindNSRecordResponse, error) {
	out := new(FindNSRecordResponse)
	err := c.cc.Invoke(ctx, NSRecordService_FindNSRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) FindNSRecordWithNameAndType(ctx context.Context, in *FindNSRecordWithNameAndTypeRequest, opts ...grpc.CallOption) (*FindNSRecordWithNameAndTypeResponse, error) {
	out := new(FindNSRecordWithNameAndTypeResponse)
	err := c.cc.Invoke(ctx, NSRecordService_FindNSRecordWithNameAndType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) FindNSRecordsWithNameAndType(ctx context.Context, in *FindNSRecordsWithNameAndTypeRequest, opts ...grpc.CallOption) (*FindNSRecordsWithNameAndTypeResponse, error) {
	out := new(FindNSRecordsWithNameAndTypeResponse)
	err := c.cc.Invoke(ctx, NSRecordService_FindNSRecordsWithNameAndType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) ListNSRecordsAfterVersion(ctx context.Context, in *ListNSRecordsAfterVersionRequest, opts ...grpc.CallOption) (*ListNSRecordsAfterVersionResponse, error) {
	out := new(ListNSRecordsAfterVersionResponse)
	err := c.cc.Invoke(ctx, NSRecordService_ListNSRecordsAfterVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) FindNSRecordHealthCheck(ctx context.Context, in *FindNSRecordHealthCheckRequest, opts ...grpc.CallOption) (*FindNSRecordHealthCheckResponse, error) {
	out := new(FindNSRecordHealthCheckResponse)
	err := c.cc.Invoke(ctx, NSRecordService_FindNSRecordHealthCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) UpdateNSRecordHealthCheck(ctx context.Context, in *UpdateNSRecordHealthCheckRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_UpdateNSRecordHealthCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRecordServiceClient) UpdateNSRecordIsUp(ctx context.Context, in *UpdateNSRecordIsUpRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRecordService_UpdateNSRecordIsUp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSRecordServiceServer is the server API for NSRecordService service.
// All implementations should embed UnimplementedNSRecordServiceServer
// for forward compatibility
type NSRecordServiceServer interface {
	// 创建记录
	CreateNSRecord(context.Context, *CreateNSRecordRequest) (*CreateNSRecordResponse, error)
	// 批量创建记录
	CreateNSRecords(context.Context, *CreateNSRecordsRequest) (*CreateNSRecordsResponse, error)
	// 为一组域名批量创建记录
	CreateNSRecordsWithDomainNames(context.Context, *CreateNSRecordsWithDomainNamesRequest) (*RPCSuccess, error)
	// 批量修改一组域名的一组记录
	UpdateNSRecordsWithDomainNames(context.Context, *UpdateNSRecordsWithDomainNamesRequest) (*RPCSuccess, error)
	// 批量删除一组域名的一组记录
	DeleteNSRecordsWithDomainNames(context.Context, *DeleteNSRecordsWithDomainNamesRequest) (*RPCSuccess, error)
	// 批量一组域名的一组记录启用状态
	UpdateNSRecordsIsOnWithDomainNames(context.Context, *UpdateNSRecordsIsOnWithDomainNamesRequest) (*RPCSuccess, error)
	// 导入域名解析
	ImportNSRecords(context.Context, *ImportNSRecordsRequest) (*RPCSuccess, error)
	// 修改记录
	UpdateNSRecord(context.Context, *UpdateNSRecordRequest) (*RPCSuccess, error)
	// 删除记录
	DeleteNSRecord(context.Context, *DeleteNSRecordRequest) (*RPCSuccess, error)
	// 计算记录数量
	CountAllNSRecords(context.Context, *CountAllNSRecordsRequest) (*RPCCountResponse, error)
	// 查询相同记录名的记录数
	CountAllNSRecordsWithName(context.Context, *CountAllNSRecordsWithNameRequest) (*RPCCountResponse, error)
	// 读取单页记录
	ListNSRecords(context.Context, *ListNSRecordsRequest) (*ListNSRecordsResponse, error)
	// 查询单个记录信息
	FindNSRecord(context.Context, *FindNSRecordRequest) (*FindNSRecordResponse, error)
	// 使用名称和类型查询单个记录信息
	FindNSRecordWithNameAndType(context.Context, *FindNSRecordWithNameAndTypeRequest) (*FindNSRecordWithNameAndTypeResponse, error)
	// 使用名称和类型查询多个记录信息
	FindNSRecordsWithNameAndType(context.Context, *FindNSRecordsWithNameAndTypeRequest) (*FindNSRecordsWithNameAndTypeResponse, error)
	// 根据版本列出一组记录
	ListNSRecordsAfterVersion(context.Context, *ListNSRecordsAfterVersionRequest) (*ListNSRecordsAfterVersionResponse, error)
	// 查询记录健康检查设置
	FindNSRecordHealthCheck(context.Context, *FindNSRecordHealthCheckRequest) (*FindNSRecordHealthCheckResponse, error)
	// 修改记录健康检查设置
	UpdateNSRecordHealthCheck(context.Context, *UpdateNSRecordHealthCheckRequest) (*RPCSuccess, error)
	// 手动修改记录在线状态
	UpdateNSRecordIsUp(context.Context, *UpdateNSRecordIsUpRequest) (*RPCSuccess, error)
}

// UnimplementedNSRecordServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSRecordServiceServer struct {
}

func (UnimplementedNSRecordServiceServer) CreateNSRecord(context.Context, *CreateNSRecordRequest) (*CreateNSRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSRecord not implemented")
}
func (UnimplementedNSRecordServiceServer) CreateNSRecords(context.Context, *CreateNSRecordsRequest) (*CreateNSRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSRecords not implemented")
}
func (UnimplementedNSRecordServiceServer) CreateNSRecordsWithDomainNames(context.Context, *CreateNSRecordsWithDomainNamesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSRecordsWithDomainNames not implemented")
}
func (UnimplementedNSRecordServiceServer) UpdateNSRecordsWithDomainNames(context.Context, *UpdateNSRecordsWithDomainNamesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRecordsWithDomainNames not implemented")
}
func (UnimplementedNSRecordServiceServer) DeleteNSRecordsWithDomainNames(context.Context, *DeleteNSRecordsWithDomainNamesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSRecordsWithDomainNames not implemented")
}
func (UnimplementedNSRecordServiceServer) UpdateNSRecordsIsOnWithDomainNames(context.Context, *UpdateNSRecordsIsOnWithDomainNamesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRecordsIsOnWithDomainNames not implemented")
}
func (UnimplementedNSRecordServiceServer) ImportNSRecords(context.Context, *ImportNSRecordsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportNSRecords not implemented")
}
func (UnimplementedNSRecordServiceServer) UpdateNSRecord(context.Context, *UpdateNSRecordRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRecord not implemented")
}
func (UnimplementedNSRecordServiceServer) DeleteNSRecord(context.Context, *DeleteNSRecordRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSRecord not implemented")
}
func (UnimplementedNSRecordServiceServer) CountAllNSRecords(context.Context, *CountAllNSRecordsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSRecords not implemented")
}
func (UnimplementedNSRecordServiceServer) CountAllNSRecordsWithName(context.Context, *CountAllNSRecordsWithNameRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSRecordsWithName not implemented")
}
func (UnimplementedNSRecordServiceServer) ListNSRecords(context.Context, *ListNSRecordsRequest) (*ListNSRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSRecords not implemented")
}
func (UnimplementedNSRecordServiceServer) FindNSRecord(context.Context, *FindNSRecordRequest) (*FindNSRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSRecord not implemented")
}
func (UnimplementedNSRecordServiceServer) FindNSRecordWithNameAndType(context.Context, *FindNSRecordWithNameAndTypeRequest) (*FindNSRecordWithNameAndTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSRecordWithNameAndType not implemented")
}
func (UnimplementedNSRecordServiceServer) FindNSRecordsWithNameAndType(context.Context, *FindNSRecordsWithNameAndTypeRequest) (*FindNSRecordsWithNameAndTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSRecordsWithNameAndType not implemented")
}
func (UnimplementedNSRecordServiceServer) ListNSRecordsAfterVersion(context.Context, *ListNSRecordsAfterVersionRequest) (*ListNSRecordsAfterVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSRecordsAfterVersion not implemented")
}
func (UnimplementedNSRecordServiceServer) FindNSRecordHealthCheck(context.Context, *FindNSRecordHealthCheckRequest) (*FindNSRecordHealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSRecordHealthCheck not implemented")
}
func (UnimplementedNSRecordServiceServer) UpdateNSRecordHealthCheck(context.Context, *UpdateNSRecordHealthCheckRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRecordHealthCheck not implemented")
}
func (UnimplementedNSRecordServiceServer) UpdateNSRecordIsUp(context.Context, *UpdateNSRecordIsUpRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRecordIsUp not implemented")
}

// UnsafeNSRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSRecordServiceServer will
// result in compilation errors.
type UnsafeNSRecordServiceServer interface {
	mustEmbedUnimplementedNSRecordServiceServer()
}

func RegisterNSRecordServiceServer(s grpc.ServiceRegistrar, srv NSRecordServiceServer) {
	s.RegisterService(&NSRecordService_ServiceDesc, srv)
}

func _NSRecordService_CreateNSRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).CreateNSRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_CreateNSRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).CreateNSRecord(ctx, req.(*CreateNSRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_CreateNSRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).CreateNSRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_CreateNSRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).CreateNSRecords(ctx, req.(*CreateNSRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_CreateNSRecordsWithDomainNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSRecordsWithDomainNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).CreateNSRecordsWithDomainNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_CreateNSRecordsWithDomainNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).CreateNSRecordsWithDomainNames(ctx, req.(*CreateNSRecordsWithDomainNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_UpdateNSRecordsWithDomainNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRecordsWithDomainNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).UpdateNSRecordsWithDomainNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_UpdateNSRecordsWithDomainNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).UpdateNSRecordsWithDomainNames(ctx, req.(*UpdateNSRecordsWithDomainNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_DeleteNSRecordsWithDomainNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSRecordsWithDomainNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).DeleteNSRecordsWithDomainNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_DeleteNSRecordsWithDomainNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).DeleteNSRecordsWithDomainNames(ctx, req.(*DeleteNSRecordsWithDomainNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_UpdateNSRecordsIsOnWithDomainNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRecordsIsOnWithDomainNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).UpdateNSRecordsIsOnWithDomainNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_UpdateNSRecordsIsOnWithDomainNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).UpdateNSRecordsIsOnWithDomainNames(ctx, req.(*UpdateNSRecordsIsOnWithDomainNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_ImportNSRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportNSRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).ImportNSRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_ImportNSRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).ImportNSRecords(ctx, req.(*ImportNSRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_UpdateNSRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).UpdateNSRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_UpdateNSRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).UpdateNSRecord(ctx, req.(*UpdateNSRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_DeleteNSRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).DeleteNSRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_DeleteNSRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).DeleteNSRecord(ctx, req.(*DeleteNSRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_CountAllNSRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).CountAllNSRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_CountAllNSRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).CountAllNSRecords(ctx, req.(*CountAllNSRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_CountAllNSRecordsWithName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSRecordsWithNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).CountAllNSRecordsWithName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_CountAllNSRecordsWithName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).CountAllNSRecordsWithName(ctx, req.(*CountAllNSRecordsWithNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_ListNSRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).ListNSRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_ListNSRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).ListNSRecords(ctx, req.(*ListNSRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_FindNSRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).FindNSRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_FindNSRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).FindNSRecord(ctx, req.(*FindNSRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_FindNSRecordWithNameAndType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSRecordWithNameAndTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).FindNSRecordWithNameAndType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_FindNSRecordWithNameAndType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).FindNSRecordWithNameAndType(ctx, req.(*FindNSRecordWithNameAndTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_FindNSRecordsWithNameAndType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSRecordsWithNameAndTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).FindNSRecordsWithNameAndType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_FindNSRecordsWithNameAndType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).FindNSRecordsWithNameAndType(ctx, req.(*FindNSRecordsWithNameAndTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_ListNSRecordsAfterVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSRecordsAfterVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).ListNSRecordsAfterVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_ListNSRecordsAfterVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).ListNSRecordsAfterVersion(ctx, req.(*ListNSRecordsAfterVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_FindNSRecordHealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSRecordHealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).FindNSRecordHealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_FindNSRecordHealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).FindNSRecordHealthCheck(ctx, req.(*FindNSRecordHealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_UpdateNSRecordHealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRecordHealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).UpdateNSRecordHealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_UpdateNSRecordHealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).UpdateNSRecordHealthCheck(ctx, req.(*UpdateNSRecordHealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRecordService_UpdateNSRecordIsUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRecordIsUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRecordServiceServer).UpdateNSRecordIsUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRecordService_UpdateNSRecordIsUp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRecordServiceServer).UpdateNSRecordIsUp(ctx, req.(*UpdateNSRecordIsUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSRecordService_ServiceDesc is the grpc.ServiceDesc for NSRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSRecordService",
	HandlerType: (*NSRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSRecord",
			Handler:    _NSRecordService_CreateNSRecord_Handler,
		},
		{
			MethodName: "createNSRecords",
			Handler:    _NSRecordService_CreateNSRecords_Handler,
		},
		{
			MethodName: "createNSRecordsWithDomainNames",
			Handler:    _NSRecordService_CreateNSRecordsWithDomainNames_Handler,
		},
		{
			MethodName: "updateNSRecordsWithDomainNames",
			Handler:    _NSRecordService_UpdateNSRecordsWithDomainNames_Handler,
		},
		{
			MethodName: "deleteNSRecordsWithDomainNames",
			Handler:    _NSRecordService_DeleteNSRecordsWithDomainNames_Handler,
		},
		{
			MethodName: "updateNSRecordsIsOnWithDomainNames",
			Handler:    _NSRecordService_UpdateNSRecordsIsOnWithDomainNames_Handler,
		},
		{
			MethodName: "importNSRecords",
			Handler:    _NSRecordService_ImportNSRecords_Handler,
		},
		{
			MethodName: "updateNSRecord",
			Handler:    _NSRecordService_UpdateNSRecord_Handler,
		},
		{
			MethodName: "deleteNSRecord",
			Handler:    _NSRecordService_DeleteNSRecord_Handler,
		},
		{
			MethodName: "countAllNSRecords",
			Handler:    _NSRecordService_CountAllNSRecords_Handler,
		},
		{
			MethodName: "countAllNSRecordsWithName",
			Handler:    _NSRecordService_CountAllNSRecordsWithName_Handler,
		},
		{
			MethodName: "listNSRecords",
			Handler:    _NSRecordService_ListNSRecords_Handler,
		},
		{
			MethodName: "findNSRecord",
			Handler:    _NSRecordService_FindNSRecord_Handler,
		},
		{
			MethodName: "findNSRecordWithNameAndType",
			Handler:    _NSRecordService_FindNSRecordWithNameAndType_Handler,
		},
		{
			MethodName: "findNSRecordsWithNameAndType",
			Handler:    _NSRecordService_FindNSRecordsWithNameAndType_Handler,
		},
		{
			MethodName: "listNSRecordsAfterVersion",
			Handler:    _NSRecordService_ListNSRecordsAfterVersion_Handler,
		},
		{
			MethodName: "findNSRecordHealthCheck",
			Handler:    _NSRecordService_FindNSRecordHealthCheck_Handler,
		},
		{
			MethodName: "updateNSRecordHealthCheck",
			Handler:    _NSRecordService_UpdateNSRecordHealthCheck_Handler,
		},
		{
			MethodName: "updateNSRecordIsUp",
			Handler:    _NSRecordService_UpdateNSRecordIsUp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_record.proto",
}
