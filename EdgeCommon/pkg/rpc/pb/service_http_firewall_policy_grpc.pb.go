// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_firewall_policy.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPFirewallPolicyService_FindAllEnabledHTTPFirewallPolicies_FullMethodName   = "/pb.HTTPFirewallPolicyService/findAllEnabledHTTPFirewallPolicies"
	HTTPFirewallPolicyService_CreateHTTPFirewallPolicy_FullMethodName             = "/pb.HTTPFirewallPolicyService/createHTTPFirewallPolicy"
	HTTPFirewallPolicyService_CreateEmptyHTTPFirewallPolicy_FullMethodName        = "/pb.HTTPFirewallPolicyService/createEmptyHTTPFirewallPolicy"
	HTTPFirewallPolicyService_UpdateHTTPFirewallPolicy_FullMethodName             = "/pb.HTTPFirewallPolicyService/updateHTTPFirewallPolicy"
	HTTPFirewallPolicyService_UpdateHTTPFirewallPolicyGroups_FullMethodName       = "/pb.HTTPFirewallPolicyService/updateHTTPFirewallPolicyGroups"
	HTTPFirewallPolicyService_UpdateHTTPFirewallInboundConfig_FullMethodName      = "/pb.HTTPFirewallPolicyService/updateHTTPFirewallInboundConfig"
	HTTPFirewallPolicyService_CountAllEnabledHTTPFirewallPolicies_FullMethodName  = "/pb.HTTPFirewallPolicyService/countAllEnabledHTTPFirewallPolicies"
	HTTPFirewallPolicyService_ListEnabledHTTPFirewallPolicies_FullMethodName      = "/pb.HTTPFirewallPolicyService/listEnabledHTTPFirewallPolicies"
	HTTPFirewallPolicyService_DeleteHTTPFirewallPolicy_FullMethodName             = "/pb.HTTPFirewallPolicyService/deleteHTTPFirewallPolicy"
	HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicyConfig_FullMethodName  = "/pb.HTTPFirewallPolicyService/findEnabledHTTPFirewallPolicyConfig"
	HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicy_FullMethodName        = "/pb.HTTPFirewallPolicyService/findEnabledHTTPFirewallPolicy"
	HTTPFirewallPolicyService_ImportHTTPFirewallPolicy_FullMethodName             = "/pb.HTTPFirewallPolicyService/importHTTPFirewallPolicy"
	HTTPFirewallPolicyService_CheckHTTPFirewallPolicyIPStatus_FullMethodName      = "/pb.HTTPFirewallPolicyService/checkHTTPFirewallPolicyIPStatus"
	HTTPFirewallPolicyService_FindServerIdWithHTTPFirewallPolicyId_FullMethodName = "/pb.HTTPFirewallPolicyService/findServerIdWithHTTPFirewallPolicyId"
)

// HTTPFirewallPolicyServiceClient is the client API for HTTPFirewallPolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPFirewallPolicyServiceClient interface {
	// 获取所有可用策略
	FindAllEnabledHTTPFirewallPolicies(ctx context.Context, in *FindAllEnabledHTTPFirewallPoliciesRequest, opts ...grpc.CallOption) (*FindAllEnabledHTTPFirewallPoliciesResponse, error)
	// 创建防火墙策略
	CreateHTTPFirewallPolicy(ctx context.Context, in *CreateHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*CreateHTTPFirewallPolicyResponse, error)
	// 创建空防火墙策略
	CreateEmptyHTTPFirewallPolicy(ctx context.Context, in *CreateEmptyHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*CreateEmptyHTTPFirewallPolicyResponse, error)
	// 修改防火墙策略
	UpdateHTTPFirewallPolicy(ctx context.Context, in *UpdateHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改分组信息
	UpdateHTTPFirewallPolicyGroups(ctx context.Context, in *UpdateHTTPFirewallPolicyGroupsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改inbound信息
	UpdateHTTPFirewallInboundConfig(ctx context.Context, in *UpdateHTTPFirewallInboundConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算可用的防火墙策略数量
	CountAllEnabledHTTPFirewallPolicies(ctx context.Context, in *CountAllEnabledHTTPFirewallPoliciesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页的防火墙策略
	ListEnabledHTTPFirewallPolicies(ctx context.Context, in *ListEnabledHTTPFirewallPoliciesRequest, opts ...grpc.CallOption) (*ListEnabledHTTPFirewallPoliciesResponse, error)
	// 删除某个防火墙策略
	DeleteHTTPFirewallPolicy(ctx context.Context, in *DeleteHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个防火墙配置
	FindEnabledHTTPFirewallPolicyConfig(ctx context.Context, in *FindEnabledHTTPFirewallPolicyConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallPolicyConfigResponse, error)
	// 获取防火墙的基本信息
	FindEnabledHTTPFirewallPolicy(ctx context.Context, in *FindEnabledHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallPolicyResponse, error)
	// 导入策略数据
	ImportHTTPFirewallPolicy(ctx context.Context, in *ImportHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 检查IP状态
	CheckHTTPFirewallPolicyIPStatus(ctx context.Context, in *CheckHTTPFirewallPolicyIPStatusRequest, opts ...grpc.CallOption) (*CheckHTTPFirewallPolicyIPStatusResponse, error)
	// 获取防火墙对应的网站ID
	FindServerIdWithHTTPFirewallPolicyId(ctx context.Context, in *FindServerIdWithHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*FindServerIdWithHTTPFirewallPolicyIdResponse, error)
}

type hTTPFirewallPolicyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPFirewallPolicyServiceClient(cc grpc.ClientConnInterface) HTTPFirewallPolicyServiceClient {
	return &hTTPFirewallPolicyServiceClient{cc}
}

func (c *hTTPFirewallPolicyServiceClient) FindAllEnabledHTTPFirewallPolicies(ctx context.Context, in *FindAllEnabledHTTPFirewallPoliciesRequest, opts ...grpc.CallOption) (*FindAllEnabledHTTPFirewallPoliciesResponse, error) {
	out := new(FindAllEnabledHTTPFirewallPoliciesResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_FindAllEnabledHTTPFirewallPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) CreateHTTPFirewallPolicy(ctx context.Context, in *CreateHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*CreateHTTPFirewallPolicyResponse, error) {
	out := new(CreateHTTPFirewallPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_CreateHTTPFirewallPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) CreateEmptyHTTPFirewallPolicy(ctx context.Context, in *CreateEmptyHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*CreateEmptyHTTPFirewallPolicyResponse, error) {
	out := new(CreateEmptyHTTPFirewallPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_CreateEmptyHTTPFirewallPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) UpdateHTTPFirewallPolicy(ctx context.Context, in *UpdateHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_UpdateHTTPFirewallPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) UpdateHTTPFirewallPolicyGroups(ctx context.Context, in *UpdateHTTPFirewallPolicyGroupsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_UpdateHTTPFirewallPolicyGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) UpdateHTTPFirewallInboundConfig(ctx context.Context, in *UpdateHTTPFirewallInboundConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_UpdateHTTPFirewallInboundConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) CountAllEnabledHTTPFirewallPolicies(ctx context.Context, in *CountAllEnabledHTTPFirewallPoliciesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_CountAllEnabledHTTPFirewallPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) ListEnabledHTTPFirewallPolicies(ctx context.Context, in *ListEnabledHTTPFirewallPoliciesRequest, opts ...grpc.CallOption) (*ListEnabledHTTPFirewallPoliciesResponse, error) {
	out := new(ListEnabledHTTPFirewallPoliciesResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_ListEnabledHTTPFirewallPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) DeleteHTTPFirewallPolicy(ctx context.Context, in *DeleteHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_DeleteHTTPFirewallPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) FindEnabledHTTPFirewallPolicyConfig(ctx context.Context, in *FindEnabledHTTPFirewallPolicyConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallPolicyConfigResponse, error) {
	out := new(FindEnabledHTTPFirewallPolicyConfigResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) FindEnabledHTTPFirewallPolicy(ctx context.Context, in *FindEnabledHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallPolicyResponse, error) {
	out := new(FindEnabledHTTPFirewallPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) ImportHTTPFirewallPolicy(ctx context.Context, in *ImportHTTPFirewallPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_ImportHTTPFirewallPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) CheckHTTPFirewallPolicyIPStatus(ctx context.Context, in *CheckHTTPFirewallPolicyIPStatusRequest, opts ...grpc.CallOption) (*CheckHTTPFirewallPolicyIPStatusResponse, error) {
	out := new(CheckHTTPFirewallPolicyIPStatusResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_CheckHTTPFirewallPolicyIPStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallPolicyServiceClient) FindServerIdWithHTTPFirewallPolicyId(ctx context.Context, in *FindServerIdWithHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*FindServerIdWithHTTPFirewallPolicyIdResponse, error) {
	out := new(FindServerIdWithHTTPFirewallPolicyIdResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallPolicyService_FindServerIdWithHTTPFirewallPolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPFirewallPolicyServiceServer is the server API for HTTPFirewallPolicyService service.
// All implementations should embed UnimplementedHTTPFirewallPolicyServiceServer
// for forward compatibility
type HTTPFirewallPolicyServiceServer interface {
	// 获取所有可用策略
	FindAllEnabledHTTPFirewallPolicies(context.Context, *FindAllEnabledHTTPFirewallPoliciesRequest) (*FindAllEnabledHTTPFirewallPoliciesResponse, error)
	// 创建防火墙策略
	CreateHTTPFirewallPolicy(context.Context, *CreateHTTPFirewallPolicyRequest) (*CreateHTTPFirewallPolicyResponse, error)
	// 创建空防火墙策略
	CreateEmptyHTTPFirewallPolicy(context.Context, *CreateEmptyHTTPFirewallPolicyRequest) (*CreateEmptyHTTPFirewallPolicyResponse, error)
	// 修改防火墙策略
	UpdateHTTPFirewallPolicy(context.Context, *UpdateHTTPFirewallPolicyRequest) (*RPCSuccess, error)
	// 修改分组信息
	UpdateHTTPFirewallPolicyGroups(context.Context, *UpdateHTTPFirewallPolicyGroupsRequest) (*RPCSuccess, error)
	// 修改inbound信息
	UpdateHTTPFirewallInboundConfig(context.Context, *UpdateHTTPFirewallInboundConfigRequest) (*RPCSuccess, error)
	// 计算可用的防火墙策略数量
	CountAllEnabledHTTPFirewallPolicies(context.Context, *CountAllEnabledHTTPFirewallPoliciesRequest) (*RPCCountResponse, error)
	// 列出单页的防火墙策略
	ListEnabledHTTPFirewallPolicies(context.Context, *ListEnabledHTTPFirewallPoliciesRequest) (*ListEnabledHTTPFirewallPoliciesResponse, error)
	// 删除某个防火墙策略
	DeleteHTTPFirewallPolicy(context.Context, *DeleteHTTPFirewallPolicyRequest) (*RPCSuccess, error)
	// 查找单个防火墙配置
	FindEnabledHTTPFirewallPolicyConfig(context.Context, *FindEnabledHTTPFirewallPolicyConfigRequest) (*FindEnabledHTTPFirewallPolicyConfigResponse, error)
	// 获取防火墙的基本信息
	FindEnabledHTTPFirewallPolicy(context.Context, *FindEnabledHTTPFirewallPolicyRequest) (*FindEnabledHTTPFirewallPolicyResponse, error)
	// 导入策略数据
	ImportHTTPFirewallPolicy(context.Context, *ImportHTTPFirewallPolicyRequest) (*RPCSuccess, error)
	// 检查IP状态
	CheckHTTPFirewallPolicyIPStatus(context.Context, *CheckHTTPFirewallPolicyIPStatusRequest) (*CheckHTTPFirewallPolicyIPStatusResponse, error)
	// 获取防火墙对应的网站ID
	FindServerIdWithHTTPFirewallPolicyId(context.Context, *FindServerIdWithHTTPFirewallPolicyIdRequest) (*FindServerIdWithHTTPFirewallPolicyIdResponse, error)
}

// UnimplementedHTTPFirewallPolicyServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPFirewallPolicyServiceServer struct {
}

func (UnimplementedHTTPFirewallPolicyServiceServer) FindAllEnabledHTTPFirewallPolicies(context.Context, *FindAllEnabledHTTPFirewallPoliciesRequest) (*FindAllEnabledHTTPFirewallPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledHTTPFirewallPolicies not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) CreateHTTPFirewallPolicy(context.Context, *CreateHTTPFirewallPolicyRequest) (*CreateHTTPFirewallPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPFirewallPolicy not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) CreateEmptyHTTPFirewallPolicy(context.Context, *CreateEmptyHTTPFirewallPolicyRequest) (*CreateEmptyHTTPFirewallPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEmptyHTTPFirewallPolicy not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) UpdateHTTPFirewallPolicy(context.Context, *UpdateHTTPFirewallPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallPolicy not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) UpdateHTTPFirewallPolicyGroups(context.Context, *UpdateHTTPFirewallPolicyGroupsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallPolicyGroups not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) UpdateHTTPFirewallInboundConfig(context.Context, *UpdateHTTPFirewallInboundConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallInboundConfig not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) CountAllEnabledHTTPFirewallPolicies(context.Context, *CountAllEnabledHTTPFirewallPoliciesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledHTTPFirewallPolicies not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) ListEnabledHTTPFirewallPolicies(context.Context, *ListEnabledHTTPFirewallPoliciesRequest) (*ListEnabledHTTPFirewallPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledHTTPFirewallPolicies not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) DeleteHTTPFirewallPolicy(context.Context, *DeleteHTTPFirewallPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHTTPFirewallPolicy not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) FindEnabledHTTPFirewallPolicyConfig(context.Context, *FindEnabledHTTPFirewallPolicyConfigRequest) (*FindEnabledHTTPFirewallPolicyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPFirewallPolicyConfig not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) FindEnabledHTTPFirewallPolicy(context.Context, *FindEnabledHTTPFirewallPolicyRequest) (*FindEnabledHTTPFirewallPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPFirewallPolicy not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) ImportHTTPFirewallPolicy(context.Context, *ImportHTTPFirewallPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportHTTPFirewallPolicy not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) CheckHTTPFirewallPolicyIPStatus(context.Context, *CheckHTTPFirewallPolicyIPStatusRequest) (*CheckHTTPFirewallPolicyIPStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckHTTPFirewallPolicyIPStatus not implemented")
}
func (UnimplementedHTTPFirewallPolicyServiceServer) FindServerIdWithHTTPFirewallPolicyId(context.Context, *FindServerIdWithHTTPFirewallPolicyIdRequest) (*FindServerIdWithHTTPFirewallPolicyIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerIdWithHTTPFirewallPolicyId not implemented")
}

// UnsafeHTTPFirewallPolicyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPFirewallPolicyServiceServer will
// result in compilation errors.
type UnsafeHTTPFirewallPolicyServiceServer interface {
	mustEmbedUnimplementedHTTPFirewallPolicyServiceServer()
}

func RegisterHTTPFirewallPolicyServiceServer(s grpc.ServiceRegistrar, srv HTTPFirewallPolicyServiceServer) {
	s.RegisterService(&HTTPFirewallPolicyService_ServiceDesc, srv)
}

func _HTTPFirewallPolicyService_FindAllEnabledHTTPFirewallPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledHTTPFirewallPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).FindAllEnabledHTTPFirewallPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_FindAllEnabledHTTPFirewallPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).FindAllEnabledHTTPFirewallPolicies(ctx, req.(*FindAllEnabledHTTPFirewallPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_CreateHTTPFirewallPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPFirewallPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).CreateHTTPFirewallPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_CreateHTTPFirewallPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).CreateHTTPFirewallPolicy(ctx, req.(*CreateHTTPFirewallPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_CreateEmptyHTTPFirewallPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEmptyHTTPFirewallPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).CreateEmptyHTTPFirewallPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_CreateEmptyHTTPFirewallPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).CreateEmptyHTTPFirewallPolicy(ctx, req.(*CreateEmptyHTTPFirewallPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_UpdateHTTPFirewallPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).UpdateHTTPFirewallPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_UpdateHTTPFirewallPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).UpdateHTTPFirewallPolicy(ctx, req.(*UpdateHTTPFirewallPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_UpdateHTTPFirewallPolicyGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallPolicyGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).UpdateHTTPFirewallPolicyGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_UpdateHTTPFirewallPolicyGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).UpdateHTTPFirewallPolicyGroups(ctx, req.(*UpdateHTTPFirewallPolicyGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_UpdateHTTPFirewallInboundConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallInboundConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).UpdateHTTPFirewallInboundConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_UpdateHTTPFirewallInboundConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).UpdateHTTPFirewallInboundConfig(ctx, req.(*UpdateHTTPFirewallInboundConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_CountAllEnabledHTTPFirewallPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledHTTPFirewallPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).CountAllEnabledHTTPFirewallPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_CountAllEnabledHTTPFirewallPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).CountAllEnabledHTTPFirewallPolicies(ctx, req.(*CountAllEnabledHTTPFirewallPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_ListEnabledHTTPFirewallPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledHTTPFirewallPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).ListEnabledHTTPFirewallPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_ListEnabledHTTPFirewallPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).ListEnabledHTTPFirewallPolicies(ctx, req.(*ListEnabledHTTPFirewallPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_DeleteHTTPFirewallPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHTTPFirewallPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).DeleteHTTPFirewallPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_DeleteHTTPFirewallPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).DeleteHTTPFirewallPolicy(ctx, req.(*DeleteHTTPFirewallPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPFirewallPolicyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).FindEnabledHTTPFirewallPolicyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).FindEnabledHTTPFirewallPolicyConfig(ctx, req.(*FindEnabledHTTPFirewallPolicyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPFirewallPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).FindEnabledHTTPFirewallPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).FindEnabledHTTPFirewallPolicy(ctx, req.(*FindEnabledHTTPFirewallPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_ImportHTTPFirewallPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportHTTPFirewallPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).ImportHTTPFirewallPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_ImportHTTPFirewallPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).ImportHTTPFirewallPolicy(ctx, req.(*ImportHTTPFirewallPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_CheckHTTPFirewallPolicyIPStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckHTTPFirewallPolicyIPStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).CheckHTTPFirewallPolicyIPStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_CheckHTTPFirewallPolicyIPStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).CheckHTTPFirewallPolicyIPStatus(ctx, req.(*CheckHTTPFirewallPolicyIPStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallPolicyService_FindServerIdWithHTTPFirewallPolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerIdWithHTTPFirewallPolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallPolicyServiceServer).FindServerIdWithHTTPFirewallPolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallPolicyService_FindServerIdWithHTTPFirewallPolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallPolicyServiceServer).FindServerIdWithHTTPFirewallPolicyId(ctx, req.(*FindServerIdWithHTTPFirewallPolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPFirewallPolicyService_ServiceDesc is the grpc.ServiceDesc for HTTPFirewallPolicyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPFirewallPolicyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPFirewallPolicyService",
	HandlerType: (*HTTPFirewallPolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllEnabledHTTPFirewallPolicies",
			Handler:    _HTTPFirewallPolicyService_FindAllEnabledHTTPFirewallPolicies_Handler,
		},
		{
			MethodName: "createHTTPFirewallPolicy",
			Handler:    _HTTPFirewallPolicyService_CreateHTTPFirewallPolicy_Handler,
		},
		{
			MethodName: "createEmptyHTTPFirewallPolicy",
			Handler:    _HTTPFirewallPolicyService_CreateEmptyHTTPFirewallPolicy_Handler,
		},
		{
			MethodName: "updateHTTPFirewallPolicy",
			Handler:    _HTTPFirewallPolicyService_UpdateHTTPFirewallPolicy_Handler,
		},
		{
			MethodName: "updateHTTPFirewallPolicyGroups",
			Handler:    _HTTPFirewallPolicyService_UpdateHTTPFirewallPolicyGroups_Handler,
		},
		{
			MethodName: "updateHTTPFirewallInboundConfig",
			Handler:    _HTTPFirewallPolicyService_UpdateHTTPFirewallInboundConfig_Handler,
		},
		{
			MethodName: "countAllEnabledHTTPFirewallPolicies",
			Handler:    _HTTPFirewallPolicyService_CountAllEnabledHTTPFirewallPolicies_Handler,
		},
		{
			MethodName: "listEnabledHTTPFirewallPolicies",
			Handler:    _HTTPFirewallPolicyService_ListEnabledHTTPFirewallPolicies_Handler,
		},
		{
			MethodName: "deleteHTTPFirewallPolicy",
			Handler:    _HTTPFirewallPolicyService_DeleteHTTPFirewallPolicy_Handler,
		},
		{
			MethodName: "findEnabledHTTPFirewallPolicyConfig",
			Handler:    _HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicyConfig_Handler,
		},
		{
			MethodName: "findEnabledHTTPFirewallPolicy",
			Handler:    _HTTPFirewallPolicyService_FindEnabledHTTPFirewallPolicy_Handler,
		},
		{
			MethodName: "importHTTPFirewallPolicy",
			Handler:    _HTTPFirewallPolicyService_ImportHTTPFirewallPolicy_Handler,
		},
		{
			MethodName: "checkHTTPFirewallPolicyIPStatus",
			Handler:    _HTTPFirewallPolicyService_CheckHTTPFirewallPolicyIPStatus_Handler,
		},
		{
			MethodName: "findServerIdWithHTTPFirewallPolicyId",
			Handler:    _HTTPFirewallPolicyService_FindServerIdWithHTTPFirewallPolicyId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_firewall_policy.proto",
}
