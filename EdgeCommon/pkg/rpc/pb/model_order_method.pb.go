// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_order_method.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 订单支付方式
type OrderMethod struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Secret        string                 `protobuf:"bytes,6,opt,name=secret,proto3" json:"secret,omitempty"`
	IsOn          bool                   `protobuf:"varint,7,opt,name=isOn,proto3" json:"isOn,omitempty"`
	Url           string                 `protobuf:"bytes,5,opt,name=url,proto3" json:"url,omitempty"`
	ParentCode    string                 `protobuf:"bytes,8,opt,name=parentCode,proto3" json:"parentCode,omitempty"`
	Params        []byte                 `protobuf:"bytes,9,opt,name=params,proto3" json:"params,omitempty"`
	ClientType    string                 `protobuf:"bytes,10,opt,name=clientType,proto3" json:"clientType,omitempty"`
	QrcodeTitle   string                 `protobuf:"bytes,11,opt,name=qrcodeTitle,proto3" json:"qrcodeTitle,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderMethod) Reset() {
	*x = OrderMethod{}
	mi := &file_models_model_order_method_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderMethod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderMethod) ProtoMessage() {}

func (x *OrderMethod) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_order_method_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderMethod.ProtoReflect.Descriptor instead.
func (*OrderMethod) Descriptor() ([]byte, []int) {
	return file_models_model_order_method_proto_rawDescGZIP(), []int{0}
}

func (x *OrderMethod) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderMethod) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderMethod) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *OrderMethod) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OrderMethod) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *OrderMethod) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *OrderMethod) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *OrderMethod) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

func (x *OrderMethod) GetParams() []byte {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *OrderMethod) GetClientType() string {
	if x != nil {
		return x.ClientType
	}
	return ""
}

func (x *OrderMethod) GetQrcodeTitle() string {
	if x != nil {
		return x.QrcodeTitle
	}
	return ""
}

var File_models_model_order_method_proto protoreflect.FileDescriptor

const file_models_model_order_method_proto_rawDesc = "" +
	"\n" +
	"\x1fmodels/model_order_method.proto\x12\x02pb\"\x9f\x02\n" +
	"\vOrderMethod\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x16\n" +
	"\x06secret\x18\x06 \x01(\tR\x06secret\x12\x12\n" +
	"\x04isOn\x18\a \x01(\bR\x04isOn\x12\x10\n" +
	"\x03url\x18\x05 \x01(\tR\x03url\x12\x1e\n" +
	"\n" +
	"parentCode\x18\b \x01(\tR\n" +
	"parentCode\x12\x16\n" +
	"\x06params\x18\t \x01(\fR\x06params\x12\x1e\n" +
	"\n" +
	"clientType\x18\n" +
	" \x01(\tR\n" +
	"clientType\x12 \n" +
	"\vqrcodeTitle\x18\v \x01(\tR\vqrcodeTitleB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_order_method_proto_rawDescOnce sync.Once
	file_models_model_order_method_proto_rawDescData []byte
)

func file_models_model_order_method_proto_rawDescGZIP() []byte {
	file_models_model_order_method_proto_rawDescOnce.Do(func() {
		file_models_model_order_method_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_order_method_proto_rawDesc), len(file_models_model_order_method_proto_rawDesc)))
	})
	return file_models_model_order_method_proto_rawDescData
}

var file_models_model_order_method_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_order_method_proto_goTypes = []any{
	(*OrderMethod)(nil), // 0: pb.OrderMethod
}
var file_models_model_order_method_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_order_method_proto_init() }
func file_models_model_order_method_proto_init() {
	if File_models_model_order_method_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_order_method_proto_rawDesc), len(file_models_model_order_method_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_order_method_proto_goTypes,
		DependencyIndexes: file_models_model_order_method_proto_depIdxs,
		MessageInfos:      file_models_model_order_method_proto_msgTypes,
	}.Build()
	File_models_model_order_method_proto = out.File
	file_models_model_order_method_proto_goTypes = nil
	file_models_model_order_method_proto_depIdxs = nil
}
