// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server_bandwidth_stat.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 带宽统计数据
type ServerBandwidthStat struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	Id                        int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                // ID
	UserId                    int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`                                        // 用户ID
	ServerId                  int64                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`                                    //服务ID
	Day                       string                 `protobuf:"bytes,4,opt,name=day,proto3" json:"day,omitempty"`                                               // 日期，格式YYYYMMDD
	TimeAt                    string                 `protobuf:"bytes,5,opt,name=timeAt,proto3" json:"timeAt,omitempty"`                                         // 时间，格式HHII
	Bytes                     int64                  `protobuf:"varint,6,opt,name=bytes,proto3" json:"bytes,omitempty"`                                          // 峰值带宽字节
	TotalBytes                int64                  `protobuf:"varint,9,opt,name=totalBytes,proto3" json:"totalBytes,omitempty"`                                // 总流量
	Bits                      int64                  `protobuf:"varint,7,opt,name=bits,proto3" json:"bits,omitempty"`                                            // 峰值带宽比特
	NodeRegionId              int64                  `protobuf:"varint,8,opt,name=nodeRegionId,proto3" json:"nodeRegionId,omitempty"`                            // 节点所在区域ID
	CachedBytes               int64                  `protobuf:"varint,10,opt,name=cachedBytes,proto3" json:"cachedBytes,omitempty"`                             // 总缓存流量
	AttackBytes               int64                  `protobuf:"varint,11,opt,name=attackBytes,proto3" json:"attackBytes,omitempty"`                             // 总攻击流量
	CountRequests             int64                  `protobuf:"varint,12,opt,name=countRequests,proto3" json:"countRequests,omitempty"`                         // 总请求数
	CountCachedRequests       int64                  `protobuf:"varint,13,opt,name=countCachedRequests,proto3" json:"countCachedRequests,omitempty"`             // 总缓存请求数
	CountAttackRequests       int64                  `protobuf:"varint,14,opt,name=countAttackRequests,proto3" json:"countAttackRequests,omitempty"`             // 总攻击请求数
	UserPlanId                int64                  `protobuf:"varint,15,opt,name=userPlanId,proto3" json:"userPlanId,omitempty"`                               // 绑定的用户套餐ID
	CountWebsocketConnections int64                  `protobuf:"varint,16,opt,name=countWebsocketConnections,proto3" json:"countWebsocketConnections,omitempty"` // Websocket连接数
	CountIPs                  int64                  `protobuf:"varint,17,opt,name=countIPs,proto3" json:"countIPs,omitempty"`                                   // 总IP数
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *ServerBandwidthStat) Reset() {
	*x = ServerBandwidthStat{}
	mi := &file_models_model_server_bandwidth_stat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerBandwidthStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerBandwidthStat) ProtoMessage() {}

func (x *ServerBandwidthStat) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_bandwidth_stat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerBandwidthStat.ProtoReflect.Descriptor instead.
func (*ServerBandwidthStat) Descriptor() ([]byte, []int) {
	return file_models_model_server_bandwidth_stat_proto_rawDescGZIP(), []int{0}
}

func (x *ServerBandwidthStat) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServerBandwidthStat) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ServerBandwidthStat) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ServerBandwidthStat) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *ServerBandwidthStat) GetTimeAt() string {
	if x != nil {
		return x.TimeAt
	}
	return ""
}

func (x *ServerBandwidthStat) GetBytes() int64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *ServerBandwidthStat) GetTotalBytes() int64 {
	if x != nil {
		return x.TotalBytes
	}
	return 0
}

func (x *ServerBandwidthStat) GetBits() int64 {
	if x != nil {
		return x.Bits
	}
	return 0
}

func (x *ServerBandwidthStat) GetNodeRegionId() int64 {
	if x != nil {
		return x.NodeRegionId
	}
	return 0
}

func (x *ServerBandwidthStat) GetCachedBytes() int64 {
	if x != nil {
		return x.CachedBytes
	}
	return 0
}

func (x *ServerBandwidthStat) GetAttackBytes() int64 {
	if x != nil {
		return x.AttackBytes
	}
	return 0
}

func (x *ServerBandwidthStat) GetCountRequests() int64 {
	if x != nil {
		return x.CountRequests
	}
	return 0
}

func (x *ServerBandwidthStat) GetCountCachedRequests() int64 {
	if x != nil {
		return x.CountCachedRequests
	}
	return 0
}

func (x *ServerBandwidthStat) GetCountAttackRequests() int64 {
	if x != nil {
		return x.CountAttackRequests
	}
	return 0
}

func (x *ServerBandwidthStat) GetUserPlanId() int64 {
	if x != nil {
		return x.UserPlanId
	}
	return 0
}

func (x *ServerBandwidthStat) GetCountWebsocketConnections() int64 {
	if x != nil {
		return x.CountWebsocketConnections
	}
	return 0
}

func (x *ServerBandwidthStat) GetCountIPs() int64 {
	if x != nil {
		return x.CountIPs
	}
	return 0
}

var File_models_model_server_bandwidth_stat_proto protoreflect.FileDescriptor

const file_models_model_server_bandwidth_stat_proto_rawDesc = "" +
	"\n" +
	"(models/model_server_bandwidth_stat.proto\x12\x02pb\"\xb9\x04\n" +
	"\x13ServerBandwidthStat\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x03R\bserverId\x12\x10\n" +
	"\x03day\x18\x04 \x01(\tR\x03day\x12\x16\n" +
	"\x06timeAt\x18\x05 \x01(\tR\x06timeAt\x12\x14\n" +
	"\x05bytes\x18\x06 \x01(\x03R\x05bytes\x12\x1e\n" +
	"\n" +
	"totalBytes\x18\t \x01(\x03R\n" +
	"totalBytes\x12\x12\n" +
	"\x04bits\x18\a \x01(\x03R\x04bits\x12\"\n" +
	"\fnodeRegionId\x18\b \x01(\x03R\fnodeRegionId\x12 \n" +
	"\vcachedBytes\x18\n" +
	" \x01(\x03R\vcachedBytes\x12 \n" +
	"\vattackBytes\x18\v \x01(\x03R\vattackBytes\x12$\n" +
	"\rcountRequests\x18\f \x01(\x03R\rcountRequests\x120\n" +
	"\x13countCachedRequests\x18\r \x01(\x03R\x13countCachedRequests\x120\n" +
	"\x13countAttackRequests\x18\x0e \x01(\x03R\x13countAttackRequests\x12\x1e\n" +
	"\n" +
	"userPlanId\x18\x0f \x01(\x03R\n" +
	"userPlanId\x12<\n" +
	"\x19countWebsocketConnections\x18\x10 \x01(\x03R\x19countWebsocketConnections\x12\x1a\n" +
	"\bcountIPs\x18\x11 \x01(\x03R\bcountIPsB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_bandwidth_stat_proto_rawDescOnce sync.Once
	file_models_model_server_bandwidth_stat_proto_rawDescData []byte
)

func file_models_model_server_bandwidth_stat_proto_rawDescGZIP() []byte {
	file_models_model_server_bandwidth_stat_proto_rawDescOnce.Do(func() {
		file_models_model_server_bandwidth_stat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_bandwidth_stat_proto_rawDesc), len(file_models_model_server_bandwidth_stat_proto_rawDesc)))
	})
	return file_models_model_server_bandwidth_stat_proto_rawDescData
}

var file_models_model_server_bandwidth_stat_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_bandwidth_stat_proto_goTypes = []any{
	(*ServerBandwidthStat)(nil), // 0: pb.ServerBandwidthStat
}
var file_models_model_server_bandwidth_stat_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_server_bandwidth_stat_proto_init() }
func file_models_model_server_bandwidth_stat_proto_init() {
	if File_models_model_server_bandwidth_stat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_bandwidth_stat_proto_rawDesc), len(file_models_model_server_bandwidth_stat_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_bandwidth_stat_proto_goTypes,
		DependencyIndexes: file_models_model_server_bandwidth_stat_proto_depIdxs,
		MessageInfos:      file_models_model_server_bandwidth_stat_proto_msgTypes,
	}.Build()
	File_models_model_server_bandwidth_stat_proto = out.File
	file_models_model_server_bandwidth_stat_proto_goTypes = nil
	file_models_model_server_bandwidth_stat_proto_depIdxs = nil
}
