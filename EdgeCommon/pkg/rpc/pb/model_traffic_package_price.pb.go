// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_traffic_package_price.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 流量包价格定义
type TrafficPackagePrice struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	TrafficPackageId       int64                  `protobuf:"varint,1,opt,name=trafficPackageId,proto3" json:"trafficPackageId,omitempty"`
	NodeRegionId           int64                  `protobuf:"varint,2,opt,name=nodeRegionId,proto3" json:"nodeRegionId,omitempty"`
	TrafficPackagePeriodId int64                  `protobuf:"varint,3,opt,name=trafficPackagePeriodId,proto3" json:"trafficPackagePeriodId,omitempty"`
	Price                  float64                `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *TrafficPackagePrice) Reset() {
	*x = TrafficPackagePrice{}
	mi := &file_models_model_traffic_package_price_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficPackagePrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficPackagePrice) ProtoMessage() {}

func (x *TrafficPackagePrice) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_traffic_package_price_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficPackagePrice.ProtoReflect.Descriptor instead.
func (*TrafficPackagePrice) Descriptor() ([]byte, []int) {
	return file_models_model_traffic_package_price_proto_rawDescGZIP(), []int{0}
}

func (x *TrafficPackagePrice) GetTrafficPackageId() int64 {
	if x != nil {
		return x.TrafficPackageId
	}
	return 0
}

func (x *TrafficPackagePrice) GetNodeRegionId() int64 {
	if x != nil {
		return x.NodeRegionId
	}
	return 0
}

func (x *TrafficPackagePrice) GetTrafficPackagePeriodId() int64 {
	if x != nil {
		return x.TrafficPackagePeriodId
	}
	return 0
}

func (x *TrafficPackagePrice) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

var File_models_model_traffic_package_price_proto protoreflect.FileDescriptor

const file_models_model_traffic_package_price_proto_rawDesc = "" +
	"\n" +
	"(models/model_traffic_package_price.proto\x12\x02pb\"\xb3\x01\n" +
	"\x13TrafficPackagePrice\x12*\n" +
	"\x10trafficPackageId\x18\x01 \x01(\x03R\x10trafficPackageId\x12\"\n" +
	"\fnodeRegionId\x18\x02 \x01(\x03R\fnodeRegionId\x126\n" +
	"\x16trafficPackagePeriodId\x18\x03 \x01(\x03R\x16trafficPackagePeriodId\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x01R\x05priceB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_traffic_package_price_proto_rawDescOnce sync.Once
	file_models_model_traffic_package_price_proto_rawDescData []byte
)

func file_models_model_traffic_package_price_proto_rawDescGZIP() []byte {
	file_models_model_traffic_package_price_proto_rawDescOnce.Do(func() {
		file_models_model_traffic_package_price_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_traffic_package_price_proto_rawDesc), len(file_models_model_traffic_package_price_proto_rawDesc)))
	})
	return file_models_model_traffic_package_price_proto_rawDescData
}

var file_models_model_traffic_package_price_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_traffic_package_price_proto_goTypes = []any{
	(*TrafficPackagePrice)(nil), // 0: pb.TrafficPackagePrice
}
var file_models_model_traffic_package_price_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_traffic_package_price_proto_init() }
func file_models_model_traffic_package_price_proto_init() {
	if File_models_model_traffic_package_price_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_traffic_package_price_proto_rawDesc), len(file_models_model_traffic_package_price_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_traffic_package_price_proto_goTypes,
		DependencyIndexes: file_models_model_traffic_package_price_proto_depIdxs,
		MessageInfos:      file_models_model_traffic_package_price_proto_msgTypes,
	}.Build()
	File_models_model_traffic_package_price_proto = out.File
	file_models_model_traffic_package_price_proto_goTypes = nil
	file_models_model_traffic_package_price_proto_depIdxs = nil
}
