// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_traffic_daily_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TrafficDailyStatService_FindTrafficDailyStatWithDay_FullMethodName = "/pb.TrafficDailyStatService/findTrafficDailyStatWithDay"
)

// TrafficDailyStatServiceClient is the client API for TrafficDailyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TrafficDailyStatServiceClient interface {
	// 查找某日统计
	FindTrafficDailyStatWithDay(ctx context.Context, in *FindTrafficDailyStatWithDayRequest, opts ...grpc.CallOption) (*FindTrafficDailyStatWithDayResponse, error)
}

type trafficDailyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTrafficDailyStatServiceClient(cc grpc.ClientConnInterface) TrafficDailyStatServiceClient {
	return &trafficDailyStatServiceClient{cc}
}

func (c *trafficDailyStatServiceClient) FindTrafficDailyStatWithDay(ctx context.Context, in *FindTrafficDailyStatWithDayRequest, opts ...grpc.CallOption) (*FindTrafficDailyStatWithDayResponse, error) {
	out := new(FindTrafficDailyStatWithDayResponse)
	err := c.cc.Invoke(ctx, TrafficDailyStatService_FindTrafficDailyStatWithDay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TrafficDailyStatServiceServer is the server API for TrafficDailyStatService service.
// All implementations should embed UnimplementedTrafficDailyStatServiceServer
// for forward compatibility
type TrafficDailyStatServiceServer interface {
	// 查找某日统计
	FindTrafficDailyStatWithDay(context.Context, *FindTrafficDailyStatWithDayRequest) (*FindTrafficDailyStatWithDayResponse, error)
}

// UnimplementedTrafficDailyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedTrafficDailyStatServiceServer struct {
}

func (UnimplementedTrafficDailyStatServiceServer) FindTrafficDailyStatWithDay(context.Context, *FindTrafficDailyStatWithDayRequest) (*FindTrafficDailyStatWithDayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTrafficDailyStatWithDay not implemented")
}

// UnsafeTrafficDailyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TrafficDailyStatServiceServer will
// result in compilation errors.
type UnsafeTrafficDailyStatServiceServer interface {
	mustEmbedUnimplementedTrafficDailyStatServiceServer()
}

func RegisterTrafficDailyStatServiceServer(s grpc.ServiceRegistrar, srv TrafficDailyStatServiceServer) {
	s.RegisterService(&TrafficDailyStatService_ServiceDesc, srv)
}

func _TrafficDailyStatService_FindTrafficDailyStatWithDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTrafficDailyStatWithDayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficDailyStatServiceServer).FindTrafficDailyStatWithDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficDailyStatService_FindTrafficDailyStatWithDay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficDailyStatServiceServer).FindTrafficDailyStatWithDay(ctx, req.(*FindTrafficDailyStatWithDayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TrafficDailyStatService_ServiceDesc is the grpc.ServiceDesc for TrafficDailyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TrafficDailyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TrafficDailyStatService",
	HandlerType: (*TrafficDailyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findTrafficDailyStatWithDay",
			Handler:    _TrafficDailyStatService_FindTrafficDailyStatWithDay_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_traffic_daily_stat.proto",
}
