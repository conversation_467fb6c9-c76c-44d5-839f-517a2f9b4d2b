// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_ticket_log.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 工单日志
type UserTicketLog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AdminId       int64                  `protobuf:"varint,2,opt,name=adminId,proto3" json:"adminId,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	TicketId      int64                  `protobuf:"varint,4,opt,name=ticketId,proto3" json:"ticketId,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Comment       string                 `protobuf:"bytes,6,opt,name=comment,proto3" json:"comment,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	IsReadonly    bool                   `protobuf:"varint,8,opt,name=isReadonly,proto3" json:"isReadonly,omitempty"`
	Admin         *Admin                 `protobuf:"bytes,30,opt,name=admin,proto3" json:"admin,omitempty"`
	User          *User                  `protobuf:"bytes,31,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTicketLog) Reset() {
	*x = UserTicketLog{}
	mi := &file_models_model_user_ticket_log_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTicketLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTicketLog) ProtoMessage() {}

func (x *UserTicketLog) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_ticket_log_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTicketLog.ProtoReflect.Descriptor instead.
func (*UserTicketLog) Descriptor() ([]byte, []int) {
	return file_models_model_user_ticket_log_proto_rawDescGZIP(), []int{0}
}

func (x *UserTicketLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserTicketLog) GetAdminId() int64 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *UserTicketLog) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserTicketLog) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *UserTicketLog) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserTicketLog) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *UserTicketLog) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserTicketLog) GetIsReadonly() bool {
	if x != nil {
		return x.IsReadonly
	}
	return false
}

func (x *UserTicketLog) GetAdmin() *Admin {
	if x != nil {
		return x.Admin
	}
	return nil
}

func (x *UserTicketLog) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

var File_models_model_user_ticket_log_proto protoreflect.FileDescriptor

const file_models_model_user_ticket_log_proto_rawDesc = "" +
	"\n" +
	"\"models/model_user_ticket_log.proto\x12\x02pb\x1a\x18models/model_admin.proto\x1a\x17models/model_user.proto\"\x9c\x02\n" +
	"\rUserTicketLog\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\aadminId\x18\x02 \x01(\x03R\aadminId\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\x03R\x06userId\x12\x1a\n" +
	"\bticketId\x18\x04 \x01(\x03R\bticketId\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x18\n" +
	"\acomment\x18\x06 \x01(\tR\acomment\x12\x1c\n" +
	"\tcreatedAt\x18\a \x01(\x03R\tcreatedAt\x12\x1e\n" +
	"\n" +
	"isReadonly\x18\b \x01(\bR\n" +
	"isReadonly\x12\x1f\n" +
	"\x05admin\x18\x1e \x01(\v2\t.pb.AdminR\x05admin\x12\x1c\n" +
	"\x04user\x18\x1f \x01(\v2\b.pb.UserR\x04userB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_ticket_log_proto_rawDescOnce sync.Once
	file_models_model_user_ticket_log_proto_rawDescData []byte
)

func file_models_model_user_ticket_log_proto_rawDescGZIP() []byte {
	file_models_model_user_ticket_log_proto_rawDescOnce.Do(func() {
		file_models_model_user_ticket_log_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_ticket_log_proto_rawDesc), len(file_models_model_user_ticket_log_proto_rawDesc)))
	})
	return file_models_model_user_ticket_log_proto_rawDescData
}

var file_models_model_user_ticket_log_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_ticket_log_proto_goTypes = []any{
	(*UserTicketLog)(nil), // 0: pb.UserTicketLog
	(*Admin)(nil),         // 1: pb.Admin
	(*User)(nil),          // 2: pb.User
}
var file_models_model_user_ticket_log_proto_depIdxs = []int32{
	1, // 0: pb.UserTicketLog.admin:type_name -> pb.Admin
	2, // 1: pb.UserTicketLog.user:type_name -> pb.User
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_models_model_user_ticket_log_proto_init() }
func file_models_model_user_ticket_log_proto_init() {
	if File_models_model_user_ticket_log_proto != nil {
		return
	}
	file_models_model_admin_proto_init()
	file_models_model_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_ticket_log_proto_rawDesc), len(file_models_model_user_ticket_log_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_ticket_log_proto_goTypes,
		DependencyIndexes: file_models_model_user_ticket_log_proto_depIdxs,
		MessageInfos:      file_models_model_user_ticket_log_proto_msgTypes,
	}.Build()
	File_models_model_user_ticket_log_proto = out.File
	file_models_model_user_ticket_log_proto_goTypes = nil
	file_models_model_user_ticket_log_proto_depIdxs = nil
}
