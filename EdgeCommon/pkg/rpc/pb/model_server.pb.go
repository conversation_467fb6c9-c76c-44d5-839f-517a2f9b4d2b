// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Server struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Id           int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOn         bool                   `protobuf:"varint,18,opt,name=isOn,proto3" json:"isOn,omitempty"`
	Type         string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Name         string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description  string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	IncludeNodes []byte                 `protobuf:"bytes,5,opt,name=includeNodes,proto3" json:"includeNodes,omitempty"`
	ExcludeNodes []byte                 `protobuf:"bytes,6,opt,name=excludeNodes,proto3" json:"excludeNodes,omitempty"`
	CreatedAt    int64                  `protobuf:"varint,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	DnsName      string                 `protobuf:"bytes,19,opt,name=dnsName,proto3" json:"dnsName,omitempty"`
	SupportCNAME bool                   `protobuf:"varint,23,opt,name=supportCNAME,proto3" json:"supportCNAME,omitempty"`
	UserPlanId   int64                  `protobuf:"varint,24,opt,name=userPlanId,proto3" json:"userPlanId,omitempty"`
	UserId       int64                  `protobuf:"varint,29,opt,name=userId,proto3" json:"userId,omitempty"` // 所属用户ID
	// 配置相关
	Config                  []byte                    `protobuf:"bytes,17,opt,name=config,proto3" json:"config,omitempty"`
	ServerNamesJSON         []byte                    `protobuf:"bytes,8,opt,name=serverNamesJSON,proto3" json:"serverNamesJSON,omitempty"`
	FirstServerName         string                    `protobuf:"bytes,33,opt,name=firstServerName,proto3" json:"firstServerName,omitempty"`
	CountServerNames        int32                     `protobuf:"varint,28,opt,name=countServerNames,proto3" json:"countServerNames,omitempty"`
	IsAuditing              bool                      `protobuf:"varint,20,opt,name=isAuditing,proto3" json:"isAuditing,omitempty"`
	AuditingAt              int64                     `protobuf:"varint,25,opt,name=auditingAt,proto3" json:"auditingAt,omitempty"`
	AuditingServerNamesJSON []byte                    `protobuf:"bytes,21,opt,name=auditingServerNamesJSON,proto3" json:"auditingServerNamesJSON,omitempty"`
	AuditingResult          *ServerNameAuditingResult `protobuf:"bytes,22,opt,name=auditingResult,proto3" json:"auditingResult,omitempty"`
	HttpJSON                []byte                    `protobuf:"bytes,9,opt,name=httpJSON,proto3" json:"httpJSON,omitempty"`
	HttpsJSON               []byte                    `protobuf:"bytes,10,opt,name=httpsJSON,proto3" json:"httpsJSON,omitempty"`
	TcpJSON                 []byte                    `protobuf:"bytes,11,opt,name=tcpJSON,proto3" json:"tcpJSON,omitempty"`
	TlsJSON                 []byte                    `protobuf:"bytes,12,opt,name=tlsJSON,proto3" json:"tlsJSON,omitempty"`
	UdpJSON                 []byte                    `protobuf:"bytes,14,opt,name=udpJSON,proto3" json:"udpJSON,omitempty"`
	WebId                   int64                     `protobuf:"varint,15,opt,name=webId,proto3" json:"webId,omitempty"`
	ReverseProxyJSON        []byte                    `protobuf:"bytes,16,opt,name=reverseProxyJSON,proto3" json:"reverseProxyJSON,omitempty"`
	BandwidthTime           string                    `protobuf:"bytes,26,opt,name=bandwidthTime,proto3" json:"bandwidthTime,omitempty"`              // 带宽时间
	BandwidthBytes          int64                     `protobuf:"varint,27,opt,name=bandwidthBytes,proto3" json:"bandwidthBytes,omitempty"`           // 带宽字节数
	CountRequests           int64                     `protobuf:"varint,34,opt,name=countRequests,proto3" json:"countRequests,omitempty"`             // 最近（通常5分钟内）访问量
	CountAttackRequests     int64                     `protobuf:"varint,35,opt,name=countAttackRequests,proto3" json:"countAttackRequests,omitempty"` // 最近识别到的攻击访问量
	NodeCluster             *NodeCluster              `protobuf:"bytes,30,opt,name=nodeCluster,proto3" json:"nodeCluster,omitempty"`
	ServerGroups            []*ServerGroup            `protobuf:"bytes,31,rep,name=serverGroups,proto3" json:"serverGroups,omitempty"`
	User                    *User                     `protobuf:"bytes,32,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_models_model_server_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_models_model_server_proto_rawDescGZIP(), []int{0}
}

func (x *Server) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Server) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *Server) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Server) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Server) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Server) GetIncludeNodes() []byte {
	if x != nil {
		return x.IncludeNodes
	}
	return nil
}

func (x *Server) GetExcludeNodes() []byte {
	if x != nil {
		return x.ExcludeNodes
	}
	return nil
}

func (x *Server) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Server) GetDnsName() string {
	if x != nil {
		return x.DnsName
	}
	return ""
}

func (x *Server) GetSupportCNAME() bool {
	if x != nil {
		return x.SupportCNAME
	}
	return false
}

func (x *Server) GetUserPlanId() int64 {
	if x != nil {
		return x.UserPlanId
	}
	return 0
}

func (x *Server) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Server) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *Server) GetServerNamesJSON() []byte {
	if x != nil {
		return x.ServerNamesJSON
	}
	return nil
}

func (x *Server) GetFirstServerName() string {
	if x != nil {
		return x.FirstServerName
	}
	return ""
}

func (x *Server) GetCountServerNames() int32 {
	if x != nil {
		return x.CountServerNames
	}
	return 0
}

func (x *Server) GetIsAuditing() bool {
	if x != nil {
		return x.IsAuditing
	}
	return false
}

func (x *Server) GetAuditingAt() int64 {
	if x != nil {
		return x.AuditingAt
	}
	return 0
}

func (x *Server) GetAuditingServerNamesJSON() []byte {
	if x != nil {
		return x.AuditingServerNamesJSON
	}
	return nil
}

func (x *Server) GetAuditingResult() *ServerNameAuditingResult {
	if x != nil {
		return x.AuditingResult
	}
	return nil
}

func (x *Server) GetHttpJSON() []byte {
	if x != nil {
		return x.HttpJSON
	}
	return nil
}

func (x *Server) GetHttpsJSON() []byte {
	if x != nil {
		return x.HttpsJSON
	}
	return nil
}

func (x *Server) GetTcpJSON() []byte {
	if x != nil {
		return x.TcpJSON
	}
	return nil
}

func (x *Server) GetTlsJSON() []byte {
	if x != nil {
		return x.TlsJSON
	}
	return nil
}

func (x *Server) GetUdpJSON() []byte {
	if x != nil {
		return x.UdpJSON
	}
	return nil
}

func (x *Server) GetWebId() int64 {
	if x != nil {
		return x.WebId
	}
	return 0
}

func (x *Server) GetReverseProxyJSON() []byte {
	if x != nil {
		return x.ReverseProxyJSON
	}
	return nil
}

func (x *Server) GetBandwidthTime() string {
	if x != nil {
		return x.BandwidthTime
	}
	return ""
}

func (x *Server) GetBandwidthBytes() int64 {
	if x != nil {
		return x.BandwidthBytes
	}
	return 0
}

func (x *Server) GetCountRequests() int64 {
	if x != nil {
		return x.CountRequests
	}
	return 0
}

func (x *Server) GetCountAttackRequests() int64 {
	if x != nil {
		return x.CountAttackRequests
	}
	return 0
}

func (x *Server) GetNodeCluster() *NodeCluster {
	if x != nil {
		return x.NodeCluster
	}
	return nil
}

func (x *Server) GetServerGroups() []*ServerGroup {
	if x != nil {
		return x.ServerGroups
	}
	return nil
}

func (x *Server) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

var File_models_model_server_proto protoreflect.FileDescriptor

const file_models_model_server_proto_rawDesc = "" +
	"\n" +
	"\x19models/model_server.proto\x12\x02pb\x1a\x1fmodels/model_node_cluster.proto\x1a\x1fmodels/model_server_group.proto\x1a\x17models/model_user.proto\x1a.models/model_server_name_auditing_result.proto\"\xa0\t\n" +
	"\x06Server\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04isOn\x18\x12 \x01(\bR\x04isOn\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\"\n" +
	"\fincludeNodes\x18\x05 \x01(\fR\fincludeNodes\x12\"\n" +
	"\fexcludeNodes\x18\x06 \x01(\fR\fexcludeNodes\x12\x1c\n" +
	"\tcreatedAt\x18\a \x01(\x03R\tcreatedAt\x12\x18\n" +
	"\adnsName\x18\x13 \x01(\tR\adnsName\x12\"\n" +
	"\fsupportCNAME\x18\x17 \x01(\bR\fsupportCNAME\x12\x1e\n" +
	"\n" +
	"userPlanId\x18\x18 \x01(\x03R\n" +
	"userPlanId\x12\x16\n" +
	"\x06userId\x18\x1d \x01(\x03R\x06userId\x12\x16\n" +
	"\x06config\x18\x11 \x01(\fR\x06config\x12(\n" +
	"\x0fserverNamesJSON\x18\b \x01(\fR\x0fserverNamesJSON\x12(\n" +
	"\x0ffirstServerName\x18! \x01(\tR\x0ffirstServerName\x12*\n" +
	"\x10countServerNames\x18\x1c \x01(\x05R\x10countServerNames\x12\x1e\n" +
	"\n" +
	"isAuditing\x18\x14 \x01(\bR\n" +
	"isAuditing\x12\x1e\n" +
	"\n" +
	"auditingAt\x18\x19 \x01(\x03R\n" +
	"auditingAt\x128\n" +
	"\x17auditingServerNamesJSON\x18\x15 \x01(\fR\x17auditingServerNamesJSON\x12D\n" +
	"\x0eauditingResult\x18\x16 \x01(\v2\x1c.pb.ServerNameAuditingResultR\x0eauditingResult\x12\x1a\n" +
	"\bhttpJSON\x18\t \x01(\fR\bhttpJSON\x12\x1c\n" +
	"\thttpsJSON\x18\n" +
	" \x01(\fR\thttpsJSON\x12\x18\n" +
	"\atcpJSON\x18\v \x01(\fR\atcpJSON\x12\x18\n" +
	"\atlsJSON\x18\f \x01(\fR\atlsJSON\x12\x18\n" +
	"\audpJSON\x18\x0e \x01(\fR\audpJSON\x12\x14\n" +
	"\x05webId\x18\x0f \x01(\x03R\x05webId\x12*\n" +
	"\x10reverseProxyJSON\x18\x10 \x01(\fR\x10reverseProxyJSON\x12$\n" +
	"\rbandwidthTime\x18\x1a \x01(\tR\rbandwidthTime\x12&\n" +
	"\x0ebandwidthBytes\x18\x1b \x01(\x03R\x0ebandwidthBytes\x12$\n" +
	"\rcountRequests\x18\" \x01(\x03R\rcountRequests\x120\n" +
	"\x13countAttackRequests\x18# \x01(\x03R\x13countAttackRequests\x121\n" +
	"\vnodeCluster\x18\x1e \x01(\v2\x0f.pb.NodeClusterR\vnodeCluster\x123\n" +
	"\fserverGroups\x18\x1f \x03(\v2\x0f.pb.ServerGroupR\fserverGroups\x12\x1c\n" +
	"\x04user\x18  \x01(\v2\b.pb.UserR\x04userB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_proto_rawDescOnce sync.Once
	file_models_model_server_proto_rawDescData []byte
)

func file_models_model_server_proto_rawDescGZIP() []byte {
	file_models_model_server_proto_rawDescOnce.Do(func() {
		file_models_model_server_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_proto_rawDesc), len(file_models_model_server_proto_rawDesc)))
	})
	return file_models_model_server_proto_rawDescData
}

var file_models_model_server_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_proto_goTypes = []any{
	(*Server)(nil),                   // 0: pb.Server
	(*ServerNameAuditingResult)(nil), // 1: pb.ServerNameAuditingResult
	(*NodeCluster)(nil),              // 2: pb.NodeCluster
	(*ServerGroup)(nil),              // 3: pb.ServerGroup
	(*User)(nil),                     // 4: pb.User
}
var file_models_model_server_proto_depIdxs = []int32{
	1, // 0: pb.Server.auditingResult:type_name -> pb.ServerNameAuditingResult
	2, // 1: pb.Server.nodeCluster:type_name -> pb.NodeCluster
	3, // 2: pb.Server.serverGroups:type_name -> pb.ServerGroup
	4, // 3: pb.Server.user:type_name -> pb.User
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_models_model_server_proto_init() }
func file_models_model_server_proto_init() {
	if File_models_model_server_proto != nil {
		return
	}
	file_models_model_node_cluster_proto_init()
	file_models_model_server_group_proto_init()
	file_models_model_user_proto_init()
	file_models_model_server_name_auditing_result_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_proto_rawDesc), len(file_models_model_server_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_proto_goTypes,
		DependencyIndexes: file_models_model_server_proto_depIdxs,
		MessageInfos:      file_models_model_server_proto_msgTypes,
	}.Build()
	File_models_model_server_proto = out.File
	file_models_model_server_proto_goTypes = nil
	file_models_model_server_proto_depIdxs = nil
}
