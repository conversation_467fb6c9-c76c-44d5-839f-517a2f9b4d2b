// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_message_receiver.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageReceiverService_UpdateMessageReceivers_FullMethodName                               = "/pb.MessageReceiverService/updateMessageReceivers"
	MessageReceiverService_FindAllEnabledMessageReceivers_FullMethodName                       = "/pb.MessageReceiverService/findAllEnabledMessageReceivers"
	MessageReceiverService_FindAllEnabledMessageReceiversWithMessageRecipientId_FullMethodName = "/pb.MessageReceiverService/findAllEnabledMessageReceiversWithMessageRecipientId"
	MessageReceiverService_DeleteMessageReceiver_FullMethodName                                = "/pb.MessageReceiverService/deleteMessageReceiver"
	MessageReceiverService_CountAllEnabledMessageReceivers_FullMethodName                      = "/pb.MessageReceiverService/countAllEnabledMessageReceivers"
)

// MessageReceiverServiceClient is the client API for MessageReceiverService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageReceiverServiceClient interface {
	// 修改接收者
	UpdateMessageReceivers(ctx context.Context, in *UpdateMessageReceiversRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找接收者
	FindAllEnabledMessageReceivers(ctx context.Context, in *FindAllEnabledMessageReceiversRequest, opts ...grpc.CallOption) (*FindAllEnabledMessageReceiversResponse, error)
	// 根据接收人查找关联的接收者
	FindAllEnabledMessageReceiversWithMessageRecipientId(ctx context.Context, in *FindAllEnabledMessageReceiversWithMessageRecipientIdRequest, opts ...grpc.CallOption) (*FindAllEnabledMessageReceiversWithMessageRecipientIdResponse, error)
	// 删除接收者
	DeleteMessageReceiver(ctx context.Context, in *DeleteMessageReceiverRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算接收者数量
	CountAllEnabledMessageReceivers(ctx context.Context, in *CountAllEnabledMessageReceiversRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
}

type messageReceiverServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageReceiverServiceClient(cc grpc.ClientConnInterface) MessageReceiverServiceClient {
	return &messageReceiverServiceClient{cc}
}

func (c *messageReceiverServiceClient) UpdateMessageReceivers(ctx context.Context, in *UpdateMessageReceiversRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageReceiverService_UpdateMessageReceivers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageReceiverServiceClient) FindAllEnabledMessageReceivers(ctx context.Context, in *FindAllEnabledMessageReceiversRequest, opts ...grpc.CallOption) (*FindAllEnabledMessageReceiversResponse, error) {
	out := new(FindAllEnabledMessageReceiversResponse)
	err := c.cc.Invoke(ctx, MessageReceiverService_FindAllEnabledMessageReceivers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageReceiverServiceClient) FindAllEnabledMessageReceiversWithMessageRecipientId(ctx context.Context, in *FindAllEnabledMessageReceiversWithMessageRecipientIdRequest, opts ...grpc.CallOption) (*FindAllEnabledMessageReceiversWithMessageRecipientIdResponse, error) {
	out := new(FindAllEnabledMessageReceiversWithMessageRecipientIdResponse)
	err := c.cc.Invoke(ctx, MessageReceiverService_FindAllEnabledMessageReceiversWithMessageRecipientId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageReceiverServiceClient) DeleteMessageReceiver(ctx context.Context, in *DeleteMessageReceiverRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageReceiverService_DeleteMessageReceiver_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageReceiverServiceClient) CountAllEnabledMessageReceivers(ctx context.Context, in *CountAllEnabledMessageReceiversRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, MessageReceiverService_CountAllEnabledMessageReceivers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageReceiverServiceServer is the server API for MessageReceiverService service.
// All implementations should embed UnimplementedMessageReceiverServiceServer
// for forward compatibility
type MessageReceiverServiceServer interface {
	// 修改接收者
	UpdateMessageReceivers(context.Context, *UpdateMessageReceiversRequest) (*RPCSuccess, error)
	// 查找接收者
	FindAllEnabledMessageReceivers(context.Context, *FindAllEnabledMessageReceiversRequest) (*FindAllEnabledMessageReceiversResponse, error)
	// 根据接收人查找关联的接收者
	FindAllEnabledMessageReceiversWithMessageRecipientId(context.Context, *FindAllEnabledMessageReceiversWithMessageRecipientIdRequest) (*FindAllEnabledMessageReceiversWithMessageRecipientIdResponse, error)
	// 删除接收者
	DeleteMessageReceiver(context.Context, *DeleteMessageReceiverRequest) (*RPCSuccess, error)
	// 计算接收者数量
	CountAllEnabledMessageReceivers(context.Context, *CountAllEnabledMessageReceiversRequest) (*RPCCountResponse, error)
}

// UnimplementedMessageReceiverServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMessageReceiverServiceServer struct {
}

func (UnimplementedMessageReceiverServiceServer) UpdateMessageReceivers(context.Context, *UpdateMessageReceiversRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMessageReceivers not implemented")
}
func (UnimplementedMessageReceiverServiceServer) FindAllEnabledMessageReceivers(context.Context, *FindAllEnabledMessageReceiversRequest) (*FindAllEnabledMessageReceiversResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledMessageReceivers not implemented")
}
func (UnimplementedMessageReceiverServiceServer) FindAllEnabledMessageReceiversWithMessageRecipientId(context.Context, *FindAllEnabledMessageReceiversWithMessageRecipientIdRequest) (*FindAllEnabledMessageReceiversWithMessageRecipientIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledMessageReceiversWithMessageRecipientId not implemented")
}
func (UnimplementedMessageReceiverServiceServer) DeleteMessageReceiver(context.Context, *DeleteMessageReceiverRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMessageReceiver not implemented")
}
func (UnimplementedMessageReceiverServiceServer) CountAllEnabledMessageReceivers(context.Context, *CountAllEnabledMessageReceiversRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledMessageReceivers not implemented")
}

// UnsafeMessageReceiverServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageReceiverServiceServer will
// result in compilation errors.
type UnsafeMessageReceiverServiceServer interface {
	mustEmbedUnimplementedMessageReceiverServiceServer()
}

func RegisterMessageReceiverServiceServer(s grpc.ServiceRegistrar, srv MessageReceiverServiceServer) {
	s.RegisterService(&MessageReceiverService_ServiceDesc, srv)
}

func _MessageReceiverService_UpdateMessageReceivers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMessageReceiversRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageReceiverServiceServer).UpdateMessageReceivers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageReceiverService_UpdateMessageReceivers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageReceiverServiceServer).UpdateMessageReceivers(ctx, req.(*UpdateMessageReceiversRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageReceiverService_FindAllEnabledMessageReceivers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledMessageReceiversRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageReceiverServiceServer).FindAllEnabledMessageReceivers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageReceiverService_FindAllEnabledMessageReceivers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageReceiverServiceServer).FindAllEnabledMessageReceivers(ctx, req.(*FindAllEnabledMessageReceiversRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageReceiverService_FindAllEnabledMessageReceiversWithMessageRecipientId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledMessageReceiversWithMessageRecipientIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageReceiverServiceServer).FindAllEnabledMessageReceiversWithMessageRecipientId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageReceiverService_FindAllEnabledMessageReceiversWithMessageRecipientId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageReceiverServiceServer).FindAllEnabledMessageReceiversWithMessageRecipientId(ctx, req.(*FindAllEnabledMessageReceiversWithMessageRecipientIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageReceiverService_DeleteMessageReceiver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMessageReceiverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageReceiverServiceServer).DeleteMessageReceiver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageReceiverService_DeleteMessageReceiver_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageReceiverServiceServer).DeleteMessageReceiver(ctx, req.(*DeleteMessageReceiverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageReceiverService_CountAllEnabledMessageReceivers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledMessageReceiversRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageReceiverServiceServer).CountAllEnabledMessageReceivers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageReceiverService_CountAllEnabledMessageReceivers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageReceiverServiceServer).CountAllEnabledMessageReceivers(ctx, req.(*CountAllEnabledMessageReceiversRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageReceiverService_ServiceDesc is the grpc.ServiceDesc for MessageReceiverService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageReceiverService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MessageReceiverService",
	HandlerType: (*MessageReceiverServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "updateMessageReceivers",
			Handler:    _MessageReceiverService_UpdateMessageReceivers_Handler,
		},
		{
			MethodName: "findAllEnabledMessageReceivers",
			Handler:    _MessageReceiverService_FindAllEnabledMessageReceivers_Handler,
		},
		{
			MethodName: "findAllEnabledMessageReceiversWithMessageRecipientId",
			Handler:    _MessageReceiverService_FindAllEnabledMessageReceiversWithMessageRecipientId_Handler,
		},
		{
			MethodName: "deleteMessageReceiver",
			Handler:    _MessageReceiverService_DeleteMessageReceiver_Handler,
		},
		{
			MethodName: "countAllEnabledMessageReceivers",
			Handler:    _MessageReceiverService_CountAllEnabledMessageReceivers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_message_receiver.proto",
}
