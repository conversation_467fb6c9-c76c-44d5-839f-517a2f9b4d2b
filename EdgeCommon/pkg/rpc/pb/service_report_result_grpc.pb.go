// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_report_result.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReportResultService_CountAllReportResults_FullMethodName = "/pb.ReportResultService/countAllReportResults"
	ReportResultService_ListReportResults_FullMethodName     = "/pb.ReportResultService/listReportResults"
	ReportResultService_UpdateReportResults_FullMethodName   = "/pb.ReportResultService/updateReportResults"
	ReportResultService_FindAllReportResults_FullMethodName  = "/pb.ReportResultService/findAllReportResults"
)

// ReportResultServiceClient is the client API for ReportResultService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportResultServiceClient interface {
	// 计算监控结果数量
	CountAllReportResults(ctx context.Context, in *CountAllReportResultsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页监控结果
	ListReportResults(ctx context.Context, in *ListReportResultsRequest, opts ...grpc.CallOption) (*ListReportResultsResponse, error)
	// 上传报告结果
	UpdateReportResults(ctx context.Context, in *UpdateReportResultsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查询某个对象的监控结果
	FindAllReportResults(ctx context.Context, in *FindAllReportResultsRequest, opts ...grpc.CallOption) (*FindAllReportResultsResponse, error)
}

type reportResultServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReportResultServiceClient(cc grpc.ClientConnInterface) ReportResultServiceClient {
	return &reportResultServiceClient{cc}
}

func (c *reportResultServiceClient) CountAllReportResults(ctx context.Context, in *CountAllReportResultsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ReportResultService_CountAllReportResults_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportResultServiceClient) ListReportResults(ctx context.Context, in *ListReportResultsRequest, opts ...grpc.CallOption) (*ListReportResultsResponse, error) {
	out := new(ListReportResultsResponse)
	err := c.cc.Invoke(ctx, ReportResultService_ListReportResults_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportResultServiceClient) UpdateReportResults(ctx context.Context, in *UpdateReportResultsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportResultService_UpdateReportResults_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportResultServiceClient) FindAllReportResults(ctx context.Context, in *FindAllReportResultsRequest, opts ...grpc.CallOption) (*FindAllReportResultsResponse, error) {
	out := new(FindAllReportResultsResponse)
	err := c.cc.Invoke(ctx, ReportResultService_FindAllReportResults_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportResultServiceServer is the server API for ReportResultService service.
// All implementations should embed UnimplementedReportResultServiceServer
// for forward compatibility
type ReportResultServiceServer interface {
	// 计算监控结果数量
	CountAllReportResults(context.Context, *CountAllReportResultsRequest) (*RPCCountResponse, error)
	// 列出单页监控结果
	ListReportResults(context.Context, *ListReportResultsRequest) (*ListReportResultsResponse, error)
	// 上传报告结果
	UpdateReportResults(context.Context, *UpdateReportResultsRequest) (*RPCSuccess, error)
	// 查询某个对象的监控结果
	FindAllReportResults(context.Context, *FindAllReportResultsRequest) (*FindAllReportResultsResponse, error)
}

// UnimplementedReportResultServiceServer should be embedded to have forward compatible implementations.
type UnimplementedReportResultServiceServer struct {
}

func (UnimplementedReportResultServiceServer) CountAllReportResults(context.Context, *CountAllReportResultsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllReportResults not implemented")
}
func (UnimplementedReportResultServiceServer) ListReportResults(context.Context, *ListReportResultsRequest) (*ListReportResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListReportResults not implemented")
}
func (UnimplementedReportResultServiceServer) UpdateReportResults(context.Context, *UpdateReportResultsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReportResults not implemented")
}
func (UnimplementedReportResultServiceServer) FindAllReportResults(context.Context, *FindAllReportResultsRequest) (*FindAllReportResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllReportResults not implemented")
}

// UnsafeReportResultServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportResultServiceServer will
// result in compilation errors.
type UnsafeReportResultServiceServer interface {
	mustEmbedUnimplementedReportResultServiceServer()
}

func RegisterReportResultServiceServer(s grpc.ServiceRegistrar, srv ReportResultServiceServer) {
	s.RegisterService(&ReportResultService_ServiceDesc, srv)
}

func _ReportResultService_CountAllReportResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllReportResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportResultServiceServer).CountAllReportResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportResultService_CountAllReportResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportResultServiceServer).CountAllReportResults(ctx, req.(*CountAllReportResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportResultService_ListReportResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListReportResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportResultServiceServer).ListReportResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportResultService_ListReportResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportResultServiceServer).ListReportResults(ctx, req.(*ListReportResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportResultService_UpdateReportResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportResultServiceServer).UpdateReportResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportResultService_UpdateReportResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportResultServiceServer).UpdateReportResults(ctx, req.(*UpdateReportResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportResultService_FindAllReportResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllReportResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportResultServiceServer).FindAllReportResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportResultService_FindAllReportResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportResultServiceServer).FindAllReportResults(ctx, req.(*FindAllReportResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportResultService_ServiceDesc is the grpc.ServiceDesc for ReportResultService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportResultService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ReportResultService",
	HandlerType: (*ReportResultServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countAllReportResults",
			Handler:    _ReportResultService_CountAllReportResults_Handler,
		},
		{
			MethodName: "listReportResults",
			Handler:    _ReportResultService_ListReportResults_Handler,
		},
		{
			MethodName: "updateReportResults",
			Handler:    _ReportResultService_UpdateReportResults_Handler,
		},
		{
			MethodName: "findAllReportResults",
			Handler:    _ReportResultService_FindAllReportResults_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_report_result.proto",
}
