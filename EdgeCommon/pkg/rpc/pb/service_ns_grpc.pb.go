// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSService_ComposeNSBoard_FullMethodName     = "/pb.NSService/composeNSBoard"
	NSService_ComposeNSUserBoard_FullMethodName = "/pb.NSService/composeNSUserBoard"
)

// NSServiceClient is the client API for NSService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSServiceClient interface {
	// 组合看板数据
	ComposeNSBoard(ctx context.Context, in *ComposeNSBoardRequest, opts ...grpc.CallOption) (*ComposeNSBoardResponse, error)
	// 组合用户看板数据
	ComposeNSUserBoard(ctx context.Context, in *ComposeNSUserBoardRequest, opts ...grpc.CallOption) (*ComposeNSUserBoardResponse, error)
}

type nSServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSServiceClient(cc grpc.ClientConnInterface) NSServiceClient {
	return &nSServiceClient{cc}
}

func (c *nSServiceClient) ComposeNSBoard(ctx context.Context, in *ComposeNSBoardRequest, opts ...grpc.CallOption) (*ComposeNSBoardResponse, error) {
	out := new(ComposeNSBoardResponse)
	err := c.cc.Invoke(ctx, NSService_ComposeNSBoard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSServiceClient) ComposeNSUserBoard(ctx context.Context, in *ComposeNSUserBoardRequest, opts ...grpc.CallOption) (*ComposeNSUserBoardResponse, error) {
	out := new(ComposeNSUserBoardResponse)
	err := c.cc.Invoke(ctx, NSService_ComposeNSUserBoard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSServiceServer is the server API for NSService service.
// All implementations should embed UnimplementedNSServiceServer
// for forward compatibility
type NSServiceServer interface {
	// 组合看板数据
	ComposeNSBoard(context.Context, *ComposeNSBoardRequest) (*ComposeNSBoardResponse, error)
	// 组合用户看板数据
	ComposeNSUserBoard(context.Context, *ComposeNSUserBoardRequest) (*ComposeNSUserBoardResponse, error)
}

// UnimplementedNSServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSServiceServer struct {
}

func (UnimplementedNSServiceServer) ComposeNSBoard(context.Context, *ComposeNSBoardRequest) (*ComposeNSBoardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeNSBoard not implemented")
}
func (UnimplementedNSServiceServer) ComposeNSUserBoard(context.Context, *ComposeNSUserBoardRequest) (*ComposeNSUserBoardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeNSUserBoard not implemented")
}

// UnsafeNSServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSServiceServer will
// result in compilation errors.
type UnsafeNSServiceServer interface {
	mustEmbedUnimplementedNSServiceServer()
}

func RegisterNSServiceServer(s grpc.ServiceRegistrar, srv NSServiceServer) {
	s.RegisterService(&NSService_ServiceDesc, srv)
}

func _NSService_ComposeNSBoard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeNSBoardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSServiceServer).ComposeNSBoard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSService_ComposeNSBoard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSServiceServer).ComposeNSBoard(ctx, req.(*ComposeNSBoardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSService_ComposeNSUserBoard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeNSUserBoardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSServiceServer).ComposeNSUserBoard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSService_ComposeNSUserBoard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSServiceServer).ComposeNSUserBoard(ctx, req.(*ComposeNSUserBoardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSService_ServiceDesc is the grpc.ServiceDesc for NSService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSService",
	HandlerType: (*NSServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "composeNSBoard",
			Handler:    _NSService_ComposeNSBoard_Handler,
		},
		{
			MethodName: "composeNSUserBoard",
			Handler:    _NSService_ComposeNSUserBoard_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns.proto",
}
