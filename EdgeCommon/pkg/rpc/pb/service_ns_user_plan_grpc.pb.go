// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_user_plan.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSUserPlanService_CreateNSUserPlan_FullMethodName = "/pb.NSUserPlanService/createNSUserPlan"
	NSUserPlanService_UpdateNSUserPlan_FullMethodName = "/pb.NSUserPlanService/updateNSUserPlan"
	NSUserPlanService_DeleteNSUserPlan_FullMethodName = "/pb.NSUserPlanService/deleteNSUserPlan"
	NSUserPlanService_FindNSUserPlan_FullMethodName   = "/pb.NSUserPlanService/findNSUserPlan"
	NSUserPlanService_CountNSUserPlans_FullMethodName = "/pb.NSUserPlanService/countNSUserPlans"
	NSUserPlanService_ListNSUserPlans_FullMethodName  = "/pb.NSUserPlanService/listNSUserPlans"
	NSUserPlanService_BuyNSUserPlan_FullMethodName    = "/pb.NSUserPlanService/buyNSUserPlan"
)

// NSUserPlanServiceClient is the client API for NSUserPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSUserPlanServiceClient interface {
	// 创建用户套餐
	CreateNSUserPlan(ctx context.Context, in *CreateNSUserPlanRequest, opts ...grpc.CallOption) (*CreateNSUserPlanResponse, error)
	// 修改用户套餐
	UpdateNSUserPlan(ctx context.Context, in *UpdateNSUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除用户套餐
	DeleteNSUserPlan(ctx context.Context, in *DeleteNSUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取用户套餐
	FindNSUserPlan(ctx context.Context, in *FindNSUserPlanRequest, opts ...grpc.CallOption) (*FindNSUserPlanResponse, error)
	// 计算用户套餐数量
	CountNSUserPlans(ctx context.Context, in *CountNSUserPlansRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页套餐
	ListNSUserPlans(ctx context.Context, in *ListNSUserPlansRequest, opts ...grpc.CallOption) (*ListNSUserPlansResponse, error)
	// 使用余额购买用户套餐
	BuyNSUserPlan(ctx context.Context, in *BuyNSUserPlanRequest, opts ...grpc.CallOption) (*BuyNSUserPlanResponse, error)
}

type nSUserPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSUserPlanServiceClient(cc grpc.ClientConnInterface) NSUserPlanServiceClient {
	return &nSUserPlanServiceClient{cc}
}

func (c *nSUserPlanServiceClient) CreateNSUserPlan(ctx context.Context, in *CreateNSUserPlanRequest, opts ...grpc.CallOption) (*CreateNSUserPlanResponse, error) {
	out := new(CreateNSUserPlanResponse)
	err := c.cc.Invoke(ctx, NSUserPlanService_CreateNSUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSUserPlanServiceClient) UpdateNSUserPlan(ctx context.Context, in *UpdateNSUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSUserPlanService_UpdateNSUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSUserPlanServiceClient) DeleteNSUserPlan(ctx context.Context, in *DeleteNSUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSUserPlanService_DeleteNSUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSUserPlanServiceClient) FindNSUserPlan(ctx context.Context, in *FindNSUserPlanRequest, opts ...grpc.CallOption) (*FindNSUserPlanResponse, error) {
	out := new(FindNSUserPlanResponse)
	err := c.cc.Invoke(ctx, NSUserPlanService_FindNSUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSUserPlanServiceClient) CountNSUserPlans(ctx context.Context, in *CountNSUserPlansRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSUserPlanService_CountNSUserPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSUserPlanServiceClient) ListNSUserPlans(ctx context.Context, in *ListNSUserPlansRequest, opts ...grpc.CallOption) (*ListNSUserPlansResponse, error) {
	out := new(ListNSUserPlansResponse)
	err := c.cc.Invoke(ctx, NSUserPlanService_ListNSUserPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSUserPlanServiceClient) BuyNSUserPlan(ctx context.Context, in *BuyNSUserPlanRequest, opts ...grpc.CallOption) (*BuyNSUserPlanResponse, error) {
	out := new(BuyNSUserPlanResponse)
	err := c.cc.Invoke(ctx, NSUserPlanService_BuyNSUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSUserPlanServiceServer is the server API for NSUserPlanService service.
// All implementations should embed UnimplementedNSUserPlanServiceServer
// for forward compatibility
type NSUserPlanServiceServer interface {
	// 创建用户套餐
	CreateNSUserPlan(context.Context, *CreateNSUserPlanRequest) (*CreateNSUserPlanResponse, error)
	// 修改用户套餐
	UpdateNSUserPlan(context.Context, *UpdateNSUserPlanRequest) (*RPCSuccess, error)
	// 删除用户套餐
	DeleteNSUserPlan(context.Context, *DeleteNSUserPlanRequest) (*RPCSuccess, error)
	// 读取用户套餐
	FindNSUserPlan(context.Context, *FindNSUserPlanRequest) (*FindNSUserPlanResponse, error)
	// 计算用户套餐数量
	CountNSUserPlans(context.Context, *CountNSUserPlansRequest) (*RPCCountResponse, error)
	// 列出单页套餐
	ListNSUserPlans(context.Context, *ListNSUserPlansRequest) (*ListNSUserPlansResponse, error)
	// 使用余额购买用户套餐
	BuyNSUserPlan(context.Context, *BuyNSUserPlanRequest) (*BuyNSUserPlanResponse, error)
}

// UnimplementedNSUserPlanServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSUserPlanServiceServer struct {
}

func (UnimplementedNSUserPlanServiceServer) CreateNSUserPlan(context.Context, *CreateNSUserPlanRequest) (*CreateNSUserPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSUserPlan not implemented")
}
func (UnimplementedNSUserPlanServiceServer) UpdateNSUserPlan(context.Context, *UpdateNSUserPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSUserPlan not implemented")
}
func (UnimplementedNSUserPlanServiceServer) DeleteNSUserPlan(context.Context, *DeleteNSUserPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSUserPlan not implemented")
}
func (UnimplementedNSUserPlanServiceServer) FindNSUserPlan(context.Context, *FindNSUserPlanRequest) (*FindNSUserPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSUserPlan not implemented")
}
func (UnimplementedNSUserPlanServiceServer) CountNSUserPlans(context.Context, *CountNSUserPlansRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountNSUserPlans not implemented")
}
func (UnimplementedNSUserPlanServiceServer) ListNSUserPlans(context.Context, *ListNSUserPlansRequest) (*ListNSUserPlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSUserPlans not implemented")
}
func (UnimplementedNSUserPlanServiceServer) BuyNSUserPlan(context.Context, *BuyNSUserPlanRequest) (*BuyNSUserPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuyNSUserPlan not implemented")
}

// UnsafeNSUserPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSUserPlanServiceServer will
// result in compilation errors.
type UnsafeNSUserPlanServiceServer interface {
	mustEmbedUnimplementedNSUserPlanServiceServer()
}

func RegisterNSUserPlanServiceServer(s grpc.ServiceRegistrar, srv NSUserPlanServiceServer) {
	s.RegisterService(&NSUserPlanService_ServiceDesc, srv)
}

func _NSUserPlanService_CreateNSUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).CreateNSUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_CreateNSUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).CreateNSUserPlan(ctx, req.(*CreateNSUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSUserPlanService_UpdateNSUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).UpdateNSUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_UpdateNSUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).UpdateNSUserPlan(ctx, req.(*UpdateNSUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSUserPlanService_DeleteNSUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).DeleteNSUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_DeleteNSUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).DeleteNSUserPlan(ctx, req.(*DeleteNSUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSUserPlanService_FindNSUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).FindNSUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_FindNSUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).FindNSUserPlan(ctx, req.(*FindNSUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSUserPlanService_CountNSUserPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountNSUserPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).CountNSUserPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_CountNSUserPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).CountNSUserPlans(ctx, req.(*CountNSUserPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSUserPlanService_ListNSUserPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSUserPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).ListNSUserPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_ListNSUserPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).ListNSUserPlans(ctx, req.(*ListNSUserPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSUserPlanService_BuyNSUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyNSUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSUserPlanServiceServer).BuyNSUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSUserPlanService_BuyNSUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSUserPlanServiceServer).BuyNSUserPlan(ctx, req.(*BuyNSUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSUserPlanService_ServiceDesc is the grpc.ServiceDesc for NSUserPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSUserPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSUserPlanService",
	HandlerType: (*NSUserPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSUserPlan",
			Handler:    _NSUserPlanService_CreateNSUserPlan_Handler,
		},
		{
			MethodName: "updateNSUserPlan",
			Handler:    _NSUserPlanService_UpdateNSUserPlan_Handler,
		},
		{
			MethodName: "deleteNSUserPlan",
			Handler:    _NSUserPlanService_DeleteNSUserPlan_Handler,
		},
		{
			MethodName: "findNSUserPlan",
			Handler:    _NSUserPlanService_FindNSUserPlan_Handler,
		},
		{
			MethodName: "countNSUserPlans",
			Handler:    _NSUserPlanService_CountNSUserPlans_Handler,
		},
		{
			MethodName: "listNSUserPlans",
			Handler:    _NSUserPlanService_ListNSUserPlans_Handler,
		},
		{
			MethodName: "buyNSUserPlan",
			Handler:    _NSUserPlanService_BuyNSUserPlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_user_plan.proto",
}
