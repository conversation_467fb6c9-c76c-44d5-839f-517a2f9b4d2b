// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_feature.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户功能定义
type UserFeature struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`                // 代号
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                // 名称
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`  // 描述
	SupportPlan   bool                   `protobuf:"varint,4,opt,name=supportPlan,proto3" json:"supportPlan,omitempty"` // 是否支持套餐
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserFeature) Reset() {
	*x = UserFeature{}
	mi := &file_models_model_user_feature_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserFeature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFeature) ProtoMessage() {}

func (x *UserFeature) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_feature_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFeature.ProtoReflect.Descriptor instead.
func (*UserFeature) Descriptor() ([]byte, []int) {
	return file_models_model_user_feature_proto_rawDescGZIP(), []int{0}
}

func (x *UserFeature) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserFeature) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserFeature) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserFeature) GetSupportPlan() bool {
	if x != nil {
		return x.SupportPlan
	}
	return false
}

var File_models_model_user_feature_proto protoreflect.FileDescriptor

const file_models_model_user_feature_proto_rawDesc = "" +
	"\n" +
	"\x1fmodels/model_user_feature.proto\x12\x02pb\"y\n" +
	"\vUserFeature\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12 \n" +
	"\vsupportPlan\x18\x04 \x01(\bR\vsupportPlanB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_feature_proto_rawDescOnce sync.Once
	file_models_model_user_feature_proto_rawDescData []byte
)

func file_models_model_user_feature_proto_rawDescGZIP() []byte {
	file_models_model_user_feature_proto_rawDescOnce.Do(func() {
		file_models_model_user_feature_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_feature_proto_rawDesc), len(file_models_model_user_feature_proto_rawDesc)))
	})
	return file_models_model_user_feature_proto_rawDescData
}

var file_models_model_user_feature_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_feature_proto_goTypes = []any{
	(*UserFeature)(nil), // 0: pb.UserFeature
}
var file_models_model_user_feature_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_user_feature_proto_init() }
func file_models_model_user_feature_proto_init() {
	if File_models_model_user_feature_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_feature_proto_rawDesc), len(file_models_model_user_feature_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_feature_proto_goTypes,
		DependencyIndexes: file_models_model_user_feature_proto_depIdxs,
		MessageInfos:      file_models_model_user_feature_proto_msgTypes,
	}.Build()
	File_models_model_user_feature_proto = out.File
	file_models_model_user_feature_proto_goTypes = nil
	file_models_model_user_feature_proto_depIdxs = nil
}
