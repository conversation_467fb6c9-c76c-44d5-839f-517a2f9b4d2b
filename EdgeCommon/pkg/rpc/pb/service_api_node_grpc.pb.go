// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_api_node.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	APINodeService_CreateAPINode_FullMethodName                        = "/pb.APINodeService/createAPINode"
	APINodeService_UpdateAPINode_FullMethodName                        = "/pb.APINodeService/updateAPINode"
	APINodeService_DeleteAPINode_FullMethodName                        = "/pb.APINodeService/deleteAPINode"
	APINodeService_FindAllEnabledAPINodes_FullMethodName               = "/pb.APINodeService/findAllEnabledAPINodes"
	APINodeService_CountAllEnabledAPINodes_FullMethodName              = "/pb.APINodeService/countAllEnabledAPINodes"
	APINodeService_CountAllEnabledAndOnAPINodes_FullMethodName         = "/pb.APINodeService/countAllEnabledAndOnAPINodes"
	APINodeService_ListEnabledAPINodes_FullMethodName                  = "/pb.APINodeService/listEnabledAPINodes"
	APINodeService_FindEnabledAPINode_FullMethodName                   = "/pb.APINodeService/findEnabledAPINode"
	APINodeService_FindCurrentAPINodeVersion_FullMethodName            = "/pb.APINodeService/findCurrentAPINodeVersion"
	APINodeService_FindCurrentAPINode_FullMethodName                   = "/pb.APINodeService/findCurrentAPINode"
	APINodeService_CountAllEnabledAPINodesWithSSLCertId_FullMethodName = "/pb.APINodeService/countAllEnabledAPINodesWithSSLCertId"
	APINodeService_DebugAPINode_FullMethodName                         = "/pb.APINodeService/debugAPINode"
	APINodeService_UploadAPINodeFile_FullMethodName                    = "/pb.APINodeService/uploadAPINodeFile"
	APINodeService_UploadDeployFileToAPINode_FullMethodName            = "/pb.APINodeService/uploadDeployFileToAPINode"
	APINodeService_FindLatestDeployFiles_FullMethodName                = "/pb.APINodeService/findLatestDeployFiles"
)

// APINodeServiceClient is the client API for APINodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type APINodeServiceClient interface {
	// 创建API节点
	CreateAPINode(ctx context.Context, in *CreateAPINodeRequest, opts ...grpc.CallOption) (*CreateAPINodeResponse, error)
	// 修改API节点
	UpdateAPINode(ctx context.Context, in *UpdateAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除API节点
	DeleteAPINode(ctx context.Context, in *DeleteAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 列出所有可用API节点
	FindAllEnabledAPINodes(ctx context.Context, in *FindAllEnabledAPINodesRequest, opts ...grpc.CallOption) (*FindAllEnabledAPINodesResponse, error)
	// 计算API节点数量
	CountAllEnabledAPINodes(ctx context.Context, in *CountAllEnabledAPINodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算启用的API节点数量
	CountAllEnabledAndOnAPINodes(ctx context.Context, in *CountAllEnabledAndOnAPINodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页的API节点
	ListEnabledAPINodes(ctx context.Context, in *ListEnabledAPINodesRequest, opts ...grpc.CallOption) (*ListEnabledAPINodesResponse, error)
	// 根据ID查找节点
	FindEnabledAPINode(ctx context.Context, in *FindEnabledAPINodeRequest, opts ...grpc.CallOption) (*FindEnabledAPINodeResponse, error)
	// 获取当前API节点的版本
	FindCurrentAPINodeVersion(ctx context.Context, in *FindCurrentAPINodeVersionRequest, opts ...grpc.CallOption) (*FindCurrentAPINodeVersionResponse, error)
	// 获取当前API节点的信息
	FindCurrentAPINode(ctx context.Context, in *FindCurrentAPINodeRequest, opts ...grpc.CallOption) (*FindCurrentAPINodeResponse, error)
	// 计算使用某个SSL证书的API节点数量
	CountAllEnabledAPINodesWithSSLCertId(ctx context.Context, in *CountAllEnabledAPINodesWithSSLCertIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 修改调试模式状态
	DebugAPINode(ctx context.Context, in *DebugAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 上传新版API节点文件
	UploadAPINodeFile(ctx context.Context, in *UploadAPINodeFileRequest, opts ...grpc.CallOption) (*UploadAPINodeFileResponse, error)
	// 上传节点安装文件
	UploadDeployFileToAPINode(ctx context.Context, in *UploadDeployFileToAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找已有节点安装文件信息
	FindLatestDeployFiles(ctx context.Context, in *FindLatestDeployFilesRequest, opts ...grpc.CallOption) (*FindLatestDeployFilesResponse, error)
}

type aPINodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAPINodeServiceClient(cc grpc.ClientConnInterface) APINodeServiceClient {
	return &aPINodeServiceClient{cc}
}

func (c *aPINodeServiceClient) CreateAPINode(ctx context.Context, in *CreateAPINodeRequest, opts ...grpc.CallOption) (*CreateAPINodeResponse, error) {
	out := new(CreateAPINodeResponse)
	err := c.cc.Invoke(ctx, APINodeService_CreateAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) UpdateAPINode(ctx context.Context, in *UpdateAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, APINodeService_UpdateAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) DeleteAPINode(ctx context.Context, in *DeleteAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, APINodeService_DeleteAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) FindAllEnabledAPINodes(ctx context.Context, in *FindAllEnabledAPINodesRequest, opts ...grpc.CallOption) (*FindAllEnabledAPINodesResponse, error) {
	out := new(FindAllEnabledAPINodesResponse)
	err := c.cc.Invoke(ctx, APINodeService_FindAllEnabledAPINodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) CountAllEnabledAPINodes(ctx context.Context, in *CountAllEnabledAPINodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, APINodeService_CountAllEnabledAPINodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) CountAllEnabledAndOnAPINodes(ctx context.Context, in *CountAllEnabledAndOnAPINodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, APINodeService_CountAllEnabledAndOnAPINodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) ListEnabledAPINodes(ctx context.Context, in *ListEnabledAPINodesRequest, opts ...grpc.CallOption) (*ListEnabledAPINodesResponse, error) {
	out := new(ListEnabledAPINodesResponse)
	err := c.cc.Invoke(ctx, APINodeService_ListEnabledAPINodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) FindEnabledAPINode(ctx context.Context, in *FindEnabledAPINodeRequest, opts ...grpc.CallOption) (*FindEnabledAPINodeResponse, error) {
	out := new(FindEnabledAPINodeResponse)
	err := c.cc.Invoke(ctx, APINodeService_FindEnabledAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) FindCurrentAPINodeVersion(ctx context.Context, in *FindCurrentAPINodeVersionRequest, opts ...grpc.CallOption) (*FindCurrentAPINodeVersionResponse, error) {
	out := new(FindCurrentAPINodeVersionResponse)
	err := c.cc.Invoke(ctx, APINodeService_FindCurrentAPINodeVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) FindCurrentAPINode(ctx context.Context, in *FindCurrentAPINodeRequest, opts ...grpc.CallOption) (*FindCurrentAPINodeResponse, error) {
	out := new(FindCurrentAPINodeResponse)
	err := c.cc.Invoke(ctx, APINodeService_FindCurrentAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) CountAllEnabledAPINodesWithSSLCertId(ctx context.Context, in *CountAllEnabledAPINodesWithSSLCertIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, APINodeService_CountAllEnabledAPINodesWithSSLCertId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) DebugAPINode(ctx context.Context, in *DebugAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, APINodeService_DebugAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) UploadAPINodeFile(ctx context.Context, in *UploadAPINodeFileRequest, opts ...grpc.CallOption) (*UploadAPINodeFileResponse, error) {
	out := new(UploadAPINodeFileResponse)
	err := c.cc.Invoke(ctx, APINodeService_UploadAPINodeFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) UploadDeployFileToAPINode(ctx context.Context, in *UploadDeployFileToAPINodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, APINodeService_UploadDeployFileToAPINode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPINodeServiceClient) FindLatestDeployFiles(ctx context.Context, in *FindLatestDeployFilesRequest, opts ...grpc.CallOption) (*FindLatestDeployFilesResponse, error) {
	out := new(FindLatestDeployFilesResponse)
	err := c.cc.Invoke(ctx, APINodeService_FindLatestDeployFiles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// APINodeServiceServer is the server API for APINodeService service.
// All implementations should embed UnimplementedAPINodeServiceServer
// for forward compatibility
type APINodeServiceServer interface {
	// 创建API节点
	CreateAPINode(context.Context, *CreateAPINodeRequest) (*CreateAPINodeResponse, error)
	// 修改API节点
	UpdateAPINode(context.Context, *UpdateAPINodeRequest) (*RPCSuccess, error)
	// 删除API节点
	DeleteAPINode(context.Context, *DeleteAPINodeRequest) (*RPCSuccess, error)
	// 列出所有可用API节点
	FindAllEnabledAPINodes(context.Context, *FindAllEnabledAPINodesRequest) (*FindAllEnabledAPINodesResponse, error)
	// 计算API节点数量
	CountAllEnabledAPINodes(context.Context, *CountAllEnabledAPINodesRequest) (*RPCCountResponse, error)
	// 计算启用的API节点数量
	CountAllEnabledAndOnAPINodes(context.Context, *CountAllEnabledAndOnAPINodesRequest) (*RPCCountResponse, error)
	// 列出单页的API节点
	ListEnabledAPINodes(context.Context, *ListEnabledAPINodesRequest) (*ListEnabledAPINodesResponse, error)
	// 根据ID查找节点
	FindEnabledAPINode(context.Context, *FindEnabledAPINodeRequest) (*FindEnabledAPINodeResponse, error)
	// 获取当前API节点的版本
	FindCurrentAPINodeVersion(context.Context, *FindCurrentAPINodeVersionRequest) (*FindCurrentAPINodeVersionResponse, error)
	// 获取当前API节点的信息
	FindCurrentAPINode(context.Context, *FindCurrentAPINodeRequest) (*FindCurrentAPINodeResponse, error)
	// 计算使用某个SSL证书的API节点数量
	CountAllEnabledAPINodesWithSSLCertId(context.Context, *CountAllEnabledAPINodesWithSSLCertIdRequest) (*RPCCountResponse, error)
	// 修改调试模式状态
	DebugAPINode(context.Context, *DebugAPINodeRequest) (*RPCSuccess, error)
	// 上传新版API节点文件
	UploadAPINodeFile(context.Context, *UploadAPINodeFileRequest) (*UploadAPINodeFileResponse, error)
	// 上传节点安装文件
	UploadDeployFileToAPINode(context.Context, *UploadDeployFileToAPINodeRequest) (*RPCSuccess, error)
	// 查找已有节点安装文件信息
	FindLatestDeployFiles(context.Context, *FindLatestDeployFilesRequest) (*FindLatestDeployFilesResponse, error)
}

// UnimplementedAPINodeServiceServer should be embedded to have forward compatible implementations.
type UnimplementedAPINodeServiceServer struct {
}

func (UnimplementedAPINodeServiceServer) CreateAPINode(context.Context, *CreateAPINodeRequest) (*CreateAPINodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) UpdateAPINode(context.Context, *UpdateAPINodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) DeleteAPINode(context.Context, *DeleteAPINodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) FindAllEnabledAPINodes(context.Context, *FindAllEnabledAPINodesRequest) (*FindAllEnabledAPINodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledAPINodes not implemented")
}
func (UnimplementedAPINodeServiceServer) CountAllEnabledAPINodes(context.Context, *CountAllEnabledAPINodesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledAPINodes not implemented")
}
func (UnimplementedAPINodeServiceServer) CountAllEnabledAndOnAPINodes(context.Context, *CountAllEnabledAndOnAPINodesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledAndOnAPINodes not implemented")
}
func (UnimplementedAPINodeServiceServer) ListEnabledAPINodes(context.Context, *ListEnabledAPINodesRequest) (*ListEnabledAPINodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledAPINodes not implemented")
}
func (UnimplementedAPINodeServiceServer) FindEnabledAPINode(context.Context, *FindEnabledAPINodeRequest) (*FindEnabledAPINodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) FindCurrentAPINodeVersion(context.Context, *FindCurrentAPINodeVersionRequest) (*FindCurrentAPINodeVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCurrentAPINodeVersion not implemented")
}
func (UnimplementedAPINodeServiceServer) FindCurrentAPINode(context.Context, *FindCurrentAPINodeRequest) (*FindCurrentAPINodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCurrentAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) CountAllEnabledAPINodesWithSSLCertId(context.Context, *CountAllEnabledAPINodesWithSSLCertIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledAPINodesWithSSLCertId not implemented")
}
func (UnimplementedAPINodeServiceServer) DebugAPINode(context.Context, *DebugAPINodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DebugAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) UploadAPINodeFile(context.Context, *UploadAPINodeFileRequest) (*UploadAPINodeFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadAPINodeFile not implemented")
}
func (UnimplementedAPINodeServiceServer) UploadDeployFileToAPINode(context.Context, *UploadDeployFileToAPINodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadDeployFileToAPINode not implemented")
}
func (UnimplementedAPINodeServiceServer) FindLatestDeployFiles(context.Context, *FindLatestDeployFilesRequest) (*FindLatestDeployFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLatestDeployFiles not implemented")
}

// UnsafeAPINodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to APINodeServiceServer will
// result in compilation errors.
type UnsafeAPINodeServiceServer interface {
	mustEmbedUnimplementedAPINodeServiceServer()
}

func RegisterAPINodeServiceServer(s grpc.ServiceRegistrar, srv APINodeServiceServer) {
	s.RegisterService(&APINodeService_ServiceDesc, srv)
}

func _APINodeService_CreateAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).CreateAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_CreateAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).CreateAPINode(ctx, req.(*CreateAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_UpdateAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).UpdateAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_UpdateAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).UpdateAPINode(ctx, req.(*UpdateAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_DeleteAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).DeleteAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_DeleteAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).DeleteAPINode(ctx, req.(*DeleteAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_FindAllEnabledAPINodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledAPINodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).FindAllEnabledAPINodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_FindAllEnabledAPINodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).FindAllEnabledAPINodes(ctx, req.(*FindAllEnabledAPINodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_CountAllEnabledAPINodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledAPINodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).CountAllEnabledAPINodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_CountAllEnabledAPINodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).CountAllEnabledAPINodes(ctx, req.(*CountAllEnabledAPINodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_CountAllEnabledAndOnAPINodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledAndOnAPINodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).CountAllEnabledAndOnAPINodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_CountAllEnabledAndOnAPINodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).CountAllEnabledAndOnAPINodes(ctx, req.(*CountAllEnabledAndOnAPINodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_ListEnabledAPINodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledAPINodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).ListEnabledAPINodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_ListEnabledAPINodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).ListEnabledAPINodes(ctx, req.(*ListEnabledAPINodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_FindEnabledAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).FindEnabledAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_FindEnabledAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).FindEnabledAPINode(ctx, req.(*FindEnabledAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_FindCurrentAPINodeVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCurrentAPINodeVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).FindCurrentAPINodeVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_FindCurrentAPINodeVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).FindCurrentAPINodeVersion(ctx, req.(*FindCurrentAPINodeVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_FindCurrentAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCurrentAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).FindCurrentAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_FindCurrentAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).FindCurrentAPINode(ctx, req.(*FindCurrentAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_CountAllEnabledAPINodesWithSSLCertId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledAPINodesWithSSLCertIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).CountAllEnabledAPINodesWithSSLCertId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_CountAllEnabledAPINodesWithSSLCertId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).CountAllEnabledAPINodesWithSSLCertId(ctx, req.(*CountAllEnabledAPINodesWithSSLCertIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_DebugAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DebugAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).DebugAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_DebugAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).DebugAPINode(ctx, req.(*DebugAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_UploadAPINodeFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadAPINodeFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).UploadAPINodeFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_UploadAPINodeFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).UploadAPINodeFile(ctx, req.(*UploadAPINodeFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_UploadDeployFileToAPINode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadDeployFileToAPINodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).UploadDeployFileToAPINode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_UploadDeployFileToAPINode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).UploadDeployFileToAPINode(ctx, req.(*UploadDeployFileToAPINodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APINodeService_FindLatestDeployFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLatestDeployFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APINodeServiceServer).FindLatestDeployFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APINodeService_FindLatestDeployFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APINodeServiceServer).FindLatestDeployFiles(ctx, req.(*FindLatestDeployFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// APINodeService_ServiceDesc is the grpc.ServiceDesc for APINodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var APINodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.APINodeService",
	HandlerType: (*APINodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createAPINode",
			Handler:    _APINodeService_CreateAPINode_Handler,
		},
		{
			MethodName: "updateAPINode",
			Handler:    _APINodeService_UpdateAPINode_Handler,
		},
		{
			MethodName: "deleteAPINode",
			Handler:    _APINodeService_DeleteAPINode_Handler,
		},
		{
			MethodName: "findAllEnabledAPINodes",
			Handler:    _APINodeService_FindAllEnabledAPINodes_Handler,
		},
		{
			MethodName: "countAllEnabledAPINodes",
			Handler:    _APINodeService_CountAllEnabledAPINodes_Handler,
		},
		{
			MethodName: "countAllEnabledAndOnAPINodes",
			Handler:    _APINodeService_CountAllEnabledAndOnAPINodes_Handler,
		},
		{
			MethodName: "listEnabledAPINodes",
			Handler:    _APINodeService_ListEnabledAPINodes_Handler,
		},
		{
			MethodName: "findEnabledAPINode",
			Handler:    _APINodeService_FindEnabledAPINode_Handler,
		},
		{
			MethodName: "findCurrentAPINodeVersion",
			Handler:    _APINodeService_FindCurrentAPINodeVersion_Handler,
		},
		{
			MethodName: "findCurrentAPINode",
			Handler:    _APINodeService_FindCurrentAPINode_Handler,
		},
		{
			MethodName: "countAllEnabledAPINodesWithSSLCertId",
			Handler:    _APINodeService_CountAllEnabledAPINodesWithSSLCertId_Handler,
		},
		{
			MethodName: "debugAPINode",
			Handler:    _APINodeService_DebugAPINode_Handler,
		},
		{
			MethodName: "uploadAPINodeFile",
			Handler:    _APINodeService_UploadAPINodeFile_Handler,
		},
		{
			MethodName: "uploadDeployFileToAPINode",
			Handler:    _APINodeService_UploadDeployFileToAPINode_Handler,
		},
		{
			MethodName: "findLatestDeployFiles",
			Handler:    _APINodeService_FindLatestDeployFiles_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_api_node.proto",
}
