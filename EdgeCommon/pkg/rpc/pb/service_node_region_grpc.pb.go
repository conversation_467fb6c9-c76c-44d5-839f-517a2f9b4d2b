// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_region.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeRegionService_CreateNodeRegion_FullMethodName            = "/pb.NodeRegionService/createNodeRegion"
	NodeRegionService_UpdateNodeRegion_FullMethodName            = "/pb.NodeRegionService/updateNodeRegion"
	NodeRegionService_DeleteNodeRegion_FullMethodName            = "/pb.NodeRegionService/deleteNodeRegion"
	NodeRegionService_FindAllEnabledNodeRegions_FullMethodName   = "/pb.NodeRegionService/findAllEnabledNodeRegions"
	NodeRegionService_FindAllAvailableNodeRegions_FullMethodName = "/pb.NodeRegionService/findAllAvailableNodeRegions"
	NodeRegionService_UpdateNodeRegionOrders_FullMethodName      = "/pb.NodeRegionService/updateNodeRegionOrders"
	NodeRegionService_FindEnabledNodeRegion_FullMethodName       = "/pb.NodeRegionService/findEnabledNodeRegion"
	NodeRegionService_UpdateNodeRegionPrice_FullMethodName       = "/pb.NodeRegionService/updateNodeRegionPrice"
)

// NodeRegionServiceClient is the client API for NodeRegionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeRegionServiceClient interface {
	// 创建区域
	CreateNodeRegion(ctx context.Context, in *CreateNodeRegionRequest, opts ...grpc.CallOption) (*CreateNodeRegionResponse, error)
	// 修改区域
	UpdateNodeRegion(ctx context.Context, in *UpdateNodeRegionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除区域
	DeleteNodeRegion(ctx context.Context, in *DeleteNodeRegionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找所有区域
	FindAllEnabledNodeRegions(ctx context.Context, in *FindAllEnabledNodeRegionsRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeRegionsResponse, error)
	// 查找所有启用的区域
	FindAllAvailableNodeRegions(ctx context.Context, in *FindAllAvailableNodeRegionsRequest, opts ...grpc.CallOption) (*FindAllAvailableNodeRegionsResponse, error)
	// 排序
	UpdateNodeRegionOrders(ctx context.Context, in *UpdateNodeRegionOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个区域信息
	FindEnabledNodeRegion(ctx context.Context, in *FindEnabledNodeRegionRequest, opts ...grpc.CallOption) (*FindEnabledNodeRegionResponse, error)
	// 修改价格项价格
	UpdateNodeRegionPrice(ctx context.Context, in *UpdateNodeRegionPriceRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeRegionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeRegionServiceClient(cc grpc.ClientConnInterface) NodeRegionServiceClient {
	return &nodeRegionServiceClient{cc}
}

func (c *nodeRegionServiceClient) CreateNodeRegion(ctx context.Context, in *CreateNodeRegionRequest, opts ...grpc.CallOption) (*CreateNodeRegionResponse, error) {
	out := new(CreateNodeRegionResponse)
	err := c.cc.Invoke(ctx, NodeRegionService_CreateNodeRegion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) UpdateNodeRegion(ctx context.Context, in *UpdateNodeRegionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeRegionService_UpdateNodeRegion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) DeleteNodeRegion(ctx context.Context, in *DeleteNodeRegionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeRegionService_DeleteNodeRegion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) FindAllEnabledNodeRegions(ctx context.Context, in *FindAllEnabledNodeRegionsRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeRegionsResponse, error) {
	out := new(FindAllEnabledNodeRegionsResponse)
	err := c.cc.Invoke(ctx, NodeRegionService_FindAllEnabledNodeRegions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) FindAllAvailableNodeRegions(ctx context.Context, in *FindAllAvailableNodeRegionsRequest, opts ...grpc.CallOption) (*FindAllAvailableNodeRegionsResponse, error) {
	out := new(FindAllAvailableNodeRegionsResponse)
	err := c.cc.Invoke(ctx, NodeRegionService_FindAllAvailableNodeRegions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) UpdateNodeRegionOrders(ctx context.Context, in *UpdateNodeRegionOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeRegionService_UpdateNodeRegionOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) FindEnabledNodeRegion(ctx context.Context, in *FindEnabledNodeRegionRequest, opts ...grpc.CallOption) (*FindEnabledNodeRegionResponse, error) {
	out := new(FindEnabledNodeRegionResponse)
	err := c.cc.Invoke(ctx, NodeRegionService_FindEnabledNodeRegion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeRegionServiceClient) UpdateNodeRegionPrice(ctx context.Context, in *UpdateNodeRegionPriceRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeRegionService_UpdateNodeRegionPrice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeRegionServiceServer is the server API for NodeRegionService service.
// All implementations should embed UnimplementedNodeRegionServiceServer
// for forward compatibility
type NodeRegionServiceServer interface {
	// 创建区域
	CreateNodeRegion(context.Context, *CreateNodeRegionRequest) (*CreateNodeRegionResponse, error)
	// 修改区域
	UpdateNodeRegion(context.Context, *UpdateNodeRegionRequest) (*RPCSuccess, error)
	// 删除区域
	DeleteNodeRegion(context.Context, *DeleteNodeRegionRequest) (*RPCSuccess, error)
	// 查找所有区域
	FindAllEnabledNodeRegions(context.Context, *FindAllEnabledNodeRegionsRequest) (*FindAllEnabledNodeRegionsResponse, error)
	// 查找所有启用的区域
	FindAllAvailableNodeRegions(context.Context, *FindAllAvailableNodeRegionsRequest) (*FindAllAvailableNodeRegionsResponse, error)
	// 排序
	UpdateNodeRegionOrders(context.Context, *UpdateNodeRegionOrdersRequest) (*RPCSuccess, error)
	// 查找单个区域信息
	FindEnabledNodeRegion(context.Context, *FindEnabledNodeRegionRequest) (*FindEnabledNodeRegionResponse, error)
	// 修改价格项价格
	UpdateNodeRegionPrice(context.Context, *UpdateNodeRegionPriceRequest) (*RPCSuccess, error)
}

// UnimplementedNodeRegionServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeRegionServiceServer struct {
}

func (UnimplementedNodeRegionServiceServer) CreateNodeRegion(context.Context, *CreateNodeRegionRequest) (*CreateNodeRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeRegion not implemented")
}
func (UnimplementedNodeRegionServiceServer) UpdateNodeRegion(context.Context, *UpdateNodeRegionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeRegion not implemented")
}
func (UnimplementedNodeRegionServiceServer) DeleteNodeRegion(context.Context, *DeleteNodeRegionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeRegion not implemented")
}
func (UnimplementedNodeRegionServiceServer) FindAllEnabledNodeRegions(context.Context, *FindAllEnabledNodeRegionsRequest) (*FindAllEnabledNodeRegionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeRegions not implemented")
}
func (UnimplementedNodeRegionServiceServer) FindAllAvailableNodeRegions(context.Context, *FindAllAvailableNodeRegionsRequest) (*FindAllAvailableNodeRegionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailableNodeRegions not implemented")
}
func (UnimplementedNodeRegionServiceServer) UpdateNodeRegionOrders(context.Context, *UpdateNodeRegionOrdersRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeRegionOrders not implemented")
}
func (UnimplementedNodeRegionServiceServer) FindEnabledNodeRegion(context.Context, *FindEnabledNodeRegionRequest) (*FindEnabledNodeRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeRegion not implemented")
}
func (UnimplementedNodeRegionServiceServer) UpdateNodeRegionPrice(context.Context, *UpdateNodeRegionPriceRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeRegionPrice not implemented")
}

// UnsafeNodeRegionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeRegionServiceServer will
// result in compilation errors.
type UnsafeNodeRegionServiceServer interface {
	mustEmbedUnimplementedNodeRegionServiceServer()
}

func RegisterNodeRegionServiceServer(s grpc.ServiceRegistrar, srv NodeRegionServiceServer) {
	s.RegisterService(&NodeRegionService_ServiceDesc, srv)
}

func _NodeRegionService_CreateNodeRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).CreateNodeRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_CreateNodeRegion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).CreateNodeRegion(ctx, req.(*CreateNodeRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_UpdateNodeRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).UpdateNodeRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_UpdateNodeRegion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).UpdateNodeRegion(ctx, req.(*UpdateNodeRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_DeleteNodeRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).DeleteNodeRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_DeleteNodeRegion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).DeleteNodeRegion(ctx, req.(*DeleteNodeRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_FindAllEnabledNodeRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).FindAllEnabledNodeRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_FindAllEnabledNodeRegions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).FindAllEnabledNodeRegions(ctx, req.(*FindAllEnabledNodeRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_FindAllAvailableNodeRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailableNodeRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).FindAllAvailableNodeRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_FindAllAvailableNodeRegions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).FindAllAvailableNodeRegions(ctx, req.(*FindAllAvailableNodeRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_UpdateNodeRegionOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeRegionOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).UpdateNodeRegionOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_UpdateNodeRegionOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).UpdateNodeRegionOrders(ctx, req.(*UpdateNodeRegionOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_FindEnabledNodeRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).FindEnabledNodeRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_FindEnabledNodeRegion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).FindEnabledNodeRegion(ctx, req.(*FindEnabledNodeRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeRegionService_UpdateNodeRegionPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeRegionPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeRegionServiceServer).UpdateNodeRegionPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeRegionService_UpdateNodeRegionPrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeRegionServiceServer).UpdateNodeRegionPrice(ctx, req.(*UpdateNodeRegionPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeRegionService_ServiceDesc is the grpc.ServiceDesc for NodeRegionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeRegionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeRegionService",
	HandlerType: (*NodeRegionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeRegion",
			Handler:    _NodeRegionService_CreateNodeRegion_Handler,
		},
		{
			MethodName: "updateNodeRegion",
			Handler:    _NodeRegionService_UpdateNodeRegion_Handler,
		},
		{
			MethodName: "deleteNodeRegion",
			Handler:    _NodeRegionService_DeleteNodeRegion_Handler,
		},
		{
			MethodName: "findAllEnabledNodeRegions",
			Handler:    _NodeRegionService_FindAllEnabledNodeRegions_Handler,
		},
		{
			MethodName: "findAllAvailableNodeRegions",
			Handler:    _NodeRegionService_FindAllAvailableNodeRegions_Handler,
		},
		{
			MethodName: "updateNodeRegionOrders",
			Handler:    _NodeRegionService_UpdateNodeRegionOrders_Handler,
		},
		{
			MethodName: "findEnabledNodeRegion",
			Handler:    _NodeRegionService_FindEnabledNodeRegion_Handler,
		},
		{
			MethodName: "updateNodeRegionPrice",
			Handler:    _NodeRegionService_UpdateNodeRegionPrice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_region.proto",
}
