// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_report_task.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// IP地址任务
type IPAddrReportTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	CostMs        float32                `protobuf:"fixed32,3,opt,name=costMs,proto3" json:"costMs,omitempty"`
	Level         string                 `protobuf:"bytes,4,opt,name=level,proto3" json:"level,omitempty"`
	Connectivity  float32                `protobuf:"fixed32,5,opt,name=connectivity,proto3" json:"connectivity,omitempty"`
	NodeIPAddress *NodeIPAddress         `protobuf:"bytes,30,opt,name=nodeIPAddress,proto3" json:"nodeIPAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IPAddrReportTask) Reset() {
	*x = IPAddrReportTask{}
	mi := &file_models_model_report_task_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPAddrReportTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAddrReportTask) ProtoMessage() {}

func (x *IPAddrReportTask) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_report_task_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAddrReportTask.ProtoReflect.Descriptor instead.
func (*IPAddrReportTask) Descriptor() ([]byte, []int) {
	return file_models_model_report_task_proto_rawDescGZIP(), []int{0}
}

func (x *IPAddrReportTask) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *IPAddrReportTask) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *IPAddrReportTask) GetCostMs() float32 {
	if x != nil {
		return x.CostMs
	}
	return 0
}

func (x *IPAddrReportTask) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *IPAddrReportTask) GetConnectivity() float32 {
	if x != nil {
		return x.Connectivity
	}
	return 0
}

func (x *IPAddrReportTask) GetNodeIPAddress() *NodeIPAddress {
	if x != nil {
		return x.NodeIPAddress
	}
	return nil
}

var File_models_model_report_task_proto protoreflect.FileDescriptor

const file_models_model_report_task_proto_rawDesc = "" +
	"\n" +
	"\x1emodels/model_report_task.proto\x12\x02pb\x1a\"models/model_node_ip_address.proto\"\xc1\x01\n" +
	"\x10IPAddrReportTask\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x16\n" +
	"\x06costMs\x18\x03 \x01(\x02R\x06costMs\x12\x14\n" +
	"\x05level\x18\x04 \x01(\tR\x05level\x12\"\n" +
	"\fconnectivity\x18\x05 \x01(\x02R\fconnectivity\x127\n" +
	"\rnodeIPAddress\x18\x1e \x01(\v2\x11.pb.NodeIPAddressR\rnodeIPAddressB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_report_task_proto_rawDescOnce sync.Once
	file_models_model_report_task_proto_rawDescData []byte
)

func file_models_model_report_task_proto_rawDescGZIP() []byte {
	file_models_model_report_task_proto_rawDescOnce.Do(func() {
		file_models_model_report_task_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_report_task_proto_rawDesc), len(file_models_model_report_task_proto_rawDesc)))
	})
	return file_models_model_report_task_proto_rawDescData
}

var file_models_model_report_task_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_report_task_proto_goTypes = []any{
	(*IPAddrReportTask)(nil), // 0: pb.IPAddrReportTask
	(*NodeIPAddress)(nil),    // 1: pb.NodeIPAddress
}
var file_models_model_report_task_proto_depIdxs = []int32{
	1, // 0: pb.IPAddrReportTask.nodeIPAddress:type_name -> pb.NodeIPAddress
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_report_task_proto_init() }
func file_models_model_report_task_proto_init() {
	if File_models_model_report_task_proto != nil {
		return
	}
	file_models_model_node_ip_address_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_report_task_proto_rawDesc), len(file_models_model_report_task_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_report_task_proto_goTypes,
		DependencyIndexes: file_models_model_report_task_proto_depIdxs,
		MessageInfos:      file_models_model_report_task_proto_msgTypes,
	}.Build()
	File_models_model_report_task_proto = out.File
	file_models_model_report_task_proto_goTypes = nil
	file_models_model_report_task_proto_depIdxs = nil
}
