// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server_stat_board_chart.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 统计看板条目
type ServerStatBoardChart struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	<PERSON><PERSON><PERSON>hart   *MetricChart           `protobuf:"bytes,30,opt,name=metric<PERSON>hart,proto3" json:"metricChart,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerStatBoardChart) Reset() {
	*x = ServerStatBoardChart{}
	mi := &file_models_model_server_stat_board_chart_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerStatBoardChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerStatBoardChart) ProtoMessage() {}

func (x *ServerStatBoardChart) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_stat_board_chart_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerStatBoardChart.ProtoReflect.Descriptor instead.
func (*ServerStatBoardChart) Descriptor() ([]byte, []int) {
	return file_models_model_server_stat_board_chart_proto_rawDescGZIP(), []int{0}
}

func (x *ServerStatBoardChart) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServerStatBoardChart) GetMetricChart() *MetricChart {
	if x != nil {
		return x.MetricChart
	}
	return nil
}

var File_models_model_server_stat_board_chart_proto protoreflect.FileDescriptor

const file_models_model_server_stat_board_chart_proto_rawDesc = "" +
	"\n" +
	"*models/model_server_stat_board_chart.proto\x12\x02pb\x1a\x1fmodels/model_metric_chart.proto\"Y\n" +
	"\x14ServerStatBoardChart\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x121\n" +
	"\vmetricChart\x18\x1e \x01(\v2\x0f.pb.MetricChartR\vmetricChartB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_stat_board_chart_proto_rawDescOnce sync.Once
	file_models_model_server_stat_board_chart_proto_rawDescData []byte
)

func file_models_model_server_stat_board_chart_proto_rawDescGZIP() []byte {
	file_models_model_server_stat_board_chart_proto_rawDescOnce.Do(func() {
		file_models_model_server_stat_board_chart_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_stat_board_chart_proto_rawDesc), len(file_models_model_server_stat_board_chart_proto_rawDesc)))
	})
	return file_models_model_server_stat_board_chart_proto_rawDescData
}

var file_models_model_server_stat_board_chart_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_stat_board_chart_proto_goTypes = []any{
	(*ServerStatBoardChart)(nil), // 0: pb.ServerStatBoardChart
	(*MetricChart)(nil),          // 1: pb.MetricChart
}
var file_models_model_server_stat_board_chart_proto_depIdxs = []int32{
	1, // 0: pb.ServerStatBoardChart.metricChart:type_name -> pb.MetricChart
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_server_stat_board_chart_proto_init() }
func file_models_model_server_stat_board_chart_proto_init() {
	if File_models_model_server_stat_board_chart_proto != nil {
		return
	}
	file_models_model_metric_chart_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_stat_board_chart_proto_rawDesc), len(file_models_model_server_stat_board_chart_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_stat_board_chart_proto_goTypes,
		DependencyIndexes: file_models_model_server_stat_board_chart_proto_depIdxs,
		MessageInfos:      file_models_model_server_stat_board_chart_proto_msgTypes,
	}.Build()
	File_models_model_server_stat_board_chart_proto = out.File
	file_models_model_server_stat_board_chart_proto_goTypes = nil
	file_models_model_server_stat_board_chart_proto_depIdxs = nil
}
