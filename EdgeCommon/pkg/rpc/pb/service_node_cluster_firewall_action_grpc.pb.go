// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_cluster_firewall_action.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeClusterFirewallActionService_CreateNodeClusterFirewallAction_FullMethodName           = "/pb.NodeClusterFirewallActionService/createNodeClusterFirewallAction"
	NodeClusterFirewallActionService_UpdateNodeClusterFirewallAction_FullMethodName           = "/pb.NodeClusterFirewallActionService/updateNodeClusterFirewallAction"
	NodeClusterFirewallActionService_DeleteNodeClusterFirewallAction_FullMethodName           = "/pb.NodeClusterFirewallActionService/deleteNodeClusterFirewallAction"
	NodeClusterFirewallActionService_FindAllEnabledNodeClusterFirewallActions_FullMethodName  = "/pb.NodeClusterFirewallActionService/findAllEnabledNodeClusterFirewallActions"
	NodeClusterFirewallActionService_FindEnabledNodeClusterFirewallAction_FullMethodName      = "/pb.NodeClusterFirewallActionService/findEnabledNodeClusterFirewallAction"
	NodeClusterFirewallActionService_CountAllEnabledNodeClusterFirewallActions_FullMethodName = "/pb.NodeClusterFirewallActionService/countAllEnabledNodeClusterFirewallActions"
)

// NodeClusterFirewallActionServiceClient is the client API for NodeClusterFirewallActionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeClusterFirewallActionServiceClient interface {
	// 创建动作
	CreateNodeClusterFirewallAction(ctx context.Context, in *CreateNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*NodeClusterFirewallActionResponse, error)
	// 修改动作
	UpdateNodeClusterFirewallAction(ctx context.Context, in *UpdateNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除动作
	DeleteNodeClusterFirewallAction(ctx context.Context, in *DeleteNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查询集群的所有动作
	FindAllEnabledNodeClusterFirewallActions(ctx context.Context, in *FindAllEnabledNodeClusterFirewallActionsRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClusterFirewallActionsResponse, error)
	// 查询单个动作
	FindEnabledNodeClusterFirewallAction(ctx context.Context, in *FindEnabledNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterFirewallActionResponse, error)
	// 计算动作数量
	CountAllEnabledNodeClusterFirewallActions(ctx context.Context, in *CountAllEnabledNodeClusterFirewallActionsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
}

type nodeClusterFirewallActionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeClusterFirewallActionServiceClient(cc grpc.ClientConnInterface) NodeClusterFirewallActionServiceClient {
	return &nodeClusterFirewallActionServiceClient{cc}
}

func (c *nodeClusterFirewallActionServiceClient) CreateNodeClusterFirewallAction(ctx context.Context, in *CreateNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*NodeClusterFirewallActionResponse, error) {
	out := new(NodeClusterFirewallActionResponse)
	err := c.cc.Invoke(ctx, NodeClusterFirewallActionService_CreateNodeClusterFirewallAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterFirewallActionServiceClient) UpdateNodeClusterFirewallAction(ctx context.Context, in *UpdateNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterFirewallActionService_UpdateNodeClusterFirewallAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterFirewallActionServiceClient) DeleteNodeClusterFirewallAction(ctx context.Context, in *DeleteNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterFirewallActionService_DeleteNodeClusterFirewallAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterFirewallActionServiceClient) FindAllEnabledNodeClusterFirewallActions(ctx context.Context, in *FindAllEnabledNodeClusterFirewallActionsRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClusterFirewallActionsResponse, error) {
	out := new(FindAllEnabledNodeClusterFirewallActionsResponse)
	err := c.cc.Invoke(ctx, NodeClusterFirewallActionService_FindAllEnabledNodeClusterFirewallActions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterFirewallActionServiceClient) FindEnabledNodeClusterFirewallAction(ctx context.Context, in *FindEnabledNodeClusterFirewallActionRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterFirewallActionResponse, error) {
	out := new(FindEnabledNodeClusterFirewallActionResponse)
	err := c.cc.Invoke(ctx, NodeClusterFirewallActionService_FindEnabledNodeClusterFirewallAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterFirewallActionServiceClient) CountAllEnabledNodeClusterFirewallActions(ctx context.Context, in *CountAllEnabledNodeClusterFirewallActionsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterFirewallActionService_CountAllEnabledNodeClusterFirewallActions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeClusterFirewallActionServiceServer is the server API for NodeClusterFirewallActionService service.
// All implementations should embed UnimplementedNodeClusterFirewallActionServiceServer
// for forward compatibility
type NodeClusterFirewallActionServiceServer interface {
	// 创建动作
	CreateNodeClusterFirewallAction(context.Context, *CreateNodeClusterFirewallActionRequest) (*NodeClusterFirewallActionResponse, error)
	// 修改动作
	UpdateNodeClusterFirewallAction(context.Context, *UpdateNodeClusterFirewallActionRequest) (*RPCSuccess, error)
	// 删除动作
	DeleteNodeClusterFirewallAction(context.Context, *DeleteNodeClusterFirewallActionRequest) (*RPCSuccess, error)
	// 查询集群的所有动作
	FindAllEnabledNodeClusterFirewallActions(context.Context, *FindAllEnabledNodeClusterFirewallActionsRequest) (*FindAllEnabledNodeClusterFirewallActionsResponse, error)
	// 查询单个动作
	FindEnabledNodeClusterFirewallAction(context.Context, *FindEnabledNodeClusterFirewallActionRequest) (*FindEnabledNodeClusterFirewallActionResponse, error)
	// 计算动作数量
	CountAllEnabledNodeClusterFirewallActions(context.Context, *CountAllEnabledNodeClusterFirewallActionsRequest) (*RPCCountResponse, error)
}

// UnimplementedNodeClusterFirewallActionServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeClusterFirewallActionServiceServer struct {
}

func (UnimplementedNodeClusterFirewallActionServiceServer) CreateNodeClusterFirewallAction(context.Context, *CreateNodeClusterFirewallActionRequest) (*NodeClusterFirewallActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeClusterFirewallAction not implemented")
}
func (UnimplementedNodeClusterFirewallActionServiceServer) UpdateNodeClusterFirewallAction(context.Context, *UpdateNodeClusterFirewallActionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterFirewallAction not implemented")
}
func (UnimplementedNodeClusterFirewallActionServiceServer) DeleteNodeClusterFirewallAction(context.Context, *DeleteNodeClusterFirewallActionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeClusterFirewallAction not implemented")
}
func (UnimplementedNodeClusterFirewallActionServiceServer) FindAllEnabledNodeClusterFirewallActions(context.Context, *FindAllEnabledNodeClusterFirewallActionsRequest) (*FindAllEnabledNodeClusterFirewallActionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeClusterFirewallActions not implemented")
}
func (UnimplementedNodeClusterFirewallActionServiceServer) FindEnabledNodeClusterFirewallAction(context.Context, *FindEnabledNodeClusterFirewallActionRequest) (*FindEnabledNodeClusterFirewallActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterFirewallAction not implemented")
}
func (UnimplementedNodeClusterFirewallActionServiceServer) CountAllEnabledNodeClusterFirewallActions(context.Context, *CountAllEnabledNodeClusterFirewallActionsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClusterFirewallActions not implemented")
}

// UnsafeNodeClusterFirewallActionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeClusterFirewallActionServiceServer will
// result in compilation errors.
type UnsafeNodeClusterFirewallActionServiceServer interface {
	mustEmbedUnimplementedNodeClusterFirewallActionServiceServer()
}

func RegisterNodeClusterFirewallActionServiceServer(s grpc.ServiceRegistrar, srv NodeClusterFirewallActionServiceServer) {
	s.RegisterService(&NodeClusterFirewallActionService_ServiceDesc, srv)
}

func _NodeClusterFirewallActionService_CreateNodeClusterFirewallAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeClusterFirewallActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterFirewallActionServiceServer).CreateNodeClusterFirewallAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterFirewallActionService_CreateNodeClusterFirewallAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterFirewallActionServiceServer).CreateNodeClusterFirewallAction(ctx, req.(*CreateNodeClusterFirewallActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterFirewallActionService_UpdateNodeClusterFirewallAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterFirewallActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterFirewallActionServiceServer).UpdateNodeClusterFirewallAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterFirewallActionService_UpdateNodeClusterFirewallAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterFirewallActionServiceServer).UpdateNodeClusterFirewallAction(ctx, req.(*UpdateNodeClusterFirewallActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterFirewallActionService_DeleteNodeClusterFirewallAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeClusterFirewallActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterFirewallActionServiceServer).DeleteNodeClusterFirewallAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterFirewallActionService_DeleteNodeClusterFirewallAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterFirewallActionServiceServer).DeleteNodeClusterFirewallAction(ctx, req.(*DeleteNodeClusterFirewallActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterFirewallActionService_FindAllEnabledNodeClusterFirewallActions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeClusterFirewallActionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterFirewallActionServiceServer).FindAllEnabledNodeClusterFirewallActions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterFirewallActionService_FindAllEnabledNodeClusterFirewallActions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterFirewallActionServiceServer).FindAllEnabledNodeClusterFirewallActions(ctx, req.(*FindAllEnabledNodeClusterFirewallActionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterFirewallActionService_FindEnabledNodeClusterFirewallAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterFirewallActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterFirewallActionServiceServer).FindEnabledNodeClusterFirewallAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterFirewallActionService_FindEnabledNodeClusterFirewallAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterFirewallActionServiceServer).FindEnabledNodeClusterFirewallAction(ctx, req.(*FindEnabledNodeClusterFirewallActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterFirewallActionService_CountAllEnabledNodeClusterFirewallActions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClusterFirewallActionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterFirewallActionServiceServer).CountAllEnabledNodeClusterFirewallActions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterFirewallActionService_CountAllEnabledNodeClusterFirewallActions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterFirewallActionServiceServer).CountAllEnabledNodeClusterFirewallActions(ctx, req.(*CountAllEnabledNodeClusterFirewallActionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeClusterFirewallActionService_ServiceDesc is the grpc.ServiceDesc for NodeClusterFirewallActionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeClusterFirewallActionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeClusterFirewallActionService",
	HandlerType: (*NodeClusterFirewallActionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeClusterFirewallAction",
			Handler:    _NodeClusterFirewallActionService_CreateNodeClusterFirewallAction_Handler,
		},
		{
			MethodName: "updateNodeClusterFirewallAction",
			Handler:    _NodeClusterFirewallActionService_UpdateNodeClusterFirewallAction_Handler,
		},
		{
			MethodName: "deleteNodeClusterFirewallAction",
			Handler:    _NodeClusterFirewallActionService_DeleteNodeClusterFirewallAction_Handler,
		},
		{
			MethodName: "findAllEnabledNodeClusterFirewallActions",
			Handler:    _NodeClusterFirewallActionService_FindAllEnabledNodeClusterFirewallActions_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterFirewallAction",
			Handler:    _NodeClusterFirewallActionService_FindEnabledNodeClusterFirewallAction_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClusterFirewallActions",
			Handler:    _NodeClusterFirewallActionService_CountAllEnabledNodeClusterFirewallActions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_cluster_firewall_action.proto",
}
