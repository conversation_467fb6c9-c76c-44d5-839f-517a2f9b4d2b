// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_script.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ScriptService_CreateScript_FullMethodName           = "/pb.ScriptService/createScript"
	ScriptService_DeleteScript_FullMethodName           = "/pb.ScriptService/deleteScript"
	ScriptService_CountAllEnabledScripts_FullMethodName = "/pb.ScriptService/countAllEnabledScripts"
	ScriptService_ListEnabledScripts_FullMethodName     = "/pb.ScriptService/listEnabledScripts"
	ScriptService_PublishScripts_FullMethodName         = "/pb.ScriptService/publishScripts"
	ScriptService_CheckScriptUpdates_FullMethodName     = "/pb.ScriptService/checkScriptUpdates"
	ScriptService_FindEnabledScript_FullMethodName      = "/pb.ScriptService/findEnabledScript"
	ScriptService_UpdateScript_FullMethodName           = "/pb.ScriptService/updateScript"
	ScriptService_ComposeScriptConfigs_FullMethodName   = "/pb.ScriptService/composeScriptConfigs"
)

// ScriptServiceClient is the client API for ScriptService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScriptServiceClient interface {
	// 添加脚本
	CreateScript(ctx context.Context, in *CreateScriptRequest, opts ...grpc.CallOption) (*CreateScriptResponse, error)
	// 删除脚本
	DeleteScript(ctx context.Context, in *DeleteScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算脚本数量
	CountAllEnabledScripts(ctx context.Context, in *CountAllEnabledScriptsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页脚本
	ListEnabledScripts(ctx context.Context, in *ListEnabledScriptsRequest, opts ...grpc.CallOption) (*ListEnabledScriptsResponse, error)
	// 发布脚本
	PublishScripts(ctx context.Context, in *PublishScriptsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 检查脚本是否需要有更新
	CheckScriptUpdates(ctx context.Context, in *CheckScriptUpdatesRequest, opts ...grpc.CallOption) (*CheckScriptUpdatesResponse, error)
	// 查找单个脚本
	FindEnabledScript(ctx context.Context, in *FindEnabledScriptRequest, opts ...grpc.CallOption) (*FindEnabledScriptResponse, error)
	// 修改脚本
	UpdateScript(ctx context.Context, in *UpdateScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 组合脚本配置
	ComposeScriptConfigs(ctx context.Context, in *ComposeScriptConfigsRequest, opts ...grpc.CallOption) (*ComposeScriptConfigsResponse, error)
}

type scriptServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewScriptServiceClient(cc grpc.ClientConnInterface) ScriptServiceClient {
	return &scriptServiceClient{cc}
}

func (c *scriptServiceClient) CreateScript(ctx context.Context, in *CreateScriptRequest, opts ...grpc.CallOption) (*CreateScriptResponse, error) {
	out := new(CreateScriptResponse)
	err := c.cc.Invoke(ctx, ScriptService_CreateScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) DeleteScript(ctx context.Context, in *DeleteScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ScriptService_DeleteScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) CountAllEnabledScripts(ctx context.Context, in *CountAllEnabledScriptsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ScriptService_CountAllEnabledScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) ListEnabledScripts(ctx context.Context, in *ListEnabledScriptsRequest, opts ...grpc.CallOption) (*ListEnabledScriptsResponse, error) {
	out := new(ListEnabledScriptsResponse)
	err := c.cc.Invoke(ctx, ScriptService_ListEnabledScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) PublishScripts(ctx context.Context, in *PublishScriptsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ScriptService_PublishScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) CheckScriptUpdates(ctx context.Context, in *CheckScriptUpdatesRequest, opts ...grpc.CallOption) (*CheckScriptUpdatesResponse, error) {
	out := new(CheckScriptUpdatesResponse)
	err := c.cc.Invoke(ctx, ScriptService_CheckScriptUpdates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) FindEnabledScript(ctx context.Context, in *FindEnabledScriptRequest, opts ...grpc.CallOption) (*FindEnabledScriptResponse, error) {
	out := new(FindEnabledScriptResponse)
	err := c.cc.Invoke(ctx, ScriptService_FindEnabledScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) UpdateScript(ctx context.Context, in *UpdateScriptRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ScriptService_UpdateScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scriptServiceClient) ComposeScriptConfigs(ctx context.Context, in *ComposeScriptConfigsRequest, opts ...grpc.CallOption) (*ComposeScriptConfigsResponse, error) {
	out := new(ComposeScriptConfigsResponse)
	err := c.cc.Invoke(ctx, ScriptService_ComposeScriptConfigs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScriptServiceServer is the server API for ScriptService service.
// All implementations should embed UnimplementedScriptServiceServer
// for forward compatibility
type ScriptServiceServer interface {
	// 添加脚本
	CreateScript(context.Context, *CreateScriptRequest) (*CreateScriptResponse, error)
	// 删除脚本
	DeleteScript(context.Context, *DeleteScriptRequest) (*RPCSuccess, error)
	// 计算脚本数量
	CountAllEnabledScripts(context.Context, *CountAllEnabledScriptsRequest) (*RPCCountResponse, error)
	// 列出单页脚本
	ListEnabledScripts(context.Context, *ListEnabledScriptsRequest) (*ListEnabledScriptsResponse, error)
	// 发布脚本
	PublishScripts(context.Context, *PublishScriptsRequest) (*RPCSuccess, error)
	// 检查脚本是否需要有更新
	CheckScriptUpdates(context.Context, *CheckScriptUpdatesRequest) (*CheckScriptUpdatesResponse, error)
	// 查找单个脚本
	FindEnabledScript(context.Context, *FindEnabledScriptRequest) (*FindEnabledScriptResponse, error)
	// 修改脚本
	UpdateScript(context.Context, *UpdateScriptRequest) (*RPCSuccess, error)
	// 组合脚本配置
	ComposeScriptConfigs(context.Context, *ComposeScriptConfigsRequest) (*ComposeScriptConfigsResponse, error)
}

// UnimplementedScriptServiceServer should be embedded to have forward compatible implementations.
type UnimplementedScriptServiceServer struct {
}

func (UnimplementedScriptServiceServer) CreateScript(context.Context, *CreateScriptRequest) (*CreateScriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateScript not implemented")
}
func (UnimplementedScriptServiceServer) DeleteScript(context.Context, *DeleteScriptRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteScript not implemented")
}
func (UnimplementedScriptServiceServer) CountAllEnabledScripts(context.Context, *CountAllEnabledScriptsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledScripts not implemented")
}
func (UnimplementedScriptServiceServer) ListEnabledScripts(context.Context, *ListEnabledScriptsRequest) (*ListEnabledScriptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledScripts not implemented")
}
func (UnimplementedScriptServiceServer) PublishScripts(context.Context, *PublishScriptsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishScripts not implemented")
}
func (UnimplementedScriptServiceServer) CheckScriptUpdates(context.Context, *CheckScriptUpdatesRequest) (*CheckScriptUpdatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckScriptUpdates not implemented")
}
func (UnimplementedScriptServiceServer) FindEnabledScript(context.Context, *FindEnabledScriptRequest) (*FindEnabledScriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledScript not implemented")
}
func (UnimplementedScriptServiceServer) UpdateScript(context.Context, *UpdateScriptRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateScript not implemented")
}
func (UnimplementedScriptServiceServer) ComposeScriptConfigs(context.Context, *ComposeScriptConfigsRequest) (*ComposeScriptConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeScriptConfigs not implemented")
}

// UnsafeScriptServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScriptServiceServer will
// result in compilation errors.
type UnsafeScriptServiceServer interface {
	mustEmbedUnimplementedScriptServiceServer()
}

func RegisterScriptServiceServer(s grpc.ServiceRegistrar, srv ScriptServiceServer) {
	s.RegisterService(&ScriptService_ServiceDesc, srv)
}

func _ScriptService_CreateScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).CreateScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_CreateScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).CreateScript(ctx, req.(*CreateScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_DeleteScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).DeleteScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_DeleteScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).DeleteScript(ctx, req.(*DeleteScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_CountAllEnabledScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).CountAllEnabledScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_CountAllEnabledScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).CountAllEnabledScripts(ctx, req.(*CountAllEnabledScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_ListEnabledScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).ListEnabledScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_ListEnabledScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).ListEnabledScripts(ctx, req.(*ListEnabledScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_PublishScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).PublishScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_PublishScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).PublishScripts(ctx, req.(*PublishScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_CheckScriptUpdates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckScriptUpdatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).CheckScriptUpdates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_CheckScriptUpdates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).CheckScriptUpdates(ctx, req.(*CheckScriptUpdatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_FindEnabledScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).FindEnabledScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_FindEnabledScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).FindEnabledScript(ctx, req.(*FindEnabledScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_UpdateScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).UpdateScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_UpdateScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).UpdateScript(ctx, req.(*UpdateScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScriptService_ComposeScriptConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeScriptConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScriptServiceServer).ComposeScriptConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScriptService_ComposeScriptConfigs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScriptServiceServer).ComposeScriptConfigs(ctx, req.(*ComposeScriptConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ScriptService_ServiceDesc is the grpc.ServiceDesc for ScriptService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ScriptService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ScriptService",
	HandlerType: (*ScriptServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createScript",
			Handler:    _ScriptService_CreateScript_Handler,
		},
		{
			MethodName: "deleteScript",
			Handler:    _ScriptService_DeleteScript_Handler,
		},
		{
			MethodName: "countAllEnabledScripts",
			Handler:    _ScriptService_CountAllEnabledScripts_Handler,
		},
		{
			MethodName: "listEnabledScripts",
			Handler:    _ScriptService_ListEnabledScripts_Handler,
		},
		{
			MethodName: "publishScripts",
			Handler:    _ScriptService_PublishScripts_Handler,
		},
		{
			MethodName: "checkScriptUpdates",
			Handler:    _ScriptService_CheckScriptUpdates_Handler,
		},
		{
			MethodName: "findEnabledScript",
			Handler:    _ScriptService_FindEnabledScript_Handler,
		},
		{
			MethodName: "updateScript",
			Handler:    _ScriptService_UpdateScript_Handler,
		},
		{
			MethodName: "composeScriptConfigs",
			Handler:    _ScriptService_ComposeScriptConfigs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_script.proto",
}
