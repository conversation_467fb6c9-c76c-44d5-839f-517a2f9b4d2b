// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_sub_user.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SubUserService_CreateSubUser_FullMethodName        = "/pb.SubUserService/createSubUser"
	SubUserService_UpdateSubUser_FullMethodName        = "/pb.SubUserService/updateSubUser"
	SubUserService_DeleteSubUser_FullMethodName        = "/pb.SubUserService/deleteSubUser"
	SubUserService_CountSubUsers_FullMethodName        = "/pb.SubUserService/countSubUsers"
	SubUserService_ListSubUsers_FullMethodName         = "/pb.SubUserService/listSubUsers"
	SubUserService_FindEnabledSubUser_FullMethodName   = "/pb.SubUserService/findEnabledSubUser"
	SubUserService_CheckSubUserUsername_FullMethodName = "/pb.SubUserService/checkSubUserUsername"
)

// SubUserServiceClient is the client API for SubUserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubUserServiceClient interface {
	// 创建子用户
	CreateSubUser(ctx context.Context, in *CreateSubUserRequest, opts ...grpc.CallOption) (*CreateSubUserResponse, error)
	// 修改子用户
	UpdateSubUser(ctx context.Context, in *UpdateSubUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除子用户
	DeleteSubUser(ctx context.Context, in *DeleteSubUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算子用户数量
	CountSubUsers(ctx context.Context, in *CountSubUsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页子用户
	ListSubUsers(ctx context.Context, in *ListSubUsersRequest, opts ...grpc.CallOption) (*ListSubUsersResponse, error)
	// 查找单个子用户
	FindEnabledSubUser(ctx context.Context, in *FindEnabledSubUserRequest, opts ...grpc.CallOption) (*FindEnabledSubUserResponse, error)
	// 检查子用户名是否存在
	CheckSubUserUsername(ctx context.Context, in *CheckSubUserUsernameRequest, opts ...grpc.CallOption) (*CheckSubUserUsernameResponse, error)
}

type subUserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSubUserServiceClient(cc grpc.ClientConnInterface) SubUserServiceClient {
	return &subUserServiceClient{cc}
}

func (c *subUserServiceClient) CreateSubUser(ctx context.Context, in *CreateSubUserRequest, opts ...grpc.CallOption) (*CreateSubUserResponse, error) {
	out := new(CreateSubUserResponse)
	err := c.cc.Invoke(ctx, SubUserService_CreateSubUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subUserServiceClient) UpdateSubUser(ctx context.Context, in *UpdateSubUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, SubUserService_UpdateSubUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subUserServiceClient) DeleteSubUser(ctx context.Context, in *DeleteSubUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, SubUserService_DeleteSubUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subUserServiceClient) CountSubUsers(ctx context.Context, in *CountSubUsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, SubUserService_CountSubUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subUserServiceClient) ListSubUsers(ctx context.Context, in *ListSubUsersRequest, opts ...grpc.CallOption) (*ListSubUsersResponse, error) {
	out := new(ListSubUsersResponse)
	err := c.cc.Invoke(ctx, SubUserService_ListSubUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subUserServiceClient) FindEnabledSubUser(ctx context.Context, in *FindEnabledSubUserRequest, opts ...grpc.CallOption) (*FindEnabledSubUserResponse, error) {
	out := new(FindEnabledSubUserResponse)
	err := c.cc.Invoke(ctx, SubUserService_FindEnabledSubUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subUserServiceClient) CheckSubUserUsername(ctx context.Context, in *CheckSubUserUsernameRequest, opts ...grpc.CallOption) (*CheckSubUserUsernameResponse, error) {
	out := new(CheckSubUserUsernameResponse)
	err := c.cc.Invoke(ctx, SubUserService_CheckSubUserUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubUserServiceServer is the server API for SubUserService service.
// All implementations should embed UnimplementedSubUserServiceServer
// for forward compatibility
type SubUserServiceServer interface {
	// 创建子用户
	CreateSubUser(context.Context, *CreateSubUserRequest) (*CreateSubUserResponse, error)
	// 修改子用户
	UpdateSubUser(context.Context, *UpdateSubUserRequest) (*RPCSuccess, error)
	// 删除子用户
	DeleteSubUser(context.Context, *DeleteSubUserRequest) (*RPCSuccess, error)
	// 计算子用户数量
	CountSubUsers(context.Context, *CountSubUsersRequest) (*RPCCountResponse, error)
	// 列出单页子用户
	ListSubUsers(context.Context, *ListSubUsersRequest) (*ListSubUsersResponse, error)
	// 查找单个子用户
	FindEnabledSubUser(context.Context, *FindEnabledSubUserRequest) (*FindEnabledSubUserResponse, error)
	// 检查子用户名是否存在
	CheckSubUserUsername(context.Context, *CheckSubUserUsernameRequest) (*CheckSubUserUsernameResponse, error)
}

// UnimplementedSubUserServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSubUserServiceServer struct {
}

func (UnimplementedSubUserServiceServer) CreateSubUser(context.Context, *CreateSubUserRequest) (*CreateSubUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSubUser not implemented")
}
func (UnimplementedSubUserServiceServer) UpdateSubUser(context.Context, *UpdateSubUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubUser not implemented")
}
func (UnimplementedSubUserServiceServer) DeleteSubUser(context.Context, *DeleteSubUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSubUser not implemented")
}
func (UnimplementedSubUserServiceServer) CountSubUsers(context.Context, *CountSubUsersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountSubUsers not implemented")
}
func (UnimplementedSubUserServiceServer) ListSubUsers(context.Context, *ListSubUsersRequest) (*ListSubUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubUsers not implemented")
}
func (UnimplementedSubUserServiceServer) FindEnabledSubUser(context.Context, *FindEnabledSubUserRequest) (*FindEnabledSubUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledSubUser not implemented")
}
func (UnimplementedSubUserServiceServer) CheckSubUserUsername(context.Context, *CheckSubUserUsernameRequest) (*CheckSubUserUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSubUserUsername not implemented")
}

// UnsafeSubUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubUserServiceServer will
// result in compilation errors.
type UnsafeSubUserServiceServer interface {
	mustEmbedUnimplementedSubUserServiceServer()
}

func RegisterSubUserServiceServer(s grpc.ServiceRegistrar, srv SubUserServiceServer) {
	s.RegisterService(&SubUserService_ServiceDesc, srv)
}

func _SubUserService_CreateSubUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSubUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).CreateSubUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_CreateSubUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).CreateSubUser(ctx, req.(*CreateSubUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubUserService_UpdateSubUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).UpdateSubUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_UpdateSubUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).UpdateSubUser(ctx, req.(*UpdateSubUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubUserService_DeleteSubUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSubUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).DeleteSubUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_DeleteSubUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).DeleteSubUser(ctx, req.(*DeleteSubUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubUserService_CountSubUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountSubUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).CountSubUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_CountSubUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).CountSubUsers(ctx, req.(*CountSubUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubUserService_ListSubUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).ListSubUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_ListSubUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).ListSubUsers(ctx, req.(*ListSubUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubUserService_FindEnabledSubUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledSubUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).FindEnabledSubUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_FindEnabledSubUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).FindEnabledSubUser(ctx, req.(*FindEnabledSubUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubUserService_CheckSubUserUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSubUserUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubUserServiceServer).CheckSubUserUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SubUserService_CheckSubUserUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubUserServiceServer).CheckSubUserUsername(ctx, req.(*CheckSubUserUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SubUserService_ServiceDesc is the grpc.ServiceDesc for SubUserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SubUserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.SubUserService",
	HandlerType: (*SubUserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createSubUser",
			Handler:    _SubUserService_CreateSubUser_Handler,
		},
		{
			MethodName: "updateSubUser",
			Handler:    _SubUserService_UpdateSubUser_Handler,
		},
		{
			MethodName: "deleteSubUser",
			Handler:    _SubUserService_DeleteSubUser_Handler,
		},
		{
			MethodName: "countSubUsers",
			Handler:    _SubUserService_CountSubUsers_Handler,
		},
		{
			MethodName: "listSubUsers",
			Handler:    _SubUserService_ListSubUsers_Handler,
		},
		{
			MethodName: "findEnabledSubUser",
			Handler:    _SubUserService_FindEnabledSubUser_Handler,
		},
		{
			MethodName: "checkSubUserUsername",
			Handler:    _SubUserService_CheckSubUserUsername_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_sub_user.proto",
}
