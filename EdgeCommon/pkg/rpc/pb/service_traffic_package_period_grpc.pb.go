// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_traffic_package_period.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TrafficPackagePeriodService_CreateTrafficPackagePeriod_FullMethodName            = "/pb.TrafficPackagePeriodService/createTrafficPackagePeriod"
	TrafficPackagePeriodService_UpdateTrafficPackagePeriod_FullMethodName            = "/pb.TrafficPackagePeriodService/updateTrafficPackagePeriod"
	TrafficPackagePeriodService_DeleteTrafficPackagePeriod_FullMethodName            = "/pb.TrafficPackagePeriodService/deleteTrafficPackagePeriod"
	TrafficPackagePeriodService_FindTrafficPackagePeriod_FullMethodName              = "/pb.TrafficPackagePeriodService/findTrafficPackagePeriod"
	TrafficPackagePeriodService_FindAllTrafficPackagePeriods_FullMethodName          = "/pb.TrafficPackagePeriodService/findAllTrafficPackagePeriods"
	TrafficPackagePeriodService_FindAllAvailableTrafficPackagePeriods_FullMethodName = "/pb.TrafficPackagePeriodService/findAllAvailableTrafficPackagePeriods"
)

// TrafficPackagePeriodServiceClient is the client API for TrafficPackagePeriodService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TrafficPackagePeriodServiceClient interface {
	// 创建有效期
	CreateTrafficPackagePeriod(ctx context.Context, in *CreateTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*CreateTrafficPackagePeriodResponse, error)
	// 修改有效期
	UpdateTrafficPackagePeriod(ctx context.Context, in *UpdateTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除有效期
	DeleteTrafficPackagePeriod(ctx context.Context, in *DeleteTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找有效期
	FindTrafficPackagePeriod(ctx context.Context, in *FindTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*FindTrafficPackagePeriodResponse, error)
	// 列出所有有效期
	FindAllTrafficPackagePeriods(ctx context.Context, in *FindAllTrafficPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllTrafficPackagePeriodsResponse, error)
	// 列出所有可用有效期
	FindAllAvailableTrafficPackagePeriods(ctx context.Context, in *FindAllAvailableTrafficPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllAvailableTrafficPackagePeriodsResponse, error)
}

type trafficPackagePeriodServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTrafficPackagePeriodServiceClient(cc grpc.ClientConnInterface) TrafficPackagePeriodServiceClient {
	return &trafficPackagePeriodServiceClient{cc}
}

func (c *trafficPackagePeriodServiceClient) CreateTrafficPackagePeriod(ctx context.Context, in *CreateTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*CreateTrafficPackagePeriodResponse, error) {
	out := new(CreateTrafficPackagePeriodResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePeriodService_CreateTrafficPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePeriodServiceClient) UpdateTrafficPackagePeriod(ctx context.Context, in *UpdateTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, TrafficPackagePeriodService_UpdateTrafficPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePeriodServiceClient) DeleteTrafficPackagePeriod(ctx context.Context, in *DeleteTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, TrafficPackagePeriodService_DeleteTrafficPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePeriodServiceClient) FindTrafficPackagePeriod(ctx context.Context, in *FindTrafficPackagePeriodRequest, opts ...grpc.CallOption) (*FindTrafficPackagePeriodResponse, error) {
	out := new(FindTrafficPackagePeriodResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePeriodService_FindTrafficPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePeriodServiceClient) FindAllTrafficPackagePeriods(ctx context.Context, in *FindAllTrafficPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllTrafficPackagePeriodsResponse, error) {
	out := new(FindAllTrafficPackagePeriodsResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePeriodService_FindAllTrafficPackagePeriods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackagePeriodServiceClient) FindAllAvailableTrafficPackagePeriods(ctx context.Context, in *FindAllAvailableTrafficPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllAvailableTrafficPackagePeriodsResponse, error) {
	out := new(FindAllAvailableTrafficPackagePeriodsResponse)
	err := c.cc.Invoke(ctx, TrafficPackagePeriodService_FindAllAvailableTrafficPackagePeriods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TrafficPackagePeriodServiceServer is the server API for TrafficPackagePeriodService service.
// All implementations should embed UnimplementedTrafficPackagePeriodServiceServer
// for forward compatibility
type TrafficPackagePeriodServiceServer interface {
	// 创建有效期
	CreateTrafficPackagePeriod(context.Context, *CreateTrafficPackagePeriodRequest) (*CreateTrafficPackagePeriodResponse, error)
	// 修改有效期
	UpdateTrafficPackagePeriod(context.Context, *UpdateTrafficPackagePeriodRequest) (*RPCSuccess, error)
	// 删除有效期
	DeleteTrafficPackagePeriod(context.Context, *DeleteTrafficPackagePeriodRequest) (*RPCSuccess, error)
	// 查找有效期
	FindTrafficPackagePeriod(context.Context, *FindTrafficPackagePeriodRequest) (*FindTrafficPackagePeriodResponse, error)
	// 列出所有有效期
	FindAllTrafficPackagePeriods(context.Context, *FindAllTrafficPackagePeriodsRequest) (*FindAllTrafficPackagePeriodsResponse, error)
	// 列出所有可用有效期
	FindAllAvailableTrafficPackagePeriods(context.Context, *FindAllAvailableTrafficPackagePeriodsRequest) (*FindAllAvailableTrafficPackagePeriodsResponse, error)
}

// UnimplementedTrafficPackagePeriodServiceServer should be embedded to have forward compatible implementations.
type UnimplementedTrafficPackagePeriodServiceServer struct {
}

func (UnimplementedTrafficPackagePeriodServiceServer) CreateTrafficPackagePeriod(context.Context, *CreateTrafficPackagePeriodRequest) (*CreateTrafficPackagePeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTrafficPackagePeriod not implemented")
}
func (UnimplementedTrafficPackagePeriodServiceServer) UpdateTrafficPackagePeriod(context.Context, *UpdateTrafficPackagePeriodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTrafficPackagePeriod not implemented")
}
func (UnimplementedTrafficPackagePeriodServiceServer) DeleteTrafficPackagePeriod(context.Context, *DeleteTrafficPackagePeriodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTrafficPackagePeriod not implemented")
}
func (UnimplementedTrafficPackagePeriodServiceServer) FindTrafficPackagePeriod(context.Context, *FindTrafficPackagePeriodRequest) (*FindTrafficPackagePeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTrafficPackagePeriod not implemented")
}
func (UnimplementedTrafficPackagePeriodServiceServer) FindAllTrafficPackagePeriods(context.Context, *FindAllTrafficPackagePeriodsRequest) (*FindAllTrafficPackagePeriodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllTrafficPackagePeriods not implemented")
}
func (UnimplementedTrafficPackagePeriodServiceServer) FindAllAvailableTrafficPackagePeriods(context.Context, *FindAllAvailableTrafficPackagePeriodsRequest) (*FindAllAvailableTrafficPackagePeriodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailableTrafficPackagePeriods not implemented")
}

// UnsafeTrafficPackagePeriodServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TrafficPackagePeriodServiceServer will
// result in compilation errors.
type UnsafeTrafficPackagePeriodServiceServer interface {
	mustEmbedUnimplementedTrafficPackagePeriodServiceServer()
}

func RegisterTrafficPackagePeriodServiceServer(s grpc.ServiceRegistrar, srv TrafficPackagePeriodServiceServer) {
	s.RegisterService(&TrafficPackagePeriodService_ServiceDesc, srv)
}

func _TrafficPackagePeriodService_CreateTrafficPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTrafficPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePeriodServiceServer).CreateTrafficPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePeriodService_CreateTrafficPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePeriodServiceServer).CreateTrafficPackagePeriod(ctx, req.(*CreateTrafficPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePeriodService_UpdateTrafficPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTrafficPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePeriodServiceServer).UpdateTrafficPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePeriodService_UpdateTrafficPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePeriodServiceServer).UpdateTrafficPackagePeriod(ctx, req.(*UpdateTrafficPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePeriodService_DeleteTrafficPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTrafficPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePeriodServiceServer).DeleteTrafficPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePeriodService_DeleteTrafficPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePeriodServiceServer).DeleteTrafficPackagePeriod(ctx, req.(*DeleteTrafficPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePeriodService_FindTrafficPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTrafficPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePeriodServiceServer).FindTrafficPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePeriodService_FindTrafficPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePeriodServiceServer).FindTrafficPackagePeriod(ctx, req.(*FindTrafficPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePeriodService_FindAllTrafficPackagePeriods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllTrafficPackagePeriodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePeriodServiceServer).FindAllTrafficPackagePeriods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePeriodService_FindAllTrafficPackagePeriods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePeriodServiceServer).FindAllTrafficPackagePeriods(ctx, req.(*FindAllTrafficPackagePeriodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackagePeriodService_FindAllAvailableTrafficPackagePeriods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailableTrafficPackagePeriodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackagePeriodServiceServer).FindAllAvailableTrafficPackagePeriods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackagePeriodService_FindAllAvailableTrafficPackagePeriods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackagePeriodServiceServer).FindAllAvailableTrafficPackagePeriods(ctx, req.(*FindAllAvailableTrafficPackagePeriodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TrafficPackagePeriodService_ServiceDesc is the grpc.ServiceDesc for TrafficPackagePeriodService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TrafficPackagePeriodService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TrafficPackagePeriodService",
	HandlerType: (*TrafficPackagePeriodServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createTrafficPackagePeriod",
			Handler:    _TrafficPackagePeriodService_CreateTrafficPackagePeriod_Handler,
		},
		{
			MethodName: "updateTrafficPackagePeriod",
			Handler:    _TrafficPackagePeriodService_UpdateTrafficPackagePeriod_Handler,
		},
		{
			MethodName: "deleteTrafficPackagePeriod",
			Handler:    _TrafficPackagePeriodService_DeleteTrafficPackagePeriod_Handler,
		},
		{
			MethodName: "findTrafficPackagePeriod",
			Handler:    _TrafficPackagePeriodService_FindTrafficPackagePeriod_Handler,
		},
		{
			MethodName: "findAllTrafficPackagePeriods",
			Handler:    _TrafficPackagePeriodService_FindAllTrafficPackagePeriods_Handler,
		},
		{
			MethodName: "findAllAvailableTrafficPackagePeriods",
			Handler:    _TrafficPackagePeriodService_FindAllAvailableTrafficPackagePeriods_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_traffic_package_period.proto",
}
