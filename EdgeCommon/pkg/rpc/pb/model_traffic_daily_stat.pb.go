// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_traffic_daily_stat.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrafficDailyStat struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Day                 string                 `protobuf:"bytes,2,opt,name=day,proto3" json:"day,omitempty"`
	CachedBytes         int64                  `protobuf:"varint,3,opt,name=cachedBytes,proto3" json:"cachedBytes,omitempty"`
	Bytes               int64                  `protobuf:"varint,4,opt,name=bytes,proto3" json:"bytes,omitempty"`
	CountRequests       int64                  `protobuf:"varint,5,opt,name=countRequests,proto3" json:"countRequests,omitempty"`
	CountCachedRequests int64                  `protobuf:"varint,6,opt,name=countCachedRequests,proto3" json:"countCachedRequests,omitempty"`
	CountAttackRequests int64                  `protobuf:"varint,7,opt,name=countAttackRequests,proto3" json:"countAttackRequests,omitempty"`
	AttackBytes         int64                  `protobuf:"varint,8,opt,name=attackBytes,proto3" json:"attackBytes,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TrafficDailyStat) Reset() {
	*x = TrafficDailyStat{}
	mi := &file_models_model_traffic_daily_stat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficDailyStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficDailyStat) ProtoMessage() {}

func (x *TrafficDailyStat) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_traffic_daily_stat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficDailyStat.ProtoReflect.Descriptor instead.
func (*TrafficDailyStat) Descriptor() ([]byte, []int) {
	return file_models_model_traffic_daily_stat_proto_rawDescGZIP(), []int{0}
}

func (x *TrafficDailyStat) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrafficDailyStat) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *TrafficDailyStat) GetCachedBytes() int64 {
	if x != nil {
		return x.CachedBytes
	}
	return 0
}

func (x *TrafficDailyStat) GetBytes() int64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *TrafficDailyStat) GetCountRequests() int64 {
	if x != nil {
		return x.CountRequests
	}
	return 0
}

func (x *TrafficDailyStat) GetCountCachedRequests() int64 {
	if x != nil {
		return x.CountCachedRequests
	}
	return 0
}

func (x *TrafficDailyStat) GetCountAttackRequests() int64 {
	if x != nil {
		return x.CountAttackRequests
	}
	return 0
}

func (x *TrafficDailyStat) GetAttackBytes() int64 {
	if x != nil {
		return x.AttackBytes
	}
	return 0
}

var File_models_model_traffic_daily_stat_proto protoreflect.FileDescriptor

const file_models_model_traffic_daily_stat_proto_rawDesc = "" +
	"\n" +
	"%models/model_traffic_daily_stat.proto\x12\x02pb\"\x98\x02\n" +
	"\x10TrafficDailyStat\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03day\x18\x02 \x01(\tR\x03day\x12 \n" +
	"\vcachedBytes\x18\x03 \x01(\x03R\vcachedBytes\x12\x14\n" +
	"\x05bytes\x18\x04 \x01(\x03R\x05bytes\x12$\n" +
	"\rcountRequests\x18\x05 \x01(\x03R\rcountRequests\x120\n" +
	"\x13countCachedRequests\x18\x06 \x01(\x03R\x13countCachedRequests\x120\n" +
	"\x13countAttackRequests\x18\a \x01(\x03R\x13countAttackRequests\x12 \n" +
	"\vattackBytes\x18\b \x01(\x03R\vattackBytesB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_traffic_daily_stat_proto_rawDescOnce sync.Once
	file_models_model_traffic_daily_stat_proto_rawDescData []byte
)

func file_models_model_traffic_daily_stat_proto_rawDescGZIP() []byte {
	file_models_model_traffic_daily_stat_proto_rawDescOnce.Do(func() {
		file_models_model_traffic_daily_stat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_traffic_daily_stat_proto_rawDesc), len(file_models_model_traffic_daily_stat_proto_rawDesc)))
	})
	return file_models_model_traffic_daily_stat_proto_rawDescData
}

var file_models_model_traffic_daily_stat_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_traffic_daily_stat_proto_goTypes = []any{
	(*TrafficDailyStat)(nil), // 0: pb.TrafficDailyStat
}
var file_models_model_traffic_daily_stat_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_traffic_daily_stat_proto_init() }
func file_models_model_traffic_daily_stat_proto_init() {
	if File_models_model_traffic_daily_stat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_traffic_daily_stat_proto_rawDesc), len(file_models_model_traffic_daily_stat_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_traffic_daily_stat_proto_goTypes,
		DependencyIndexes: file_models_model_traffic_daily_stat_proto_depIdxs,
		MessageInfos:      file_models_model_traffic_daily_stat_proto_msgTypes,
	}.Build()
	File_models_model_traffic_daily_stat_proto = out.File
	file_models_model_traffic_daily_stat_proto_goTypes = nil
	file_models_model_traffic_daily_stat_proto_depIdxs = nil
}
