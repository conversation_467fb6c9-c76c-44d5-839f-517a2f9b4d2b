// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_sys_setting.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SysSettingService_UpdateSysSetting_FullMethodName = "/pb.SysSettingService/updateSysSetting"
	SysSettingService_ReadSysSetting_FullMethodName   = "/pb.SysSettingService/readSysSetting"
)

// SysSettingServiceClient is the client API for SysSettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SysSettingServiceClient interface {
	// 更改配置
	UpdateSysSetting(ctx context.Context, in *UpdateSysSettingRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取配置
	ReadSysSetting(ctx context.Context, in *ReadSysSettingRequest, opts ...grpc.CallOption) (*ReadSysSettingResponse, error)
}

type sysSettingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSysSettingServiceClient(cc grpc.ClientConnInterface) SysSettingServiceClient {
	return &sysSettingServiceClient{cc}
}

func (c *sysSettingServiceClient) UpdateSysSetting(ctx context.Context, in *UpdateSysSettingRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, SysSettingService_UpdateSysSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sysSettingServiceClient) ReadSysSetting(ctx context.Context, in *ReadSysSettingRequest, opts ...grpc.CallOption) (*ReadSysSettingResponse, error) {
	out := new(ReadSysSettingResponse)
	err := c.cc.Invoke(ctx, SysSettingService_ReadSysSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SysSettingServiceServer is the server API for SysSettingService service.
// All implementations should embed UnimplementedSysSettingServiceServer
// for forward compatibility
type SysSettingServiceServer interface {
	// 更改配置
	UpdateSysSetting(context.Context, *UpdateSysSettingRequest) (*RPCSuccess, error)
	// 读取配置
	ReadSysSetting(context.Context, *ReadSysSettingRequest) (*ReadSysSettingResponse, error)
}

// UnimplementedSysSettingServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSysSettingServiceServer struct {
}

func (UnimplementedSysSettingServiceServer) UpdateSysSetting(context.Context, *UpdateSysSettingRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSysSetting not implemented")
}
func (UnimplementedSysSettingServiceServer) ReadSysSetting(context.Context, *ReadSysSettingRequest) (*ReadSysSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadSysSetting not implemented")
}

// UnsafeSysSettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SysSettingServiceServer will
// result in compilation errors.
type UnsafeSysSettingServiceServer interface {
	mustEmbedUnimplementedSysSettingServiceServer()
}

func RegisterSysSettingServiceServer(s grpc.ServiceRegistrar, srv SysSettingServiceServer) {
	s.RegisterService(&SysSettingService_ServiceDesc, srv)
}

func _SysSettingService_UpdateSysSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSysSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SysSettingServiceServer).UpdateSysSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SysSettingService_UpdateSysSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SysSettingServiceServer).UpdateSysSetting(ctx, req.(*UpdateSysSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SysSettingService_ReadSysSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadSysSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SysSettingServiceServer).ReadSysSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SysSettingService_ReadSysSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SysSettingServiceServer).ReadSysSetting(ctx, req.(*ReadSysSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SysSettingService_ServiceDesc is the grpc.ServiceDesc for SysSettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SysSettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.SysSettingService",
	HandlerType: (*SysSettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "updateSysSetting",
			Handler:    _SysSettingService_UpdateSysSetting_Handler,
		},
		{
			MethodName: "readSysSetting",
			Handler:    _SysSettingService_ReadSysSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_sys_setting.proto",
}
