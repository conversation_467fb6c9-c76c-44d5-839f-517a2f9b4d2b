// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_login_session.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LoginSessionService_WriteLoginSessionValue_FullMethodName = "/pb.LoginSessionService/writeLoginSessionValue"
	LoginSessionService_DeleteLoginSession_FullMethodName     = "/pb.LoginSessionService/deleteLoginSession"
	LoginSessionService_FindLoginSession_FullMethodName       = "/pb.LoginSessionService/findLoginSession"
	LoginSessionService_ClearOldLoginSessions_FullMethodName  = "/pb.LoginSessionService/clearOldLoginSessions"
)

// LoginSessionServiceClient is the client API for LoginSessionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LoginSessionServiceClient interface {
	// 写入SESSION数据
	WriteLoginSessionValue(ctx context.Context, in *WriteLoginSessionValueRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除SESSION
	DeleteLoginSession(ctx context.Context, in *DeleteLoginSessionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找SESSION
	FindLoginSession(ctx context.Context, in *FindLoginSessionRequest, opts ...grpc.CallOption) (*FindLoginSessionResponse, error)
	// 清理老的SESSION
	ClearOldLoginSessions(ctx context.Context, in *ClearOldLoginSessionsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type loginSessionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLoginSessionServiceClient(cc grpc.ClientConnInterface) LoginSessionServiceClient {
	return &loginSessionServiceClient{cc}
}

func (c *loginSessionServiceClient) WriteLoginSessionValue(ctx context.Context, in *WriteLoginSessionValueRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, LoginSessionService_WriteLoginSessionValue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loginSessionServiceClient) DeleteLoginSession(ctx context.Context, in *DeleteLoginSessionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, LoginSessionService_DeleteLoginSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loginSessionServiceClient) FindLoginSession(ctx context.Context, in *FindLoginSessionRequest, opts ...grpc.CallOption) (*FindLoginSessionResponse, error) {
	out := new(FindLoginSessionResponse)
	err := c.cc.Invoke(ctx, LoginSessionService_FindLoginSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loginSessionServiceClient) ClearOldLoginSessions(ctx context.Context, in *ClearOldLoginSessionsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, LoginSessionService_ClearOldLoginSessions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LoginSessionServiceServer is the server API for LoginSessionService service.
// All implementations should embed UnimplementedLoginSessionServiceServer
// for forward compatibility
type LoginSessionServiceServer interface {
	// 写入SESSION数据
	WriteLoginSessionValue(context.Context, *WriteLoginSessionValueRequest) (*RPCSuccess, error)
	// 删除SESSION
	DeleteLoginSession(context.Context, *DeleteLoginSessionRequest) (*RPCSuccess, error)
	// 查找SESSION
	FindLoginSession(context.Context, *FindLoginSessionRequest) (*FindLoginSessionResponse, error)
	// 清理老的SESSION
	ClearOldLoginSessions(context.Context, *ClearOldLoginSessionsRequest) (*RPCSuccess, error)
}

// UnimplementedLoginSessionServiceServer should be embedded to have forward compatible implementations.
type UnimplementedLoginSessionServiceServer struct {
}

func (UnimplementedLoginSessionServiceServer) WriteLoginSessionValue(context.Context, *WriteLoginSessionValueRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteLoginSessionValue not implemented")
}
func (UnimplementedLoginSessionServiceServer) DeleteLoginSession(context.Context, *DeleteLoginSessionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLoginSession not implemented")
}
func (UnimplementedLoginSessionServiceServer) FindLoginSession(context.Context, *FindLoginSessionRequest) (*FindLoginSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLoginSession not implemented")
}
func (UnimplementedLoginSessionServiceServer) ClearOldLoginSessions(context.Context, *ClearOldLoginSessionsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearOldLoginSessions not implemented")
}

// UnsafeLoginSessionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LoginSessionServiceServer will
// result in compilation errors.
type UnsafeLoginSessionServiceServer interface {
	mustEmbedUnimplementedLoginSessionServiceServer()
}

func RegisterLoginSessionServiceServer(s grpc.ServiceRegistrar, srv LoginSessionServiceServer) {
	s.RegisterService(&LoginSessionService_ServiceDesc, srv)
}

func _LoginSessionService_WriteLoginSessionValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteLoginSessionValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoginSessionServiceServer).WriteLoginSessionValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoginSessionService_WriteLoginSessionValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoginSessionServiceServer).WriteLoginSessionValue(ctx, req.(*WriteLoginSessionValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoginSessionService_DeleteLoginSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLoginSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoginSessionServiceServer).DeleteLoginSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoginSessionService_DeleteLoginSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoginSessionServiceServer).DeleteLoginSession(ctx, req.(*DeleteLoginSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoginSessionService_FindLoginSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLoginSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoginSessionServiceServer).FindLoginSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoginSessionService_FindLoginSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoginSessionServiceServer).FindLoginSession(ctx, req.(*FindLoginSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoginSessionService_ClearOldLoginSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearOldLoginSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoginSessionServiceServer).ClearOldLoginSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoginSessionService_ClearOldLoginSessions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoginSessionServiceServer).ClearOldLoginSessions(ctx, req.(*ClearOldLoginSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LoginSessionService_ServiceDesc is the grpc.ServiceDesc for LoginSessionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LoginSessionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.LoginSessionService",
	HandlerType: (*LoginSessionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "writeLoginSessionValue",
			Handler:    _LoginSessionService_WriteLoginSessionValue_Handler,
		},
		{
			MethodName: "deleteLoginSession",
			Handler:    _LoginSessionService_DeleteLoginSession_Handler,
		},
		{
			MethodName: "findLoginSession",
			Handler:    _LoginSessionService_FindLoginSession_Handler,
		},
		{
			MethodName: "clearOldLoginSessions",
			Handler:    _LoginSessionService_ClearOldLoginSessions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_login_session.proto",
}
