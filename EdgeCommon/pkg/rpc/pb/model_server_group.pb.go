// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server_group.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerGroup struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         // ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`      // 分组名称
	UserId        int64                  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"` // 所属用户ID
	IsOn          bool                   `protobuf:"varint,4,opt,name=isOn,proto3" json:"isOn,omitempty"`     // 是否启用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerGroup) Reset() {
	*x = ServerGroup{}
	mi := &file_models_model_server_group_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerGroup) ProtoMessage() {}

func (x *ServerGroup) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_group_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerGroup.ProtoReflect.Descriptor instead.
func (*ServerGroup) Descriptor() ([]byte, []int) {
	return file_models_model_server_group_proto_rawDescGZIP(), []int{0}
}

func (x *ServerGroup) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServerGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServerGroup) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ServerGroup) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

var File_models_model_server_group_proto protoreflect.FileDescriptor

const file_models_model_server_group_proto_rawDesc = "" +
	"\n" +
	"\x1fmodels/model_server_group.proto\x12\x02pb\"]\n" +
	"\vServerGroup\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\x03R\x06userId\x12\x12\n" +
	"\x04isOn\x18\x04 \x01(\bR\x04isOnB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_group_proto_rawDescOnce sync.Once
	file_models_model_server_group_proto_rawDescData []byte
)

func file_models_model_server_group_proto_rawDescGZIP() []byte {
	file_models_model_server_group_proto_rawDescOnce.Do(func() {
		file_models_model_server_group_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_group_proto_rawDesc), len(file_models_model_server_group_proto_rawDesc)))
	})
	return file_models_model_server_group_proto_rawDescData
}

var file_models_model_server_group_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_group_proto_goTypes = []any{
	(*ServerGroup)(nil), // 0: pb.ServerGroup
}
var file_models_model_server_group_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_server_group_proto_init() }
func file_models_model_server_group_proto_init() {
	if File_models_model_server_group_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_group_proto_rawDesc), len(file_models_model_server_group_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_group_proto_goTypes,
		DependencyIndexes: file_models_model_server_group_proto_depIdxs,
		MessageInfos:      file_models_model_server_group_proto_msgTypes,
	}.Build()
	File_models_model_server_group_proto = out.File
	file_models_model_server_group_proto_goTypes = nil
	file_models_model_server_group_proto_depIdxs = nil
}
