// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_region_provider_monthly_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerRegionProviderMonthlyStatService_FindTopServerRegionProviderMonthlyStats_FullMethodName = "/pb.ServerRegionProviderMonthlyStatService/findTopServerRegionProviderMonthlyStats"
)

// ServerRegionProviderMonthlyStatServiceClient is the client API for ServerRegionProviderMonthlyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerRegionProviderMonthlyStatServiceClient interface {
	// 查找前N个运营商
	FindTopServerRegionProviderMonthlyStats(ctx context.Context, in *FindTopServerRegionProviderMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerRegionProviderMonthlyStatsResponse, error)
}

type serverRegionProviderMonthlyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerRegionProviderMonthlyStatServiceClient(cc grpc.ClientConnInterface) ServerRegionProviderMonthlyStatServiceClient {
	return &serverRegionProviderMonthlyStatServiceClient{cc}
}

func (c *serverRegionProviderMonthlyStatServiceClient) FindTopServerRegionProviderMonthlyStats(ctx context.Context, in *FindTopServerRegionProviderMonthlyStatsRequest, opts ...grpc.CallOption) (*FindTopServerRegionProviderMonthlyStatsResponse, error) {
	out := new(FindTopServerRegionProviderMonthlyStatsResponse)
	err := c.cc.Invoke(ctx, ServerRegionProviderMonthlyStatService_FindTopServerRegionProviderMonthlyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerRegionProviderMonthlyStatServiceServer is the server API for ServerRegionProviderMonthlyStatService service.
// All implementations should embed UnimplementedServerRegionProviderMonthlyStatServiceServer
// for forward compatibility
type ServerRegionProviderMonthlyStatServiceServer interface {
	// 查找前N个运营商
	FindTopServerRegionProviderMonthlyStats(context.Context, *FindTopServerRegionProviderMonthlyStatsRequest) (*FindTopServerRegionProviderMonthlyStatsResponse, error)
}

// UnimplementedServerRegionProviderMonthlyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerRegionProviderMonthlyStatServiceServer struct {
}

func (UnimplementedServerRegionProviderMonthlyStatServiceServer) FindTopServerRegionProviderMonthlyStats(context.Context, *FindTopServerRegionProviderMonthlyStatsRequest) (*FindTopServerRegionProviderMonthlyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTopServerRegionProviderMonthlyStats not implemented")
}

// UnsafeServerRegionProviderMonthlyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerRegionProviderMonthlyStatServiceServer will
// result in compilation errors.
type UnsafeServerRegionProviderMonthlyStatServiceServer interface {
	mustEmbedUnimplementedServerRegionProviderMonthlyStatServiceServer()
}

func RegisterServerRegionProviderMonthlyStatServiceServer(s grpc.ServiceRegistrar, srv ServerRegionProviderMonthlyStatServiceServer) {
	s.RegisterService(&ServerRegionProviderMonthlyStatService_ServiceDesc, srv)
}

func _ServerRegionProviderMonthlyStatService_FindTopServerRegionProviderMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTopServerRegionProviderMonthlyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerRegionProviderMonthlyStatServiceServer).FindTopServerRegionProviderMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerRegionProviderMonthlyStatService_FindTopServerRegionProviderMonthlyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerRegionProviderMonthlyStatServiceServer).FindTopServerRegionProviderMonthlyStats(ctx, req.(*FindTopServerRegionProviderMonthlyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerRegionProviderMonthlyStatService_ServiceDesc is the grpc.ServiceDesc for ServerRegionProviderMonthlyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerRegionProviderMonthlyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerRegionProviderMonthlyStatService",
	HandlerType: (*ServerRegionProviderMonthlyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findTopServerRegionProviderMonthlyStats",
			Handler:    _ServerRegionProviderMonthlyStatService_FindTopServerRegionProviderMonthlyStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_region_provider_monthly_stat.proto",
}
