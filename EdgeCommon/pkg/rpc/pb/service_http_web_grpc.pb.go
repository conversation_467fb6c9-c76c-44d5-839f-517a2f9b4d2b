// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_web.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPWebService_CreateHTTPWeb_FullMethodName                   = "/pb.HTTPWebService/createHTTPWeb"
	HTTPWebService_FindEnabledHTTPWeb_FullMethodName              = "/pb.HTTPWebService/findEnabledHTTPWeb"
	HTTPWebService_FindEnabledHTTPWebConfig_FullMethodName        = "/pb.HTTPWebService/findEnabledHTTPWebConfig"
	HTTPWebService_UpdateHTTPWeb_FullMethodName                   = "/pb.HTTPWebService/updateHTTPWeb"
	HTTPWebService_UpdateHTTPWebCompression_FullMethodName        = "/pb.HTTPWebService/updateHTTPWebCompression"
	HTTPWebService_UpdateHTTPWebOptimization_FullMethodName       = "/pb.HTTPWebService/updateHTTPWebOptimization"
	HTTPWebService_UpdateHTTPWebWebP_FullMethodName               = "/pb.HTTPWebService/updateHTTPWebWebP"
	HTTPWebService_UpdateHTTPWebRemoteAddr_FullMethodName         = "/pb.HTTPWebService/updateHTTPWebRemoteAddr"
	HTTPWebService_UpdateHTTPWebCharset_FullMethodName            = "/pb.HTTPWebService/updateHTTPWebCharset"
	HTTPWebService_UpdateHTTPWebRequestHeader_FullMethodName      = "/pb.HTTPWebService/updateHTTPWebRequestHeader"
	HTTPWebService_UpdateHTTPWebResponseHeader_FullMethodName     = "/pb.HTTPWebService/updateHTTPWebResponseHeader"
	HTTPWebService_UpdateHTTPWebShutdown_FullMethodName           = "/pb.HTTPWebService/updateHTTPWebShutdown"
	HTTPWebService_UpdateHTTPWebPages_FullMethodName              = "/pb.HTTPWebService/updateHTTPWebPages"
	HTTPWebService_UpdateHTTPWebGlobalPagesEnabled_FullMethodName = "/pb.HTTPWebService/updateHTTPWebGlobalPagesEnabled"
	HTTPWebService_UpdateHTTPWebAccessLog_FullMethodName          = "/pb.HTTPWebService/updateHTTPWebAccessLog"
	HTTPWebService_UpdateHTTPWebStat_FullMethodName               = "/pb.HTTPWebService/updateHTTPWebStat"
	HTTPWebService_UpdateHTTPWebCache_FullMethodName              = "/pb.HTTPWebService/updateHTTPWebCache"
	HTTPWebService_UpdateHTTPWebFirewall_FullMethodName           = "/pb.HTTPWebService/updateHTTPWebFirewall"
	HTTPWebService_UpdateHTTPWebLocations_FullMethodName          = "/pb.HTTPWebService/updateHTTPWebLocations"
	HTTPWebService_UpdateHTTPWebRedirectToHTTPS_FullMethodName    = "/pb.HTTPWebService/updateHTTPWebRedirectToHTTPS"
	HTTPWebService_UpdateHTTPWebWebsocket_FullMethodName          = "/pb.HTTPWebService/updateHTTPWebWebsocket"
	HTTPWebService_UpdateHTTPWebFastcgi_FullMethodName            = "/pb.HTTPWebService/updateHTTPWebFastcgi"
	HTTPWebService_UpdateHTTPWebRewriteRules_FullMethodName       = "/pb.HTTPWebService/updateHTTPWebRewriteRules"
	HTTPWebService_UpdateHTTPWebHostRedirects_FullMethodName      = "/pb.HTTPWebService/updateHTTPWebHostRedirects"
	HTTPWebService_FindHTTPWebHostRedirects_FullMethodName        = "/pb.HTTPWebService/findHTTPWebHostRedirects"
	HTTPWebService_UpdateHTTPWebAuth_FullMethodName               = "/pb.HTTPWebService/updateHTTPWebAuth"
	HTTPWebService_UpdateHTTPWebCommon_FullMethodName             = "/pb.HTTPWebService/updateHTTPWebCommon"
	HTTPWebService_UpdateHTTPWebRequestLimit_FullMethodName       = "/pb.HTTPWebService/updateHTTPWebRequestLimit"
	HTTPWebService_FindHTTPWebRequestLimit_FullMethodName         = "/pb.HTTPWebService/findHTTPWebRequestLimit"
	HTTPWebService_UpdateHTTPWebRequestScripts_FullMethodName     = "/pb.HTTPWebService/updateHTTPWebRequestScripts"
	HTTPWebService_FindHTTPWebRequestScripts_FullMethodName       = "/pb.HTTPWebService/findHTTPWebRequestScripts"
	HTTPWebService_UpdateHTTPWebUAM_FullMethodName                = "/pb.HTTPWebService/updateHTTPWebUAM"
	HTTPWebService_FindHTTPWebUAM_FullMethodName                  = "/pb.HTTPWebService/findHTTPWebUAM"
	HTTPWebService_UpdateHTTPWebCC_FullMethodName                 = "/pb.HTTPWebService/updateHTTPWebCC"
	HTTPWebService_FindHTTPWebCC_FullMethodName                   = "/pb.HTTPWebService/findHTTPWebCC"
	HTTPWebService_UpdateHTTPWebReferers_FullMethodName           = "/pb.HTTPWebService/updateHTTPWebReferers"
	HTTPWebService_FindHTTPWebReferers_FullMethodName             = "/pb.HTTPWebService/findHTTPWebReferers"
	HTTPWebService_UpdateHTTPWebUserAgent_FullMethodName          = "/pb.HTTPWebService/updateHTTPWebUserAgent"
	HTTPWebService_FindHTTPWebUserAgent_FullMethodName            = "/pb.HTTPWebService/findHTTPWebUserAgent"
	HTTPWebService_UpdateHTTPWebHLS_FullMethodName                = "/pb.HTTPWebService/updateHTTPWebHLS"
	HTTPWebService_FindHTTPWebHLS_FullMethodName                  = "/pb.HTTPWebService/findHTTPWebHLS"
	HTTPWebService_FindServerIdWithHTTPWebId_FullMethodName       = "/pb.HTTPWebService/findServerIdWithHTTPWebId"
)

// HTTPWebServiceClient is the client API for HTTPWebService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPWebServiceClient interface {
	// 创建Web配置
	CreateHTTPWeb(ctx context.Context, in *CreateHTTPWebRequest, opts ...grpc.CallOption) (*CreateHTTPWebResponse, error)
	// 查找Web信息
	FindEnabledHTTPWeb(ctx context.Context, in *FindEnabledHTTPWebRequest, opts ...grpc.CallOption) (*FindEnabledHTTPWebResponse, error)
	// 查找Web配置
	FindEnabledHTTPWebConfig(ctx context.Context, in *FindEnabledHTTPWebConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPWebConfigResponse, error)
	// 更改Web配置
	UpdateHTTPWeb(ctx context.Context, in *UpdateHTTPWebRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改压缩配置
	UpdateHTTPWebCompression(ctx context.Context, in *UpdateHTTPWebCompressionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改页面优化配置
	UpdateHTTPWebOptimization(ctx context.Context, in *UpdateHTTPWebOptimizationRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改WebP配置
	UpdateHTTPWebWebP(ctx context.Context, in *UpdateHTTPWebWebPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改RemoteAddr配置
	UpdateHTTPWebRemoteAddr(ctx context.Context, in *UpdateHTTPWebRemoteAddrRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改字符集配置
	UpdateHTTPWebCharset(ctx context.Context, in *UpdateHTTPWebCharsetRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改请求Header策略
	UpdateHTTPWebRequestHeader(ctx context.Context, in *UpdateHTTPWebRequestHeaderRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改响应Header策略
	UpdateHTTPWebResponseHeader(ctx context.Context, in *UpdateHTTPWebResponseHeaderRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改Shutdown
	UpdateHTTPWebShutdown(ctx context.Context, in *UpdateHTTPWebShutdownRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改Pages
	UpdateHTTPWebPages(ctx context.Context, in *UpdateHTTPWebPagesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改系统自定义页面启用状态
	UpdateHTTPWebGlobalPagesEnabled(ctx context.Context, in *UpdateHTTPWebGlobalPagesEnabledRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改访问日志配置
	UpdateHTTPWebAccessLog(ctx context.Context, in *UpdateHTTPWebAccessLogRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改统计配置
	UpdateHTTPWebStat(ctx context.Context, in *UpdateHTTPWebStatRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改缓存配置
	UpdateHTTPWebCache(ctx context.Context, in *UpdateHTTPWebCacheRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改防火墙设置
	UpdateHTTPWebFirewall(ctx context.Context, in *UpdateHTTPWebFirewallRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改路径规则配置
	UpdateHTTPWebLocations(ctx context.Context, in *UpdateHTTPWebLocationsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改跳转到HTTPS设置
	UpdateHTTPWebRedirectToHTTPS(ctx context.Context, in *UpdateHTTPWebRedirectToHTTPSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改Websocket设置
	UpdateHTTPWebWebsocket(ctx context.Context, in *UpdateHTTPWebWebsocketRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改Fastcgi设置
	UpdateHTTPWebFastcgi(ctx context.Context, in *UpdateHTTPWebFastcgiRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改重写规则设置
	UpdateHTTPWebRewriteRules(ctx context.Context, in *UpdateHTTPWebRewriteRulesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改主机跳转设置
	UpdateHTTPWebHostRedirects(ctx context.Context, in *UpdateHTTPWebHostRedirectsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找主机跳转设置
	FindHTTPWebHostRedirects(ctx context.Context, in *FindHTTPWebHostRedirectsRequest, opts ...grpc.CallOption) (*FindHTTPWebHostRedirectsResponse, error)
	// 更改认证设置
	UpdateHTTPWebAuth(ctx context.Context, in *UpdateHTTPWebAuthRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 更改通用设置
	UpdateHTTPWebCommon(ctx context.Context, in *UpdateHTTPWebCommonRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改请求限制
	UpdateHTTPWebRequestLimit(ctx context.Context, in *UpdateHTTPWebRequestLimitRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找请求限制
	FindHTTPWebRequestLimit(ctx context.Context, in *FindHTTPWebRequestLimitRequest, opts ...grpc.CallOption) (*FindHTTPWebRequestLimitResponse, error)
	// 修改请求脚本
	UpdateHTTPWebRequestScripts(ctx context.Context, in *UpdateHTTPWebRequestScriptsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找请求脚本
	FindHTTPWebRequestScripts(ctx context.Context, in *FindHTTPWebRequestScriptsRequest, opts ...grpc.CallOption) (*FindHTTPWebRequestScriptsResponse, error)
	// 修改UAM设置
	UpdateHTTPWebUAM(ctx context.Context, in *UpdateHTTPWebUAMRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找UAM设置
	FindHTTPWebUAM(ctx context.Context, in *FindHTTPWebUAMRequest, opts ...grpc.CallOption) (*FindHTTPWebUAMResponse, error)
	// 修改CC设置
	UpdateHTTPWebCC(ctx context.Context, in *UpdateHTTPWebCCRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找CC设置
	FindHTTPWebCC(ctx context.Context, in *FindHTTPWebCCRequest, opts ...grpc.CallOption) (*FindHTTPWebCCResponse, error)
	// 修改防盗链设置
	UpdateHTTPWebReferers(ctx context.Context, in *UpdateHTTPWebReferersRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找防盗链设置
	FindHTTPWebReferers(ctx context.Context, in *FindHTTPWebReferersRequest, opts ...grpc.CallOption) (*FindHTTPWebReferersResponse, error)
	// 修改UserAgent设置
	UpdateHTTPWebUserAgent(ctx context.Context, in *UpdateHTTPWebUserAgentRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找UserAgent设置
	FindHTTPWebUserAgent(ctx context.Context, in *FindHTTPWebUserAgentRequest, opts ...grpc.CallOption) (*FindHTTPWebUserAgentResponse, error)
	// 修改HLS设置
	UpdateHTTPWebHLS(ctx context.Context, in *UpdateHTTPWebHLSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找HLS设置
	FindHTTPWebHLS(ctx context.Context, in *FindHTTPWebHLSRequest, opts ...grpc.CallOption) (*FindHTTPWebHLSResponse, error)
	// 根据WebId查找ServerId
	FindServerIdWithHTTPWebId(ctx context.Context, in *FindServerIdWithHTTPWebIdRequest, opts ...grpc.CallOption) (*FindServerIdWithHTTPWebIdResponse, error)
}

type hTTPWebServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPWebServiceClient(cc grpc.ClientConnInterface) HTTPWebServiceClient {
	return &hTTPWebServiceClient{cc}
}

func (c *hTTPWebServiceClient) CreateHTTPWeb(ctx context.Context, in *CreateHTTPWebRequest, opts ...grpc.CallOption) (*CreateHTTPWebResponse, error) {
	out := new(CreateHTTPWebResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_CreateHTTPWeb_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindEnabledHTTPWeb(ctx context.Context, in *FindEnabledHTTPWebRequest, opts ...grpc.CallOption) (*FindEnabledHTTPWebResponse, error) {
	out := new(FindEnabledHTTPWebResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindEnabledHTTPWeb_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindEnabledHTTPWebConfig(ctx context.Context, in *FindEnabledHTTPWebConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPWebConfigResponse, error) {
	out := new(FindEnabledHTTPWebConfigResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindEnabledHTTPWebConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWeb(ctx context.Context, in *UpdateHTTPWebRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWeb_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebCompression(ctx context.Context, in *UpdateHTTPWebCompressionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebCompression_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebOptimization(ctx context.Context, in *UpdateHTTPWebOptimizationRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebOptimization_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebWebP(ctx context.Context, in *UpdateHTTPWebWebPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebWebP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebRemoteAddr(ctx context.Context, in *UpdateHTTPWebRemoteAddrRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebRemoteAddr_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebCharset(ctx context.Context, in *UpdateHTTPWebCharsetRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebCharset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebRequestHeader(ctx context.Context, in *UpdateHTTPWebRequestHeaderRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebRequestHeader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebResponseHeader(ctx context.Context, in *UpdateHTTPWebResponseHeaderRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebResponseHeader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebShutdown(ctx context.Context, in *UpdateHTTPWebShutdownRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebShutdown_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebPages(ctx context.Context, in *UpdateHTTPWebPagesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebPages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebGlobalPagesEnabled(ctx context.Context, in *UpdateHTTPWebGlobalPagesEnabledRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebGlobalPagesEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebAccessLog(ctx context.Context, in *UpdateHTTPWebAccessLogRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebAccessLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebStat(ctx context.Context, in *UpdateHTTPWebStatRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebStat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebCache(ctx context.Context, in *UpdateHTTPWebCacheRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebFirewall(ctx context.Context, in *UpdateHTTPWebFirewallRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebFirewall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebLocations(ctx context.Context, in *UpdateHTTPWebLocationsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebLocations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebRedirectToHTTPS(ctx context.Context, in *UpdateHTTPWebRedirectToHTTPSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebRedirectToHTTPS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebWebsocket(ctx context.Context, in *UpdateHTTPWebWebsocketRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebWebsocket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebFastcgi(ctx context.Context, in *UpdateHTTPWebFastcgiRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebFastcgi_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebRewriteRules(ctx context.Context, in *UpdateHTTPWebRewriteRulesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebRewriteRules_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebHostRedirects(ctx context.Context, in *UpdateHTTPWebHostRedirectsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebHostRedirects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebHostRedirects(ctx context.Context, in *FindHTTPWebHostRedirectsRequest, opts ...grpc.CallOption) (*FindHTTPWebHostRedirectsResponse, error) {
	out := new(FindHTTPWebHostRedirectsResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebHostRedirects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebAuth(ctx context.Context, in *UpdateHTTPWebAuthRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebAuth_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebCommon(ctx context.Context, in *UpdateHTTPWebCommonRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebCommon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebRequestLimit(ctx context.Context, in *UpdateHTTPWebRequestLimitRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebRequestLimit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebRequestLimit(ctx context.Context, in *FindHTTPWebRequestLimitRequest, opts ...grpc.CallOption) (*FindHTTPWebRequestLimitResponse, error) {
	out := new(FindHTTPWebRequestLimitResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebRequestLimit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebRequestScripts(ctx context.Context, in *UpdateHTTPWebRequestScriptsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebRequestScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebRequestScripts(ctx context.Context, in *FindHTTPWebRequestScriptsRequest, opts ...grpc.CallOption) (*FindHTTPWebRequestScriptsResponse, error) {
	out := new(FindHTTPWebRequestScriptsResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebRequestScripts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebUAM(ctx context.Context, in *UpdateHTTPWebUAMRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebUAM_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebUAM(ctx context.Context, in *FindHTTPWebUAMRequest, opts ...grpc.CallOption) (*FindHTTPWebUAMResponse, error) {
	out := new(FindHTTPWebUAMResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebUAM_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebCC(ctx context.Context, in *UpdateHTTPWebCCRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebCC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebCC(ctx context.Context, in *FindHTTPWebCCRequest, opts ...grpc.CallOption) (*FindHTTPWebCCResponse, error) {
	out := new(FindHTTPWebCCResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebCC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebReferers(ctx context.Context, in *UpdateHTTPWebReferersRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebReferers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebReferers(ctx context.Context, in *FindHTTPWebReferersRequest, opts ...grpc.CallOption) (*FindHTTPWebReferersResponse, error) {
	out := new(FindHTTPWebReferersResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebReferers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebUserAgent(ctx context.Context, in *UpdateHTTPWebUserAgentRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebUserAgent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebUserAgent(ctx context.Context, in *FindHTTPWebUserAgentRequest, opts ...grpc.CallOption) (*FindHTTPWebUserAgentResponse, error) {
	out := new(FindHTTPWebUserAgentResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebUserAgent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) UpdateHTTPWebHLS(ctx context.Context, in *UpdateHTTPWebHLSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPWebService_UpdateHTTPWebHLS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindHTTPWebHLS(ctx context.Context, in *FindHTTPWebHLSRequest, opts ...grpc.CallOption) (*FindHTTPWebHLSResponse, error) {
	out := new(FindHTTPWebHLSResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindHTTPWebHLS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPWebServiceClient) FindServerIdWithHTTPWebId(ctx context.Context, in *FindServerIdWithHTTPWebIdRequest, opts ...grpc.CallOption) (*FindServerIdWithHTTPWebIdResponse, error) {
	out := new(FindServerIdWithHTTPWebIdResponse)
	err := c.cc.Invoke(ctx, HTTPWebService_FindServerIdWithHTTPWebId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPWebServiceServer is the server API for HTTPWebService service.
// All implementations should embed UnimplementedHTTPWebServiceServer
// for forward compatibility
type HTTPWebServiceServer interface {
	// 创建Web配置
	CreateHTTPWeb(context.Context, *CreateHTTPWebRequest) (*CreateHTTPWebResponse, error)
	// 查找Web信息
	FindEnabledHTTPWeb(context.Context, *FindEnabledHTTPWebRequest) (*FindEnabledHTTPWebResponse, error)
	// 查找Web配置
	FindEnabledHTTPWebConfig(context.Context, *FindEnabledHTTPWebConfigRequest) (*FindEnabledHTTPWebConfigResponse, error)
	// 更改Web配置
	UpdateHTTPWeb(context.Context, *UpdateHTTPWebRequest) (*RPCSuccess, error)
	// 更改压缩配置
	UpdateHTTPWebCompression(context.Context, *UpdateHTTPWebCompressionRequest) (*RPCSuccess, error)
	// 更改页面优化配置
	UpdateHTTPWebOptimization(context.Context, *UpdateHTTPWebOptimizationRequest) (*RPCSuccess, error)
	// 更改WebP配置
	UpdateHTTPWebWebP(context.Context, *UpdateHTTPWebWebPRequest) (*RPCSuccess, error)
	// 更改RemoteAddr配置
	UpdateHTTPWebRemoteAddr(context.Context, *UpdateHTTPWebRemoteAddrRequest) (*RPCSuccess, error)
	// 更改字符集配置
	UpdateHTTPWebCharset(context.Context, *UpdateHTTPWebCharsetRequest) (*RPCSuccess, error)
	// 更改请求Header策略
	UpdateHTTPWebRequestHeader(context.Context, *UpdateHTTPWebRequestHeaderRequest) (*RPCSuccess, error)
	// 更改响应Header策略
	UpdateHTTPWebResponseHeader(context.Context, *UpdateHTTPWebResponseHeaderRequest) (*RPCSuccess, error)
	// 更改Shutdown
	UpdateHTTPWebShutdown(context.Context, *UpdateHTTPWebShutdownRequest) (*RPCSuccess, error)
	// 更改Pages
	UpdateHTTPWebPages(context.Context, *UpdateHTTPWebPagesRequest) (*RPCSuccess, error)
	// 更改系统自定义页面启用状态
	UpdateHTTPWebGlobalPagesEnabled(context.Context, *UpdateHTTPWebGlobalPagesEnabledRequest) (*RPCSuccess, error)
	// 更改访问日志配置
	UpdateHTTPWebAccessLog(context.Context, *UpdateHTTPWebAccessLogRequest) (*RPCSuccess, error)
	// 更改统计配置
	UpdateHTTPWebStat(context.Context, *UpdateHTTPWebStatRequest) (*RPCSuccess, error)
	// 更改缓存配置
	UpdateHTTPWebCache(context.Context, *UpdateHTTPWebCacheRequest) (*RPCSuccess, error)
	// 更改防火墙设置
	UpdateHTTPWebFirewall(context.Context, *UpdateHTTPWebFirewallRequest) (*RPCSuccess, error)
	// 更改路径规则配置
	UpdateHTTPWebLocations(context.Context, *UpdateHTTPWebLocationsRequest) (*RPCSuccess, error)
	// 更改跳转到HTTPS设置
	UpdateHTTPWebRedirectToHTTPS(context.Context, *UpdateHTTPWebRedirectToHTTPSRequest) (*RPCSuccess, error)
	// 更改Websocket设置
	UpdateHTTPWebWebsocket(context.Context, *UpdateHTTPWebWebsocketRequest) (*RPCSuccess, error)
	// 更改Fastcgi设置
	UpdateHTTPWebFastcgi(context.Context, *UpdateHTTPWebFastcgiRequest) (*RPCSuccess, error)
	// 更改重写规则设置
	UpdateHTTPWebRewriteRules(context.Context, *UpdateHTTPWebRewriteRulesRequest) (*RPCSuccess, error)
	// 更改主机跳转设置
	UpdateHTTPWebHostRedirects(context.Context, *UpdateHTTPWebHostRedirectsRequest) (*RPCSuccess, error)
	// 查找主机跳转设置
	FindHTTPWebHostRedirects(context.Context, *FindHTTPWebHostRedirectsRequest) (*FindHTTPWebHostRedirectsResponse, error)
	// 更改认证设置
	UpdateHTTPWebAuth(context.Context, *UpdateHTTPWebAuthRequest) (*RPCSuccess, error)
	// 更改通用设置
	UpdateHTTPWebCommon(context.Context, *UpdateHTTPWebCommonRequest) (*RPCSuccess, error)
	// 修改请求限制
	UpdateHTTPWebRequestLimit(context.Context, *UpdateHTTPWebRequestLimitRequest) (*RPCSuccess, error)
	// 查找请求限制
	FindHTTPWebRequestLimit(context.Context, *FindHTTPWebRequestLimitRequest) (*FindHTTPWebRequestLimitResponse, error)
	// 修改请求脚本
	UpdateHTTPWebRequestScripts(context.Context, *UpdateHTTPWebRequestScriptsRequest) (*RPCSuccess, error)
	// 查找请求脚本
	FindHTTPWebRequestScripts(context.Context, *FindHTTPWebRequestScriptsRequest) (*FindHTTPWebRequestScriptsResponse, error)
	// 修改UAM设置
	UpdateHTTPWebUAM(context.Context, *UpdateHTTPWebUAMRequest) (*RPCSuccess, error)
	// 查找UAM设置
	FindHTTPWebUAM(context.Context, *FindHTTPWebUAMRequest) (*FindHTTPWebUAMResponse, error)
	// 修改CC设置
	UpdateHTTPWebCC(context.Context, *UpdateHTTPWebCCRequest) (*RPCSuccess, error)
	// 查找CC设置
	FindHTTPWebCC(context.Context, *FindHTTPWebCCRequest) (*FindHTTPWebCCResponse, error)
	// 修改防盗链设置
	UpdateHTTPWebReferers(context.Context, *UpdateHTTPWebReferersRequest) (*RPCSuccess, error)
	// 查找防盗链设置
	FindHTTPWebReferers(context.Context, *FindHTTPWebReferersRequest) (*FindHTTPWebReferersResponse, error)
	// 修改UserAgent设置
	UpdateHTTPWebUserAgent(context.Context, *UpdateHTTPWebUserAgentRequest) (*RPCSuccess, error)
	// 查找UserAgent设置
	FindHTTPWebUserAgent(context.Context, *FindHTTPWebUserAgentRequest) (*FindHTTPWebUserAgentResponse, error)
	// 修改HLS设置
	UpdateHTTPWebHLS(context.Context, *UpdateHTTPWebHLSRequest) (*RPCSuccess, error)
	// 查找HLS设置
	FindHTTPWebHLS(context.Context, *FindHTTPWebHLSRequest) (*FindHTTPWebHLSResponse, error)
	// 根据WebId查找ServerId
	FindServerIdWithHTTPWebId(context.Context, *FindServerIdWithHTTPWebIdRequest) (*FindServerIdWithHTTPWebIdResponse, error)
}

// UnimplementedHTTPWebServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPWebServiceServer struct {
}

func (UnimplementedHTTPWebServiceServer) CreateHTTPWeb(context.Context, *CreateHTTPWebRequest) (*CreateHTTPWebResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPWeb not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindEnabledHTTPWeb(context.Context, *FindEnabledHTTPWebRequest) (*FindEnabledHTTPWebResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPWeb not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindEnabledHTTPWebConfig(context.Context, *FindEnabledHTTPWebConfigRequest) (*FindEnabledHTTPWebConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPWebConfig not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWeb(context.Context, *UpdateHTTPWebRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWeb not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebCompression(context.Context, *UpdateHTTPWebCompressionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebCompression not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebOptimization(context.Context, *UpdateHTTPWebOptimizationRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebOptimization not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebWebP(context.Context, *UpdateHTTPWebWebPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebWebP not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebRemoteAddr(context.Context, *UpdateHTTPWebRemoteAddrRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebRemoteAddr not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebCharset(context.Context, *UpdateHTTPWebCharsetRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebCharset not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebRequestHeader(context.Context, *UpdateHTTPWebRequestHeaderRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebRequestHeader not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebResponseHeader(context.Context, *UpdateHTTPWebResponseHeaderRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebResponseHeader not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebShutdown(context.Context, *UpdateHTTPWebShutdownRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebShutdown not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebPages(context.Context, *UpdateHTTPWebPagesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebPages not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebGlobalPagesEnabled(context.Context, *UpdateHTTPWebGlobalPagesEnabledRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebGlobalPagesEnabled not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebAccessLog(context.Context, *UpdateHTTPWebAccessLogRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebAccessLog not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebStat(context.Context, *UpdateHTTPWebStatRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebStat not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebCache(context.Context, *UpdateHTTPWebCacheRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebCache not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebFirewall(context.Context, *UpdateHTTPWebFirewallRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebFirewall not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebLocations(context.Context, *UpdateHTTPWebLocationsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebLocations not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebRedirectToHTTPS(context.Context, *UpdateHTTPWebRedirectToHTTPSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebRedirectToHTTPS not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebWebsocket(context.Context, *UpdateHTTPWebWebsocketRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebWebsocket not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebFastcgi(context.Context, *UpdateHTTPWebFastcgiRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebFastcgi not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebRewriteRules(context.Context, *UpdateHTTPWebRewriteRulesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebRewriteRules not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebHostRedirects(context.Context, *UpdateHTTPWebHostRedirectsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebHostRedirects not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebHostRedirects(context.Context, *FindHTTPWebHostRedirectsRequest) (*FindHTTPWebHostRedirectsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebHostRedirects not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebAuth(context.Context, *UpdateHTTPWebAuthRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebAuth not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebCommon(context.Context, *UpdateHTTPWebCommonRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebCommon not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebRequestLimit(context.Context, *UpdateHTTPWebRequestLimitRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebRequestLimit not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebRequestLimit(context.Context, *FindHTTPWebRequestLimitRequest) (*FindHTTPWebRequestLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebRequestLimit not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebRequestScripts(context.Context, *UpdateHTTPWebRequestScriptsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebRequestScripts not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebRequestScripts(context.Context, *FindHTTPWebRequestScriptsRequest) (*FindHTTPWebRequestScriptsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebRequestScripts not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebUAM(context.Context, *UpdateHTTPWebUAMRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebUAM not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebUAM(context.Context, *FindHTTPWebUAMRequest) (*FindHTTPWebUAMResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebUAM not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebCC(context.Context, *UpdateHTTPWebCCRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebCC not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebCC(context.Context, *FindHTTPWebCCRequest) (*FindHTTPWebCCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebCC not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebReferers(context.Context, *UpdateHTTPWebReferersRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebReferers not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebReferers(context.Context, *FindHTTPWebReferersRequest) (*FindHTTPWebReferersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebReferers not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebUserAgent(context.Context, *UpdateHTTPWebUserAgentRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebUserAgent not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebUserAgent(context.Context, *FindHTTPWebUserAgentRequest) (*FindHTTPWebUserAgentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebUserAgent not implemented")
}
func (UnimplementedHTTPWebServiceServer) UpdateHTTPWebHLS(context.Context, *UpdateHTTPWebHLSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPWebHLS not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindHTTPWebHLS(context.Context, *FindHTTPWebHLSRequest) (*FindHTTPWebHLSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPWebHLS not implemented")
}
func (UnimplementedHTTPWebServiceServer) FindServerIdWithHTTPWebId(context.Context, *FindServerIdWithHTTPWebIdRequest) (*FindServerIdWithHTTPWebIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerIdWithHTTPWebId not implemented")
}

// UnsafeHTTPWebServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPWebServiceServer will
// result in compilation errors.
type UnsafeHTTPWebServiceServer interface {
	mustEmbedUnimplementedHTTPWebServiceServer()
}

func RegisterHTTPWebServiceServer(s grpc.ServiceRegistrar, srv HTTPWebServiceServer) {
	s.RegisterService(&HTTPWebService_ServiceDesc, srv)
}

func _HTTPWebService_CreateHTTPWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPWebRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).CreateHTTPWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_CreateHTTPWeb_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).CreateHTTPWeb(ctx, req.(*CreateHTTPWebRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindEnabledHTTPWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPWebRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindEnabledHTTPWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindEnabledHTTPWeb_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindEnabledHTTPWeb(ctx, req.(*FindEnabledHTTPWebRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindEnabledHTTPWebConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPWebConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindEnabledHTTPWebConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindEnabledHTTPWebConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindEnabledHTTPWebConfig(ctx, req.(*FindEnabledHTTPWebConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWeb_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWeb(ctx, req.(*UpdateHTTPWebRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebCompression_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebCompressionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCompression(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebCompression_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCompression(ctx, req.(*UpdateHTTPWebCompressionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebOptimization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebOptimizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebOptimization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebOptimization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebOptimization(ctx, req.(*UpdateHTTPWebOptimizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebWebP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebWebPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebWebP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebWebP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebWebP(ctx, req.(*UpdateHTTPWebWebPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebRemoteAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRemoteAddrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRemoteAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebRemoteAddr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRemoteAddr(ctx, req.(*UpdateHTTPWebRemoteAddrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebCharset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebCharsetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCharset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebCharset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCharset(ctx, req.(*UpdateHTTPWebCharsetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebRequestHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRequestHeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRequestHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebRequestHeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRequestHeader(ctx, req.(*UpdateHTTPWebRequestHeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebResponseHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebResponseHeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebResponseHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebResponseHeader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebResponseHeader(ctx, req.(*UpdateHTTPWebResponseHeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebShutdown_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebShutdownRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebShutdown(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebShutdown_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebShutdown(ctx, req.(*UpdateHTTPWebShutdownRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebPagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebPages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebPages(ctx, req.(*UpdateHTTPWebPagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebGlobalPagesEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebGlobalPagesEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebGlobalPagesEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebGlobalPagesEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebGlobalPagesEnabled(ctx, req.(*UpdateHTTPWebGlobalPagesEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebAccessLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebAccessLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebAccessLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebAccessLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebAccessLog(ctx, req.(*UpdateHTTPWebAccessLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebStatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebStat(ctx, req.(*UpdateHTTPWebStatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCache(ctx, req.(*UpdateHTTPWebCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebFirewall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebFirewallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebFirewall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebFirewall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebFirewall(ctx, req.(*UpdateHTTPWebFirewallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebLocations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebLocations(ctx, req.(*UpdateHTTPWebLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebRedirectToHTTPS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRedirectToHTTPSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRedirectToHTTPS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebRedirectToHTTPS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRedirectToHTTPS(ctx, req.(*UpdateHTTPWebRedirectToHTTPSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebWebsocket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebWebsocketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebWebsocket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebWebsocket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebWebsocket(ctx, req.(*UpdateHTTPWebWebsocketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebFastcgi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebFastcgiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebFastcgi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebFastcgi_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebFastcgi(ctx, req.(*UpdateHTTPWebFastcgiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebRewriteRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRewriteRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRewriteRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebRewriteRules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRewriteRules(ctx, req.(*UpdateHTTPWebRewriteRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebHostRedirects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebHostRedirectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebHostRedirects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebHostRedirects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebHostRedirects(ctx, req.(*UpdateHTTPWebHostRedirectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebHostRedirects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebHostRedirectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebHostRedirects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebHostRedirects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebHostRedirects(ctx, req.(*FindHTTPWebHostRedirectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebAuth(ctx, req.(*UpdateHTTPWebAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebCommon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebCommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCommon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebCommon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCommon(ctx, req.(*UpdateHTTPWebCommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebRequestLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRequestLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRequestLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebRequestLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRequestLimit(ctx, req.(*UpdateHTTPWebRequestLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebRequestLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebRequestLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebRequestLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebRequestLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebRequestLimit(ctx, req.(*FindHTTPWebRequestLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebRequestScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebRequestScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRequestScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebRequestScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebRequestScripts(ctx, req.(*UpdateHTTPWebRequestScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebRequestScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebRequestScriptsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebRequestScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebRequestScripts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebRequestScripts(ctx, req.(*FindHTTPWebRequestScriptsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebUAM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebUAMRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebUAM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebUAM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebUAM(ctx, req.(*UpdateHTTPWebUAMRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebUAM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebUAMRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebUAM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebUAM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebUAM(ctx, req.(*FindHTTPWebUAMRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebCC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebCCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebCC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebCC(ctx, req.(*UpdateHTTPWebCCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebCC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebCCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebCC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebCC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebCC(ctx, req.(*FindHTTPWebCCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebReferers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebReferersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebReferers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebReferers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebReferers(ctx, req.(*UpdateHTTPWebReferersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebReferers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebReferersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebReferers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebReferers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebReferers(ctx, req.(*FindHTTPWebReferersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebUserAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebUserAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebUserAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebUserAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebUserAgent(ctx, req.(*UpdateHTTPWebUserAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebUserAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebUserAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebUserAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebUserAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebUserAgent(ctx, req.(*FindHTTPWebUserAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_UpdateHTTPWebHLS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPWebHLSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebHLS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_UpdateHTTPWebHLS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).UpdateHTTPWebHLS(ctx, req.(*UpdateHTTPWebHLSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindHTTPWebHLS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPWebHLSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindHTTPWebHLS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindHTTPWebHLS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindHTTPWebHLS(ctx, req.(*FindHTTPWebHLSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPWebService_FindServerIdWithHTTPWebId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerIdWithHTTPWebIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPWebServiceServer).FindServerIdWithHTTPWebId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPWebService_FindServerIdWithHTTPWebId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPWebServiceServer).FindServerIdWithHTTPWebId(ctx, req.(*FindServerIdWithHTTPWebIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPWebService_ServiceDesc is the grpc.ServiceDesc for HTTPWebService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPWebService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPWebService",
	HandlerType: (*HTTPWebServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHTTPWeb",
			Handler:    _HTTPWebService_CreateHTTPWeb_Handler,
		},
		{
			MethodName: "findEnabledHTTPWeb",
			Handler:    _HTTPWebService_FindEnabledHTTPWeb_Handler,
		},
		{
			MethodName: "findEnabledHTTPWebConfig",
			Handler:    _HTTPWebService_FindEnabledHTTPWebConfig_Handler,
		},
		{
			MethodName: "updateHTTPWeb",
			Handler:    _HTTPWebService_UpdateHTTPWeb_Handler,
		},
		{
			MethodName: "updateHTTPWebCompression",
			Handler:    _HTTPWebService_UpdateHTTPWebCompression_Handler,
		},
		{
			MethodName: "updateHTTPWebOptimization",
			Handler:    _HTTPWebService_UpdateHTTPWebOptimization_Handler,
		},
		{
			MethodName: "updateHTTPWebWebP",
			Handler:    _HTTPWebService_UpdateHTTPWebWebP_Handler,
		},
		{
			MethodName: "updateHTTPWebRemoteAddr",
			Handler:    _HTTPWebService_UpdateHTTPWebRemoteAddr_Handler,
		},
		{
			MethodName: "updateHTTPWebCharset",
			Handler:    _HTTPWebService_UpdateHTTPWebCharset_Handler,
		},
		{
			MethodName: "updateHTTPWebRequestHeader",
			Handler:    _HTTPWebService_UpdateHTTPWebRequestHeader_Handler,
		},
		{
			MethodName: "updateHTTPWebResponseHeader",
			Handler:    _HTTPWebService_UpdateHTTPWebResponseHeader_Handler,
		},
		{
			MethodName: "updateHTTPWebShutdown",
			Handler:    _HTTPWebService_UpdateHTTPWebShutdown_Handler,
		},
		{
			MethodName: "updateHTTPWebPages",
			Handler:    _HTTPWebService_UpdateHTTPWebPages_Handler,
		},
		{
			MethodName: "updateHTTPWebGlobalPagesEnabled",
			Handler:    _HTTPWebService_UpdateHTTPWebGlobalPagesEnabled_Handler,
		},
		{
			MethodName: "updateHTTPWebAccessLog",
			Handler:    _HTTPWebService_UpdateHTTPWebAccessLog_Handler,
		},
		{
			MethodName: "updateHTTPWebStat",
			Handler:    _HTTPWebService_UpdateHTTPWebStat_Handler,
		},
		{
			MethodName: "updateHTTPWebCache",
			Handler:    _HTTPWebService_UpdateHTTPWebCache_Handler,
		},
		{
			MethodName: "updateHTTPWebFirewall",
			Handler:    _HTTPWebService_UpdateHTTPWebFirewall_Handler,
		},
		{
			MethodName: "updateHTTPWebLocations",
			Handler:    _HTTPWebService_UpdateHTTPWebLocations_Handler,
		},
		{
			MethodName: "updateHTTPWebRedirectToHTTPS",
			Handler:    _HTTPWebService_UpdateHTTPWebRedirectToHTTPS_Handler,
		},
		{
			MethodName: "updateHTTPWebWebsocket",
			Handler:    _HTTPWebService_UpdateHTTPWebWebsocket_Handler,
		},
		{
			MethodName: "updateHTTPWebFastcgi",
			Handler:    _HTTPWebService_UpdateHTTPWebFastcgi_Handler,
		},
		{
			MethodName: "updateHTTPWebRewriteRules",
			Handler:    _HTTPWebService_UpdateHTTPWebRewriteRules_Handler,
		},
		{
			MethodName: "updateHTTPWebHostRedirects",
			Handler:    _HTTPWebService_UpdateHTTPWebHostRedirects_Handler,
		},
		{
			MethodName: "findHTTPWebHostRedirects",
			Handler:    _HTTPWebService_FindHTTPWebHostRedirects_Handler,
		},
		{
			MethodName: "updateHTTPWebAuth",
			Handler:    _HTTPWebService_UpdateHTTPWebAuth_Handler,
		},
		{
			MethodName: "updateHTTPWebCommon",
			Handler:    _HTTPWebService_UpdateHTTPWebCommon_Handler,
		},
		{
			MethodName: "updateHTTPWebRequestLimit",
			Handler:    _HTTPWebService_UpdateHTTPWebRequestLimit_Handler,
		},
		{
			MethodName: "findHTTPWebRequestLimit",
			Handler:    _HTTPWebService_FindHTTPWebRequestLimit_Handler,
		},
		{
			MethodName: "updateHTTPWebRequestScripts",
			Handler:    _HTTPWebService_UpdateHTTPWebRequestScripts_Handler,
		},
		{
			MethodName: "findHTTPWebRequestScripts",
			Handler:    _HTTPWebService_FindHTTPWebRequestScripts_Handler,
		},
		{
			MethodName: "updateHTTPWebUAM",
			Handler:    _HTTPWebService_UpdateHTTPWebUAM_Handler,
		},
		{
			MethodName: "findHTTPWebUAM",
			Handler:    _HTTPWebService_FindHTTPWebUAM_Handler,
		},
		{
			MethodName: "updateHTTPWebCC",
			Handler:    _HTTPWebService_UpdateHTTPWebCC_Handler,
		},
		{
			MethodName: "findHTTPWebCC",
			Handler:    _HTTPWebService_FindHTTPWebCC_Handler,
		},
		{
			MethodName: "updateHTTPWebReferers",
			Handler:    _HTTPWebService_UpdateHTTPWebReferers_Handler,
		},
		{
			MethodName: "findHTTPWebReferers",
			Handler:    _HTTPWebService_FindHTTPWebReferers_Handler,
		},
		{
			MethodName: "updateHTTPWebUserAgent",
			Handler:    _HTTPWebService_UpdateHTTPWebUserAgent_Handler,
		},
		{
			MethodName: "findHTTPWebUserAgent",
			Handler:    _HTTPWebService_FindHTTPWebUserAgent_Handler,
		},
		{
			MethodName: "updateHTTPWebHLS",
			Handler:    _HTTPWebService_UpdateHTTPWebHLS_Handler,
		},
		{
			MethodName: "findHTTPWebHLS",
			Handler:    _HTTPWebService_FindHTTPWebHLS_Handler,
		},
		{
			MethodName: "findServerIdWithHTTPWebId",
			Handler:    _HTTPWebService_FindServerIdWithHTTPWebId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_web.proto",
}
