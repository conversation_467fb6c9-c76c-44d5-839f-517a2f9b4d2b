// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_ip_address_threshold.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeIPAddressThresholdService_CreateNodeIPAddressThreshold_FullMethodName           = "/pb.NodeIPAddressThresholdService/createNodeIPAddressThreshold"
	NodeIPAddressThresholdService_UpdateNodeIPAddressThreshold_FullMethodName           = "/pb.NodeIPAddressThresholdService/updateNodeIPAddressThreshold"
	NodeIPAddressThresholdService_DeleteNodeIPAddressThreshold_FullMethodName           = "/pb.NodeIPAddressThresholdService/deleteNodeIPAddressThreshold"
	NodeIPAddressThresholdService_FindAllEnabledNodeIPAddressThresholds_FullMethodName  = "/pb.NodeIPAddressThresholdService/findAllEnabledNodeIPAddressThresholds"
	NodeIPAddressThresholdService_CountAllEnabledNodeIPAddressThresholds_FullMethodName = "/pb.NodeIPAddressThresholdService/countAllEnabledNodeIPAddressThresholds"
	NodeIPAddressThresholdService_UpdateAllNodeIPAddressThresholds_FullMethodName       = "/pb.NodeIPAddressThresholdService/updateAllNodeIPAddressThresholds"
)

// NodeIPAddressThresholdServiceClient is the client API for NodeIPAddressThresholdService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeIPAddressThresholdServiceClient interface {
	// 创建阈值
	CreateNodeIPAddressThreshold(ctx context.Context, in *CreateNodeIPAddressThresholdRequest, opts ...grpc.CallOption) (*CreateNodeIPAddressThresholdResponse, error)
	// 修改阈值
	UpdateNodeIPAddressThreshold(ctx context.Context, in *UpdateNodeIPAddressThresholdRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除阈值
	DeleteNodeIPAddressThreshold(ctx context.Context, in *DeleteNodeIPAddressThresholdRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找IP的所有阈值
	FindAllEnabledNodeIPAddressThresholds(ctx context.Context, in *FindAllEnabledNodeIPAddressThresholdsRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeIPAddressThresholdsResponse, error)
	// 计算IP阈值的数量
	CountAllEnabledNodeIPAddressThresholds(ctx context.Context, in *CountAllEnabledNodeIPAddressThresholdsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 批量更新阈值
	UpdateAllNodeIPAddressThresholds(ctx context.Context, in *UpdateAllNodeIPAddressThresholdsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeIPAddressThresholdServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeIPAddressThresholdServiceClient(cc grpc.ClientConnInterface) NodeIPAddressThresholdServiceClient {
	return &nodeIPAddressThresholdServiceClient{cc}
}

func (c *nodeIPAddressThresholdServiceClient) CreateNodeIPAddressThreshold(ctx context.Context, in *CreateNodeIPAddressThresholdRequest, opts ...grpc.CallOption) (*CreateNodeIPAddressThresholdResponse, error) {
	out := new(CreateNodeIPAddressThresholdResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressThresholdService_CreateNodeIPAddressThreshold_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressThresholdServiceClient) UpdateNodeIPAddressThreshold(ctx context.Context, in *UpdateNodeIPAddressThresholdRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressThresholdService_UpdateNodeIPAddressThreshold_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressThresholdServiceClient) DeleteNodeIPAddressThreshold(ctx context.Context, in *DeleteNodeIPAddressThresholdRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressThresholdService_DeleteNodeIPAddressThreshold_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressThresholdServiceClient) FindAllEnabledNodeIPAddressThresholds(ctx context.Context, in *FindAllEnabledNodeIPAddressThresholdsRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeIPAddressThresholdsResponse, error) {
	out := new(FindAllEnabledNodeIPAddressThresholdsResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressThresholdService_FindAllEnabledNodeIPAddressThresholds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressThresholdServiceClient) CountAllEnabledNodeIPAddressThresholds(ctx context.Context, in *CountAllEnabledNodeIPAddressThresholdsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressThresholdService_CountAllEnabledNodeIPAddressThresholds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressThresholdServiceClient) UpdateAllNodeIPAddressThresholds(ctx context.Context, in *UpdateAllNodeIPAddressThresholdsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeIPAddressThresholdService_UpdateAllNodeIPAddressThresholds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeIPAddressThresholdServiceServer is the server API for NodeIPAddressThresholdService service.
// All implementations should embed UnimplementedNodeIPAddressThresholdServiceServer
// for forward compatibility
type NodeIPAddressThresholdServiceServer interface {
	// 创建阈值
	CreateNodeIPAddressThreshold(context.Context, *CreateNodeIPAddressThresholdRequest) (*CreateNodeIPAddressThresholdResponse, error)
	// 修改阈值
	UpdateNodeIPAddressThreshold(context.Context, *UpdateNodeIPAddressThresholdRequest) (*RPCSuccess, error)
	// 删除阈值
	DeleteNodeIPAddressThreshold(context.Context, *DeleteNodeIPAddressThresholdRequest) (*RPCSuccess, error)
	// 查找IP的所有阈值
	FindAllEnabledNodeIPAddressThresholds(context.Context, *FindAllEnabledNodeIPAddressThresholdsRequest) (*FindAllEnabledNodeIPAddressThresholdsResponse, error)
	// 计算IP阈值的数量
	CountAllEnabledNodeIPAddressThresholds(context.Context, *CountAllEnabledNodeIPAddressThresholdsRequest) (*RPCCountResponse, error)
	// 批量更新阈值
	UpdateAllNodeIPAddressThresholds(context.Context, *UpdateAllNodeIPAddressThresholdsRequest) (*RPCSuccess, error)
}

// UnimplementedNodeIPAddressThresholdServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeIPAddressThresholdServiceServer struct {
}

func (UnimplementedNodeIPAddressThresholdServiceServer) CreateNodeIPAddressThreshold(context.Context, *CreateNodeIPAddressThresholdRequest) (*CreateNodeIPAddressThresholdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeIPAddressThreshold not implemented")
}
func (UnimplementedNodeIPAddressThresholdServiceServer) UpdateNodeIPAddressThreshold(context.Context, *UpdateNodeIPAddressThresholdRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeIPAddressThreshold not implemented")
}
func (UnimplementedNodeIPAddressThresholdServiceServer) DeleteNodeIPAddressThreshold(context.Context, *DeleteNodeIPAddressThresholdRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeIPAddressThreshold not implemented")
}
func (UnimplementedNodeIPAddressThresholdServiceServer) FindAllEnabledNodeIPAddressThresholds(context.Context, *FindAllEnabledNodeIPAddressThresholdsRequest) (*FindAllEnabledNodeIPAddressThresholdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeIPAddressThresholds not implemented")
}
func (UnimplementedNodeIPAddressThresholdServiceServer) CountAllEnabledNodeIPAddressThresholds(context.Context, *CountAllEnabledNodeIPAddressThresholdsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeIPAddressThresholds not implemented")
}
func (UnimplementedNodeIPAddressThresholdServiceServer) UpdateAllNodeIPAddressThresholds(context.Context, *UpdateAllNodeIPAddressThresholdsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAllNodeIPAddressThresholds not implemented")
}

// UnsafeNodeIPAddressThresholdServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeIPAddressThresholdServiceServer will
// result in compilation errors.
type UnsafeNodeIPAddressThresholdServiceServer interface {
	mustEmbedUnimplementedNodeIPAddressThresholdServiceServer()
}

func RegisterNodeIPAddressThresholdServiceServer(s grpc.ServiceRegistrar, srv NodeIPAddressThresholdServiceServer) {
	s.RegisterService(&NodeIPAddressThresholdService_ServiceDesc, srv)
}

func _NodeIPAddressThresholdService_CreateNodeIPAddressThreshold_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeIPAddressThresholdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressThresholdServiceServer).CreateNodeIPAddressThreshold(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressThresholdService_CreateNodeIPAddressThreshold_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressThresholdServiceServer).CreateNodeIPAddressThreshold(ctx, req.(*CreateNodeIPAddressThresholdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressThresholdService_UpdateNodeIPAddressThreshold_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeIPAddressThresholdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressThresholdServiceServer).UpdateNodeIPAddressThreshold(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressThresholdService_UpdateNodeIPAddressThreshold_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressThresholdServiceServer).UpdateNodeIPAddressThreshold(ctx, req.(*UpdateNodeIPAddressThresholdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressThresholdService_DeleteNodeIPAddressThreshold_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeIPAddressThresholdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressThresholdServiceServer).DeleteNodeIPAddressThreshold(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressThresholdService_DeleteNodeIPAddressThreshold_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressThresholdServiceServer).DeleteNodeIPAddressThreshold(ctx, req.(*DeleteNodeIPAddressThresholdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressThresholdService_FindAllEnabledNodeIPAddressThresholds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeIPAddressThresholdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressThresholdServiceServer).FindAllEnabledNodeIPAddressThresholds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressThresholdService_FindAllEnabledNodeIPAddressThresholds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressThresholdServiceServer).FindAllEnabledNodeIPAddressThresholds(ctx, req.(*FindAllEnabledNodeIPAddressThresholdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressThresholdService_CountAllEnabledNodeIPAddressThresholds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeIPAddressThresholdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressThresholdServiceServer).CountAllEnabledNodeIPAddressThresholds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressThresholdService_CountAllEnabledNodeIPAddressThresholds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressThresholdServiceServer).CountAllEnabledNodeIPAddressThresholds(ctx, req.(*CountAllEnabledNodeIPAddressThresholdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressThresholdService_UpdateAllNodeIPAddressThresholds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAllNodeIPAddressThresholdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressThresholdServiceServer).UpdateAllNodeIPAddressThresholds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressThresholdService_UpdateAllNodeIPAddressThresholds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressThresholdServiceServer).UpdateAllNodeIPAddressThresholds(ctx, req.(*UpdateAllNodeIPAddressThresholdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeIPAddressThresholdService_ServiceDesc is the grpc.ServiceDesc for NodeIPAddressThresholdService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeIPAddressThresholdService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeIPAddressThresholdService",
	HandlerType: (*NodeIPAddressThresholdServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeIPAddressThreshold",
			Handler:    _NodeIPAddressThresholdService_CreateNodeIPAddressThreshold_Handler,
		},
		{
			MethodName: "updateNodeIPAddressThreshold",
			Handler:    _NodeIPAddressThresholdService_UpdateNodeIPAddressThreshold_Handler,
		},
		{
			MethodName: "deleteNodeIPAddressThreshold",
			Handler:    _NodeIPAddressThresholdService_DeleteNodeIPAddressThreshold_Handler,
		},
		{
			MethodName: "findAllEnabledNodeIPAddressThresholds",
			Handler:    _NodeIPAddressThresholdService_FindAllEnabledNodeIPAddressThresholds_Handler,
		},
		{
			MethodName: "countAllEnabledNodeIPAddressThresholds",
			Handler:    _NodeIPAddressThresholdService_CountAllEnabledNodeIPAddressThresholds_Handler,
		},
		{
			MethodName: "updateAllNodeIPAddressThresholds",
			Handler:    _NodeIPAddressThresholdService_UpdateAllNodeIPAddressThresholds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_ip_address_threshold.proto",
}
