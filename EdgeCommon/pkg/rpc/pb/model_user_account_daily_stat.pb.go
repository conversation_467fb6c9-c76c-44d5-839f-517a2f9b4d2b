// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_account_daily_stat.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserAccountDailyStat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Day           string                 `protobuf:"bytes,2,opt,name=day,proto3" json:"day,omitempty"`
	Month         string                 `protobuf:"bytes,3,opt,name=month,proto3" json:"month,omitempty"`
	Income        float32                `protobuf:"fixed32,4,opt,name=income,proto3" json:"income,omitempty"`
	Expense       float32                `protobuf:"fixed32,5,opt,name=expense,proto3" json:"expense,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAccountDailyStat) Reset() {
	*x = UserAccountDailyStat{}
	mi := &file_models_model_user_account_daily_stat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccountDailyStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccountDailyStat) ProtoMessage() {}

func (x *UserAccountDailyStat) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_account_daily_stat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccountDailyStat.ProtoReflect.Descriptor instead.
func (*UserAccountDailyStat) Descriptor() ([]byte, []int) {
	return file_models_model_user_account_daily_stat_proto_rawDescGZIP(), []int{0}
}

func (x *UserAccountDailyStat) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAccountDailyStat) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *UserAccountDailyStat) GetMonth() string {
	if x != nil {
		return x.Month
	}
	return ""
}

func (x *UserAccountDailyStat) GetIncome() float32 {
	if x != nil {
		return x.Income
	}
	return 0
}

func (x *UserAccountDailyStat) GetExpense() float32 {
	if x != nil {
		return x.Expense
	}
	return 0
}

var File_models_model_user_account_daily_stat_proto protoreflect.FileDescriptor

const file_models_model_user_account_daily_stat_proto_rawDesc = "" +
	"\n" +
	"*models/model_user_account_daily_stat.proto\x12\x02pb\"\x80\x01\n" +
	"\x14UserAccountDailyStat\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03day\x18\x02 \x01(\tR\x03day\x12\x14\n" +
	"\x05month\x18\x03 \x01(\tR\x05month\x12\x16\n" +
	"\x06income\x18\x04 \x01(\x02R\x06income\x12\x18\n" +
	"\aexpense\x18\x05 \x01(\x02R\aexpenseB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_account_daily_stat_proto_rawDescOnce sync.Once
	file_models_model_user_account_daily_stat_proto_rawDescData []byte
)

func file_models_model_user_account_daily_stat_proto_rawDescGZIP() []byte {
	file_models_model_user_account_daily_stat_proto_rawDescOnce.Do(func() {
		file_models_model_user_account_daily_stat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_account_daily_stat_proto_rawDesc), len(file_models_model_user_account_daily_stat_proto_rawDesc)))
	})
	return file_models_model_user_account_daily_stat_proto_rawDescData
}

var file_models_model_user_account_daily_stat_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_account_daily_stat_proto_goTypes = []any{
	(*UserAccountDailyStat)(nil), // 0: pb.UserAccountDailyStat
}
var file_models_model_user_account_daily_stat_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_user_account_daily_stat_proto_init() }
func file_models_model_user_account_daily_stat_proto_init() {
	if File_models_model_user_account_daily_stat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_account_daily_stat_proto_rawDesc), len(file_models_model_user_account_daily_stat_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_account_daily_stat_proto_goTypes,
		DependencyIndexes: file_models_model_user_account_daily_stat_proto_depIdxs,
		MessageInfos:      file_models_model_user_account_daily_stat_proto_msgTypes,
	}.Build()
	File_models_model_user_account_daily_stat_proto = out.File
	file_models_model_user_account_daily_stat_proto_goTypes = nil
	file_models_model_user_account_daily_stat_proto_depIdxs = nil
}
