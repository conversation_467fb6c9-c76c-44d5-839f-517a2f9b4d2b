// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_message_recipient.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageRecipientService_CreateMessageRecipient_FullMethodName           = "/pb.MessageRecipientService/createMessageRecipient"
	MessageRecipientService_UpdateMessageRecipient_FullMethodName           = "/pb.MessageRecipientService/updateMessageRecipient"
	MessageRecipientService_DeleteMessageRecipient_FullMethodName           = "/pb.MessageRecipientService/deleteMessageRecipient"
	MessageRecipientService_CountAllEnabledMessageRecipients_FullMethodName = "/pb.MessageRecipientService/countAllEnabledMessageRecipients"
	MessageRecipientService_ListEnabledMessageRecipients_FullMethodName     = "/pb.MessageRecipientService/listEnabledMessageRecipients"
	MessageRecipientService_FindEnabledMessageRecipient_FullMethodName      = "/pb.MessageRecipientService/findEnabledMessageRecipient"
)

// MessageRecipientServiceClient is the client API for MessageRecipientService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageRecipientServiceClient interface {
	// 创建接收人
	CreateMessageRecipient(ctx context.Context, in *CreateMessageRecipientRequest, opts ...grpc.CallOption) (*CreateMessageRecipientResponse, error)
	// 修改接收人
	UpdateMessageRecipient(ctx context.Context, in *UpdateMessageRecipientRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除接收人
	DeleteMessageRecipient(ctx context.Context, in *DeleteMessageRecipientRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算接收人数量
	CountAllEnabledMessageRecipients(ctx context.Context, in *CountAllEnabledMessageRecipientsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页接收人
	ListEnabledMessageRecipients(ctx context.Context, in *ListEnabledMessageRecipientsRequest, opts ...grpc.CallOption) (*ListEnabledMessageRecipientsResponse, error)
	// 查找单个接收人信息
	FindEnabledMessageRecipient(ctx context.Context, in *FindEnabledMessageRecipientRequest, opts ...grpc.CallOption) (*FindEnabledMessageRecipientResponse, error)
}

type messageRecipientServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageRecipientServiceClient(cc grpc.ClientConnInterface) MessageRecipientServiceClient {
	return &messageRecipientServiceClient{cc}
}

func (c *messageRecipientServiceClient) CreateMessageRecipient(ctx context.Context, in *CreateMessageRecipientRequest, opts ...grpc.CallOption) (*CreateMessageRecipientResponse, error) {
	out := new(CreateMessageRecipientResponse)
	err := c.cc.Invoke(ctx, MessageRecipientService_CreateMessageRecipient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientServiceClient) UpdateMessageRecipient(ctx context.Context, in *UpdateMessageRecipientRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageRecipientService_UpdateMessageRecipient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientServiceClient) DeleteMessageRecipient(ctx context.Context, in *DeleteMessageRecipientRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageRecipientService_DeleteMessageRecipient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientServiceClient) CountAllEnabledMessageRecipients(ctx context.Context, in *CountAllEnabledMessageRecipientsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, MessageRecipientService_CountAllEnabledMessageRecipients_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientServiceClient) ListEnabledMessageRecipients(ctx context.Context, in *ListEnabledMessageRecipientsRequest, opts ...grpc.CallOption) (*ListEnabledMessageRecipientsResponse, error) {
	out := new(ListEnabledMessageRecipientsResponse)
	err := c.cc.Invoke(ctx, MessageRecipientService_ListEnabledMessageRecipients_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageRecipientServiceClient) FindEnabledMessageRecipient(ctx context.Context, in *FindEnabledMessageRecipientRequest, opts ...grpc.CallOption) (*FindEnabledMessageRecipientResponse, error) {
	out := new(FindEnabledMessageRecipientResponse)
	err := c.cc.Invoke(ctx, MessageRecipientService_FindEnabledMessageRecipient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageRecipientServiceServer is the server API for MessageRecipientService service.
// All implementations should embed UnimplementedMessageRecipientServiceServer
// for forward compatibility
type MessageRecipientServiceServer interface {
	// 创建接收人
	CreateMessageRecipient(context.Context, *CreateMessageRecipientRequest) (*CreateMessageRecipientResponse, error)
	// 修改接收人
	UpdateMessageRecipient(context.Context, *UpdateMessageRecipientRequest) (*RPCSuccess, error)
	// 删除接收人
	DeleteMessageRecipient(context.Context, *DeleteMessageRecipientRequest) (*RPCSuccess, error)
	// 计算接收人数量
	CountAllEnabledMessageRecipients(context.Context, *CountAllEnabledMessageRecipientsRequest) (*RPCCountResponse, error)
	// 列出单页接收人
	ListEnabledMessageRecipients(context.Context, *ListEnabledMessageRecipientsRequest) (*ListEnabledMessageRecipientsResponse, error)
	// 查找单个接收人信息
	FindEnabledMessageRecipient(context.Context, *FindEnabledMessageRecipientRequest) (*FindEnabledMessageRecipientResponse, error)
}

// UnimplementedMessageRecipientServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMessageRecipientServiceServer struct {
}

func (UnimplementedMessageRecipientServiceServer) CreateMessageRecipient(context.Context, *CreateMessageRecipientRequest) (*CreateMessageRecipientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMessageRecipient not implemented")
}
func (UnimplementedMessageRecipientServiceServer) UpdateMessageRecipient(context.Context, *UpdateMessageRecipientRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMessageRecipient not implemented")
}
func (UnimplementedMessageRecipientServiceServer) DeleteMessageRecipient(context.Context, *DeleteMessageRecipientRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMessageRecipient not implemented")
}
func (UnimplementedMessageRecipientServiceServer) CountAllEnabledMessageRecipients(context.Context, *CountAllEnabledMessageRecipientsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledMessageRecipients not implemented")
}
func (UnimplementedMessageRecipientServiceServer) ListEnabledMessageRecipients(context.Context, *ListEnabledMessageRecipientsRequest) (*ListEnabledMessageRecipientsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledMessageRecipients not implemented")
}
func (UnimplementedMessageRecipientServiceServer) FindEnabledMessageRecipient(context.Context, *FindEnabledMessageRecipientRequest) (*FindEnabledMessageRecipientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledMessageRecipient not implemented")
}

// UnsafeMessageRecipientServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageRecipientServiceServer will
// result in compilation errors.
type UnsafeMessageRecipientServiceServer interface {
	mustEmbedUnimplementedMessageRecipientServiceServer()
}

func RegisterMessageRecipientServiceServer(s grpc.ServiceRegistrar, srv MessageRecipientServiceServer) {
	s.RegisterService(&MessageRecipientService_ServiceDesc, srv)
}

func _MessageRecipientService_CreateMessageRecipient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMessageRecipientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientServiceServer).CreateMessageRecipient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientService_CreateMessageRecipient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientServiceServer).CreateMessageRecipient(ctx, req.(*CreateMessageRecipientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientService_UpdateMessageRecipient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMessageRecipientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientServiceServer).UpdateMessageRecipient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientService_UpdateMessageRecipient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientServiceServer).UpdateMessageRecipient(ctx, req.(*UpdateMessageRecipientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientService_DeleteMessageRecipient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMessageRecipientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientServiceServer).DeleteMessageRecipient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientService_DeleteMessageRecipient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientServiceServer).DeleteMessageRecipient(ctx, req.(*DeleteMessageRecipientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientService_CountAllEnabledMessageRecipients_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledMessageRecipientsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientServiceServer).CountAllEnabledMessageRecipients(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientService_CountAllEnabledMessageRecipients_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientServiceServer).CountAllEnabledMessageRecipients(ctx, req.(*CountAllEnabledMessageRecipientsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientService_ListEnabledMessageRecipients_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledMessageRecipientsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientServiceServer).ListEnabledMessageRecipients(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientService_ListEnabledMessageRecipients_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientServiceServer).ListEnabledMessageRecipients(ctx, req.(*ListEnabledMessageRecipientsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageRecipientService_FindEnabledMessageRecipient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledMessageRecipientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageRecipientServiceServer).FindEnabledMessageRecipient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageRecipientService_FindEnabledMessageRecipient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageRecipientServiceServer).FindEnabledMessageRecipient(ctx, req.(*FindEnabledMessageRecipientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageRecipientService_ServiceDesc is the grpc.ServiceDesc for MessageRecipientService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageRecipientService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MessageRecipientService",
	HandlerType: (*MessageRecipientServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createMessageRecipient",
			Handler:    _MessageRecipientService_CreateMessageRecipient_Handler,
		},
		{
			MethodName: "updateMessageRecipient",
			Handler:    _MessageRecipientService_UpdateMessageRecipient_Handler,
		},
		{
			MethodName: "deleteMessageRecipient",
			Handler:    _MessageRecipientService_DeleteMessageRecipient_Handler,
		},
		{
			MethodName: "countAllEnabledMessageRecipients",
			Handler:    _MessageRecipientService_CountAllEnabledMessageRecipients_Handler,
		},
		{
			MethodName: "listEnabledMessageRecipients",
			Handler:    _MessageRecipientService_ListEnabledMessageRecipients_Handler,
		},
		{
			MethodName: "findEnabledMessageRecipient",
			Handler:    _MessageRecipientService_FindEnabledMessageRecipient_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_message_recipient.proto",
}
