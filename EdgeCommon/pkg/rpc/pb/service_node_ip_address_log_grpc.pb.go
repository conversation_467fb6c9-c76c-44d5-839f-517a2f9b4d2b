// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_ip_address_log.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeIPAddressLogService_CountAllNodeIPAddressLogs_FullMethodName = "/pb.NodeIPAddressLogService/countAllNodeIPAddressLogs"
	NodeIPAddressLogService_ListNodeIPAddressLogs_FullMethodName     = "/pb.NodeIPAddressLogService/listNodeIPAddressLogs"
)

// NodeIPAddressLogServiceClient is the client API for NodeIPAddressLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeIPAddressLogServiceClient interface {
	// 计算日志数量
	CountAllNodeIPAddressLogs(ctx context.Context, in *CountAllNodeIPAddressLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页日志
	ListNodeIPAddressLogs(ctx context.Context, in *ListNodeIPAddressLogsRequest, opts ...grpc.CallOption) (*ListNodeIPAddressLogsResponse, error)
}

type nodeIPAddressLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeIPAddressLogServiceClient(cc grpc.ClientConnInterface) NodeIPAddressLogServiceClient {
	return &nodeIPAddressLogServiceClient{cc}
}

func (c *nodeIPAddressLogServiceClient) CountAllNodeIPAddressLogs(ctx context.Context, in *CountAllNodeIPAddressLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressLogService_CountAllNodeIPAddressLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeIPAddressLogServiceClient) ListNodeIPAddressLogs(ctx context.Context, in *ListNodeIPAddressLogsRequest, opts ...grpc.CallOption) (*ListNodeIPAddressLogsResponse, error) {
	out := new(ListNodeIPAddressLogsResponse)
	err := c.cc.Invoke(ctx, NodeIPAddressLogService_ListNodeIPAddressLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeIPAddressLogServiceServer is the server API for NodeIPAddressLogService service.
// All implementations should embed UnimplementedNodeIPAddressLogServiceServer
// for forward compatibility
type NodeIPAddressLogServiceServer interface {
	// 计算日志数量
	CountAllNodeIPAddressLogs(context.Context, *CountAllNodeIPAddressLogsRequest) (*RPCCountResponse, error)
	// 列出单页日志
	ListNodeIPAddressLogs(context.Context, *ListNodeIPAddressLogsRequest) (*ListNodeIPAddressLogsResponse, error)
}

// UnimplementedNodeIPAddressLogServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeIPAddressLogServiceServer struct {
}

func (UnimplementedNodeIPAddressLogServiceServer) CountAllNodeIPAddressLogs(context.Context, *CountAllNodeIPAddressLogsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNodeIPAddressLogs not implemented")
}
func (UnimplementedNodeIPAddressLogServiceServer) ListNodeIPAddressLogs(context.Context, *ListNodeIPAddressLogsRequest) (*ListNodeIPAddressLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodeIPAddressLogs not implemented")
}

// UnsafeNodeIPAddressLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeIPAddressLogServiceServer will
// result in compilation errors.
type UnsafeNodeIPAddressLogServiceServer interface {
	mustEmbedUnimplementedNodeIPAddressLogServiceServer()
}

func RegisterNodeIPAddressLogServiceServer(s grpc.ServiceRegistrar, srv NodeIPAddressLogServiceServer) {
	s.RegisterService(&NodeIPAddressLogService_ServiceDesc, srv)
}

func _NodeIPAddressLogService_CountAllNodeIPAddressLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNodeIPAddressLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressLogServiceServer).CountAllNodeIPAddressLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressLogService_CountAllNodeIPAddressLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressLogServiceServer).CountAllNodeIPAddressLogs(ctx, req.(*CountAllNodeIPAddressLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeIPAddressLogService_ListNodeIPAddressLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeIPAddressLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeIPAddressLogServiceServer).ListNodeIPAddressLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeIPAddressLogService_ListNodeIPAddressLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeIPAddressLogServiceServer).ListNodeIPAddressLogs(ctx, req.(*ListNodeIPAddressLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeIPAddressLogService_ServiceDesc is the grpc.ServiceDesc for NodeIPAddressLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeIPAddressLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeIPAddressLogService",
	HandlerType: (*NodeIPAddressLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countAllNodeIPAddressLogs",
			Handler:    _NodeIPAddressLogService_CountAllNodeIPAddressLogs_Handler,
		},
		{
			MethodName: "listNodeIPAddressLogs",
			Handler:    _NodeIPAddressLogService_ListNodeIPAddressLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_ip_address_log.proto",
}
