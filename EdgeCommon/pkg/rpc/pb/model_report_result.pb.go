// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_report_result.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReportResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	TargetId      int64                  `protobuf:"varint,3,opt,name=targetId,proto3" json:"targetId,omitempty"`
	TargetDesc    string                 `protobuf:"bytes,4,opt,name=targetDesc,proto3" json:"targetDesc,omitempty"`
	ReportNodeId  int64                  `protobuf:"varint,5,opt,name=reportNodeId,proto3" json:"reportNodeId,omitempty"`
	IsOk          bool                   `protobuf:"varint,6,opt,name=isOk,proto3" json:"isOk,omitempty"`
	CostMs        float32                `protobuf:"fixed32,7,opt,name=costMs,proto3" json:"costMs,omitempty"`
	Error         string                 `protobuf:"bytes,8,opt,name=error,proto3" json:"error,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,9,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	Level         string                 `protobuf:"bytes,10,opt,name=level,proto3" json:"level,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportResult) Reset() {
	*x = ReportResult{}
	mi := &file_models_model_report_result_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportResult) ProtoMessage() {}

func (x *ReportResult) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_report_result_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportResult.ProtoReflect.Descriptor instead.
func (*ReportResult) Descriptor() ([]byte, []int) {
	return file_models_model_report_result_proto_rawDescGZIP(), []int{0}
}

func (x *ReportResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReportResult) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ReportResult) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *ReportResult) GetTargetDesc() string {
	if x != nil {
		return x.TargetDesc
	}
	return ""
}

func (x *ReportResult) GetReportNodeId() int64 {
	if x != nil {
		return x.ReportNodeId
	}
	return 0
}

func (x *ReportResult) GetIsOk() bool {
	if x != nil {
		return x.IsOk
	}
	return false
}

func (x *ReportResult) GetCostMs() float32 {
	if x != nil {
		return x.CostMs
	}
	return 0
}

func (x *ReportResult) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *ReportResult) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ReportResult) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

var File_models_model_report_result_proto protoreflect.FileDescriptor

const file_models_model_report_result_proto_rawDesc = "" +
	"\n" +
	" models/model_report_result.proto\x12\x02pb\"\x88\x02\n" +
	"\fReportResult\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x1a\n" +
	"\btargetId\x18\x03 \x01(\x03R\btargetId\x12\x1e\n" +
	"\n" +
	"targetDesc\x18\x04 \x01(\tR\n" +
	"targetDesc\x12\"\n" +
	"\freportNodeId\x18\x05 \x01(\x03R\freportNodeId\x12\x12\n" +
	"\x04isOk\x18\x06 \x01(\bR\x04isOk\x12\x16\n" +
	"\x06costMs\x18\a \x01(\x02R\x06costMs\x12\x14\n" +
	"\x05error\x18\b \x01(\tR\x05error\x12\x1c\n" +
	"\tupdatedAt\x18\t \x01(\x03R\tupdatedAt\x12\x14\n" +
	"\x05level\x18\n" +
	" \x01(\tR\x05levelB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_report_result_proto_rawDescOnce sync.Once
	file_models_model_report_result_proto_rawDescData []byte
)

func file_models_model_report_result_proto_rawDescGZIP() []byte {
	file_models_model_report_result_proto_rawDescOnce.Do(func() {
		file_models_model_report_result_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_report_result_proto_rawDesc), len(file_models_model_report_result_proto_rawDesc)))
	})
	return file_models_model_report_result_proto_rawDescData
}

var file_models_model_report_result_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_report_result_proto_goTypes = []any{
	(*ReportResult)(nil), // 0: pb.ReportResult
}
var file_models_model_report_result_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_report_result_proto_init() }
func file_models_model_report_result_proto_init() {
	if File_models_model_report_result_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_report_result_proto_rawDesc), len(file_models_model_report_result_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_report_result_proto_goTypes,
		DependencyIndexes: file_models_model_report_result_proto_depIdxs,
		MessageInfos:      file_models_model_report_result_proto_msgTypes,
	}.Build()
	File_models_model_report_result_proto = out.File
	file_models_model_report_result_proto_goTypes = nil
	file_models_model_report_result_proto_depIdxs = nil
}
