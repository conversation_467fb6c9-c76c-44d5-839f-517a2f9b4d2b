// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_gzip.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPGzipService_CreateHTTPGzip_FullMethodName            = "/pb.HTTPGzipService/createHTTPGzip"
	HTTPGzipService_FindEnabledHTTPGzipConfig_FullMethodName = "/pb.HTTPGzipService/findEnabledHTTPGzipConfig"
	HTTPGzipService_UpdateHTTPGzip_FullMethodName            = "/pb.HTTPGzipService/updateHTTPGzip"
)

// HTTPGzipServiceClient is the client API for HTTPGzipService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPGzipServiceClient interface {
	// 创建Gzip配置
	CreateHTTPGzip(ctx context.Context, in *CreateHTTPGzipRequest, opts ...grpc.CallOption) (*CreateHTTPGzipResponse, error)
	// 查找Gzip配置
	FindEnabledHTTPGzipConfig(ctx context.Context, in *FindEnabledGzipConfigRequest, opts ...grpc.CallOption) (*FindEnabledGzipConfigResponse, error)
	// 修改Gzip配置
	UpdateHTTPGzip(ctx context.Context, in *UpdateHTTPGzipRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type hTTPGzipServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPGzipServiceClient(cc grpc.ClientConnInterface) HTTPGzipServiceClient {
	return &hTTPGzipServiceClient{cc}
}

func (c *hTTPGzipServiceClient) CreateHTTPGzip(ctx context.Context, in *CreateHTTPGzipRequest, opts ...grpc.CallOption) (*CreateHTTPGzipResponse, error) {
	out := new(CreateHTTPGzipResponse)
	err := c.cc.Invoke(ctx, HTTPGzipService_CreateHTTPGzip_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPGzipServiceClient) FindEnabledHTTPGzipConfig(ctx context.Context, in *FindEnabledGzipConfigRequest, opts ...grpc.CallOption) (*FindEnabledGzipConfigResponse, error) {
	out := new(FindEnabledGzipConfigResponse)
	err := c.cc.Invoke(ctx, HTTPGzipService_FindEnabledHTTPGzipConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPGzipServiceClient) UpdateHTTPGzip(ctx context.Context, in *UpdateHTTPGzipRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPGzipService_UpdateHTTPGzip_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPGzipServiceServer is the server API for HTTPGzipService service.
// All implementations should embed UnimplementedHTTPGzipServiceServer
// for forward compatibility
type HTTPGzipServiceServer interface {
	// 创建Gzip配置
	CreateHTTPGzip(context.Context, *CreateHTTPGzipRequest) (*CreateHTTPGzipResponse, error)
	// 查找Gzip配置
	FindEnabledHTTPGzipConfig(context.Context, *FindEnabledGzipConfigRequest) (*FindEnabledGzipConfigResponse, error)
	// 修改Gzip配置
	UpdateHTTPGzip(context.Context, *UpdateHTTPGzipRequest) (*RPCSuccess, error)
}

// UnimplementedHTTPGzipServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPGzipServiceServer struct {
}

func (UnimplementedHTTPGzipServiceServer) CreateHTTPGzip(context.Context, *CreateHTTPGzipRequest) (*CreateHTTPGzipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPGzip not implemented")
}
func (UnimplementedHTTPGzipServiceServer) FindEnabledHTTPGzipConfig(context.Context, *FindEnabledGzipConfigRequest) (*FindEnabledGzipConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPGzipConfig not implemented")
}
func (UnimplementedHTTPGzipServiceServer) UpdateHTTPGzip(context.Context, *UpdateHTTPGzipRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPGzip not implemented")
}

// UnsafeHTTPGzipServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPGzipServiceServer will
// result in compilation errors.
type UnsafeHTTPGzipServiceServer interface {
	mustEmbedUnimplementedHTTPGzipServiceServer()
}

func RegisterHTTPGzipServiceServer(s grpc.ServiceRegistrar, srv HTTPGzipServiceServer) {
	s.RegisterService(&HTTPGzipService_ServiceDesc, srv)
}

func _HTTPGzipService_CreateHTTPGzip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPGzipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPGzipServiceServer).CreateHTTPGzip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPGzipService_CreateHTTPGzip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPGzipServiceServer).CreateHTTPGzip(ctx, req.(*CreateHTTPGzipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPGzipService_FindEnabledHTTPGzipConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledGzipConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPGzipServiceServer).FindEnabledHTTPGzipConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPGzipService_FindEnabledHTTPGzipConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPGzipServiceServer).FindEnabledHTTPGzipConfig(ctx, req.(*FindEnabledGzipConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPGzipService_UpdateHTTPGzip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPGzipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPGzipServiceServer).UpdateHTTPGzip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPGzipService_UpdateHTTPGzip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPGzipServiceServer).UpdateHTTPGzip(ctx, req.(*UpdateHTTPGzipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPGzipService_ServiceDesc is the grpc.ServiceDesc for HTTPGzipService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPGzipService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPGzipService",
	HandlerType: (*HTTPGzipServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHTTPGzip",
			Handler:    _HTTPGzipService_CreateHTTPGzip_Handler,
		},
		{
			MethodName: "findEnabledHTTPGzipConfig",
			Handler:    _HTTPGzipService_FindEnabledHTTPGzipConfig_Handler,
		},
		{
			MethodName: "updateHTTPGzip",
			Handler:    _HTTPGzipService_UpdateHTTPGzip_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_gzip.proto",
}
