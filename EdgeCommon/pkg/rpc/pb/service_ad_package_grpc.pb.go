// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ad_package.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ADPackageService_CreateADPackage_FullMethodName        = "/pb.ADPackageService/createADPackage"
	ADPackageService_UpdateADPackage_FullMethodName        = "/pb.ADPackageService/updateADPackage"
	ADPackageService_FindADPackage_FullMethodName          = "/pb.ADPackageService/findADPackage"
	ADPackageService_CountADPackages_FullMethodName        = "/pb.ADPackageService/countADPackages"
	ADPackageService_CountAllIdleADPackages_FullMethodName = "/pb.ADPackageService/countAllIdleADPackages"
	ADPackageService_ListADPackages_FullMethodName         = "/pb.ADPackageService/listADPackages"
	ADPackageService_FindAllIdleADPackages_FullMethodName  = "/pb.ADPackageService/findAllIdleADPackages"
	ADPackageService_DeleteADPackage_FullMethodName        = "/pb.ADPackageService/deleteADPackage"
)

// ADPackageServiceClient is the client API for ADPackageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ADPackageServiceClient interface {
	// 创建高防产品
	CreateADPackage(ctx context.Context, in *CreateADPackageRequest, opts ...grpc.CallOption) (*CreateADPackageResponse, error)
	// 修改高防产品
	UpdateADPackage(ctx context.Context, in *UpdateADPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个高防产品
	FindADPackage(ctx context.Context, in *FindADPackageRequest, opts ...grpc.CallOption) (*FindADPackageResponse, error)
	// 查询高防产品数量
	CountADPackages(ctx context.Context, in *CountADPackagesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查询可用的产品数量
	CountAllIdleADPackages(ctx context.Context, in *CountAllIdleADPackages, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页高防产品
	ListADPackages(ctx context.Context, in *ListADPackagesRequest, opts ...grpc.CallOption) (*ListADPackagesResponse, error)
	// 列出所有可用的高防产品
	FindAllIdleADPackages(ctx context.Context, in *FindAllIdleADPackagesRequest, opts ...grpc.CallOption) (*FindAllIdleADPackagesResponse, error)
	// 删除高防产品
	DeleteADPackage(ctx context.Context, in *DeleteADPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type aDPackageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewADPackageServiceClient(cc grpc.ClientConnInterface) ADPackageServiceClient {
	return &aDPackageServiceClient{cc}
}

func (c *aDPackageServiceClient) CreateADPackage(ctx context.Context, in *CreateADPackageRequest, opts ...grpc.CallOption) (*CreateADPackageResponse, error) {
	out := new(CreateADPackageResponse)
	err := c.cc.Invoke(ctx, ADPackageService_CreateADPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) UpdateADPackage(ctx context.Context, in *UpdateADPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ADPackageService_UpdateADPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) FindADPackage(ctx context.Context, in *FindADPackageRequest, opts ...grpc.CallOption) (*FindADPackageResponse, error) {
	out := new(FindADPackageResponse)
	err := c.cc.Invoke(ctx, ADPackageService_FindADPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) CountADPackages(ctx context.Context, in *CountADPackagesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ADPackageService_CountADPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) CountAllIdleADPackages(ctx context.Context, in *CountAllIdleADPackages, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ADPackageService_CountAllIdleADPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) ListADPackages(ctx context.Context, in *ListADPackagesRequest, opts ...grpc.CallOption) (*ListADPackagesResponse, error) {
	out := new(ListADPackagesResponse)
	err := c.cc.Invoke(ctx, ADPackageService_ListADPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) FindAllIdleADPackages(ctx context.Context, in *FindAllIdleADPackagesRequest, opts ...grpc.CallOption) (*FindAllIdleADPackagesResponse, error) {
	out := new(FindAllIdleADPackagesResponse)
	err := c.cc.Invoke(ctx, ADPackageService_FindAllIdleADPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackageServiceClient) DeleteADPackage(ctx context.Context, in *DeleteADPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ADPackageService_DeleteADPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ADPackageServiceServer is the server API for ADPackageService service.
// All implementations should embed UnimplementedADPackageServiceServer
// for forward compatibility
type ADPackageServiceServer interface {
	// 创建高防产品
	CreateADPackage(context.Context, *CreateADPackageRequest) (*CreateADPackageResponse, error)
	// 修改高防产品
	UpdateADPackage(context.Context, *UpdateADPackageRequest) (*RPCSuccess, error)
	// 查找单个高防产品
	FindADPackage(context.Context, *FindADPackageRequest) (*FindADPackageResponse, error)
	// 查询高防产品数量
	CountADPackages(context.Context, *CountADPackagesRequest) (*RPCCountResponse, error)
	// 查询可用的产品数量
	CountAllIdleADPackages(context.Context, *CountAllIdleADPackages) (*RPCCountResponse, error)
	// 列出单页高防产品
	ListADPackages(context.Context, *ListADPackagesRequest) (*ListADPackagesResponse, error)
	// 列出所有可用的高防产品
	FindAllIdleADPackages(context.Context, *FindAllIdleADPackagesRequest) (*FindAllIdleADPackagesResponse, error)
	// 删除高防产品
	DeleteADPackage(context.Context, *DeleteADPackageRequest) (*RPCSuccess, error)
}

// UnimplementedADPackageServiceServer should be embedded to have forward compatible implementations.
type UnimplementedADPackageServiceServer struct {
}

func (UnimplementedADPackageServiceServer) CreateADPackage(context.Context, *CreateADPackageRequest) (*CreateADPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateADPackage not implemented")
}
func (UnimplementedADPackageServiceServer) UpdateADPackage(context.Context, *UpdateADPackageRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateADPackage not implemented")
}
func (UnimplementedADPackageServiceServer) FindADPackage(context.Context, *FindADPackageRequest) (*FindADPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindADPackage not implemented")
}
func (UnimplementedADPackageServiceServer) CountADPackages(context.Context, *CountADPackagesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountADPackages not implemented")
}
func (UnimplementedADPackageServiceServer) CountAllIdleADPackages(context.Context, *CountAllIdleADPackages) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllIdleADPackages not implemented")
}
func (UnimplementedADPackageServiceServer) ListADPackages(context.Context, *ListADPackagesRequest) (*ListADPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListADPackages not implemented")
}
func (UnimplementedADPackageServiceServer) FindAllIdleADPackages(context.Context, *FindAllIdleADPackagesRequest) (*FindAllIdleADPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllIdleADPackages not implemented")
}
func (UnimplementedADPackageServiceServer) DeleteADPackage(context.Context, *DeleteADPackageRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteADPackage not implemented")
}

// UnsafeADPackageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ADPackageServiceServer will
// result in compilation errors.
type UnsafeADPackageServiceServer interface {
	mustEmbedUnimplementedADPackageServiceServer()
}

func RegisterADPackageServiceServer(s grpc.ServiceRegistrar, srv ADPackageServiceServer) {
	s.RegisterService(&ADPackageService_ServiceDesc, srv)
}

func _ADPackageService_CreateADPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateADPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).CreateADPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_CreateADPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).CreateADPackage(ctx, req.(*CreateADPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_UpdateADPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateADPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).UpdateADPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_UpdateADPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).UpdateADPackage(ctx, req.(*UpdateADPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_FindADPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindADPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).FindADPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_FindADPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).FindADPackage(ctx, req.(*FindADPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_CountADPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountADPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).CountADPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_CountADPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).CountADPackages(ctx, req.(*CountADPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_CountAllIdleADPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllIdleADPackages)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).CountAllIdleADPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_CountAllIdleADPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).CountAllIdleADPackages(ctx, req.(*CountAllIdleADPackages))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_ListADPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListADPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).ListADPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_ListADPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).ListADPackages(ctx, req.(*ListADPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_FindAllIdleADPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllIdleADPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).FindAllIdleADPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_FindAllIdleADPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).FindAllIdleADPackages(ctx, req.(*FindAllIdleADPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackageService_DeleteADPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteADPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackageServiceServer).DeleteADPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackageService_DeleteADPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackageServiceServer).DeleteADPackage(ctx, req.(*DeleteADPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ADPackageService_ServiceDesc is the grpc.ServiceDesc for ADPackageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ADPackageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ADPackageService",
	HandlerType: (*ADPackageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createADPackage",
			Handler:    _ADPackageService_CreateADPackage_Handler,
		},
		{
			MethodName: "updateADPackage",
			Handler:    _ADPackageService_UpdateADPackage_Handler,
		},
		{
			MethodName: "findADPackage",
			Handler:    _ADPackageService_FindADPackage_Handler,
		},
		{
			MethodName: "countADPackages",
			Handler:    _ADPackageService_CountADPackages_Handler,
		},
		{
			MethodName: "countAllIdleADPackages",
			Handler:    _ADPackageService_CountAllIdleADPackages_Handler,
		},
		{
			MethodName: "listADPackages",
			Handler:    _ADPackageService_ListADPackages_Handler,
		},
		{
			MethodName: "findAllIdleADPackages",
			Handler:    _ADPackageService_FindAllIdleADPackages_Handler,
		},
		{
			MethodName: "deleteADPackage",
			Handler:    _ADPackageService_DeleteADPackage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ad_package.proto",
}
