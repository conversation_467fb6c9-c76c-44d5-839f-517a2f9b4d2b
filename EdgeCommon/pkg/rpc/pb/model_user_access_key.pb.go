// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_access_key.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户AccessKey
type UserAccessKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	SubUserId     int64                  `protobuf:"varint,3,opt,name=subUserId,proto3" json:"subUserId,omitempty"`
	IsOn          bool                   `protobuf:"varint,4,opt,name=isOn,proto3" json:"isOn,omitempty"`
	UniqueId      string                 `protobuf:"bytes,5,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
	Secret        string                 `protobuf:"bytes,6,opt,name=secret,proto3" json:"secret,omitempty"`
	Description   string                 `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	AccessedAt    int64                  `protobuf:"varint,8,opt,name=accessedAt,proto3" json:"accessedAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAccessKey) Reset() {
	*x = UserAccessKey{}
	mi := &file_models_model_user_access_key_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccessKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccessKey) ProtoMessage() {}

func (x *UserAccessKey) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_access_key_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccessKey.ProtoReflect.Descriptor instead.
func (*UserAccessKey) Descriptor() ([]byte, []int) {
	return file_models_model_user_access_key_proto_rawDescGZIP(), []int{0}
}

func (x *UserAccessKey) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAccessKey) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserAccessKey) GetSubUserId() int64 {
	if x != nil {
		return x.SubUserId
	}
	return 0
}

func (x *UserAccessKey) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *UserAccessKey) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

func (x *UserAccessKey) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *UserAccessKey) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserAccessKey) GetAccessedAt() int64 {
	if x != nil {
		return x.AccessedAt
	}
	return 0
}

var File_models_model_user_access_key_proto protoreflect.FileDescriptor

const file_models_model_user_access_key_proto_rawDesc = "" +
	"\n" +
	"\"models/model_user_access_key.proto\x12\x02pb\"\xdf\x01\n" +
	"\rUserAccessKey\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x1c\n" +
	"\tsubUserId\x18\x03 \x01(\x03R\tsubUserId\x12\x12\n" +
	"\x04isOn\x18\x04 \x01(\bR\x04isOn\x12\x1a\n" +
	"\buniqueId\x18\x05 \x01(\tR\buniqueId\x12\x16\n" +
	"\x06secret\x18\x06 \x01(\tR\x06secret\x12 \n" +
	"\vdescription\x18\a \x01(\tR\vdescription\x12\x1e\n" +
	"\n" +
	"accessedAt\x18\b \x01(\x03R\n" +
	"accessedAtB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_access_key_proto_rawDescOnce sync.Once
	file_models_model_user_access_key_proto_rawDescData []byte
)

func file_models_model_user_access_key_proto_rawDescGZIP() []byte {
	file_models_model_user_access_key_proto_rawDescOnce.Do(func() {
		file_models_model_user_access_key_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_access_key_proto_rawDesc), len(file_models_model_user_access_key_proto_rawDesc)))
	})
	return file_models_model_user_access_key_proto_rawDescData
}

var file_models_model_user_access_key_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_access_key_proto_goTypes = []any{
	(*UserAccessKey)(nil), // 0: pb.UserAccessKey
}
var file_models_model_user_access_key_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_user_access_key_proto_init() }
func file_models_model_user_access_key_proto_init() {
	if File_models_model_user_access_key_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_access_key_proto_rawDesc), len(file_models_model_user_access_key_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_access_key_proto_goTypes,
		DependencyIndexes: file_models_model_user_access_key_proto_depIdxs,
		MessageInfos:      file_models_model_user_access_key_proto_msgTypes,
	}.Build()
	File_models_model_user_access_key_proto = out.File
	file_models_model_user_access_key_proto_goTypes = nil
	file_models_model_user_access_key_proto_depIdxs = nil
}
