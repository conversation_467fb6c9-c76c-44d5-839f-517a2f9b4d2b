// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_acme_task.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ACMETaskService_CountAllEnabledACMETasksWithACMEUserId_FullMethodName = "/pb.ACMETaskService/countAllEnabledACMETasksWithACMEUserId"
	ACMETaskService_CountEnabledACMETasksWithDNSProviderId_FullMethodName = "/pb.ACMETaskService/countEnabledACMETasksWithDNSProviderId"
	ACMETaskService_CountAllEnabledACMETasks_FullMethodName               = "/pb.ACMETaskService/countAllEnabledACMETasks"
	ACMETaskService_ListEnabledACMETasks_FullMethodName                   = "/pb.ACMETaskService/listEnabledACMETasks"
	ACMETaskService_CreateACMETask_FullMethodName                         = "/pb.ACMETaskService/createACMETask"
	ACMETaskService_UpdateACMETask_FullMethodName                         = "/pb.ACMETaskService/updateACMETask"
	ACMETaskService_DeleteACMETask_FullMethodName                         = "/pb.ACMETaskService/deleteACMETask"
	ACMETaskService_RunACMETask_FullMethodName                            = "/pb.ACMETaskService/runACMETask"
	ACMETaskService_FindEnabledACMETask_FullMethodName                    = "/pb.ACMETaskService/findEnabledACMETask"
	ACMETaskService_FindACMETaskUser_FullMethodName                       = "/pb.ACMETaskService/findACMETaskUser"
)

// ACMETaskServiceClient is the client API for ACMETaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ACMETaskServiceClient interface {
	// 计算某个ACME用户相关的任务数量
	CountAllEnabledACMETasksWithACMEUserId(ctx context.Context, in *CountAllEnabledACMETasksWithACMEUserIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算跟某个DNS服务商相关的任务数量
	CountEnabledACMETasksWithDNSProviderId(ctx context.Context, in *CountEnabledACMETasksWithDNSProviderIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算所有任务数量
	CountAllEnabledACMETasks(ctx context.Context, in *CountAllEnabledACMETasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页任务
	ListEnabledACMETasks(ctx context.Context, in *ListEnabledACMETasksRequest, opts ...grpc.CallOption) (*ListEnabledACMETasksResponse, error)
	// 创建任务
	CreateACMETask(ctx context.Context, in *CreateACMETaskRequest, opts ...grpc.CallOption) (*CreateACMETaskResponse, error)
	// 修改任务
	UpdateACMETask(ctx context.Context, in *UpdateACMETaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除任务
	DeleteACMETask(ctx context.Context, in *DeleteACMETaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 运行某个任务
	RunACMETask(ctx context.Context, in *RunACMETaskRequest, opts ...grpc.CallOption) (*RunACMETaskResponse, error)
	// 查找单个任务信息
	FindEnabledACMETask(ctx context.Context, in *FindEnabledACMETaskRequest, opts ...grpc.CallOption) (*FindEnabledACMETaskResponse, error)
	// 查找任务所属用户
	FindACMETaskUser(ctx context.Context, in *FindACMETaskUserRequest, opts ...grpc.CallOption) (*FindACMETaskUserResponse, error)
}

type aCMETaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewACMETaskServiceClient(cc grpc.ClientConnInterface) ACMETaskServiceClient {
	return &aCMETaskServiceClient{cc}
}

func (c *aCMETaskServiceClient) CountAllEnabledACMETasksWithACMEUserId(ctx context.Context, in *CountAllEnabledACMETasksWithACMEUserIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_CountAllEnabledACMETasksWithACMEUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) CountEnabledACMETasksWithDNSProviderId(ctx context.Context, in *CountEnabledACMETasksWithDNSProviderIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_CountEnabledACMETasksWithDNSProviderId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) CountAllEnabledACMETasks(ctx context.Context, in *CountAllEnabledACMETasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_CountAllEnabledACMETasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) ListEnabledACMETasks(ctx context.Context, in *ListEnabledACMETasksRequest, opts ...grpc.CallOption) (*ListEnabledACMETasksResponse, error) {
	out := new(ListEnabledACMETasksResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_ListEnabledACMETasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) CreateACMETask(ctx context.Context, in *CreateACMETaskRequest, opts ...grpc.CallOption) (*CreateACMETaskResponse, error) {
	out := new(CreateACMETaskResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_CreateACMETask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) UpdateACMETask(ctx context.Context, in *UpdateACMETaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ACMETaskService_UpdateACMETask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) DeleteACMETask(ctx context.Context, in *DeleteACMETaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ACMETaskService_DeleteACMETask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) RunACMETask(ctx context.Context, in *RunACMETaskRequest, opts ...grpc.CallOption) (*RunACMETaskResponse, error) {
	out := new(RunACMETaskResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_RunACMETask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) FindEnabledACMETask(ctx context.Context, in *FindEnabledACMETaskRequest, opts ...grpc.CallOption) (*FindEnabledACMETaskResponse, error) {
	out := new(FindEnabledACMETaskResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_FindEnabledACMETask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMETaskServiceClient) FindACMETaskUser(ctx context.Context, in *FindACMETaskUserRequest, opts ...grpc.CallOption) (*FindACMETaskUserResponse, error) {
	out := new(FindACMETaskUserResponse)
	err := c.cc.Invoke(ctx, ACMETaskService_FindACMETaskUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ACMETaskServiceServer is the server API for ACMETaskService service.
// All implementations should embed UnimplementedACMETaskServiceServer
// for forward compatibility
type ACMETaskServiceServer interface {
	// 计算某个ACME用户相关的任务数量
	CountAllEnabledACMETasksWithACMEUserId(context.Context, *CountAllEnabledACMETasksWithACMEUserIdRequest) (*RPCCountResponse, error)
	// 计算跟某个DNS服务商相关的任务数量
	CountEnabledACMETasksWithDNSProviderId(context.Context, *CountEnabledACMETasksWithDNSProviderIdRequest) (*RPCCountResponse, error)
	// 计算所有任务数量
	CountAllEnabledACMETasks(context.Context, *CountAllEnabledACMETasksRequest) (*RPCCountResponse, error)
	// 列出单页任务
	ListEnabledACMETasks(context.Context, *ListEnabledACMETasksRequest) (*ListEnabledACMETasksResponse, error)
	// 创建任务
	CreateACMETask(context.Context, *CreateACMETaskRequest) (*CreateACMETaskResponse, error)
	// 修改任务
	UpdateACMETask(context.Context, *UpdateACMETaskRequest) (*RPCSuccess, error)
	// 删除任务
	DeleteACMETask(context.Context, *DeleteACMETaskRequest) (*RPCSuccess, error)
	// 运行某个任务
	RunACMETask(context.Context, *RunACMETaskRequest) (*RunACMETaskResponse, error)
	// 查找单个任务信息
	FindEnabledACMETask(context.Context, *FindEnabledACMETaskRequest) (*FindEnabledACMETaskResponse, error)
	// 查找任务所属用户
	FindACMETaskUser(context.Context, *FindACMETaskUserRequest) (*FindACMETaskUserResponse, error)
}

// UnimplementedACMETaskServiceServer should be embedded to have forward compatible implementations.
type UnimplementedACMETaskServiceServer struct {
}

func (UnimplementedACMETaskServiceServer) CountAllEnabledACMETasksWithACMEUserId(context.Context, *CountAllEnabledACMETasksWithACMEUserIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledACMETasksWithACMEUserId not implemented")
}
func (UnimplementedACMETaskServiceServer) CountEnabledACMETasksWithDNSProviderId(context.Context, *CountEnabledACMETasksWithDNSProviderIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountEnabledACMETasksWithDNSProviderId not implemented")
}
func (UnimplementedACMETaskServiceServer) CountAllEnabledACMETasks(context.Context, *CountAllEnabledACMETasksRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledACMETasks not implemented")
}
func (UnimplementedACMETaskServiceServer) ListEnabledACMETasks(context.Context, *ListEnabledACMETasksRequest) (*ListEnabledACMETasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledACMETasks not implemented")
}
func (UnimplementedACMETaskServiceServer) CreateACMETask(context.Context, *CreateACMETaskRequest) (*CreateACMETaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateACMETask not implemented")
}
func (UnimplementedACMETaskServiceServer) UpdateACMETask(context.Context, *UpdateACMETaskRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateACMETask not implemented")
}
func (UnimplementedACMETaskServiceServer) DeleteACMETask(context.Context, *DeleteACMETaskRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteACMETask not implemented")
}
func (UnimplementedACMETaskServiceServer) RunACMETask(context.Context, *RunACMETaskRequest) (*RunACMETaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunACMETask not implemented")
}
func (UnimplementedACMETaskServiceServer) FindEnabledACMETask(context.Context, *FindEnabledACMETaskRequest) (*FindEnabledACMETaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledACMETask not implemented")
}
func (UnimplementedACMETaskServiceServer) FindACMETaskUser(context.Context, *FindACMETaskUserRequest) (*FindACMETaskUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindACMETaskUser not implemented")
}

// UnsafeACMETaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ACMETaskServiceServer will
// result in compilation errors.
type UnsafeACMETaskServiceServer interface {
	mustEmbedUnimplementedACMETaskServiceServer()
}

func RegisterACMETaskServiceServer(s grpc.ServiceRegistrar, srv ACMETaskServiceServer) {
	s.RegisterService(&ACMETaskService_ServiceDesc, srv)
}

func _ACMETaskService_CountAllEnabledACMETasksWithACMEUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledACMETasksWithACMEUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).CountAllEnabledACMETasksWithACMEUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_CountAllEnabledACMETasksWithACMEUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).CountAllEnabledACMETasksWithACMEUserId(ctx, req.(*CountAllEnabledACMETasksWithACMEUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_CountEnabledACMETasksWithDNSProviderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountEnabledACMETasksWithDNSProviderIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).CountEnabledACMETasksWithDNSProviderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_CountEnabledACMETasksWithDNSProviderId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).CountEnabledACMETasksWithDNSProviderId(ctx, req.(*CountEnabledACMETasksWithDNSProviderIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_CountAllEnabledACMETasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledACMETasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).CountAllEnabledACMETasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_CountAllEnabledACMETasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).CountAllEnabledACMETasks(ctx, req.(*CountAllEnabledACMETasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_ListEnabledACMETasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledACMETasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).ListEnabledACMETasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_ListEnabledACMETasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).ListEnabledACMETasks(ctx, req.(*ListEnabledACMETasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_CreateACMETask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateACMETaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).CreateACMETask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_CreateACMETask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).CreateACMETask(ctx, req.(*CreateACMETaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_UpdateACMETask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateACMETaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).UpdateACMETask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_UpdateACMETask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).UpdateACMETask(ctx, req.(*UpdateACMETaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_DeleteACMETask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteACMETaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).DeleteACMETask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_DeleteACMETask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).DeleteACMETask(ctx, req.(*DeleteACMETaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_RunACMETask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunACMETaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).RunACMETask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_RunACMETask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).RunACMETask(ctx, req.(*RunACMETaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_FindEnabledACMETask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledACMETaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).FindEnabledACMETask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_FindEnabledACMETask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).FindEnabledACMETask(ctx, req.(*FindEnabledACMETaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMETaskService_FindACMETaskUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindACMETaskUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMETaskServiceServer).FindACMETaskUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMETaskService_FindACMETaskUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMETaskServiceServer).FindACMETaskUser(ctx, req.(*FindACMETaskUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ACMETaskService_ServiceDesc is the grpc.ServiceDesc for ACMETaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ACMETaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ACMETaskService",
	HandlerType: (*ACMETaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countAllEnabledACMETasksWithACMEUserId",
			Handler:    _ACMETaskService_CountAllEnabledACMETasksWithACMEUserId_Handler,
		},
		{
			MethodName: "countEnabledACMETasksWithDNSProviderId",
			Handler:    _ACMETaskService_CountEnabledACMETasksWithDNSProviderId_Handler,
		},
		{
			MethodName: "countAllEnabledACMETasks",
			Handler:    _ACMETaskService_CountAllEnabledACMETasks_Handler,
		},
		{
			MethodName: "listEnabledACMETasks",
			Handler:    _ACMETaskService_ListEnabledACMETasks_Handler,
		},
		{
			MethodName: "createACMETask",
			Handler:    _ACMETaskService_CreateACMETask_Handler,
		},
		{
			MethodName: "updateACMETask",
			Handler:    _ACMETaskService_UpdateACMETask_Handler,
		},
		{
			MethodName: "deleteACMETask",
			Handler:    _ACMETaskService_DeleteACMETask_Handler,
		},
		{
			MethodName: "runACMETask",
			Handler:    _ACMETaskService_RunACMETask_Handler,
		},
		{
			MethodName: "findEnabledACMETask",
			Handler:    _ACMETaskService_FindEnabledACMETask_Handler,
		},
		{
			MethodName: "findACMETaskUser",
			Handler:    _ACMETaskService_FindACMETaskUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_acme_task.proto",
}
