// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserService_CreateUser_FullMethodName                        = "/pb.UserService/createUser"
	UserService_RegisterUser_FullMethodName                      = "/pb.UserService/registerUser"
	UserService_VerifyUser_FullMethodName                        = "/pb.UserService/verifyUser"
	UserService_UpdateUser_FullMethodName                        = "/pb.UserService/updateUser"
	UserService_DeleteUser_FullMethodName                        = "/pb.UserService/deleteUser"
	UserService_CountAllEnabledUsers_FullMethodName              = "/pb.UserService/countAllEnabledUsers"
	UserService_ListEnabledUsers_FullMethodName                  = "/pb.UserService/listEnabledUsers"
	UserService_FindEnabledUser_FullMethodName                   = "/pb.UserService/findEnabledUser"
	UserService_CheckUserUsername_FullMethodName                 = "/pb.UserService/checkUserUsername"
	UserService_LoginUser_FullMethodName                         = "/pb.UserService/loginUser"
	UserService_UpdateUserInfo_FullMethodName                    = "/pb.UserService/updateUserInfo"
	UserService_UpdateUserLogin_FullMethodName                   = "/pb.UserService/updateUserLogin"
	UserService_ComposeUserDashboard_FullMethodName              = "/pb.UserService/composeUserDashboard"
	UserService_FindUserNodeClusterId_FullMethodName             = "/pb.UserService/findUserNodeClusterId"
	UserService_UpdateUserFeatures_FullMethodName                = "/pb.UserService/updateUserFeatures"
	UserService_UpdateAllUsersFeatures_FullMethodName            = "/pb.UserService/updateAllUsersFeatures"
	UserService_FindUserFeatures_FullMethodName                  = "/pb.UserService/findUserFeatures"
	UserService_FindAllUserFeatureDefinitions_FullMethodName     = "/pb.UserService/findAllUserFeatureDefinitions"
	UserService_ComposeUserGlobalBoard_FullMethodName            = "/pb.UserService/composeUserGlobalBoard"
	UserService_CheckUserOTPWithUsername_FullMethodName          = "/pb.UserService/checkUserOTPWithUsername"
	UserService_FindUserPriceInfo_FullMethodName                 = "/pb.UserService/findUserPriceInfo"
	UserService_UpdateUserPriceType_FullMethodName               = "/pb.UserService/updateUserPriceType"
	UserService_UpdateUserPricePeriod_FullMethodName             = "/pb.UserService/updateUserPricePeriod"
	UserService_CheckUserServersState_FullMethodName             = "/pb.UserService/checkUserServersState"
	UserService_RenewUserServersState_FullMethodName             = "/pb.UserService/renewUserServersState"
	UserService_CheckUserEmail_FullMethodName                    = "/pb.UserService/checkUserEmail"
	UserService_CheckUserMobile_FullMethodName                   = "/pb.UserService/checkUserMobile"
	UserService_FindUserVerifiedEmailWithUsername_FullMethodName = "/pb.UserService/findUserVerifiedEmailWithUsername"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserServiceClient interface {
	// 创建用户
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error)
	// 注册用户
	RegisterUser(ctx context.Context, in *RegisterUserRequest, opts ...grpc.CallOption) (*RegisterUserResponse, error)
	// 审核用户
	VerifyUser(ctx context.Context, in *VerifyUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改用户
	UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除用户
	DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算用户数量
	CountAllEnabledUsers(ctx context.Context, in *CountAllEnabledUsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页用户
	ListEnabledUsers(ctx context.Context, in *ListEnabledUsersRequest, opts ...grpc.CallOption) (*ListEnabledUsersResponse, error)
	// 查询单个用户信息
	FindEnabledUser(ctx context.Context, in *FindEnabledUserRequest, opts ...grpc.CallOption) (*FindEnabledUserResponse, error)
	// 检查用户名是否存在
	CheckUserUsername(ctx context.Context, in *CheckUserUsernameRequest, opts ...grpc.CallOption) (*CheckUserUsernameResponse, error)
	// 登录
	LoginUser(ctx context.Context, in *LoginUserRequest, opts ...grpc.CallOption) (*LoginUserResponse, error)
	// 修改用户基本信息
	UpdateUserInfo(ctx context.Context, in *UpdateUserInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改用户登录信息
	UpdateUserLogin(ctx context.Context, in *UpdateUserLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 取得用户Dashboard数据
	ComposeUserDashboard(ctx context.Context, in *ComposeUserDashboardRequest, opts ...grpc.CallOption) (*ComposeUserDashboardResponse, error)
	// 获取用户所在的集群ID
	FindUserNodeClusterId(ctx context.Context, in *FindUserNodeClusterIdRequest, opts ...grpc.CallOption) (*FindUserNodeClusterIdResponse, error)
	// 设置单个用户能使用的功能
	UpdateUserFeatures(ctx context.Context, in *UpdateUserFeaturesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 设置所有用户能使用的功能
	UpdateAllUsersFeatures(ctx context.Context, in *UpdateAllUsersFeaturesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取用户所有的功能列表
	FindUserFeatures(ctx context.Context, in *FindUserFeaturesRequest, opts ...grpc.CallOption) (*FindUserFeaturesResponse, error)
	// 获取所有的功能定义
	FindAllUserFeatureDefinitions(ctx context.Context, in *FindAllUserFeatureDefinitionsRequest, opts ...grpc.CallOption) (*FindAllUserFeatureDefinitionsResponse, error)
	// 组合全局的看板数据
	ComposeUserGlobalBoard(ctx context.Context, in *ComposeUserGlobalBoardRequest, opts ...grpc.CallOption) (*ComposeUserGlobalBoardResponse, error)
	// 根据用户名检查是否需要输入OTP
	CheckUserOTPWithUsername(ctx context.Context, in *CheckUserOTPWithUsernameRequest, opts ...grpc.CallOption) (*CheckUserOTPWithUsernameResponse, error)
	// 读取用户计费信息
	FindUserPriceInfo(ctx context.Context, in *FindUserPriceInfoRequest, opts ...grpc.CallOption) (*FindUserPriceInfoResponse, error)
	// 修改用户计费方式
	UpdateUserPriceType(ctx context.Context, in *UpdateUserPriceTypeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改用户计费周期
	UpdateUserPricePeriod(ctx context.Context, in *UpdateUserPricePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 检查用户服务可用状态
	CheckUserServersState(ctx context.Context, in *CheckUserServersStateRequest, opts ...grpc.CallOption) (*CheckUserServersStateResponse, error)
	// 更新用户服务可用状态
	RenewUserServersState(ctx context.Context, in *RenewUserServersStateRequest, opts ...grpc.CallOption) (*RenewUserServersStateResponse, error)
	// 检查邮箱是否已被使用
	CheckUserEmail(ctx context.Context, in *CheckUserEmailRequest, opts ...grpc.CallOption) (*CheckUserEmailResponse, error)
	// 检查手机号码是否已被使用
	CheckUserMobile(ctx context.Context, in *CheckUserMobileRequest, opts ...grpc.CallOption) (*CheckUserMobileResponse, error)
	// 根据用户名查询用户绑定的邮箱
	FindUserVerifiedEmailWithUsername(ctx context.Context, in *FindUserVerifiedEmailWithUsernameRequest, opts ...grpc.CallOption) (*FindUserVerifiedEmailWithUsernameResponse, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error) {
	out := new(CreateUserResponse)
	err := c.cc.Invoke(ctx, UserService_CreateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RegisterUser(ctx context.Context, in *RegisterUserRequest, opts ...grpc.CallOption) (*RegisterUserResponse, error) {
	out := new(RegisterUserResponse)
	err := c.cc.Invoke(ctx, UserService_RegisterUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) VerifyUser(ctx context.Context, in *VerifyUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_VerifyUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_DeleteUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CountAllEnabledUsers(ctx context.Context, in *CountAllEnabledUsersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserService_CountAllEnabledUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ListEnabledUsers(ctx context.Context, in *ListEnabledUsersRequest, opts ...grpc.CallOption) (*ListEnabledUsersResponse, error) {
	out := new(ListEnabledUsersResponse)
	err := c.cc.Invoke(ctx, UserService_ListEnabledUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindEnabledUser(ctx context.Context, in *FindEnabledUserRequest, opts ...grpc.CallOption) (*FindEnabledUserResponse, error) {
	out := new(FindEnabledUserResponse)
	err := c.cc.Invoke(ctx, UserService_FindEnabledUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckUserUsername(ctx context.Context, in *CheckUserUsernameRequest, opts ...grpc.CallOption) (*CheckUserUsernameResponse, error) {
	out := new(CheckUserUsernameResponse)
	err := c.cc.Invoke(ctx, UserService_CheckUserUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) LoginUser(ctx context.Context, in *LoginUserRequest, opts ...grpc.CallOption) (*LoginUserResponse, error) {
	out := new(LoginUserResponse)
	err := c.cc.Invoke(ctx, UserService_LoginUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserInfo(ctx context.Context, in *UpdateUserInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserLogin(ctx context.Context, in *UpdateUserLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateUserLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ComposeUserDashboard(ctx context.Context, in *ComposeUserDashboardRequest, opts ...grpc.CallOption) (*ComposeUserDashboardResponse, error) {
	out := new(ComposeUserDashboardResponse)
	err := c.cc.Invoke(ctx, UserService_ComposeUserDashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindUserNodeClusterId(ctx context.Context, in *FindUserNodeClusterIdRequest, opts ...grpc.CallOption) (*FindUserNodeClusterIdResponse, error) {
	out := new(FindUserNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, UserService_FindUserNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserFeatures(ctx context.Context, in *UpdateUserFeaturesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateUserFeatures_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateAllUsersFeatures(ctx context.Context, in *UpdateAllUsersFeaturesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateAllUsersFeatures_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindUserFeatures(ctx context.Context, in *FindUserFeaturesRequest, opts ...grpc.CallOption) (*FindUserFeaturesResponse, error) {
	out := new(FindUserFeaturesResponse)
	err := c.cc.Invoke(ctx, UserService_FindUserFeatures_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindAllUserFeatureDefinitions(ctx context.Context, in *FindAllUserFeatureDefinitionsRequest, opts ...grpc.CallOption) (*FindAllUserFeatureDefinitionsResponse, error) {
	out := new(FindAllUserFeatureDefinitionsResponse)
	err := c.cc.Invoke(ctx, UserService_FindAllUserFeatureDefinitions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ComposeUserGlobalBoard(ctx context.Context, in *ComposeUserGlobalBoardRequest, opts ...grpc.CallOption) (*ComposeUserGlobalBoardResponse, error) {
	out := new(ComposeUserGlobalBoardResponse)
	err := c.cc.Invoke(ctx, UserService_ComposeUserGlobalBoard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckUserOTPWithUsername(ctx context.Context, in *CheckUserOTPWithUsernameRequest, opts ...grpc.CallOption) (*CheckUserOTPWithUsernameResponse, error) {
	out := new(CheckUserOTPWithUsernameResponse)
	err := c.cc.Invoke(ctx, UserService_CheckUserOTPWithUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindUserPriceInfo(ctx context.Context, in *FindUserPriceInfoRequest, opts ...grpc.CallOption) (*FindUserPriceInfoResponse, error) {
	out := new(FindUserPriceInfoResponse)
	err := c.cc.Invoke(ctx, UserService_FindUserPriceInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserPriceType(ctx context.Context, in *UpdateUserPriceTypeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateUserPriceType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserPricePeriod(ctx context.Context, in *UpdateUserPricePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserService_UpdateUserPricePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckUserServersState(ctx context.Context, in *CheckUserServersStateRequest, opts ...grpc.CallOption) (*CheckUserServersStateResponse, error) {
	out := new(CheckUserServersStateResponse)
	err := c.cc.Invoke(ctx, UserService_CheckUserServersState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RenewUserServersState(ctx context.Context, in *RenewUserServersStateRequest, opts ...grpc.CallOption) (*RenewUserServersStateResponse, error) {
	out := new(RenewUserServersStateResponse)
	err := c.cc.Invoke(ctx, UserService_RenewUserServersState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckUserEmail(ctx context.Context, in *CheckUserEmailRequest, opts ...grpc.CallOption) (*CheckUserEmailResponse, error) {
	out := new(CheckUserEmailResponse)
	err := c.cc.Invoke(ctx, UserService_CheckUserEmail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CheckUserMobile(ctx context.Context, in *CheckUserMobileRequest, opts ...grpc.CallOption) (*CheckUserMobileResponse, error) {
	out := new(CheckUserMobileResponse)
	err := c.cc.Invoke(ctx, UserService_CheckUserMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) FindUserVerifiedEmailWithUsername(ctx context.Context, in *FindUserVerifiedEmailWithUsernameRequest, opts ...grpc.CallOption) (*FindUserVerifiedEmailWithUsernameResponse, error) {
	out := new(FindUserVerifiedEmailWithUsernameResponse)
	err := c.cc.Invoke(ctx, UserService_FindUserVerifiedEmailWithUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations should embed UnimplementedUserServiceServer
// for forward compatibility
type UserServiceServer interface {
	// 创建用户
	CreateUser(context.Context, *CreateUserRequest) (*CreateUserResponse, error)
	// 注册用户
	RegisterUser(context.Context, *RegisterUserRequest) (*RegisterUserResponse, error)
	// 审核用户
	VerifyUser(context.Context, *VerifyUserRequest) (*RPCSuccess, error)
	// 修改用户
	UpdateUser(context.Context, *UpdateUserRequest) (*RPCSuccess, error)
	// 删除用户
	DeleteUser(context.Context, *DeleteUserRequest) (*RPCSuccess, error)
	// 计算用户数量
	CountAllEnabledUsers(context.Context, *CountAllEnabledUsersRequest) (*RPCCountResponse, error)
	// 列出单页用户
	ListEnabledUsers(context.Context, *ListEnabledUsersRequest) (*ListEnabledUsersResponse, error)
	// 查询单个用户信息
	FindEnabledUser(context.Context, *FindEnabledUserRequest) (*FindEnabledUserResponse, error)
	// 检查用户名是否存在
	CheckUserUsername(context.Context, *CheckUserUsernameRequest) (*CheckUserUsernameResponse, error)
	// 登录
	LoginUser(context.Context, *LoginUserRequest) (*LoginUserResponse, error)
	// 修改用户基本信息
	UpdateUserInfo(context.Context, *UpdateUserInfoRequest) (*RPCSuccess, error)
	// 修改用户登录信息
	UpdateUserLogin(context.Context, *UpdateUserLoginRequest) (*RPCSuccess, error)
	// 取得用户Dashboard数据
	ComposeUserDashboard(context.Context, *ComposeUserDashboardRequest) (*ComposeUserDashboardResponse, error)
	// 获取用户所在的集群ID
	FindUserNodeClusterId(context.Context, *FindUserNodeClusterIdRequest) (*FindUserNodeClusterIdResponse, error)
	// 设置单个用户能使用的功能
	UpdateUserFeatures(context.Context, *UpdateUserFeaturesRequest) (*RPCSuccess, error)
	// 设置所有用户能使用的功能
	UpdateAllUsersFeatures(context.Context, *UpdateAllUsersFeaturesRequest) (*RPCSuccess, error)
	// 获取用户所有的功能列表
	FindUserFeatures(context.Context, *FindUserFeaturesRequest) (*FindUserFeaturesResponse, error)
	// 获取所有的功能定义
	FindAllUserFeatureDefinitions(context.Context, *FindAllUserFeatureDefinitionsRequest) (*FindAllUserFeatureDefinitionsResponse, error)
	// 组合全局的看板数据
	ComposeUserGlobalBoard(context.Context, *ComposeUserGlobalBoardRequest) (*ComposeUserGlobalBoardResponse, error)
	// 根据用户名检查是否需要输入OTP
	CheckUserOTPWithUsername(context.Context, *CheckUserOTPWithUsernameRequest) (*CheckUserOTPWithUsernameResponse, error)
	// 读取用户计费信息
	FindUserPriceInfo(context.Context, *FindUserPriceInfoRequest) (*FindUserPriceInfoResponse, error)
	// 修改用户计费方式
	UpdateUserPriceType(context.Context, *UpdateUserPriceTypeRequest) (*RPCSuccess, error)
	// 修改用户计费周期
	UpdateUserPricePeriod(context.Context, *UpdateUserPricePeriodRequest) (*RPCSuccess, error)
	// 检查用户服务可用状态
	CheckUserServersState(context.Context, *CheckUserServersStateRequest) (*CheckUserServersStateResponse, error)
	// 更新用户服务可用状态
	RenewUserServersState(context.Context, *RenewUserServersStateRequest) (*RenewUserServersStateResponse, error)
	// 检查邮箱是否已被使用
	CheckUserEmail(context.Context, *CheckUserEmailRequest) (*CheckUserEmailResponse, error)
	// 检查手机号码是否已被使用
	CheckUserMobile(context.Context, *CheckUserMobileRequest) (*CheckUserMobileResponse, error)
	// 根据用户名查询用户绑定的邮箱
	FindUserVerifiedEmailWithUsername(context.Context, *FindUserVerifiedEmailWithUsernameRequest) (*FindUserVerifiedEmailWithUsernameResponse, error)
}

// UnimplementedUserServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (UnimplementedUserServiceServer) CreateUser(context.Context, *CreateUserRequest) (*CreateUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedUserServiceServer) RegisterUser(context.Context, *RegisterUserRequest) (*RegisterUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterUser not implemented")
}
func (UnimplementedUserServiceServer) VerifyUser(context.Context, *VerifyUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyUser not implemented")
}
func (UnimplementedUserServiceServer) UpdateUser(context.Context, *UpdateUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUser not implemented")
}
func (UnimplementedUserServiceServer) DeleteUser(context.Context, *DeleteUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedUserServiceServer) CountAllEnabledUsers(context.Context, *CountAllEnabledUsersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledUsers not implemented")
}
func (UnimplementedUserServiceServer) ListEnabledUsers(context.Context, *ListEnabledUsersRequest) (*ListEnabledUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledUsers not implemented")
}
func (UnimplementedUserServiceServer) FindEnabledUser(context.Context, *FindEnabledUserRequest) (*FindEnabledUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUser not implemented")
}
func (UnimplementedUserServiceServer) CheckUserUsername(context.Context, *CheckUserUsernameRequest) (*CheckUserUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserUsername not implemented")
}
func (UnimplementedUserServiceServer) LoginUser(context.Context, *LoginUserRequest) (*LoginUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginUser not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserInfo(context.Context, *UpdateUserInfoRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserLogin(context.Context, *UpdateUserLoginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserLogin not implemented")
}
func (UnimplementedUserServiceServer) ComposeUserDashboard(context.Context, *ComposeUserDashboardRequest) (*ComposeUserDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeUserDashboard not implemented")
}
func (UnimplementedUserServiceServer) FindUserNodeClusterId(context.Context, *FindUserNodeClusterIdRequest) (*FindUserNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserNodeClusterId not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserFeatures(context.Context, *UpdateUserFeaturesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserFeatures not implemented")
}
func (UnimplementedUserServiceServer) UpdateAllUsersFeatures(context.Context, *UpdateAllUsersFeaturesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAllUsersFeatures not implemented")
}
func (UnimplementedUserServiceServer) FindUserFeatures(context.Context, *FindUserFeaturesRequest) (*FindUserFeaturesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserFeatures not implemented")
}
func (UnimplementedUserServiceServer) FindAllUserFeatureDefinitions(context.Context, *FindAllUserFeatureDefinitionsRequest) (*FindAllUserFeatureDefinitionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllUserFeatureDefinitions not implemented")
}
func (UnimplementedUserServiceServer) ComposeUserGlobalBoard(context.Context, *ComposeUserGlobalBoardRequest) (*ComposeUserGlobalBoardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeUserGlobalBoard not implemented")
}
func (UnimplementedUserServiceServer) CheckUserOTPWithUsername(context.Context, *CheckUserOTPWithUsernameRequest) (*CheckUserOTPWithUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserOTPWithUsername not implemented")
}
func (UnimplementedUserServiceServer) FindUserPriceInfo(context.Context, *FindUserPriceInfoRequest) (*FindUserPriceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserPriceInfo not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserPriceType(context.Context, *UpdateUserPriceTypeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPriceType not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserPricePeriod(context.Context, *UpdateUserPricePeriodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPricePeriod not implemented")
}
func (UnimplementedUserServiceServer) CheckUserServersState(context.Context, *CheckUserServersStateRequest) (*CheckUserServersStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserServersState not implemented")
}
func (UnimplementedUserServiceServer) RenewUserServersState(context.Context, *RenewUserServersStateRequest) (*RenewUserServersStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewUserServersState not implemented")
}
func (UnimplementedUserServiceServer) CheckUserEmail(context.Context, *CheckUserEmailRequest) (*CheckUserEmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserEmail not implemented")
}
func (UnimplementedUserServiceServer) CheckUserMobile(context.Context, *CheckUserMobileRequest) (*CheckUserMobileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserMobile not implemented")
}
func (UnimplementedUserServiceServer) FindUserVerifiedEmailWithUsername(context.Context, *FindUserVerifiedEmailWithUsernameRequest) (*FindUserVerifiedEmailWithUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserVerifiedEmailWithUsername not implemented")
}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RegisterUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RegisterUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RegisterUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RegisterUser(ctx, req.(*RegisterUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_VerifyUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).VerifyUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_VerifyUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).VerifyUser(ctx, req.(*VerifyUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUser(ctx, req.(*UpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteUser(ctx, req.(*DeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CountAllEnabledUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CountAllEnabledUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CountAllEnabledUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CountAllEnabledUsers(ctx, req.(*CountAllEnabledUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ListEnabledUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ListEnabledUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ListEnabledUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ListEnabledUsers(ctx, req.(*ListEnabledUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindEnabledUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindEnabledUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindEnabledUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindEnabledUser(ctx, req.(*FindEnabledUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckUserUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckUserUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckUserUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckUserUsername(ctx, req.(*CheckUserUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_LoginUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).LoginUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_LoginUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).LoginUser(ctx, req.(*LoginUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, req.(*UpdateUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserLogin(ctx, req.(*UpdateUserLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ComposeUserDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeUserDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ComposeUserDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ComposeUserDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ComposeUserDashboard(ctx, req.(*ComposeUserDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindUserNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindUserNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindUserNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindUserNodeClusterId(ctx, req.(*FindUserNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserFeatures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserFeaturesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserFeatures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserFeatures_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserFeatures(ctx, req.(*UpdateUserFeaturesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateAllUsersFeatures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAllUsersFeaturesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateAllUsersFeatures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateAllUsersFeatures_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateAllUsersFeatures(ctx, req.(*UpdateAllUsersFeaturesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindUserFeatures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserFeaturesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindUserFeatures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindUserFeatures_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindUserFeatures(ctx, req.(*FindUserFeaturesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindAllUserFeatureDefinitions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllUserFeatureDefinitionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindAllUserFeatureDefinitions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindAllUserFeatureDefinitions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindAllUserFeatureDefinitions(ctx, req.(*FindAllUserFeatureDefinitionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ComposeUserGlobalBoard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeUserGlobalBoardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ComposeUserGlobalBoard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ComposeUserGlobalBoard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ComposeUserGlobalBoard(ctx, req.(*ComposeUserGlobalBoardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckUserOTPWithUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserOTPWithUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckUserOTPWithUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckUserOTPWithUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckUserOTPWithUsername(ctx, req.(*CheckUserOTPWithUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindUserPriceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserPriceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindUserPriceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindUserPriceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindUserPriceInfo(ctx, req.(*FindUserPriceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserPriceType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPriceTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserPriceType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserPriceType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserPriceType(ctx, req.(*UpdateUserPriceTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserPricePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPricePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserPricePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserPricePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserPricePeriod(ctx, req.(*UpdateUserPricePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckUserServersState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserServersStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckUserServersState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckUserServersState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckUserServersState(ctx, req.(*CheckUserServersStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RenewUserServersState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewUserServersStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RenewUserServersState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RenewUserServersState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RenewUserServersState(ctx, req.(*RenewUserServersStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckUserEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckUserEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckUserEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckUserEmail(ctx, req.(*CheckUserEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CheckUserMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserMobileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CheckUserMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CheckUserMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CheckUserMobile(ctx, req.(*CheckUserMobileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_FindUserVerifiedEmailWithUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserVerifiedEmailWithUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).FindUserVerifiedEmailWithUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_FindUserVerifiedEmailWithUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).FindUserVerifiedEmailWithUsername(ctx, req.(*FindUserVerifiedEmailWithUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createUser",
			Handler:    _UserService_CreateUser_Handler,
		},
		{
			MethodName: "registerUser",
			Handler:    _UserService_RegisterUser_Handler,
		},
		{
			MethodName: "verifyUser",
			Handler:    _UserService_VerifyUser_Handler,
		},
		{
			MethodName: "updateUser",
			Handler:    _UserService_UpdateUser_Handler,
		},
		{
			MethodName: "deleteUser",
			Handler:    _UserService_DeleteUser_Handler,
		},
		{
			MethodName: "countAllEnabledUsers",
			Handler:    _UserService_CountAllEnabledUsers_Handler,
		},
		{
			MethodName: "listEnabledUsers",
			Handler:    _UserService_ListEnabledUsers_Handler,
		},
		{
			MethodName: "findEnabledUser",
			Handler:    _UserService_FindEnabledUser_Handler,
		},
		{
			MethodName: "checkUserUsername",
			Handler:    _UserService_CheckUserUsername_Handler,
		},
		{
			MethodName: "loginUser",
			Handler:    _UserService_LoginUser_Handler,
		},
		{
			MethodName: "updateUserInfo",
			Handler:    _UserService_UpdateUserInfo_Handler,
		},
		{
			MethodName: "updateUserLogin",
			Handler:    _UserService_UpdateUserLogin_Handler,
		},
		{
			MethodName: "composeUserDashboard",
			Handler:    _UserService_ComposeUserDashboard_Handler,
		},
		{
			MethodName: "findUserNodeClusterId",
			Handler:    _UserService_FindUserNodeClusterId_Handler,
		},
		{
			MethodName: "updateUserFeatures",
			Handler:    _UserService_UpdateUserFeatures_Handler,
		},
		{
			MethodName: "updateAllUsersFeatures",
			Handler:    _UserService_UpdateAllUsersFeatures_Handler,
		},
		{
			MethodName: "findUserFeatures",
			Handler:    _UserService_FindUserFeatures_Handler,
		},
		{
			MethodName: "findAllUserFeatureDefinitions",
			Handler:    _UserService_FindAllUserFeatureDefinitions_Handler,
		},
		{
			MethodName: "composeUserGlobalBoard",
			Handler:    _UserService_ComposeUserGlobalBoard_Handler,
		},
		{
			MethodName: "checkUserOTPWithUsername",
			Handler:    _UserService_CheckUserOTPWithUsername_Handler,
		},
		{
			MethodName: "findUserPriceInfo",
			Handler:    _UserService_FindUserPriceInfo_Handler,
		},
		{
			MethodName: "updateUserPriceType",
			Handler:    _UserService_UpdateUserPriceType_Handler,
		},
		{
			MethodName: "updateUserPricePeriod",
			Handler:    _UserService_UpdateUserPricePeriod_Handler,
		},
		{
			MethodName: "checkUserServersState",
			Handler:    _UserService_CheckUserServersState_Handler,
		},
		{
			MethodName: "renewUserServersState",
			Handler:    _UserService_RenewUserServersState_Handler,
		},
		{
			MethodName: "checkUserEmail",
			Handler:    _UserService_CheckUserEmail_Handler,
		},
		{
			MethodName: "checkUserMobile",
			Handler:    _UserService_CheckUserMobile_Handler,
		},
		{
			MethodName: "findUserVerifiedEmailWithUsername",
			Handler:    _UserService_FindUserVerifiedEmailWithUsername_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user.proto",
}
