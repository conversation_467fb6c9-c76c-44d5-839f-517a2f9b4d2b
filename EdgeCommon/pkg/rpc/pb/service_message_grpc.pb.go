// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_message.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageService_CountUnreadMessages_FullMethodName   = "/pb.MessageService/countUnreadMessages"
	MessageService_ListUnreadMessages_FullMethodName    = "/pb.MessageService/listUnreadMessages"
	MessageService_UpdateMessageRead_FullMethodName     = "/pb.MessageService/updateMessageRead"
	MessageService_UpdateMessagesRead_FullMethodName    = "/pb.MessageService/updateMessagesRead"
	MessageService_UpdateAllMessagesRead_FullMethodName = "/pb.MessageService/updateAllMessagesRead"
)

// MessageServiceClient is the client API for MessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageServiceClient interface {
	// 计算未读消息数
	CountUnreadMessages(ctx context.Context, in *CountUnreadMessagesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页未读消息
	ListUnreadMessages(ctx context.Context, in *ListUnreadMessagesRequest, opts ...grpc.CallOption) (*ListUnreadMessagesResponse, error)
	// 设置消息已读状态
	UpdateMessageRead(ctx context.Context, in *UpdateMessageReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 设置一组消息已读状态
	UpdateMessagesRead(ctx context.Context, in *UpdateMessagesReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 设置所有消息为已读
	UpdateAllMessagesRead(ctx context.Context, in *UpdateAllMessagesReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type messageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageServiceClient(cc grpc.ClientConnInterface) MessageServiceClient {
	return &messageServiceClient{cc}
}

func (c *messageServiceClient) CountUnreadMessages(ctx context.Context, in *CountUnreadMessagesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, MessageService_CountUnreadMessages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) ListUnreadMessages(ctx context.Context, in *ListUnreadMessagesRequest, opts ...grpc.CallOption) (*ListUnreadMessagesResponse, error) {
	out := new(ListUnreadMessagesResponse)
	err := c.cc.Invoke(ctx, MessageService_ListUnreadMessages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) UpdateMessageRead(ctx context.Context, in *UpdateMessageReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageService_UpdateMessageRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) UpdateMessagesRead(ctx context.Context, in *UpdateMessagesReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageService_UpdateMessagesRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageServiceClient) UpdateAllMessagesRead(ctx context.Context, in *UpdateAllMessagesReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageService_UpdateAllMessagesRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageServiceServer is the server API for MessageService service.
// All implementations should embed UnimplementedMessageServiceServer
// for forward compatibility
type MessageServiceServer interface {
	// 计算未读消息数
	CountUnreadMessages(context.Context, *CountUnreadMessagesRequest) (*RPCCountResponse, error)
	// 列出单页未读消息
	ListUnreadMessages(context.Context, *ListUnreadMessagesRequest) (*ListUnreadMessagesResponse, error)
	// 设置消息已读状态
	UpdateMessageRead(context.Context, *UpdateMessageReadRequest) (*RPCSuccess, error)
	// 设置一组消息已读状态
	UpdateMessagesRead(context.Context, *UpdateMessagesReadRequest) (*RPCSuccess, error)
	// 设置所有消息为已读
	UpdateAllMessagesRead(context.Context, *UpdateAllMessagesReadRequest) (*RPCSuccess, error)
}

// UnimplementedMessageServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMessageServiceServer struct {
}

func (UnimplementedMessageServiceServer) CountUnreadMessages(context.Context, *CountUnreadMessagesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUnreadMessages not implemented")
}
func (UnimplementedMessageServiceServer) ListUnreadMessages(context.Context, *ListUnreadMessagesRequest) (*ListUnreadMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUnreadMessages not implemented")
}
func (UnimplementedMessageServiceServer) UpdateMessageRead(context.Context, *UpdateMessageReadRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMessageRead not implemented")
}
func (UnimplementedMessageServiceServer) UpdateMessagesRead(context.Context, *UpdateMessagesReadRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMessagesRead not implemented")
}
func (UnimplementedMessageServiceServer) UpdateAllMessagesRead(context.Context, *UpdateAllMessagesReadRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAllMessagesRead not implemented")
}

// UnsafeMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageServiceServer will
// result in compilation errors.
type UnsafeMessageServiceServer interface {
	mustEmbedUnimplementedMessageServiceServer()
}

func RegisterMessageServiceServer(s grpc.ServiceRegistrar, srv MessageServiceServer) {
	s.RegisterService(&MessageService_ServiceDesc, srv)
}

func _MessageService_CountUnreadMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUnreadMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).CountUnreadMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_CountUnreadMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).CountUnreadMessages(ctx, req.(*CountUnreadMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_ListUnreadMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUnreadMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).ListUnreadMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_ListUnreadMessages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).ListUnreadMessages(ctx, req.(*ListUnreadMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_UpdateMessageRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMessageReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).UpdateMessageRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_UpdateMessageRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).UpdateMessageRead(ctx, req.(*UpdateMessageReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_UpdateMessagesRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMessagesReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).UpdateMessagesRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_UpdateMessagesRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).UpdateMessagesRead(ctx, req.(*UpdateMessagesReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageService_UpdateAllMessagesRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAllMessagesReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageServiceServer).UpdateAllMessagesRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageService_UpdateAllMessagesRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageServiceServer).UpdateAllMessagesRead(ctx, req.(*UpdateAllMessagesReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageService_ServiceDesc is the grpc.ServiceDesc for MessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MessageService",
	HandlerType: (*MessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countUnreadMessages",
			Handler:    _MessageService_CountUnreadMessages_Handler,
		},
		{
			MethodName: "listUnreadMessages",
			Handler:    _MessageService_ListUnreadMessages_Handler,
		},
		{
			MethodName: "updateMessageRead",
			Handler:    _MessageService_UpdateMessageRead_Handler,
		},
		{
			MethodName: "updateMessagesRead",
			Handler:    _MessageService_UpdateMessagesRead_Handler,
		},
		{
			MethodName: "updateAllMessagesRead",
			Handler:    _MessageService_UpdateAllMessagesRead_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_message.proto",
}
