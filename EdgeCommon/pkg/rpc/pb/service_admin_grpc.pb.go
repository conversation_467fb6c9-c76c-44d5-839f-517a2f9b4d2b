// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_admin.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AdminService_LoginAdmin_FullMethodName                = "/pb.AdminService/loginAdmin"
	AdminService_CheckAdminExists_FullMethodName          = "/pb.AdminService/checkAdminExists"
	AdminService_CheckAdminUsername_FullMethodName        = "/pb.AdminService/checkAdminUsername"
	AdminService_FindAdminWithUsername_FullMethodName     = "/pb.AdminService/findAdminWithUsername"
	AdminService_FindAdminFullname_FullMethodName         = "/pb.AdminService/findAdminFullname"
	AdminService_FindEnabledAdmin_FullMethodName          = "/pb.AdminService/findEnabledAdmin"
	AdminService_CreateOrUpdateAdmin_FullMethodName       = "/pb.AdminService/createOrUpdateAdmin"
	AdminService_UpdateAdminInfo_FullMethodName           = "/pb.AdminService/updateAdminInfo"
	AdminService_UpdateAdminLogin_FullMethodName          = "/pb.AdminService/updateAdminLogin"
	AdminService_FindAllAdminModules_FullMethodName       = "/pb.AdminService/findAllAdminModules"
	AdminService_CreateAdmin_FullMethodName               = "/pb.AdminService/createAdmin"
	AdminService_UpdateAdmin_FullMethodName               = "/pb.AdminService/updateAdmin"
	AdminService_CountAllEnabledAdmins_FullMethodName     = "/pb.AdminService/countAllEnabledAdmins"
	AdminService_ListEnabledAdmins_FullMethodName         = "/pb.AdminService/listEnabledAdmins"
	AdminService_DeleteAdmin_FullMethodName               = "/pb.AdminService/deleteAdmin"
	AdminService_CheckAdminOTPWithUsername_FullMethodName = "/pb.AdminService/checkAdminOTPWithUsername"
	AdminService_ComposeAdminDashboard_FullMethodName     = "/pb.AdminService/composeAdminDashboard"
	AdminService_UpdateAdminTheme_FullMethodName          = "/pb.AdminService/updateAdminTheme"
	AdminService_UpdateAdminLang_FullMethodName           = "/pb.AdminService/updateAdminLang"
)

// AdminServiceClient is the client API for AdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminServiceClient interface {
	// 登录
	LoginAdmin(ctx context.Context, in *LoginAdminRequest, opts ...grpc.CallOption) (*LoginAdminResponse, error)
	// 检查管理员是否存在
	CheckAdminExists(ctx context.Context, in *CheckAdminExistsRequest, opts ...grpc.CallOption) (*CheckAdminExistsResponse, error)
	// 检查管理员用户名是否存在
	CheckAdminUsername(ctx context.Context, in *CheckAdminUsernameRequest, opts ...grpc.CallOption) (*CheckAdminUsernameResponse, error)
	// 使用用管理员户名查找管理员信息
	FindAdminWithUsername(ctx context.Context, in *FindAdminWithUsernameRequest, opts ...grpc.CallOption) (*FindAdminWithUsernameResponse, error)
	// 获取管理员名称
	FindAdminFullname(ctx context.Context, in *FindAdminFullnameRequest, opts ...grpc.CallOption) (*FindAdminFullnameResponse, error)
	// 获取管理员信息
	FindEnabledAdmin(ctx context.Context, in *FindEnabledAdminRequest, opts ...grpc.CallOption) (*FindEnabledAdminResponse, error)
	// 创建或修改管理员
	CreateOrUpdateAdmin(ctx context.Context, in *CreateOrUpdateAdminRequest, opts ...grpc.CallOption) (*CreateOrUpdateAdminResponse, error)
	// 修改管理员信息
	UpdateAdminInfo(ctx context.Context, in *UpdateAdminInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改管理员登录信息
	UpdateAdminLogin(ctx context.Context, in *UpdateAdminLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取所有管理员的权限列表
	FindAllAdminModules(ctx context.Context, in *FindAllAdminModulesRequest, opts ...grpc.CallOption) (*FindAllAdminModulesResponse, error)
	// 创建管理员
	CreateAdmin(ctx context.Context, in *CreateAdminRequest, opts ...grpc.CallOption) (*CreateAdminResponse, error)
	// 修改管理员
	UpdateAdmin(ctx context.Context, in *UpdateAdminRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算管理员数量
	CountAllEnabledAdmins(ctx context.Context, in *CountAllEnabledAdminsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页的管理员
	ListEnabledAdmins(ctx context.Context, in *ListEnabledAdminsRequest, opts ...grpc.CallOption) (*ListEnabledAdminsResponse, error)
	// 删除管理员
	DeleteAdmin(ctx context.Context, in *DeleteAdminRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 根据用户名检查是否需要输入OTP
	CheckAdminOTPWithUsername(ctx context.Context, in *CheckAdminOTPWithUsernameRequest, opts ...grpc.CallOption) (*CheckAdminOTPWithUsernameResponse, error)
	// 取得管理员Dashboard数据
	ComposeAdminDashboard(ctx context.Context, in *ComposeAdminDashboardRequest, opts ...grpc.CallOption) (*ComposeAdminDashboardResponse, error)
	// 修改管理员使用的界面风格
	UpdateAdminTheme(ctx context.Context, in *UpdateAdminThemeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改管理员使用的语言
	UpdateAdminLang(ctx context.Context, in *UpdateAdminLangRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type adminServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminServiceClient(cc grpc.ClientConnInterface) AdminServiceClient {
	return &adminServiceClient{cc}
}

func (c *adminServiceClient) LoginAdmin(ctx context.Context, in *LoginAdminRequest, opts ...grpc.CallOption) (*LoginAdminResponse, error) {
	out := new(LoginAdminResponse)
	err := c.cc.Invoke(ctx, AdminService_LoginAdmin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) CheckAdminExists(ctx context.Context, in *CheckAdminExistsRequest, opts ...grpc.CallOption) (*CheckAdminExistsResponse, error) {
	out := new(CheckAdminExistsResponse)
	err := c.cc.Invoke(ctx, AdminService_CheckAdminExists_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) CheckAdminUsername(ctx context.Context, in *CheckAdminUsernameRequest, opts ...grpc.CallOption) (*CheckAdminUsernameResponse, error) {
	out := new(CheckAdminUsernameResponse)
	err := c.cc.Invoke(ctx, AdminService_CheckAdminUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) FindAdminWithUsername(ctx context.Context, in *FindAdminWithUsernameRequest, opts ...grpc.CallOption) (*FindAdminWithUsernameResponse, error) {
	out := new(FindAdminWithUsernameResponse)
	err := c.cc.Invoke(ctx, AdminService_FindAdminWithUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) FindAdminFullname(ctx context.Context, in *FindAdminFullnameRequest, opts ...grpc.CallOption) (*FindAdminFullnameResponse, error) {
	out := new(FindAdminFullnameResponse)
	err := c.cc.Invoke(ctx, AdminService_FindAdminFullname_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) FindEnabledAdmin(ctx context.Context, in *FindEnabledAdminRequest, opts ...grpc.CallOption) (*FindEnabledAdminResponse, error) {
	out := new(FindEnabledAdminResponse)
	err := c.cc.Invoke(ctx, AdminService_FindEnabledAdmin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) CreateOrUpdateAdmin(ctx context.Context, in *CreateOrUpdateAdminRequest, opts ...grpc.CallOption) (*CreateOrUpdateAdminResponse, error) {
	out := new(CreateOrUpdateAdminResponse)
	err := c.cc.Invoke(ctx, AdminService_CreateOrUpdateAdmin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) UpdateAdminInfo(ctx context.Context, in *UpdateAdminInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, AdminService_UpdateAdminInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) UpdateAdminLogin(ctx context.Context, in *UpdateAdminLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, AdminService_UpdateAdminLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) FindAllAdminModules(ctx context.Context, in *FindAllAdminModulesRequest, opts ...grpc.CallOption) (*FindAllAdminModulesResponse, error) {
	out := new(FindAllAdminModulesResponse)
	err := c.cc.Invoke(ctx, AdminService_FindAllAdminModules_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) CreateAdmin(ctx context.Context, in *CreateAdminRequest, opts ...grpc.CallOption) (*CreateAdminResponse, error) {
	out := new(CreateAdminResponse)
	err := c.cc.Invoke(ctx, AdminService_CreateAdmin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) UpdateAdmin(ctx context.Context, in *UpdateAdminRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, AdminService_UpdateAdmin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) CountAllEnabledAdmins(ctx context.Context, in *CountAllEnabledAdminsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, AdminService_CountAllEnabledAdmins_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) ListEnabledAdmins(ctx context.Context, in *ListEnabledAdminsRequest, opts ...grpc.CallOption) (*ListEnabledAdminsResponse, error) {
	out := new(ListEnabledAdminsResponse)
	err := c.cc.Invoke(ctx, AdminService_ListEnabledAdmins_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) DeleteAdmin(ctx context.Context, in *DeleteAdminRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, AdminService_DeleteAdmin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) CheckAdminOTPWithUsername(ctx context.Context, in *CheckAdminOTPWithUsernameRequest, opts ...grpc.CallOption) (*CheckAdminOTPWithUsernameResponse, error) {
	out := new(CheckAdminOTPWithUsernameResponse)
	err := c.cc.Invoke(ctx, AdminService_CheckAdminOTPWithUsername_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) ComposeAdminDashboard(ctx context.Context, in *ComposeAdminDashboardRequest, opts ...grpc.CallOption) (*ComposeAdminDashboardResponse, error) {
	out := new(ComposeAdminDashboardResponse)
	err := c.cc.Invoke(ctx, AdminService_ComposeAdminDashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) UpdateAdminTheme(ctx context.Context, in *UpdateAdminThemeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, AdminService_UpdateAdminTheme_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminServiceClient) UpdateAdminLang(ctx context.Context, in *UpdateAdminLangRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, AdminService_UpdateAdminLang_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminServiceServer is the server API for AdminService service.
// All implementations should embed UnimplementedAdminServiceServer
// for forward compatibility
type AdminServiceServer interface {
	// 登录
	LoginAdmin(context.Context, *LoginAdminRequest) (*LoginAdminResponse, error)
	// 检查管理员是否存在
	CheckAdminExists(context.Context, *CheckAdminExistsRequest) (*CheckAdminExistsResponse, error)
	// 检查管理员用户名是否存在
	CheckAdminUsername(context.Context, *CheckAdminUsernameRequest) (*CheckAdminUsernameResponse, error)
	// 使用用管理员户名查找管理员信息
	FindAdminWithUsername(context.Context, *FindAdminWithUsernameRequest) (*FindAdminWithUsernameResponse, error)
	// 获取管理员名称
	FindAdminFullname(context.Context, *FindAdminFullnameRequest) (*FindAdminFullnameResponse, error)
	// 获取管理员信息
	FindEnabledAdmin(context.Context, *FindEnabledAdminRequest) (*FindEnabledAdminResponse, error)
	// 创建或修改管理员
	CreateOrUpdateAdmin(context.Context, *CreateOrUpdateAdminRequest) (*CreateOrUpdateAdminResponse, error)
	// 修改管理员信息
	UpdateAdminInfo(context.Context, *UpdateAdminInfoRequest) (*RPCSuccess, error)
	// 修改管理员登录信息
	UpdateAdminLogin(context.Context, *UpdateAdminLoginRequest) (*RPCSuccess, error)
	// 获取所有管理员的权限列表
	FindAllAdminModules(context.Context, *FindAllAdminModulesRequest) (*FindAllAdminModulesResponse, error)
	// 创建管理员
	CreateAdmin(context.Context, *CreateAdminRequest) (*CreateAdminResponse, error)
	// 修改管理员
	UpdateAdmin(context.Context, *UpdateAdminRequest) (*RPCSuccess, error)
	// 计算管理员数量
	CountAllEnabledAdmins(context.Context, *CountAllEnabledAdminsRequest) (*RPCCountResponse, error)
	// 列出单页的管理员
	ListEnabledAdmins(context.Context, *ListEnabledAdminsRequest) (*ListEnabledAdminsResponse, error)
	// 删除管理员
	DeleteAdmin(context.Context, *DeleteAdminRequest) (*RPCSuccess, error)
	// 根据用户名检查是否需要输入OTP
	CheckAdminOTPWithUsername(context.Context, *CheckAdminOTPWithUsernameRequest) (*CheckAdminOTPWithUsernameResponse, error)
	// 取得管理员Dashboard数据
	ComposeAdminDashboard(context.Context, *ComposeAdminDashboardRequest) (*ComposeAdminDashboardResponse, error)
	// 修改管理员使用的界面风格
	UpdateAdminTheme(context.Context, *UpdateAdminThemeRequest) (*RPCSuccess, error)
	// 修改管理员使用的语言
	UpdateAdminLang(context.Context, *UpdateAdminLangRequest) (*RPCSuccess, error)
}

// UnimplementedAdminServiceServer should be embedded to have forward compatible implementations.
type UnimplementedAdminServiceServer struct {
}

func (UnimplementedAdminServiceServer) LoginAdmin(context.Context, *LoginAdminRequest) (*LoginAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginAdmin not implemented")
}
func (UnimplementedAdminServiceServer) CheckAdminExists(context.Context, *CheckAdminExistsRequest) (*CheckAdminExistsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAdminExists not implemented")
}
func (UnimplementedAdminServiceServer) CheckAdminUsername(context.Context, *CheckAdminUsernameRequest) (*CheckAdminUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAdminUsername not implemented")
}
func (UnimplementedAdminServiceServer) FindAdminWithUsername(context.Context, *FindAdminWithUsernameRequest) (*FindAdminWithUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAdminWithUsername not implemented")
}
func (UnimplementedAdminServiceServer) FindAdminFullname(context.Context, *FindAdminFullnameRequest) (*FindAdminFullnameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAdminFullname not implemented")
}
func (UnimplementedAdminServiceServer) FindEnabledAdmin(context.Context, *FindEnabledAdminRequest) (*FindEnabledAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledAdmin not implemented")
}
func (UnimplementedAdminServiceServer) CreateOrUpdateAdmin(context.Context, *CreateOrUpdateAdminRequest) (*CreateOrUpdateAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateAdmin not implemented")
}
func (UnimplementedAdminServiceServer) UpdateAdminInfo(context.Context, *UpdateAdminInfoRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAdminInfo not implemented")
}
func (UnimplementedAdminServiceServer) UpdateAdminLogin(context.Context, *UpdateAdminLoginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAdminLogin not implemented")
}
func (UnimplementedAdminServiceServer) FindAllAdminModules(context.Context, *FindAllAdminModulesRequest) (*FindAllAdminModulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAdminModules not implemented")
}
func (UnimplementedAdminServiceServer) CreateAdmin(context.Context, *CreateAdminRequest) (*CreateAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAdmin not implemented")
}
func (UnimplementedAdminServiceServer) UpdateAdmin(context.Context, *UpdateAdminRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAdmin not implemented")
}
func (UnimplementedAdminServiceServer) CountAllEnabledAdmins(context.Context, *CountAllEnabledAdminsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledAdmins not implemented")
}
func (UnimplementedAdminServiceServer) ListEnabledAdmins(context.Context, *ListEnabledAdminsRequest) (*ListEnabledAdminsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledAdmins not implemented")
}
func (UnimplementedAdminServiceServer) DeleteAdmin(context.Context, *DeleteAdminRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAdmin not implemented")
}
func (UnimplementedAdminServiceServer) CheckAdminOTPWithUsername(context.Context, *CheckAdminOTPWithUsernameRequest) (*CheckAdminOTPWithUsernameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAdminOTPWithUsername not implemented")
}
func (UnimplementedAdminServiceServer) ComposeAdminDashboard(context.Context, *ComposeAdminDashboardRequest) (*ComposeAdminDashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeAdminDashboard not implemented")
}
func (UnimplementedAdminServiceServer) UpdateAdminTheme(context.Context, *UpdateAdminThemeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAdminTheme not implemented")
}
func (UnimplementedAdminServiceServer) UpdateAdminLang(context.Context, *UpdateAdminLangRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAdminLang not implemented")
}

// UnsafeAdminServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminServiceServer will
// result in compilation errors.
type UnsafeAdminServiceServer interface {
	mustEmbedUnimplementedAdminServiceServer()
}

func RegisterAdminServiceServer(s grpc.ServiceRegistrar, srv AdminServiceServer) {
	s.RegisterService(&AdminService_ServiceDesc, srv)
}

func _AdminService_LoginAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).LoginAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_LoginAdmin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).LoginAdmin(ctx, req.(*LoginAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_CheckAdminExists_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAdminExistsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).CheckAdminExists(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_CheckAdminExists_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).CheckAdminExists(ctx, req.(*CheckAdminExistsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_CheckAdminUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAdminUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).CheckAdminUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_CheckAdminUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).CheckAdminUsername(ctx, req.(*CheckAdminUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_FindAdminWithUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAdminWithUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).FindAdminWithUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_FindAdminWithUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).FindAdminWithUsername(ctx, req.(*FindAdminWithUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_FindAdminFullname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAdminFullnameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).FindAdminFullname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_FindAdminFullname_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).FindAdminFullname(ctx, req.(*FindAdminFullnameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_FindEnabledAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).FindEnabledAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_FindEnabledAdmin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).FindEnabledAdmin(ctx, req.(*FindEnabledAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_CreateOrUpdateAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).CreateOrUpdateAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_CreateOrUpdateAdmin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).CreateOrUpdateAdmin(ctx, req.(*CreateOrUpdateAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_UpdateAdminInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAdminInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).UpdateAdminInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_UpdateAdminInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).UpdateAdminInfo(ctx, req.(*UpdateAdminInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_UpdateAdminLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAdminLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).UpdateAdminLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_UpdateAdminLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).UpdateAdminLogin(ctx, req.(*UpdateAdminLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_FindAllAdminModules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAdminModulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).FindAllAdminModules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_FindAllAdminModules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).FindAllAdminModules(ctx, req.(*FindAllAdminModulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_CreateAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).CreateAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_CreateAdmin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).CreateAdmin(ctx, req.(*CreateAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_UpdateAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).UpdateAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_UpdateAdmin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).UpdateAdmin(ctx, req.(*UpdateAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_CountAllEnabledAdmins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledAdminsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).CountAllEnabledAdmins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_CountAllEnabledAdmins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).CountAllEnabledAdmins(ctx, req.(*CountAllEnabledAdminsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_ListEnabledAdmins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledAdminsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).ListEnabledAdmins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_ListEnabledAdmins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).ListEnabledAdmins(ctx, req.(*ListEnabledAdminsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_DeleteAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).DeleteAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_DeleteAdmin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).DeleteAdmin(ctx, req.(*DeleteAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_CheckAdminOTPWithUsername_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAdminOTPWithUsernameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).CheckAdminOTPWithUsername(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_CheckAdminOTPWithUsername_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).CheckAdminOTPWithUsername(ctx, req.(*CheckAdminOTPWithUsernameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_ComposeAdminDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeAdminDashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).ComposeAdminDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_ComposeAdminDashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).ComposeAdminDashboard(ctx, req.(*ComposeAdminDashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_UpdateAdminTheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAdminThemeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).UpdateAdminTheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_UpdateAdminTheme_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).UpdateAdminTheme(ctx, req.(*UpdateAdminThemeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminService_UpdateAdminLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAdminLangRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServiceServer).UpdateAdminLang(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminService_UpdateAdminLang_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServiceServer).UpdateAdminLang(ctx, req.(*UpdateAdminLangRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminService_ServiceDesc is the grpc.ServiceDesc for AdminService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.AdminService",
	HandlerType: (*AdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "loginAdmin",
			Handler:    _AdminService_LoginAdmin_Handler,
		},
		{
			MethodName: "checkAdminExists",
			Handler:    _AdminService_CheckAdminExists_Handler,
		},
		{
			MethodName: "checkAdminUsername",
			Handler:    _AdminService_CheckAdminUsername_Handler,
		},
		{
			MethodName: "findAdminWithUsername",
			Handler:    _AdminService_FindAdminWithUsername_Handler,
		},
		{
			MethodName: "findAdminFullname",
			Handler:    _AdminService_FindAdminFullname_Handler,
		},
		{
			MethodName: "findEnabledAdmin",
			Handler:    _AdminService_FindEnabledAdmin_Handler,
		},
		{
			MethodName: "createOrUpdateAdmin",
			Handler:    _AdminService_CreateOrUpdateAdmin_Handler,
		},
		{
			MethodName: "updateAdminInfo",
			Handler:    _AdminService_UpdateAdminInfo_Handler,
		},
		{
			MethodName: "updateAdminLogin",
			Handler:    _AdminService_UpdateAdminLogin_Handler,
		},
		{
			MethodName: "findAllAdminModules",
			Handler:    _AdminService_FindAllAdminModules_Handler,
		},
		{
			MethodName: "createAdmin",
			Handler:    _AdminService_CreateAdmin_Handler,
		},
		{
			MethodName: "updateAdmin",
			Handler:    _AdminService_UpdateAdmin_Handler,
		},
		{
			MethodName: "countAllEnabledAdmins",
			Handler:    _AdminService_CountAllEnabledAdmins_Handler,
		},
		{
			MethodName: "listEnabledAdmins",
			Handler:    _AdminService_ListEnabledAdmins_Handler,
		},
		{
			MethodName: "deleteAdmin",
			Handler:    _AdminService_DeleteAdmin_Handler,
		},
		{
			MethodName: "checkAdminOTPWithUsername",
			Handler:    _AdminService_CheckAdminOTPWithUsername_Handler,
		},
		{
			MethodName: "composeAdminDashboard",
			Handler:    _AdminService_ComposeAdminDashboard_Handler,
		},
		{
			MethodName: "updateAdminTheme",
			Handler:    _AdminService_UpdateAdminTheme_Handler,
		},
		{
			MethodName: "updateAdminLang",
			Handler:    _AdminService_UpdateAdminLang_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_admin.proto",
}
