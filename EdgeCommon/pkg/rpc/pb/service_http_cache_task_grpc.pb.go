// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_cache_task.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPCacheTaskService_CreateHTTPCacheTask_FullMethodName      = "/pb.HTTPCacheTaskService/createHTTPCacheTask"
	HTTPCacheTaskService_CountHTTPCacheTasks_FullMethodName      = "/pb.HTTPCacheTaskService/countHTTPCacheTasks"
	HTTPCacheTaskService_CountDoingHTTPCacheTasks_FullMethodName = "/pb.HTTPCacheTaskService/countDoingHTTPCacheTasks"
	HTTPCacheTaskService_ListHTTPCacheTasks_FullMethodName       = "/pb.HTTPCacheTaskService/listHTTPCacheTasks"
	HTTPCacheTaskService_FindEnabledHTTPCacheTask_FullMethodName = "/pb.HTTPCacheTaskService/findEnabledHTTPCacheTask"
	HTTPCacheTaskService_DeleteHTTPCacheTask_FullMethodName      = "/pb.HTTPCacheTaskService/deleteHTTPCacheTask"
	HTTPCacheTaskService_ResetHTTPCacheTask_FullMethodName       = "/pb.HTTPCacheTaskService/resetHTTPCacheTask"
)

// HTTPCacheTaskServiceClient is the client API for HTTPCacheTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPCacheTaskServiceClient interface {
	// 创建任务
	CreateHTTPCacheTask(ctx context.Context, in *CreateHTTPCacheTaskRequest, opts ...grpc.CallOption) (*CreateHTTPCacheTaskResponse, error)
	// 计算任务总数量
	CountHTTPCacheTasks(ctx context.Context, in *CountHTTPCacheTasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算正在执行的任务数量
	CountDoingHTTPCacheTasks(ctx context.Context, in *CountDoingHTTPCacheTasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页任务
	ListHTTPCacheTasks(ctx context.Context, in *ListHTTPCacheTasksRequest, opts ...grpc.CallOption) (*ListHTTPCacheTasksResponse, error)
	// 查找单个任务
	FindEnabledHTTPCacheTask(ctx context.Context, in *FindEnabledHTTPCacheTaskRequest, opts ...grpc.CallOption) (*FindEnabledHTTPCacheTaskResponse, error)
	// 删除任务
	DeleteHTTPCacheTask(ctx context.Context, in *DeleteHTTPCacheTaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 重置任务状态
	ResetHTTPCacheTask(ctx context.Context, in *ResetHTTPCacheTaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type hTTPCacheTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPCacheTaskServiceClient(cc grpc.ClientConnInterface) HTTPCacheTaskServiceClient {
	return &hTTPCacheTaskServiceClient{cc}
}

func (c *hTTPCacheTaskServiceClient) CreateHTTPCacheTask(ctx context.Context, in *CreateHTTPCacheTaskRequest, opts ...grpc.CallOption) (*CreateHTTPCacheTaskResponse, error) {
	out := new(CreateHTTPCacheTaskResponse)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_CreateHTTPCacheTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPCacheTaskServiceClient) CountHTTPCacheTasks(ctx context.Context, in *CountHTTPCacheTasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_CountHTTPCacheTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPCacheTaskServiceClient) CountDoingHTTPCacheTasks(ctx context.Context, in *CountDoingHTTPCacheTasksRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_CountDoingHTTPCacheTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPCacheTaskServiceClient) ListHTTPCacheTasks(ctx context.Context, in *ListHTTPCacheTasksRequest, opts ...grpc.CallOption) (*ListHTTPCacheTasksResponse, error) {
	out := new(ListHTTPCacheTasksResponse)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_ListHTTPCacheTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPCacheTaskServiceClient) FindEnabledHTTPCacheTask(ctx context.Context, in *FindEnabledHTTPCacheTaskRequest, opts ...grpc.CallOption) (*FindEnabledHTTPCacheTaskResponse, error) {
	out := new(FindEnabledHTTPCacheTaskResponse)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_FindEnabledHTTPCacheTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPCacheTaskServiceClient) DeleteHTTPCacheTask(ctx context.Context, in *DeleteHTTPCacheTaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_DeleteHTTPCacheTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPCacheTaskServiceClient) ResetHTTPCacheTask(ctx context.Context, in *ResetHTTPCacheTaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPCacheTaskService_ResetHTTPCacheTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPCacheTaskServiceServer is the server API for HTTPCacheTaskService service.
// All implementations should embed UnimplementedHTTPCacheTaskServiceServer
// for forward compatibility
type HTTPCacheTaskServiceServer interface {
	// 创建任务
	CreateHTTPCacheTask(context.Context, *CreateHTTPCacheTaskRequest) (*CreateHTTPCacheTaskResponse, error)
	// 计算任务总数量
	CountHTTPCacheTasks(context.Context, *CountHTTPCacheTasksRequest) (*RPCCountResponse, error)
	// 计算正在执行的任务数量
	CountDoingHTTPCacheTasks(context.Context, *CountDoingHTTPCacheTasksRequest) (*RPCCountResponse, error)
	// 列出单页任务
	ListHTTPCacheTasks(context.Context, *ListHTTPCacheTasksRequest) (*ListHTTPCacheTasksResponse, error)
	// 查找单个任务
	FindEnabledHTTPCacheTask(context.Context, *FindEnabledHTTPCacheTaskRequest) (*FindEnabledHTTPCacheTaskResponse, error)
	// 删除任务
	DeleteHTTPCacheTask(context.Context, *DeleteHTTPCacheTaskRequest) (*RPCSuccess, error)
	// 重置任务状态
	ResetHTTPCacheTask(context.Context, *ResetHTTPCacheTaskRequest) (*RPCSuccess, error)
}

// UnimplementedHTTPCacheTaskServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPCacheTaskServiceServer struct {
}

func (UnimplementedHTTPCacheTaskServiceServer) CreateHTTPCacheTask(context.Context, *CreateHTTPCacheTaskRequest) (*CreateHTTPCacheTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPCacheTask not implemented")
}
func (UnimplementedHTTPCacheTaskServiceServer) CountHTTPCacheTasks(context.Context, *CountHTTPCacheTasksRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountHTTPCacheTasks not implemented")
}
func (UnimplementedHTTPCacheTaskServiceServer) CountDoingHTTPCacheTasks(context.Context, *CountDoingHTTPCacheTasksRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountDoingHTTPCacheTasks not implemented")
}
func (UnimplementedHTTPCacheTaskServiceServer) ListHTTPCacheTasks(context.Context, *ListHTTPCacheTasksRequest) (*ListHTTPCacheTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHTTPCacheTasks not implemented")
}
func (UnimplementedHTTPCacheTaskServiceServer) FindEnabledHTTPCacheTask(context.Context, *FindEnabledHTTPCacheTaskRequest) (*FindEnabledHTTPCacheTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPCacheTask not implemented")
}
func (UnimplementedHTTPCacheTaskServiceServer) DeleteHTTPCacheTask(context.Context, *DeleteHTTPCacheTaskRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHTTPCacheTask not implemented")
}
func (UnimplementedHTTPCacheTaskServiceServer) ResetHTTPCacheTask(context.Context, *ResetHTTPCacheTaskRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetHTTPCacheTask not implemented")
}

// UnsafeHTTPCacheTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPCacheTaskServiceServer will
// result in compilation errors.
type UnsafeHTTPCacheTaskServiceServer interface {
	mustEmbedUnimplementedHTTPCacheTaskServiceServer()
}

func RegisterHTTPCacheTaskServiceServer(s grpc.ServiceRegistrar, srv HTTPCacheTaskServiceServer) {
	s.RegisterService(&HTTPCacheTaskService_ServiceDesc, srv)
}

func _HTTPCacheTaskService_CreateHTTPCacheTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPCacheTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).CreateHTTPCacheTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_CreateHTTPCacheTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).CreateHTTPCacheTask(ctx, req.(*CreateHTTPCacheTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPCacheTaskService_CountHTTPCacheTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountHTTPCacheTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).CountHTTPCacheTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_CountHTTPCacheTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).CountHTTPCacheTasks(ctx, req.(*CountHTTPCacheTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPCacheTaskService_CountDoingHTTPCacheTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountDoingHTTPCacheTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).CountDoingHTTPCacheTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_CountDoingHTTPCacheTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).CountDoingHTTPCacheTasks(ctx, req.(*CountDoingHTTPCacheTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPCacheTaskService_ListHTTPCacheTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHTTPCacheTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).ListHTTPCacheTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_ListHTTPCacheTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).ListHTTPCacheTasks(ctx, req.(*ListHTTPCacheTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPCacheTaskService_FindEnabledHTTPCacheTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPCacheTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).FindEnabledHTTPCacheTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_FindEnabledHTTPCacheTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).FindEnabledHTTPCacheTask(ctx, req.(*FindEnabledHTTPCacheTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPCacheTaskService_DeleteHTTPCacheTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHTTPCacheTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).DeleteHTTPCacheTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_DeleteHTTPCacheTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).DeleteHTTPCacheTask(ctx, req.(*DeleteHTTPCacheTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPCacheTaskService_ResetHTTPCacheTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetHTTPCacheTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPCacheTaskServiceServer).ResetHTTPCacheTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPCacheTaskService_ResetHTTPCacheTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPCacheTaskServiceServer).ResetHTTPCacheTask(ctx, req.(*ResetHTTPCacheTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPCacheTaskService_ServiceDesc is the grpc.ServiceDesc for HTTPCacheTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPCacheTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPCacheTaskService",
	HandlerType: (*HTTPCacheTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHTTPCacheTask",
			Handler:    _HTTPCacheTaskService_CreateHTTPCacheTask_Handler,
		},
		{
			MethodName: "countHTTPCacheTasks",
			Handler:    _HTTPCacheTaskService_CountHTTPCacheTasks_Handler,
		},
		{
			MethodName: "countDoingHTTPCacheTasks",
			Handler:    _HTTPCacheTaskService_CountDoingHTTPCacheTasks_Handler,
		},
		{
			MethodName: "listHTTPCacheTasks",
			Handler:    _HTTPCacheTaskService_ListHTTPCacheTasks_Handler,
		},
		{
			MethodName: "findEnabledHTTPCacheTask",
			Handler:    _HTTPCacheTaskService_FindEnabledHTTPCacheTask_Handler,
		},
		{
			MethodName: "deleteHTTPCacheTask",
			Handler:    _HTTPCacheTaskService_DeleteHTTPCacheTask_Handler,
		},
		{
			MethodName: "resetHTTPCacheTask",
			Handler:    _HTTPCacheTaskService_ResetHTTPCacheTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_cache_task.proto",
}
