// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_reverse_proxy.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReverseProxyService_CreateReverseProxy_FullMethodName               = "/pb.ReverseProxyService/createReverseProxy"
	ReverseProxyService_FindEnabledReverseProxy_FullMethodName          = "/pb.ReverseProxyService/findEnabledReverseProxy"
	ReverseProxyService_FindEnabledReverseProxyConfig_FullMethodName    = "/pb.ReverseProxyService/findEnabledReverseProxyConfig"
	ReverseProxyService_UpdateReverseProxyScheduling_FullMethodName     = "/pb.ReverseProxyService/updateReverseProxyScheduling"
	ReverseProxyService_UpdateReverseProxyPrimaryOrigins_FullMethodName = "/pb.ReverseProxyService/updateReverseProxyPrimaryOrigins"
	ReverseProxyService_UpdateReverseProxyBackupOrigins_FullMethodName  = "/pb.ReverseProxyService/updateReverseProxyBackupOrigins"
	ReverseProxyService_UpdateReverseProxy_FullMethodName               = "/pb.ReverseProxyService/updateReverseProxy"
)

// ReverseProxyServiceClient is the client API for ReverseProxyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReverseProxyServiceClient interface {
	// 创建反向代理
	CreateReverseProxy(ctx context.Context, in *CreateReverseProxyRequest, opts ...grpc.CallOption) (*CreateReverseProxyResponse, error)
	// 查找反向代理
	FindEnabledReverseProxy(ctx context.Context, in *FindEnabledReverseProxyRequest, opts ...grpc.CallOption) (*FindEnabledReverseProxyResponse, error)
	// 查找反向代理配置
	FindEnabledReverseProxyConfig(ctx context.Context, in *FindEnabledReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindEnabledReverseProxyConfigResponse, error)
	// 修改反向代理的调度算法
	UpdateReverseProxyScheduling(ctx context.Context, in *UpdateReverseProxySchedulingRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改主要源站信息
	UpdateReverseProxyPrimaryOrigins(ctx context.Context, in *UpdateReverseProxyPrimaryOriginsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改备用源站信息
	UpdateReverseProxyBackupOrigins(ctx context.Context, in *UpdateReverseProxyBackupOriginsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改反向代理设置
	UpdateReverseProxy(ctx context.Context, in *UpdateReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type reverseProxyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReverseProxyServiceClient(cc grpc.ClientConnInterface) ReverseProxyServiceClient {
	return &reverseProxyServiceClient{cc}
}

func (c *reverseProxyServiceClient) CreateReverseProxy(ctx context.Context, in *CreateReverseProxyRequest, opts ...grpc.CallOption) (*CreateReverseProxyResponse, error) {
	out := new(CreateReverseProxyResponse)
	err := c.cc.Invoke(ctx, ReverseProxyService_CreateReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reverseProxyServiceClient) FindEnabledReverseProxy(ctx context.Context, in *FindEnabledReverseProxyRequest, opts ...grpc.CallOption) (*FindEnabledReverseProxyResponse, error) {
	out := new(FindEnabledReverseProxyResponse)
	err := c.cc.Invoke(ctx, ReverseProxyService_FindEnabledReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reverseProxyServiceClient) FindEnabledReverseProxyConfig(ctx context.Context, in *FindEnabledReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindEnabledReverseProxyConfigResponse, error) {
	out := new(FindEnabledReverseProxyConfigResponse)
	err := c.cc.Invoke(ctx, ReverseProxyService_FindEnabledReverseProxyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reverseProxyServiceClient) UpdateReverseProxyScheduling(ctx context.Context, in *UpdateReverseProxySchedulingRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReverseProxyService_UpdateReverseProxyScheduling_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reverseProxyServiceClient) UpdateReverseProxyPrimaryOrigins(ctx context.Context, in *UpdateReverseProxyPrimaryOriginsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReverseProxyService_UpdateReverseProxyPrimaryOrigins_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reverseProxyServiceClient) UpdateReverseProxyBackupOrigins(ctx context.Context, in *UpdateReverseProxyBackupOriginsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReverseProxyService_UpdateReverseProxyBackupOrigins_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reverseProxyServiceClient) UpdateReverseProxy(ctx context.Context, in *UpdateReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReverseProxyService_UpdateReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReverseProxyServiceServer is the server API for ReverseProxyService service.
// All implementations should embed UnimplementedReverseProxyServiceServer
// for forward compatibility
type ReverseProxyServiceServer interface {
	// 创建反向代理
	CreateReverseProxy(context.Context, *CreateReverseProxyRequest) (*CreateReverseProxyResponse, error)
	// 查找反向代理
	FindEnabledReverseProxy(context.Context, *FindEnabledReverseProxyRequest) (*FindEnabledReverseProxyResponse, error)
	// 查找反向代理配置
	FindEnabledReverseProxyConfig(context.Context, *FindEnabledReverseProxyConfigRequest) (*FindEnabledReverseProxyConfigResponse, error)
	// 修改反向代理的调度算法
	UpdateReverseProxyScheduling(context.Context, *UpdateReverseProxySchedulingRequest) (*RPCSuccess, error)
	// 修改主要源站信息
	UpdateReverseProxyPrimaryOrigins(context.Context, *UpdateReverseProxyPrimaryOriginsRequest) (*RPCSuccess, error)
	// 修改备用源站信息
	UpdateReverseProxyBackupOrigins(context.Context, *UpdateReverseProxyBackupOriginsRequest) (*RPCSuccess, error)
	// 修改反向代理设置
	UpdateReverseProxy(context.Context, *UpdateReverseProxyRequest) (*RPCSuccess, error)
}

// UnimplementedReverseProxyServiceServer should be embedded to have forward compatible implementations.
type UnimplementedReverseProxyServiceServer struct {
}

func (UnimplementedReverseProxyServiceServer) CreateReverseProxy(context.Context, *CreateReverseProxyRequest) (*CreateReverseProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReverseProxy not implemented")
}
func (UnimplementedReverseProxyServiceServer) FindEnabledReverseProxy(context.Context, *FindEnabledReverseProxyRequest) (*FindEnabledReverseProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledReverseProxy not implemented")
}
func (UnimplementedReverseProxyServiceServer) FindEnabledReverseProxyConfig(context.Context, *FindEnabledReverseProxyConfigRequest) (*FindEnabledReverseProxyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledReverseProxyConfig not implemented")
}
func (UnimplementedReverseProxyServiceServer) UpdateReverseProxyScheduling(context.Context, *UpdateReverseProxySchedulingRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReverseProxyScheduling not implemented")
}
func (UnimplementedReverseProxyServiceServer) UpdateReverseProxyPrimaryOrigins(context.Context, *UpdateReverseProxyPrimaryOriginsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReverseProxyPrimaryOrigins not implemented")
}
func (UnimplementedReverseProxyServiceServer) UpdateReverseProxyBackupOrigins(context.Context, *UpdateReverseProxyBackupOriginsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReverseProxyBackupOrigins not implemented")
}
func (UnimplementedReverseProxyServiceServer) UpdateReverseProxy(context.Context, *UpdateReverseProxyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReverseProxy not implemented")
}

// UnsafeReverseProxyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReverseProxyServiceServer will
// result in compilation errors.
type UnsafeReverseProxyServiceServer interface {
	mustEmbedUnimplementedReverseProxyServiceServer()
}

func RegisterReverseProxyServiceServer(s grpc.ServiceRegistrar, srv ReverseProxyServiceServer) {
	s.RegisterService(&ReverseProxyService_ServiceDesc, srv)
}

func _ReverseProxyService_CreateReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).CreateReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_CreateReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).CreateReverseProxy(ctx, req.(*CreateReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReverseProxyService_FindEnabledReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).FindEnabledReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_FindEnabledReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).FindEnabledReverseProxy(ctx, req.(*FindEnabledReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReverseProxyService_FindEnabledReverseProxyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledReverseProxyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).FindEnabledReverseProxyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_FindEnabledReverseProxyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).FindEnabledReverseProxyConfig(ctx, req.(*FindEnabledReverseProxyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReverseProxyService_UpdateReverseProxyScheduling_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReverseProxySchedulingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxyScheduling(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_UpdateReverseProxyScheduling_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxyScheduling(ctx, req.(*UpdateReverseProxySchedulingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReverseProxyService_UpdateReverseProxyPrimaryOrigins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReverseProxyPrimaryOriginsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxyPrimaryOrigins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_UpdateReverseProxyPrimaryOrigins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxyPrimaryOrigins(ctx, req.(*UpdateReverseProxyPrimaryOriginsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReverseProxyService_UpdateReverseProxyBackupOrigins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReverseProxyBackupOriginsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxyBackupOrigins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_UpdateReverseProxyBackupOrigins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxyBackupOrigins(ctx, req.(*UpdateReverseProxyBackupOriginsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReverseProxyService_UpdateReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReverseProxyService_UpdateReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReverseProxyServiceServer).UpdateReverseProxy(ctx, req.(*UpdateReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReverseProxyService_ServiceDesc is the grpc.ServiceDesc for ReverseProxyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReverseProxyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ReverseProxyService",
	HandlerType: (*ReverseProxyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createReverseProxy",
			Handler:    _ReverseProxyService_CreateReverseProxy_Handler,
		},
		{
			MethodName: "findEnabledReverseProxy",
			Handler:    _ReverseProxyService_FindEnabledReverseProxy_Handler,
		},
		{
			MethodName: "findEnabledReverseProxyConfig",
			Handler:    _ReverseProxyService_FindEnabledReverseProxyConfig_Handler,
		},
		{
			MethodName: "updateReverseProxyScheduling",
			Handler:    _ReverseProxyService_UpdateReverseProxyScheduling_Handler,
		},
		{
			MethodName: "updateReverseProxyPrimaryOrigins",
			Handler:    _ReverseProxyService_UpdateReverseProxyPrimaryOrigins_Handler,
		},
		{
			MethodName: "updateReverseProxyBackupOrigins",
			Handler:    _ReverseProxyService_UpdateReverseProxyBackupOrigins_Handler,
		},
		{
			MethodName: "updateReverseProxy",
			Handler:    _ReverseProxyService_UpdateReverseProxy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_reverse_proxy.proto",
}
