// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_account_daily_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserAccountDailyStatService_ListUserAccountDailyStats_FullMethodName   = "/pb.UserAccountDailyStatService/listUserAccountDailyStats"
	UserAccountDailyStatService_ListUserAccountMonthlyStats_FullMethodName = "/pb.UserAccountDailyStatService/listUserAccountMonthlyStats"
)

// UserAccountDailyStatServiceClient is the client API for UserAccountDailyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserAccountDailyStatServiceClient interface {
	// 列出按天统计
	ListUserAccountDailyStats(ctx context.Context, in *ListUserAccountDailyStatsRequest, opts ...grpc.CallOption) (*ListUserAccountDailyStatsResponse, error)
	// 列出按月统计
	ListUserAccountMonthlyStats(ctx context.Context, in *ListUserAccountMonthlyStatsRequest, opts ...grpc.CallOption) (*ListUserAccountMonthlyStatsResponse, error)
}

type userAccountDailyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserAccountDailyStatServiceClient(cc grpc.ClientConnInterface) UserAccountDailyStatServiceClient {
	return &userAccountDailyStatServiceClient{cc}
}

func (c *userAccountDailyStatServiceClient) ListUserAccountDailyStats(ctx context.Context, in *ListUserAccountDailyStatsRequest, opts ...grpc.CallOption) (*ListUserAccountDailyStatsResponse, error) {
	out := new(ListUserAccountDailyStatsResponse)
	err := c.cc.Invoke(ctx, UserAccountDailyStatService_ListUserAccountDailyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAccountDailyStatServiceClient) ListUserAccountMonthlyStats(ctx context.Context, in *ListUserAccountMonthlyStatsRequest, opts ...grpc.CallOption) (*ListUserAccountMonthlyStatsResponse, error) {
	out := new(ListUserAccountMonthlyStatsResponse)
	err := c.cc.Invoke(ctx, UserAccountDailyStatService_ListUserAccountMonthlyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserAccountDailyStatServiceServer is the server API for UserAccountDailyStatService service.
// All implementations should embed UnimplementedUserAccountDailyStatServiceServer
// for forward compatibility
type UserAccountDailyStatServiceServer interface {
	// 列出按天统计
	ListUserAccountDailyStats(context.Context, *ListUserAccountDailyStatsRequest) (*ListUserAccountDailyStatsResponse, error)
	// 列出按月统计
	ListUserAccountMonthlyStats(context.Context, *ListUserAccountMonthlyStatsRequest) (*ListUserAccountMonthlyStatsResponse, error)
}

// UnimplementedUserAccountDailyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserAccountDailyStatServiceServer struct {
}

func (UnimplementedUserAccountDailyStatServiceServer) ListUserAccountDailyStats(context.Context, *ListUserAccountDailyStatsRequest) (*ListUserAccountDailyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserAccountDailyStats not implemented")
}
func (UnimplementedUserAccountDailyStatServiceServer) ListUserAccountMonthlyStats(context.Context, *ListUserAccountMonthlyStatsRequest) (*ListUserAccountMonthlyStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserAccountMonthlyStats not implemented")
}

// UnsafeUserAccountDailyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserAccountDailyStatServiceServer will
// result in compilation errors.
type UnsafeUserAccountDailyStatServiceServer interface {
	mustEmbedUnimplementedUserAccountDailyStatServiceServer()
}

func RegisterUserAccountDailyStatServiceServer(s grpc.ServiceRegistrar, srv UserAccountDailyStatServiceServer) {
	s.RegisterService(&UserAccountDailyStatService_ServiceDesc, srv)
}

func _UserAccountDailyStatService_ListUserAccountDailyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserAccountDailyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountDailyStatServiceServer).ListUserAccountDailyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountDailyStatService_ListUserAccountDailyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountDailyStatServiceServer).ListUserAccountDailyStats(ctx, req.(*ListUserAccountDailyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAccountDailyStatService_ListUserAccountMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserAccountMonthlyStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountDailyStatServiceServer).ListUserAccountMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountDailyStatService_ListUserAccountMonthlyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountDailyStatServiceServer).ListUserAccountMonthlyStats(ctx, req.(*ListUserAccountMonthlyStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserAccountDailyStatService_ServiceDesc is the grpc.ServiceDesc for UserAccountDailyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserAccountDailyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserAccountDailyStatService",
	HandlerType: (*UserAccountDailyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "listUserAccountDailyStats",
			Handler:    _UserAccountDailyStatService_ListUserAccountDailyStats_Handler,
		},
		{
			MethodName: "listUserAccountMonthlyStats",
			Handler:    _UserAccountDailyStatService_ListUserAccountMonthlyStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_account_daily_stat.proto",
}
