// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_client_agent.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ClientAgentService_FindAllClientAgents_FullMethodName = "/pb.ClientAgentService/findAllClientAgents"
)

// ClientAgentServiceClient is the client API for ClientAgentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ClientAgentServiceClient interface {
	// 查找所有Agent
	FindAllClientAgents(ctx context.Context, in *FindAllClientAgentsRequest, opts ...grpc.CallOption) (*FindAllClientAgentsResponse, error)
}

type clientAgentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewClientAgentServiceClient(cc grpc.ClientConnInterface) ClientAgentServiceClient {
	return &clientAgentServiceClient{cc}
}

func (c *clientAgentServiceClient) FindAllClientAgents(ctx context.Context, in *FindAllClientAgentsRequest, opts ...grpc.CallOption) (*FindAllClientAgentsResponse, error) {
	out := new(FindAllClientAgentsResponse)
	err := c.cc.Invoke(ctx, ClientAgentService_FindAllClientAgents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClientAgentServiceServer is the server API for ClientAgentService service.
// All implementations should embed UnimplementedClientAgentServiceServer
// for forward compatibility
type ClientAgentServiceServer interface {
	// 查找所有Agent
	FindAllClientAgents(context.Context, *FindAllClientAgentsRequest) (*FindAllClientAgentsResponse, error)
}

// UnimplementedClientAgentServiceServer should be embedded to have forward compatible implementations.
type UnimplementedClientAgentServiceServer struct {
}

func (UnimplementedClientAgentServiceServer) FindAllClientAgents(context.Context, *FindAllClientAgentsRequest) (*FindAllClientAgentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllClientAgents not implemented")
}

// UnsafeClientAgentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ClientAgentServiceServer will
// result in compilation errors.
type UnsafeClientAgentServiceServer interface {
	mustEmbedUnimplementedClientAgentServiceServer()
}

func RegisterClientAgentServiceServer(s grpc.ServiceRegistrar, srv ClientAgentServiceServer) {
	s.RegisterService(&ClientAgentService_ServiceDesc, srv)
}

func _ClientAgentService_FindAllClientAgents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllClientAgentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientAgentServiceServer).FindAllClientAgents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClientAgentService_FindAllClientAgents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientAgentServiceServer).FindAllClientAgents(ctx, req.(*FindAllClientAgentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ClientAgentService_ServiceDesc is the grpc.ServiceDesc for ClientAgentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ClientAgentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ClientAgentService",
	HandlerType: (*ClientAgentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllClientAgents",
			Handler:    _ClientAgentService_FindAllClientAgents_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_client_agent.proto",
}
