// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_region_country.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RegionCountryService_FindAllEnabledRegionCountries_FullMethodName = "/pb.RegionCountryService/findAllEnabledRegionCountries"
	RegionCountryService_FindEnabledRegionCountry_FullMethodName      = "/pb.RegionCountryService/findEnabledRegionCountry"
	RegionCountryService_FindAllRegionCountries_FullMethodName        = "/pb.RegionCountryService/findAllRegionCountries"
	RegionCountryService_FindRegionCountry_FullMethodName             = "/pb.RegionCountryService/findRegionCountry"
	RegionCountryService_UpdateRegionCountryCustom_FullMethodName     = "/pb.RegionCountryService/updateRegionCountryCustom"
)

// RegionCountryServiceClient is the client API for RegionCountryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RegionCountryServiceClient interface {
	// Deprecated: Do not use.
	// 查找所有的国家/地区列表
	FindAllEnabledRegionCountries(ctx context.Context, in *FindAllEnabledRegionCountriesRequest, opts ...grpc.CallOption) (*FindAllEnabledRegionCountriesResponse, error)
	// Deprecated: Do not use.
	// 查找单个国家/地区信息
	FindEnabledRegionCountry(ctx context.Context, in *FindEnabledRegionCountryRequest, opts ...grpc.CallOption) (*FindEnabledRegionCountryResponse, error)
	// 查找所有的国家/地区列表
	FindAllRegionCountries(ctx context.Context, in *FindAllRegionCountriesRequest, opts ...grpc.CallOption) (*FindAllRegionCountriesResponse, error)
	// 查找单个国家/地区信息
	FindRegionCountry(ctx context.Context, in *FindRegionCountryRequest, opts ...grpc.CallOption) (*FindRegionCountryResponse, error)
	// 修改国家/地区定制信息
	UpdateRegionCountryCustom(ctx context.Context, in *UpdateRegionCountryCustomRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type regionCountryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRegionCountryServiceClient(cc grpc.ClientConnInterface) RegionCountryServiceClient {
	return &regionCountryServiceClient{cc}
}

// Deprecated: Do not use.
func (c *regionCountryServiceClient) FindAllEnabledRegionCountries(ctx context.Context, in *FindAllEnabledRegionCountriesRequest, opts ...grpc.CallOption) (*FindAllEnabledRegionCountriesResponse, error) {
	out := new(FindAllEnabledRegionCountriesResponse)
	err := c.cc.Invoke(ctx, RegionCountryService_FindAllEnabledRegionCountries_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *regionCountryServiceClient) FindEnabledRegionCountry(ctx context.Context, in *FindEnabledRegionCountryRequest, opts ...grpc.CallOption) (*FindEnabledRegionCountryResponse, error) {
	out := new(FindEnabledRegionCountryResponse)
	err := c.cc.Invoke(ctx, RegionCountryService_FindEnabledRegionCountry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionCountryServiceClient) FindAllRegionCountries(ctx context.Context, in *FindAllRegionCountriesRequest, opts ...grpc.CallOption) (*FindAllRegionCountriesResponse, error) {
	out := new(FindAllRegionCountriesResponse)
	err := c.cc.Invoke(ctx, RegionCountryService_FindAllRegionCountries_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionCountryServiceClient) FindRegionCountry(ctx context.Context, in *FindRegionCountryRequest, opts ...grpc.CallOption) (*FindRegionCountryResponse, error) {
	out := new(FindRegionCountryResponse)
	err := c.cc.Invoke(ctx, RegionCountryService_FindRegionCountry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionCountryServiceClient) UpdateRegionCountryCustom(ctx context.Context, in *UpdateRegionCountryCustomRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, RegionCountryService_UpdateRegionCountryCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RegionCountryServiceServer is the server API for RegionCountryService service.
// All implementations should embed UnimplementedRegionCountryServiceServer
// for forward compatibility
type RegionCountryServiceServer interface {
	// Deprecated: Do not use.
	// 查找所有的国家/地区列表
	FindAllEnabledRegionCountries(context.Context, *FindAllEnabledRegionCountriesRequest) (*FindAllEnabledRegionCountriesResponse, error)
	// Deprecated: Do not use.
	// 查找单个国家/地区信息
	FindEnabledRegionCountry(context.Context, *FindEnabledRegionCountryRequest) (*FindEnabledRegionCountryResponse, error)
	// 查找所有的国家/地区列表
	FindAllRegionCountries(context.Context, *FindAllRegionCountriesRequest) (*FindAllRegionCountriesResponse, error)
	// 查找单个国家/地区信息
	FindRegionCountry(context.Context, *FindRegionCountryRequest) (*FindRegionCountryResponse, error)
	// 修改国家/地区定制信息
	UpdateRegionCountryCustom(context.Context, *UpdateRegionCountryCustomRequest) (*RPCSuccess, error)
}

// UnimplementedRegionCountryServiceServer should be embedded to have forward compatible implementations.
type UnimplementedRegionCountryServiceServer struct {
}

func (UnimplementedRegionCountryServiceServer) FindAllEnabledRegionCountries(context.Context, *FindAllEnabledRegionCountriesRequest) (*FindAllEnabledRegionCountriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledRegionCountries not implemented")
}
func (UnimplementedRegionCountryServiceServer) FindEnabledRegionCountry(context.Context, *FindEnabledRegionCountryRequest) (*FindEnabledRegionCountryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledRegionCountry not implemented")
}
func (UnimplementedRegionCountryServiceServer) FindAllRegionCountries(context.Context, *FindAllRegionCountriesRequest) (*FindAllRegionCountriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllRegionCountries not implemented")
}
func (UnimplementedRegionCountryServiceServer) FindRegionCountry(context.Context, *FindRegionCountryRequest) (*FindRegionCountryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindRegionCountry not implemented")
}
func (UnimplementedRegionCountryServiceServer) UpdateRegionCountryCustom(context.Context, *UpdateRegionCountryCustomRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRegionCountryCustom not implemented")
}

// UnsafeRegionCountryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RegionCountryServiceServer will
// result in compilation errors.
type UnsafeRegionCountryServiceServer interface {
	mustEmbedUnimplementedRegionCountryServiceServer()
}

func RegisterRegionCountryServiceServer(s grpc.ServiceRegistrar, srv RegionCountryServiceServer) {
	s.RegisterService(&RegionCountryService_ServiceDesc, srv)
}

func _RegionCountryService_FindAllEnabledRegionCountries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledRegionCountriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionCountryServiceServer).FindAllEnabledRegionCountries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionCountryService_FindAllEnabledRegionCountries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionCountryServiceServer).FindAllEnabledRegionCountries(ctx, req.(*FindAllEnabledRegionCountriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionCountryService_FindEnabledRegionCountry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledRegionCountryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionCountryServiceServer).FindEnabledRegionCountry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionCountryService_FindEnabledRegionCountry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionCountryServiceServer).FindEnabledRegionCountry(ctx, req.(*FindEnabledRegionCountryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionCountryService_FindAllRegionCountries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllRegionCountriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionCountryServiceServer).FindAllRegionCountries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionCountryService_FindAllRegionCountries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionCountryServiceServer).FindAllRegionCountries(ctx, req.(*FindAllRegionCountriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionCountryService_FindRegionCountry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindRegionCountryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionCountryServiceServer).FindRegionCountry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionCountryService_FindRegionCountry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionCountryServiceServer).FindRegionCountry(ctx, req.(*FindRegionCountryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionCountryService_UpdateRegionCountryCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRegionCountryCustomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionCountryServiceServer).UpdateRegionCountryCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionCountryService_UpdateRegionCountryCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionCountryServiceServer).UpdateRegionCountryCustom(ctx, req.(*UpdateRegionCountryCustomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RegionCountryService_ServiceDesc is the grpc.ServiceDesc for RegionCountryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RegionCountryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.RegionCountryService",
	HandlerType: (*RegionCountryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllEnabledRegionCountries",
			Handler:    _RegionCountryService_FindAllEnabledRegionCountries_Handler,
		},
		{
			MethodName: "findEnabledRegionCountry",
			Handler:    _RegionCountryService_FindEnabledRegionCountry_Handler,
		},
		{
			MethodName: "findAllRegionCountries",
			Handler:    _RegionCountryService_FindAllRegionCountries_Handler,
		},
		{
			MethodName: "findRegionCountry",
			Handler:    _RegionCountryService_FindRegionCountry_Handler,
		},
		{
			MethodName: "updateRegionCountryCustom",
			Handler:    _RegionCountryService_UpdateRegionCountryCustom_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_region_country.proto",
}
