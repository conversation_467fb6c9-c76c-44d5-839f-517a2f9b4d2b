// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_traffic_package.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TrafficPackageService_CreateTrafficPackage_FullMethodName            = "/pb.TrafficPackageService/createTrafficPackage"
	TrafficPackageService_UpdateTrafficPackage_FullMethodName            = "/pb.TrafficPackageService/updateTrafficPackage"
	TrafficPackageService_DeleteTrafficPackage_FullMethodName            = "/pb.TrafficPackageService/deleteTrafficPackage"
	TrafficPackageService_FindTrafficPackage_FullMethodName              = "/pb.TrafficPackageService/findTrafficPackage"
	TrafficPackageService_FindAllTrafficPackages_FullMethodName          = "/pb.TrafficPackageService/findAllTrafficPackages"
	TrafficPackageService_FindAllAvailableTrafficPackages_FullMethodName = "/pb.TrafficPackageService/findAllAvailableTrafficPackages"
)

// TrafficPackageServiceClient is the client API for TrafficPackageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TrafficPackageServiceClient interface {
	// 创建流量包
	CreateTrafficPackage(ctx context.Context, in *CreateTrafficPackageRequest, opts ...grpc.CallOption) (*CreateTrafficPackageResponse, error)
	// 修改流量包
	UpdateTrafficPackage(ctx context.Context, in *UpdateTrafficPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除流量包
	DeleteTrafficPackage(ctx context.Context, in *DeleteTrafficPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找流量包
	FindTrafficPackage(ctx context.Context, in *FindTrafficPackageRequest, opts ...grpc.CallOption) (*FindTrafficPackageResponse, error)
	// 查找所有流量包
	FindAllTrafficPackages(ctx context.Context, in *FindAllTrafficPackagesRequest, opts ...grpc.CallOption) (*FindAllTrafficPackagesResponse, error)
	// 查找所有可用流量包
	FindAllAvailableTrafficPackages(ctx context.Context, in *FindAllAvailableTrafficPackagesRequest, opts ...grpc.CallOption) (*FindAllAvailableTrafficPackagesResponse, error)
}

type trafficPackageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTrafficPackageServiceClient(cc grpc.ClientConnInterface) TrafficPackageServiceClient {
	return &trafficPackageServiceClient{cc}
}

func (c *trafficPackageServiceClient) CreateTrafficPackage(ctx context.Context, in *CreateTrafficPackageRequest, opts ...grpc.CallOption) (*CreateTrafficPackageResponse, error) {
	out := new(CreateTrafficPackageResponse)
	err := c.cc.Invoke(ctx, TrafficPackageService_CreateTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackageServiceClient) UpdateTrafficPackage(ctx context.Context, in *UpdateTrafficPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, TrafficPackageService_UpdateTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackageServiceClient) DeleteTrafficPackage(ctx context.Context, in *DeleteTrafficPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, TrafficPackageService_DeleteTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackageServiceClient) FindTrafficPackage(ctx context.Context, in *FindTrafficPackageRequest, opts ...grpc.CallOption) (*FindTrafficPackageResponse, error) {
	out := new(FindTrafficPackageResponse)
	err := c.cc.Invoke(ctx, TrafficPackageService_FindTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackageServiceClient) FindAllTrafficPackages(ctx context.Context, in *FindAllTrafficPackagesRequest, opts ...grpc.CallOption) (*FindAllTrafficPackagesResponse, error) {
	out := new(FindAllTrafficPackagesResponse)
	err := c.cc.Invoke(ctx, TrafficPackageService_FindAllTrafficPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficPackageServiceClient) FindAllAvailableTrafficPackages(ctx context.Context, in *FindAllAvailableTrafficPackagesRequest, opts ...grpc.CallOption) (*FindAllAvailableTrafficPackagesResponse, error) {
	out := new(FindAllAvailableTrafficPackagesResponse)
	err := c.cc.Invoke(ctx, TrafficPackageService_FindAllAvailableTrafficPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TrafficPackageServiceServer is the server API for TrafficPackageService service.
// All implementations should embed UnimplementedTrafficPackageServiceServer
// for forward compatibility
type TrafficPackageServiceServer interface {
	// 创建流量包
	CreateTrafficPackage(context.Context, *CreateTrafficPackageRequest) (*CreateTrafficPackageResponse, error)
	// 修改流量包
	UpdateTrafficPackage(context.Context, *UpdateTrafficPackageRequest) (*RPCSuccess, error)
	// 删除流量包
	DeleteTrafficPackage(context.Context, *DeleteTrafficPackageRequest) (*RPCSuccess, error)
	// 查找流量包
	FindTrafficPackage(context.Context, *FindTrafficPackageRequest) (*FindTrafficPackageResponse, error)
	// 查找所有流量包
	FindAllTrafficPackages(context.Context, *FindAllTrafficPackagesRequest) (*FindAllTrafficPackagesResponse, error)
	// 查找所有可用流量包
	FindAllAvailableTrafficPackages(context.Context, *FindAllAvailableTrafficPackagesRequest) (*FindAllAvailableTrafficPackagesResponse, error)
}

// UnimplementedTrafficPackageServiceServer should be embedded to have forward compatible implementations.
type UnimplementedTrafficPackageServiceServer struct {
}

func (UnimplementedTrafficPackageServiceServer) CreateTrafficPackage(context.Context, *CreateTrafficPackageRequest) (*CreateTrafficPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTrafficPackage not implemented")
}
func (UnimplementedTrafficPackageServiceServer) UpdateTrafficPackage(context.Context, *UpdateTrafficPackageRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTrafficPackage not implemented")
}
func (UnimplementedTrafficPackageServiceServer) DeleteTrafficPackage(context.Context, *DeleteTrafficPackageRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTrafficPackage not implemented")
}
func (UnimplementedTrafficPackageServiceServer) FindTrafficPackage(context.Context, *FindTrafficPackageRequest) (*FindTrafficPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTrafficPackage not implemented")
}
func (UnimplementedTrafficPackageServiceServer) FindAllTrafficPackages(context.Context, *FindAllTrafficPackagesRequest) (*FindAllTrafficPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllTrafficPackages not implemented")
}
func (UnimplementedTrafficPackageServiceServer) FindAllAvailableTrafficPackages(context.Context, *FindAllAvailableTrafficPackagesRequest) (*FindAllAvailableTrafficPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailableTrafficPackages not implemented")
}

// UnsafeTrafficPackageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TrafficPackageServiceServer will
// result in compilation errors.
type UnsafeTrafficPackageServiceServer interface {
	mustEmbedUnimplementedTrafficPackageServiceServer()
}

func RegisterTrafficPackageServiceServer(s grpc.ServiceRegistrar, srv TrafficPackageServiceServer) {
	s.RegisterService(&TrafficPackageService_ServiceDesc, srv)
}

func _TrafficPackageService_CreateTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackageServiceServer).CreateTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackageService_CreateTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackageServiceServer).CreateTrafficPackage(ctx, req.(*CreateTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackageService_UpdateTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackageServiceServer).UpdateTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackageService_UpdateTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackageServiceServer).UpdateTrafficPackage(ctx, req.(*UpdateTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackageService_DeleteTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackageServiceServer).DeleteTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackageService_DeleteTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackageServiceServer).DeleteTrafficPackage(ctx, req.(*DeleteTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackageService_FindTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackageServiceServer).FindTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackageService_FindTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackageServiceServer).FindTrafficPackage(ctx, req.(*FindTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackageService_FindAllTrafficPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllTrafficPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackageServiceServer).FindAllTrafficPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackageService_FindAllTrafficPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackageServiceServer).FindAllTrafficPackages(ctx, req.(*FindAllTrafficPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficPackageService_FindAllAvailableTrafficPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailableTrafficPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficPackageServiceServer).FindAllAvailableTrafficPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TrafficPackageService_FindAllAvailableTrafficPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficPackageServiceServer).FindAllAvailableTrafficPackages(ctx, req.(*FindAllAvailableTrafficPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TrafficPackageService_ServiceDesc is the grpc.ServiceDesc for TrafficPackageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TrafficPackageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TrafficPackageService",
	HandlerType: (*TrafficPackageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createTrafficPackage",
			Handler:    _TrafficPackageService_CreateTrafficPackage_Handler,
		},
		{
			MethodName: "updateTrafficPackage",
			Handler:    _TrafficPackageService_UpdateTrafficPackage_Handler,
		},
		{
			MethodName: "deleteTrafficPackage",
			Handler:    _TrafficPackageService_DeleteTrafficPackage_Handler,
		},
		{
			MethodName: "findTrafficPackage",
			Handler:    _TrafficPackageService_FindTrafficPackage_Handler,
		},
		{
			MethodName: "findAllTrafficPackages",
			Handler:    _TrafficPackageService_FindAllTrafficPackages_Handler,
		},
		{
			MethodName: "findAllAvailableTrafficPackages",
			Handler:    _TrafficPackageService_FindAllAvailableTrafficPackages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_traffic_package.proto",
}
