// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_region_town.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RegionTownService_FindAllRegionTowns_FullMethodName                 = "/pb.RegionTownService/findAllRegionTowns"
	RegionTownService_FindAllRegionTownsWithRegionCityId_FullMethodName = "/pb.RegionTownService/findAllRegionTownsWithRegionCityId"
	RegionTownService_FindRegionTown_FullMethodName                     = "/pb.RegionTownService/findRegionTown"
	RegionTownService_UpdateRegionTownCustom_FullMethodName             = "/pb.RegionTownService/updateRegionTownCustom"
)

// RegionTownServiceClient is the client API for RegionTownService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RegionTownServiceClient interface {
	// 查找所有区县
	FindAllRegionTowns(ctx context.Context, in *FindAllRegionTownsRequest, opts ...grpc.CallOption) (*FindAllRegionTownsResponse, error)
	// 查找某个城市的所有区县
	FindAllRegionTownsWithRegionCityId(ctx context.Context, in *FindAllRegionTownsWithRegionCityIdRequest, opts ...grpc.CallOption) (*FindAllRegionTownsWithRegionCityIdResponse, error)
	// 查找单个区县信息
	FindRegionTown(ctx context.Context, in *FindRegionTownRequest, opts ...grpc.CallOption) (*FindRegionTownResponse, error)
	// 修改区县定制信息
	UpdateRegionTownCustom(ctx context.Context, in *UpdateRegionTownCustomRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type regionTownServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRegionTownServiceClient(cc grpc.ClientConnInterface) RegionTownServiceClient {
	return &regionTownServiceClient{cc}
}

func (c *regionTownServiceClient) FindAllRegionTowns(ctx context.Context, in *FindAllRegionTownsRequest, opts ...grpc.CallOption) (*FindAllRegionTownsResponse, error) {
	out := new(FindAllRegionTownsResponse)
	err := c.cc.Invoke(ctx, RegionTownService_FindAllRegionTowns_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionTownServiceClient) FindAllRegionTownsWithRegionCityId(ctx context.Context, in *FindAllRegionTownsWithRegionCityIdRequest, opts ...grpc.CallOption) (*FindAllRegionTownsWithRegionCityIdResponse, error) {
	out := new(FindAllRegionTownsWithRegionCityIdResponse)
	err := c.cc.Invoke(ctx, RegionTownService_FindAllRegionTownsWithRegionCityId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionTownServiceClient) FindRegionTown(ctx context.Context, in *FindRegionTownRequest, opts ...grpc.CallOption) (*FindRegionTownResponse, error) {
	out := new(FindRegionTownResponse)
	err := c.cc.Invoke(ctx, RegionTownService_FindRegionTown_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionTownServiceClient) UpdateRegionTownCustom(ctx context.Context, in *UpdateRegionTownCustomRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, RegionTownService_UpdateRegionTownCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RegionTownServiceServer is the server API for RegionTownService service.
// All implementations should embed UnimplementedRegionTownServiceServer
// for forward compatibility
type RegionTownServiceServer interface {
	// 查找所有区县
	FindAllRegionTowns(context.Context, *FindAllRegionTownsRequest) (*FindAllRegionTownsResponse, error)
	// 查找某个城市的所有区县
	FindAllRegionTownsWithRegionCityId(context.Context, *FindAllRegionTownsWithRegionCityIdRequest) (*FindAllRegionTownsWithRegionCityIdResponse, error)
	// 查找单个区县信息
	FindRegionTown(context.Context, *FindRegionTownRequest) (*FindRegionTownResponse, error)
	// 修改区县定制信息
	UpdateRegionTownCustom(context.Context, *UpdateRegionTownCustomRequest) (*RPCSuccess, error)
}

// UnimplementedRegionTownServiceServer should be embedded to have forward compatible implementations.
type UnimplementedRegionTownServiceServer struct {
}

func (UnimplementedRegionTownServiceServer) FindAllRegionTowns(context.Context, *FindAllRegionTownsRequest) (*FindAllRegionTownsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllRegionTowns not implemented")
}
func (UnimplementedRegionTownServiceServer) FindAllRegionTownsWithRegionCityId(context.Context, *FindAllRegionTownsWithRegionCityIdRequest) (*FindAllRegionTownsWithRegionCityIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllRegionTownsWithRegionCityId not implemented")
}
func (UnimplementedRegionTownServiceServer) FindRegionTown(context.Context, *FindRegionTownRequest) (*FindRegionTownResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindRegionTown not implemented")
}
func (UnimplementedRegionTownServiceServer) UpdateRegionTownCustom(context.Context, *UpdateRegionTownCustomRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRegionTownCustom not implemented")
}

// UnsafeRegionTownServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RegionTownServiceServer will
// result in compilation errors.
type UnsafeRegionTownServiceServer interface {
	mustEmbedUnimplementedRegionTownServiceServer()
}

func RegisterRegionTownServiceServer(s grpc.ServiceRegistrar, srv RegionTownServiceServer) {
	s.RegisterService(&RegionTownService_ServiceDesc, srv)
}

func _RegionTownService_FindAllRegionTowns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllRegionTownsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionTownServiceServer).FindAllRegionTowns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionTownService_FindAllRegionTowns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionTownServiceServer).FindAllRegionTowns(ctx, req.(*FindAllRegionTownsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionTownService_FindAllRegionTownsWithRegionCityId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllRegionTownsWithRegionCityIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionTownServiceServer).FindAllRegionTownsWithRegionCityId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionTownService_FindAllRegionTownsWithRegionCityId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionTownServiceServer).FindAllRegionTownsWithRegionCityId(ctx, req.(*FindAllRegionTownsWithRegionCityIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionTownService_FindRegionTown_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindRegionTownRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionTownServiceServer).FindRegionTown(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionTownService_FindRegionTown_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionTownServiceServer).FindRegionTown(ctx, req.(*FindRegionTownRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionTownService_UpdateRegionTownCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRegionTownCustomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionTownServiceServer).UpdateRegionTownCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegionTownService_UpdateRegionTownCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionTownServiceServer).UpdateRegionTownCustom(ctx, req.(*UpdateRegionTownCustomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RegionTownService_ServiceDesc is the grpc.ServiceDesc for RegionTownService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RegionTownService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.RegionTownService",
	HandlerType: (*RegionTownServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllRegionTowns",
			Handler:    _RegionTownService_FindAllRegionTowns_Handler,
		},
		{
			MethodName: "findAllRegionTownsWithRegionCityId",
			Handler:    _RegionTownService_FindAllRegionTownsWithRegionCityId_Handler,
		},
		{
			MethodName: "findRegionTown",
			Handler:    _RegionTownService_FindRegionTown_Handler,
		},
		{
			MethodName: "updateRegionTownCustom",
			Handler:    _RegionTownService_UpdateRegionTownCustom_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_region_town.proto",
}
