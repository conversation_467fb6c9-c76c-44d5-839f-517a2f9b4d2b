// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_post_category.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PostCategoryService_CreatePostCategory_FullMethodName             = "/pb.PostCategoryService/createPostCategory"
	PostCategoryService_UpdatePostCategory_FullMethodName             = "/pb.PostCategoryService/updatePostCategory"
	PostCategoryService_DeletePostCategory_FullMethodName             = "/pb.PostCategoryService/deletePostCategory"
	PostCategoryService_FindAllPostCategories_FullMethodName          = "/pb.PostCategoryService/findAllPostCategories"
	PostCategoryService_FindAllAvailablePostCategories_FullMethodName = "/pb.PostCategoryService/findAllAvailablePostCategories"
	PostCategoryService_FindPostCategory_FullMethodName               = "/pb.PostCategoryService/findPostCategory"
	PostCategoryService_SortPostCategories_FullMethodName             = "/pb.PostCategoryService/sortPostCategories"
)

// PostCategoryServiceClient is the client API for PostCategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PostCategoryServiceClient interface {
	// 创建分类
	CreatePostCategory(ctx context.Context, in *CreatePostCategoryRequest, opts ...grpc.CallOption) (*CreatePostCategoryResponse, error)
	// 修改分类
	UpdatePostCategory(ctx context.Context, in *UpdatePostCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除分类
	DeletePostCategory(ctx context.Context, in *DeletePostCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 列出所有分类
	FindAllPostCategories(ctx context.Context, in *FindAllPostCategoriesRequest, opts ...grpc.CallOption) (*FindAllPostCategoriesResponse, error)
	// 列出所有可用分类
	FindAllAvailablePostCategories(ctx context.Context, in *FindAllAvailablePostCategoriesRequest, opts ...grpc.CallOption) (*FindAllAvailablePostCategoriesResponse, error)
	// 查询单个分类
	FindPostCategory(ctx context.Context, in *FindPostCategoryRequest, opts ...grpc.CallOption) (*FindPostCategoryResponse, error)
	// 对分类进行排序
	SortPostCategories(ctx context.Context, in *SortPostCategoriesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type postCategoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPostCategoryServiceClient(cc grpc.ClientConnInterface) PostCategoryServiceClient {
	return &postCategoryServiceClient{cc}
}

func (c *postCategoryServiceClient) CreatePostCategory(ctx context.Context, in *CreatePostCategoryRequest, opts ...grpc.CallOption) (*CreatePostCategoryResponse, error) {
	out := new(CreatePostCategoryResponse)
	err := c.cc.Invoke(ctx, PostCategoryService_CreatePostCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *postCategoryServiceClient) UpdatePostCategory(ctx context.Context, in *UpdatePostCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, PostCategoryService_UpdatePostCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *postCategoryServiceClient) DeletePostCategory(ctx context.Context, in *DeletePostCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, PostCategoryService_DeletePostCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *postCategoryServiceClient) FindAllPostCategories(ctx context.Context, in *FindAllPostCategoriesRequest, opts ...grpc.CallOption) (*FindAllPostCategoriesResponse, error) {
	out := new(FindAllPostCategoriesResponse)
	err := c.cc.Invoke(ctx, PostCategoryService_FindAllPostCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *postCategoryServiceClient) FindAllAvailablePostCategories(ctx context.Context, in *FindAllAvailablePostCategoriesRequest, opts ...grpc.CallOption) (*FindAllAvailablePostCategoriesResponse, error) {
	out := new(FindAllAvailablePostCategoriesResponse)
	err := c.cc.Invoke(ctx, PostCategoryService_FindAllAvailablePostCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *postCategoryServiceClient) FindPostCategory(ctx context.Context, in *FindPostCategoryRequest, opts ...grpc.CallOption) (*FindPostCategoryResponse, error) {
	out := new(FindPostCategoryResponse)
	err := c.cc.Invoke(ctx, PostCategoryService_FindPostCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *postCategoryServiceClient) SortPostCategories(ctx context.Context, in *SortPostCategoriesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, PostCategoryService_SortPostCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PostCategoryServiceServer is the server API for PostCategoryService service.
// All implementations should embed UnimplementedPostCategoryServiceServer
// for forward compatibility
type PostCategoryServiceServer interface {
	// 创建分类
	CreatePostCategory(context.Context, *CreatePostCategoryRequest) (*CreatePostCategoryResponse, error)
	// 修改分类
	UpdatePostCategory(context.Context, *UpdatePostCategoryRequest) (*RPCSuccess, error)
	// 删除分类
	DeletePostCategory(context.Context, *DeletePostCategoryRequest) (*RPCSuccess, error)
	// 列出所有分类
	FindAllPostCategories(context.Context, *FindAllPostCategoriesRequest) (*FindAllPostCategoriesResponse, error)
	// 列出所有可用分类
	FindAllAvailablePostCategories(context.Context, *FindAllAvailablePostCategoriesRequest) (*FindAllAvailablePostCategoriesResponse, error)
	// 查询单个分类
	FindPostCategory(context.Context, *FindPostCategoryRequest) (*FindPostCategoryResponse, error)
	// 对分类进行排序
	SortPostCategories(context.Context, *SortPostCategoriesRequest) (*RPCSuccess, error)
}

// UnimplementedPostCategoryServiceServer should be embedded to have forward compatible implementations.
type UnimplementedPostCategoryServiceServer struct {
}

func (UnimplementedPostCategoryServiceServer) CreatePostCategory(context.Context, *CreatePostCategoryRequest) (*CreatePostCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePostCategory not implemented")
}
func (UnimplementedPostCategoryServiceServer) UpdatePostCategory(context.Context, *UpdatePostCategoryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePostCategory not implemented")
}
func (UnimplementedPostCategoryServiceServer) DeletePostCategory(context.Context, *DeletePostCategoryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePostCategory not implemented")
}
func (UnimplementedPostCategoryServiceServer) FindAllPostCategories(context.Context, *FindAllPostCategoriesRequest) (*FindAllPostCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllPostCategories not implemented")
}
func (UnimplementedPostCategoryServiceServer) FindAllAvailablePostCategories(context.Context, *FindAllAvailablePostCategoriesRequest) (*FindAllAvailablePostCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailablePostCategories not implemented")
}
func (UnimplementedPostCategoryServiceServer) FindPostCategory(context.Context, *FindPostCategoryRequest) (*FindPostCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindPostCategory not implemented")
}
func (UnimplementedPostCategoryServiceServer) SortPostCategories(context.Context, *SortPostCategoriesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPostCategories not implemented")
}

// UnsafePostCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PostCategoryServiceServer will
// result in compilation errors.
type UnsafePostCategoryServiceServer interface {
	mustEmbedUnimplementedPostCategoryServiceServer()
}

func RegisterPostCategoryServiceServer(s grpc.ServiceRegistrar, srv PostCategoryServiceServer) {
	s.RegisterService(&PostCategoryService_ServiceDesc, srv)
}

func _PostCategoryService_CreatePostCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePostCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).CreatePostCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_CreatePostCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).CreatePostCategory(ctx, req.(*CreatePostCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PostCategoryService_UpdatePostCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).UpdatePostCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_UpdatePostCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).UpdatePostCategory(ctx, req.(*UpdatePostCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PostCategoryService_DeletePostCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePostCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).DeletePostCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_DeletePostCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).DeletePostCategory(ctx, req.(*DeletePostCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PostCategoryService_FindAllPostCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllPostCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).FindAllPostCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_FindAllPostCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).FindAllPostCategories(ctx, req.(*FindAllPostCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PostCategoryService_FindAllAvailablePostCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailablePostCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).FindAllAvailablePostCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_FindAllAvailablePostCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).FindAllAvailablePostCategories(ctx, req.(*FindAllAvailablePostCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PostCategoryService_FindPostCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindPostCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).FindPostCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_FindPostCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).FindPostCategory(ctx, req.(*FindPostCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PostCategoryService_SortPostCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPostCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PostCategoryServiceServer).SortPostCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PostCategoryService_SortPostCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PostCategoryServiceServer).SortPostCategories(ctx, req.(*SortPostCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PostCategoryService_ServiceDesc is the grpc.ServiceDesc for PostCategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PostCategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.PostCategoryService",
	HandlerType: (*PostCategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createPostCategory",
			Handler:    _PostCategoryService_CreatePostCategory_Handler,
		},
		{
			MethodName: "updatePostCategory",
			Handler:    _PostCategoryService_UpdatePostCategory_Handler,
		},
		{
			MethodName: "deletePostCategory",
			Handler:    _PostCategoryService_DeletePostCategory_Handler,
		},
		{
			MethodName: "findAllPostCategories",
			Handler:    _PostCategoryService_FindAllPostCategories_Handler,
		},
		{
			MethodName: "findAllAvailablePostCategories",
			Handler:    _PostCategoryService_FindAllAvailablePostCategories_Handler,
		},
		{
			MethodName: "findPostCategory",
			Handler:    _PostCategoryService_FindPostCategory_Handler,
		},
		{
			MethodName: "sortPostCategories",
			Handler:    _PostCategoryService_SortPostCategories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_post_category.proto",
}
