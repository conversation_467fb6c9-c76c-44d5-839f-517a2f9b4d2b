// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_plan.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSPlanService_CreateNSPlan_FullMethodName          = "/pb.NSPlanService/createNSPlan"
	NSPlanService_UpdateNSPlan_FullMethodName          = "/pb.NSPlanService/updateNSPlan"
	NSPlanService_SortNSPlanOrders_FullMethodName      = "/pb.NSPlanService/sortNSPlanOrders"
	NSPlanService_FindAllNSPlans_FullMethodName        = "/pb.NSPlanService/findAllNSPlans"
	NSPlanService_FindAllEnabledNSPlans_FullMethodName = "/pb.NSPlanService/findAllEnabledNSPlans"
	NSPlanService_FindNSPlan_FullMethodName            = "/pb.NSPlanService/findNSPlan"
	NSPlanService_DeleteNSPlan_FullMethodName          = "/pb.NSPlanService/deleteNSPlan"
)

// NSPlanServiceClient is the client API for NSPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSPlanServiceClient interface {
	// 创建DNS套餐
	CreateNSPlan(ctx context.Context, in *CreateNSPlanRequest, opts ...grpc.CallOption) (*CreateNSPlanResponse, error)
	// 修改DNS套餐
	UpdateNSPlan(ctx context.Context, in *UpdateNSPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改DNS套餐顺序
	SortNSPlanOrders(ctx context.Context, in *SortNSPlansRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找所有DNS套餐
	FindAllNSPlans(ctx context.Context, in *FindAllNSPlansRequest, opts ...grpc.CallOption) (*FindAllNSPlansResponse, error)
	// 查找所有可用DNS套餐
	FindAllEnabledNSPlans(ctx context.Context, in *FindAllEnabledNSPlansRequest, opts ...grpc.CallOption) (*FindAllEnabledNSPlansResponse, error)
	// 查找DNS套餐
	FindNSPlan(ctx context.Context, in *FindNSPlanRequest, opts ...grpc.CallOption) (*FindNSPlanResponse, error)
	// 删除DNS套餐
	DeleteNSPlan(ctx context.Context, in *DeleteNSPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nSPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSPlanServiceClient(cc grpc.ClientConnInterface) NSPlanServiceClient {
	return &nSPlanServiceClient{cc}
}

func (c *nSPlanServiceClient) CreateNSPlan(ctx context.Context, in *CreateNSPlanRequest, opts ...grpc.CallOption) (*CreateNSPlanResponse, error) {
	out := new(CreateNSPlanResponse)
	err := c.cc.Invoke(ctx, NSPlanService_CreateNSPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSPlanServiceClient) UpdateNSPlan(ctx context.Context, in *UpdateNSPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSPlanService_UpdateNSPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSPlanServiceClient) SortNSPlanOrders(ctx context.Context, in *SortNSPlansRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSPlanService_SortNSPlanOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSPlanServiceClient) FindAllNSPlans(ctx context.Context, in *FindAllNSPlansRequest, opts ...grpc.CallOption) (*FindAllNSPlansResponse, error) {
	out := new(FindAllNSPlansResponse)
	err := c.cc.Invoke(ctx, NSPlanService_FindAllNSPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSPlanServiceClient) FindAllEnabledNSPlans(ctx context.Context, in *FindAllEnabledNSPlansRequest, opts ...grpc.CallOption) (*FindAllEnabledNSPlansResponse, error) {
	out := new(FindAllEnabledNSPlansResponse)
	err := c.cc.Invoke(ctx, NSPlanService_FindAllEnabledNSPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSPlanServiceClient) FindNSPlan(ctx context.Context, in *FindNSPlanRequest, opts ...grpc.CallOption) (*FindNSPlanResponse, error) {
	out := new(FindNSPlanResponse)
	err := c.cc.Invoke(ctx, NSPlanService_FindNSPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSPlanServiceClient) DeleteNSPlan(ctx context.Context, in *DeleteNSPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSPlanService_DeleteNSPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSPlanServiceServer is the server API for NSPlanService service.
// All implementations should embed UnimplementedNSPlanServiceServer
// for forward compatibility
type NSPlanServiceServer interface {
	// 创建DNS套餐
	CreateNSPlan(context.Context, *CreateNSPlanRequest) (*CreateNSPlanResponse, error)
	// 修改DNS套餐
	UpdateNSPlan(context.Context, *UpdateNSPlanRequest) (*RPCSuccess, error)
	// 修改DNS套餐顺序
	SortNSPlanOrders(context.Context, *SortNSPlansRequest) (*RPCSuccess, error)
	// 查找所有DNS套餐
	FindAllNSPlans(context.Context, *FindAllNSPlansRequest) (*FindAllNSPlansResponse, error)
	// 查找所有可用DNS套餐
	FindAllEnabledNSPlans(context.Context, *FindAllEnabledNSPlansRequest) (*FindAllEnabledNSPlansResponse, error)
	// 查找DNS套餐
	FindNSPlan(context.Context, *FindNSPlanRequest) (*FindNSPlanResponse, error)
	// 删除DNS套餐
	DeleteNSPlan(context.Context, *DeleteNSPlanRequest) (*RPCSuccess, error)
}

// UnimplementedNSPlanServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSPlanServiceServer struct {
}

func (UnimplementedNSPlanServiceServer) CreateNSPlan(context.Context, *CreateNSPlanRequest) (*CreateNSPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSPlan not implemented")
}
func (UnimplementedNSPlanServiceServer) UpdateNSPlan(context.Context, *UpdateNSPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSPlan not implemented")
}
func (UnimplementedNSPlanServiceServer) SortNSPlanOrders(context.Context, *SortNSPlansRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortNSPlanOrders not implemented")
}
func (UnimplementedNSPlanServiceServer) FindAllNSPlans(context.Context, *FindAllNSPlansRequest) (*FindAllNSPlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNSPlans not implemented")
}
func (UnimplementedNSPlanServiceServer) FindAllEnabledNSPlans(context.Context, *FindAllEnabledNSPlansRequest) (*FindAllEnabledNSPlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNSPlans not implemented")
}
func (UnimplementedNSPlanServiceServer) FindNSPlan(context.Context, *FindNSPlanRequest) (*FindNSPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSPlan not implemented")
}
func (UnimplementedNSPlanServiceServer) DeleteNSPlan(context.Context, *DeleteNSPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSPlan not implemented")
}

// UnsafeNSPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSPlanServiceServer will
// result in compilation errors.
type UnsafeNSPlanServiceServer interface {
	mustEmbedUnimplementedNSPlanServiceServer()
}

func RegisterNSPlanServiceServer(s grpc.ServiceRegistrar, srv NSPlanServiceServer) {
	s.RegisterService(&NSPlanService_ServiceDesc, srv)
}

func _NSPlanService_CreateNSPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).CreateNSPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_CreateNSPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).CreateNSPlan(ctx, req.(*CreateNSPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSPlanService_UpdateNSPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).UpdateNSPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_UpdateNSPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).UpdateNSPlan(ctx, req.(*UpdateNSPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSPlanService_SortNSPlanOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortNSPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).SortNSPlanOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_SortNSPlanOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).SortNSPlanOrders(ctx, req.(*SortNSPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSPlanService_FindAllNSPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNSPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).FindAllNSPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_FindAllNSPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).FindAllNSPlans(ctx, req.(*FindAllNSPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSPlanService_FindAllEnabledNSPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNSPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).FindAllEnabledNSPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_FindAllEnabledNSPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).FindAllEnabledNSPlans(ctx, req.(*FindAllEnabledNSPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSPlanService_FindNSPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).FindNSPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_FindNSPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).FindNSPlan(ctx, req.(*FindNSPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSPlanService_DeleteNSPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSPlanServiceServer).DeleteNSPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSPlanService_DeleteNSPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSPlanServiceServer).DeleteNSPlan(ctx, req.(*DeleteNSPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSPlanService_ServiceDesc is the grpc.ServiceDesc for NSPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSPlanService",
	HandlerType: (*NSPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSPlan",
			Handler:    _NSPlanService_CreateNSPlan_Handler,
		},
		{
			MethodName: "updateNSPlan",
			Handler:    _NSPlanService_UpdateNSPlan_Handler,
		},
		{
			MethodName: "sortNSPlanOrders",
			Handler:    _NSPlanService_SortNSPlanOrders_Handler,
		},
		{
			MethodName: "findAllNSPlans",
			Handler:    _NSPlanService_FindAllNSPlans_Handler,
		},
		{
			MethodName: "findAllEnabledNSPlans",
			Handler:    _NSPlanService_FindAllEnabledNSPlans_Handler,
		},
		{
			MethodName: "findNSPlan",
			Handler:    _NSPlanService_FindNSPlan_Handler,
		},
		{
			MethodName: "deleteNSPlan",
			Handler:    _NSPlanService_DeleteNSPlan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_plan.proto",
}
