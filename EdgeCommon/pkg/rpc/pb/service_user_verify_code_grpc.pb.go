// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_verify_code.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserVerifyCodeService_SendUserVerifyCode_FullMethodName     = "/pb.UserVerifyCodeService/sendUserVerifyCode"
	UserVerifyCodeService_ValidateUserVerifyCode_FullMethodName = "/pb.UserVerifyCodeService/validateUserVerifyCode"
)

// UserVerifyCodeServiceClient is the client API for UserVerifyCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserVerifyCodeServiceClient interface {
	// 发送重置密码验证码
	SendUserVerifyCode(ctx context.Context, in *SendUserVerifyCodeRequest, opts ...grpc.CallOption) (*SendUserVerifyCodeResponse, error)
	// 校验验证码
	ValidateUserVerifyCode(ctx context.Context, in *ValidateUserVerifyCodeRequest, opts ...grpc.CallOption) (*ValidateUserVerifyCodeResponse, error)
}

type userVerifyCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserVerifyCodeServiceClient(cc grpc.ClientConnInterface) UserVerifyCodeServiceClient {
	return &userVerifyCodeServiceClient{cc}
}

func (c *userVerifyCodeServiceClient) SendUserVerifyCode(ctx context.Context, in *SendUserVerifyCodeRequest, opts ...grpc.CallOption) (*SendUserVerifyCodeResponse, error) {
	out := new(SendUserVerifyCodeResponse)
	err := c.cc.Invoke(ctx, UserVerifyCodeService_SendUserVerifyCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userVerifyCodeServiceClient) ValidateUserVerifyCode(ctx context.Context, in *ValidateUserVerifyCodeRequest, opts ...grpc.CallOption) (*ValidateUserVerifyCodeResponse, error) {
	out := new(ValidateUserVerifyCodeResponse)
	err := c.cc.Invoke(ctx, UserVerifyCodeService_ValidateUserVerifyCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserVerifyCodeServiceServer is the server API for UserVerifyCodeService service.
// All implementations should embed UnimplementedUserVerifyCodeServiceServer
// for forward compatibility
type UserVerifyCodeServiceServer interface {
	// 发送重置密码验证码
	SendUserVerifyCode(context.Context, *SendUserVerifyCodeRequest) (*SendUserVerifyCodeResponse, error)
	// 校验验证码
	ValidateUserVerifyCode(context.Context, *ValidateUserVerifyCodeRequest) (*ValidateUserVerifyCodeResponse, error)
}

// UnimplementedUserVerifyCodeServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserVerifyCodeServiceServer struct {
}

func (UnimplementedUserVerifyCodeServiceServer) SendUserVerifyCode(context.Context, *SendUserVerifyCodeRequest) (*SendUserVerifyCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendUserVerifyCode not implemented")
}
func (UnimplementedUserVerifyCodeServiceServer) ValidateUserVerifyCode(context.Context, *ValidateUserVerifyCodeRequest) (*ValidateUserVerifyCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateUserVerifyCode not implemented")
}

// UnsafeUserVerifyCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserVerifyCodeServiceServer will
// result in compilation errors.
type UnsafeUserVerifyCodeServiceServer interface {
	mustEmbedUnimplementedUserVerifyCodeServiceServer()
}

func RegisterUserVerifyCodeServiceServer(s grpc.ServiceRegistrar, srv UserVerifyCodeServiceServer) {
	s.RegisterService(&UserVerifyCodeService_ServiceDesc, srv)
}

func _UserVerifyCodeService_SendUserVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendUserVerifyCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserVerifyCodeServiceServer).SendUserVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserVerifyCodeService_SendUserVerifyCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserVerifyCodeServiceServer).SendUserVerifyCode(ctx, req.(*SendUserVerifyCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserVerifyCodeService_ValidateUserVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateUserVerifyCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserVerifyCodeServiceServer).ValidateUserVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserVerifyCodeService_ValidateUserVerifyCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserVerifyCodeServiceServer).ValidateUserVerifyCode(ctx, req.(*ValidateUserVerifyCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserVerifyCodeService_ServiceDesc is the grpc.ServiceDesc for UserVerifyCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserVerifyCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserVerifyCodeService",
	HandlerType: (*UserVerifyCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "sendUserVerifyCode",
			Handler:    _UserVerifyCodeService_SendUserVerifyCode_Handler,
		},
		{
			MethodName: "validateUserVerifyCode",
			Handler:    _UserVerifyCodeService_ValidateUserVerifyCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_verify_code.proto",
}
