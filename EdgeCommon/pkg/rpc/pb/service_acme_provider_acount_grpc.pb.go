// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_acme_provider_acount.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ACMEProviderAccountService_CreateACMEProviderAccount_FullMethodName                   = "/pb.ACMEProviderAccountService/createACMEProviderAccount"
	ACMEProviderAccountService_FindAllACMEProviderAccountsWithProviderCode_FullMethodName = "/pb.ACMEProviderAccountService/findAllACMEProviderAccountsWithProviderCode"
	ACMEProviderAccountService_UpdateACMEProviderAccount_FullMethodName                   = "/pb.ACMEProviderAccountService/updateACMEProviderAccount"
	ACMEProviderAccountService_DeleteACMEProviderAccount_FullMethodName                   = "/pb.ACMEProviderAccountService/deleteACMEProviderAccount"
	ACMEProviderAccountService_FindEnabledACMEProviderAccount_FullMethodName              = "/pb.ACMEProviderAccountService/findEnabledACMEProviderAccount"
	ACMEProviderAccountService_CountAllEnabledACMEProviderAccounts_FullMethodName         = "/pb.ACMEProviderAccountService/countAllEnabledACMEProviderAccounts"
	ACMEProviderAccountService_ListEnabledACMEProviderAccounts_FullMethodName             = "/pb.ACMEProviderAccountService/listEnabledACMEProviderAccounts"
)

// ACMEProviderAccountServiceClient is the client API for ACMEProviderAccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ACMEProviderAccountServiceClient interface {
	// 创建服务商账号
	CreateACMEProviderAccount(ctx context.Context, in *CreateACMEProviderAccountRequest, opts ...grpc.CallOption) (*CreateACMEProviderAccountResponse, error)
	// 使用代号查找服务商账号
	FindAllACMEProviderAccountsWithProviderCode(ctx context.Context, in *FindAllACMEProviderAccountsWithProviderCodeRequest, opts ...grpc.CallOption) (*FindAllACMEProviderAccountsWithProviderCodeResponse, error)
	// 修改服务商账号
	UpdateACMEProviderAccount(ctx context.Context, in *UpdateACMEProviderAccountRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除服务商账号
	DeleteACMEProviderAccount(ctx context.Context, in *DeleteACMEProviderAccountRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个服务商账号
	FindEnabledACMEProviderAccount(ctx context.Context, in *FindEnabledACMEProviderAccountRequest, opts ...grpc.CallOption) (*FindEnabledACMEProviderAccountResponse, error)
	// 计算所有服务商账号数量
	CountAllEnabledACMEProviderAccounts(ctx context.Context, in *CountAllEnabledACMEProviderAccountsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页服务商账号
	ListEnabledACMEProviderAccounts(ctx context.Context, in *ListEnabledACMEProviderAccountsRequest, opts ...grpc.CallOption) (*ListEnabledACMEProviderAccountsResponse, error)
}

type aCMEProviderAccountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewACMEProviderAccountServiceClient(cc grpc.ClientConnInterface) ACMEProviderAccountServiceClient {
	return &aCMEProviderAccountServiceClient{cc}
}

func (c *aCMEProviderAccountServiceClient) CreateACMEProviderAccount(ctx context.Context, in *CreateACMEProviderAccountRequest, opts ...grpc.CallOption) (*CreateACMEProviderAccountResponse, error) {
	out := new(CreateACMEProviderAccountResponse)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_CreateACMEProviderAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEProviderAccountServiceClient) FindAllACMEProviderAccountsWithProviderCode(ctx context.Context, in *FindAllACMEProviderAccountsWithProviderCodeRequest, opts ...grpc.CallOption) (*FindAllACMEProviderAccountsWithProviderCodeResponse, error) {
	out := new(FindAllACMEProviderAccountsWithProviderCodeResponse)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_FindAllACMEProviderAccountsWithProviderCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEProviderAccountServiceClient) UpdateACMEProviderAccount(ctx context.Context, in *UpdateACMEProviderAccountRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_UpdateACMEProviderAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEProviderAccountServiceClient) DeleteACMEProviderAccount(ctx context.Context, in *DeleteACMEProviderAccountRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_DeleteACMEProviderAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEProviderAccountServiceClient) FindEnabledACMEProviderAccount(ctx context.Context, in *FindEnabledACMEProviderAccountRequest, opts ...grpc.CallOption) (*FindEnabledACMEProviderAccountResponse, error) {
	out := new(FindEnabledACMEProviderAccountResponse)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_FindEnabledACMEProviderAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEProviderAccountServiceClient) CountAllEnabledACMEProviderAccounts(ctx context.Context, in *CountAllEnabledACMEProviderAccountsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_CountAllEnabledACMEProviderAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCMEProviderAccountServiceClient) ListEnabledACMEProviderAccounts(ctx context.Context, in *ListEnabledACMEProviderAccountsRequest, opts ...grpc.CallOption) (*ListEnabledACMEProviderAccountsResponse, error) {
	out := new(ListEnabledACMEProviderAccountsResponse)
	err := c.cc.Invoke(ctx, ACMEProviderAccountService_ListEnabledACMEProviderAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ACMEProviderAccountServiceServer is the server API for ACMEProviderAccountService service.
// All implementations should embed UnimplementedACMEProviderAccountServiceServer
// for forward compatibility
type ACMEProviderAccountServiceServer interface {
	// 创建服务商账号
	CreateACMEProviderAccount(context.Context, *CreateACMEProviderAccountRequest) (*CreateACMEProviderAccountResponse, error)
	// 使用代号查找服务商账号
	FindAllACMEProviderAccountsWithProviderCode(context.Context, *FindAllACMEProviderAccountsWithProviderCodeRequest) (*FindAllACMEProviderAccountsWithProviderCodeResponse, error)
	// 修改服务商账号
	UpdateACMEProviderAccount(context.Context, *UpdateACMEProviderAccountRequest) (*RPCSuccess, error)
	// 删除服务商账号
	DeleteACMEProviderAccount(context.Context, *DeleteACMEProviderAccountRequest) (*RPCSuccess, error)
	// 查找单个服务商账号
	FindEnabledACMEProviderAccount(context.Context, *FindEnabledACMEProviderAccountRequest) (*FindEnabledACMEProviderAccountResponse, error)
	// 计算所有服务商账号数量
	CountAllEnabledACMEProviderAccounts(context.Context, *CountAllEnabledACMEProviderAccountsRequest) (*RPCCountResponse, error)
	// 列出单页服务商账号
	ListEnabledACMEProviderAccounts(context.Context, *ListEnabledACMEProviderAccountsRequest) (*ListEnabledACMEProviderAccountsResponse, error)
}

// UnimplementedACMEProviderAccountServiceServer should be embedded to have forward compatible implementations.
type UnimplementedACMEProviderAccountServiceServer struct {
}

func (UnimplementedACMEProviderAccountServiceServer) CreateACMEProviderAccount(context.Context, *CreateACMEProviderAccountRequest) (*CreateACMEProviderAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateACMEProviderAccount not implemented")
}
func (UnimplementedACMEProviderAccountServiceServer) FindAllACMEProviderAccountsWithProviderCode(context.Context, *FindAllACMEProviderAccountsWithProviderCodeRequest) (*FindAllACMEProviderAccountsWithProviderCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllACMEProviderAccountsWithProviderCode not implemented")
}
func (UnimplementedACMEProviderAccountServiceServer) UpdateACMEProviderAccount(context.Context, *UpdateACMEProviderAccountRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateACMEProviderAccount not implemented")
}
func (UnimplementedACMEProviderAccountServiceServer) DeleteACMEProviderAccount(context.Context, *DeleteACMEProviderAccountRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteACMEProviderAccount not implemented")
}
func (UnimplementedACMEProviderAccountServiceServer) FindEnabledACMEProviderAccount(context.Context, *FindEnabledACMEProviderAccountRequest) (*FindEnabledACMEProviderAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledACMEProviderAccount not implemented")
}
func (UnimplementedACMEProviderAccountServiceServer) CountAllEnabledACMEProviderAccounts(context.Context, *CountAllEnabledACMEProviderAccountsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledACMEProviderAccounts not implemented")
}
func (UnimplementedACMEProviderAccountServiceServer) ListEnabledACMEProviderAccounts(context.Context, *ListEnabledACMEProviderAccountsRequest) (*ListEnabledACMEProviderAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledACMEProviderAccounts not implemented")
}

// UnsafeACMEProviderAccountServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ACMEProviderAccountServiceServer will
// result in compilation errors.
type UnsafeACMEProviderAccountServiceServer interface {
	mustEmbedUnimplementedACMEProviderAccountServiceServer()
}

func RegisterACMEProviderAccountServiceServer(s grpc.ServiceRegistrar, srv ACMEProviderAccountServiceServer) {
	s.RegisterService(&ACMEProviderAccountService_ServiceDesc, srv)
}

func _ACMEProviderAccountService_CreateACMEProviderAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateACMEProviderAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).CreateACMEProviderAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_CreateACMEProviderAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).CreateACMEProviderAccount(ctx, req.(*CreateACMEProviderAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEProviderAccountService_FindAllACMEProviderAccountsWithProviderCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllACMEProviderAccountsWithProviderCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).FindAllACMEProviderAccountsWithProviderCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_FindAllACMEProviderAccountsWithProviderCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).FindAllACMEProviderAccountsWithProviderCode(ctx, req.(*FindAllACMEProviderAccountsWithProviderCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEProviderAccountService_UpdateACMEProviderAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateACMEProviderAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).UpdateACMEProviderAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_UpdateACMEProviderAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).UpdateACMEProviderAccount(ctx, req.(*UpdateACMEProviderAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEProviderAccountService_DeleteACMEProviderAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteACMEProviderAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).DeleteACMEProviderAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_DeleteACMEProviderAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).DeleteACMEProviderAccount(ctx, req.(*DeleteACMEProviderAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEProviderAccountService_FindEnabledACMEProviderAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledACMEProviderAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).FindEnabledACMEProviderAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_FindEnabledACMEProviderAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).FindEnabledACMEProviderAccount(ctx, req.(*FindEnabledACMEProviderAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEProviderAccountService_CountAllEnabledACMEProviderAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledACMEProviderAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).CountAllEnabledACMEProviderAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_CountAllEnabledACMEProviderAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).CountAllEnabledACMEProviderAccounts(ctx, req.(*CountAllEnabledACMEProviderAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACMEProviderAccountService_ListEnabledACMEProviderAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledACMEProviderAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEProviderAccountServiceServer).ListEnabledACMEProviderAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEProviderAccountService_ListEnabledACMEProviderAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEProviderAccountServiceServer).ListEnabledACMEProviderAccounts(ctx, req.(*ListEnabledACMEProviderAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ACMEProviderAccountService_ServiceDesc is the grpc.ServiceDesc for ACMEProviderAccountService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ACMEProviderAccountService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ACMEProviderAccountService",
	HandlerType: (*ACMEProviderAccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createACMEProviderAccount",
			Handler:    _ACMEProviderAccountService_CreateACMEProviderAccount_Handler,
		},
		{
			MethodName: "findAllACMEProviderAccountsWithProviderCode",
			Handler:    _ACMEProviderAccountService_FindAllACMEProviderAccountsWithProviderCode_Handler,
		},
		{
			MethodName: "updateACMEProviderAccount",
			Handler:    _ACMEProviderAccountService_UpdateACMEProviderAccount_Handler,
		},
		{
			MethodName: "deleteACMEProviderAccount",
			Handler:    _ACMEProviderAccountService_DeleteACMEProviderAccount_Handler,
		},
		{
			MethodName: "findEnabledACMEProviderAccount",
			Handler:    _ACMEProviderAccountService_FindEnabledACMEProviderAccount_Handler,
		},
		{
			MethodName: "countAllEnabledACMEProviderAccounts",
			Handler:    _ACMEProviderAccountService_CountAllEnabledACMEProviderAccounts_Handler,
		},
		{
			MethodName: "listEnabledACMEProviderAccounts",
			Handler:    _ACMEProviderAccountService_ListEnabledACMEProviderAccounts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_acme_provider_acount.proto",
}
