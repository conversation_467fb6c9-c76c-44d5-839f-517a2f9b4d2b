// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_script.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户脚本
type UserScript struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                        // 用户脚本ID
	UserId         int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`                // 用户ID
	AdminId        int64                  `protobuf:"varint,3,opt,name=adminId,proto3" json:"adminId,omitempty"`              // 审核的管理员
	Code           string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`                     // 脚本代码
	CodeMD5        string                 `protobuf:"bytes,5,opt,name=codeMD5,proto3" json:"codeMD5,omitempty"`               // 脚本代码MD5
	CreatedAt      int64                  `protobuf:"varint,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`          // 创建时间
	IsRejected     bool                   `protobuf:"varint,7,opt,name=isRejected,proto3" json:"isRejected,omitempty"`        // 是否被驳回
	RejectedAt     int64                  `protobuf:"varint,8,opt,name=rejectedAt,proto3" json:"rejectedAt,omitempty"`        // 驳回时间
	RejectedReason string                 `protobuf:"bytes,9,opt,name=rejectedReason,proto3" json:"rejectedReason,omitempty"` // 驳回原因
	IsPassed       bool                   `protobuf:"varint,10,opt,name=isPassed,proto3" json:"isPassed,omitempty"`           // 是否被通过
	PassedAt       int64                  `protobuf:"varint,11,opt,name=passedAt,proto3" json:"passedAt,omitempty"`           // 通过时间
	User           *User                  `protobuf:"bytes,30,opt,name=user,proto3" json:"user,omitempty"`                    // 用户信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserScript) Reset() {
	*x = UserScript{}
	mi := &file_models_model_user_script_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserScript) ProtoMessage() {}

func (x *UserScript) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_script_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserScript.ProtoReflect.Descriptor instead.
func (*UserScript) Descriptor() ([]byte, []int) {
	return file_models_model_user_script_proto_rawDescGZIP(), []int{0}
}

func (x *UserScript) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserScript) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserScript) GetAdminId() int64 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *UserScript) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserScript) GetCodeMD5() string {
	if x != nil {
		return x.CodeMD5
	}
	return ""
}

func (x *UserScript) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserScript) GetIsRejected() bool {
	if x != nil {
		return x.IsRejected
	}
	return false
}

func (x *UserScript) GetRejectedAt() int64 {
	if x != nil {
		return x.RejectedAt
	}
	return 0
}

func (x *UserScript) GetRejectedReason() string {
	if x != nil {
		return x.RejectedReason
	}
	return ""
}

func (x *UserScript) GetIsPassed() bool {
	if x != nil {
		return x.IsPassed
	}
	return false
}

func (x *UserScript) GetPassedAt() int64 {
	if x != nil {
		return x.PassedAt
	}
	return 0
}

func (x *UserScript) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

var File_models_model_user_script_proto protoreflect.FileDescriptor

const file_models_model_user_script_proto_rawDesc = "" +
	"\n" +
	"\x1emodels/model_user_script.proto\x12\x02pb\x1a\x17models/model_user.proto\"\xd8\x02\n" +
	"\n" +
	"UserScript\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x18\n" +
	"\aadminId\x18\x03 \x01(\x03R\aadminId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x18\n" +
	"\acodeMD5\x18\x05 \x01(\tR\acodeMD5\x12\x1c\n" +
	"\tcreatedAt\x18\x06 \x01(\x03R\tcreatedAt\x12\x1e\n" +
	"\n" +
	"isRejected\x18\a \x01(\bR\n" +
	"isRejected\x12\x1e\n" +
	"\n" +
	"rejectedAt\x18\b \x01(\x03R\n" +
	"rejectedAt\x12&\n" +
	"\x0erejectedReason\x18\t \x01(\tR\x0erejectedReason\x12\x1a\n" +
	"\bisPassed\x18\n" +
	" \x01(\bR\bisPassed\x12\x1a\n" +
	"\bpassedAt\x18\v \x01(\x03R\bpassedAt\x12\x1c\n" +
	"\x04user\x18\x1e \x01(\v2\b.pb.UserR\x04userB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_script_proto_rawDescOnce sync.Once
	file_models_model_user_script_proto_rawDescData []byte
)

func file_models_model_user_script_proto_rawDescGZIP() []byte {
	file_models_model_user_script_proto_rawDescOnce.Do(func() {
		file_models_model_user_script_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_script_proto_rawDesc), len(file_models_model_user_script_proto_rawDesc)))
	})
	return file_models_model_user_script_proto_rawDescData
}

var file_models_model_user_script_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_script_proto_goTypes = []any{
	(*UserScript)(nil), // 0: pb.UserScript
	(*User)(nil),       // 1: pb.User
}
var file_models_model_user_script_proto_depIdxs = []int32{
	1, // 0: pb.UserScript.user:type_name -> pb.User
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_user_script_proto_init() }
func file_models_model_user_script_proto_init() {
	if File_models_model_user_script_proto != nil {
		return
	}
	file_models_model_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_script_proto_rawDesc), len(file_models_model_user_script_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_script_proto_goTypes,
		DependencyIndexes: file_models_model_user_script_proto_depIdxs,
		MessageInfos:      file_models_model_user_script_proto_msgTypes,
	}.Build()
	File_models_model_user_script_proto = out.File
	file_models_model_user_script_proto_goTypes = nil
	file_models_model_user_script_proto_depIdxs = nil
}
