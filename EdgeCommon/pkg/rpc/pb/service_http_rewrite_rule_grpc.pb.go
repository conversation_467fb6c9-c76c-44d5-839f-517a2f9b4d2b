// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_rewrite_rule.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPRewriteRuleService_CreateHTTPRewriteRule_FullMethodName = "/pb.HTTPRewriteRuleService/createHTTPRewriteRule"
	HTTPRewriteRuleService_UpdateHTTPRewriteRule_FullMethodName = "/pb.HTTPRewriteRuleService/updateHTTPRewriteRule"
)

// HTTPRewriteRuleServiceClient is the client API for HTTPRewriteRuleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPRewriteRuleServiceClient interface {
	// 创建重写规则
	CreateHTTPRewriteRule(ctx context.Context, in *CreateHTTPRewriteRuleRequest, opts ...grpc.CallOption) (*CreateHTTPRewriteRuleResponse, error)
	// 修改重写规则
	UpdateHTTPRewriteRule(ctx context.Context, in *UpdateHTTPRewriteRuleRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type hTTPRewriteRuleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPRewriteRuleServiceClient(cc grpc.ClientConnInterface) HTTPRewriteRuleServiceClient {
	return &hTTPRewriteRuleServiceClient{cc}
}

func (c *hTTPRewriteRuleServiceClient) CreateHTTPRewriteRule(ctx context.Context, in *CreateHTTPRewriteRuleRequest, opts ...grpc.CallOption) (*CreateHTTPRewriteRuleResponse, error) {
	out := new(CreateHTTPRewriteRuleResponse)
	err := c.cc.Invoke(ctx, HTTPRewriteRuleService_CreateHTTPRewriteRule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPRewriteRuleServiceClient) UpdateHTTPRewriteRule(ctx context.Context, in *UpdateHTTPRewriteRuleRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPRewriteRuleService_UpdateHTTPRewriteRule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPRewriteRuleServiceServer is the server API for HTTPRewriteRuleService service.
// All implementations should embed UnimplementedHTTPRewriteRuleServiceServer
// for forward compatibility
type HTTPRewriteRuleServiceServer interface {
	// 创建重写规则
	CreateHTTPRewriteRule(context.Context, *CreateHTTPRewriteRuleRequest) (*CreateHTTPRewriteRuleResponse, error)
	// 修改重写规则
	UpdateHTTPRewriteRule(context.Context, *UpdateHTTPRewriteRuleRequest) (*RPCSuccess, error)
}

// UnimplementedHTTPRewriteRuleServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPRewriteRuleServiceServer struct {
}

func (UnimplementedHTTPRewriteRuleServiceServer) CreateHTTPRewriteRule(context.Context, *CreateHTTPRewriteRuleRequest) (*CreateHTTPRewriteRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPRewriteRule not implemented")
}
func (UnimplementedHTTPRewriteRuleServiceServer) UpdateHTTPRewriteRule(context.Context, *UpdateHTTPRewriteRuleRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPRewriteRule not implemented")
}

// UnsafeHTTPRewriteRuleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPRewriteRuleServiceServer will
// result in compilation errors.
type UnsafeHTTPRewriteRuleServiceServer interface {
	mustEmbedUnimplementedHTTPRewriteRuleServiceServer()
}

func RegisterHTTPRewriteRuleServiceServer(s grpc.ServiceRegistrar, srv HTTPRewriteRuleServiceServer) {
	s.RegisterService(&HTTPRewriteRuleService_ServiceDesc, srv)
}

func _HTTPRewriteRuleService_CreateHTTPRewriteRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPRewriteRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPRewriteRuleServiceServer).CreateHTTPRewriteRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPRewriteRuleService_CreateHTTPRewriteRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPRewriteRuleServiceServer).CreateHTTPRewriteRule(ctx, req.(*CreateHTTPRewriteRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPRewriteRuleService_UpdateHTTPRewriteRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPRewriteRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPRewriteRuleServiceServer).UpdateHTTPRewriteRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPRewriteRuleService_UpdateHTTPRewriteRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPRewriteRuleServiceServer).UpdateHTTPRewriteRule(ctx, req.(*UpdateHTTPRewriteRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPRewriteRuleService_ServiceDesc is the grpc.ServiceDesc for HTTPRewriteRuleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPRewriteRuleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPRewriteRuleService",
	HandlerType: (*HTTPRewriteRuleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHTTPRewriteRule",
			Handler:    _HTTPRewriteRuleService_CreateHTTPRewriteRule_Handler,
		},
		{
			MethodName: "updateHTTPRewriteRule",
			Handler:    _HTTPRewriteRuleService_UpdateHTTPRewriteRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_rewrite_rule.proto",
}
