// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_message_task_log.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageTaskLogService_CountMessageTaskLogs_FullMethodName = "/pb.MessageTaskLogService/countMessageTaskLogs"
	MessageTaskLogService_ListMessageTaskLogs_FullMethodName  = "/pb.MessageTaskLogService/listMessageTaskLogs"
)

// MessageTaskLogServiceClient is the client API for MessageTaskLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageTaskLogServiceClient interface {
	// 计算日志数量
	CountMessageTaskLogs(ctx context.Context, in *CountMessageTaskLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出当页日志
	ListMessageTaskLogs(ctx context.Context, in *ListMessageTaskLogsRequest, opts ...grpc.CallOption) (*ListMessageTaskLogsResponse, error)
}

type messageTaskLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageTaskLogServiceClient(cc grpc.ClientConnInterface) MessageTaskLogServiceClient {
	return &messageTaskLogServiceClient{cc}
}

func (c *messageTaskLogServiceClient) CountMessageTaskLogs(ctx context.Context, in *CountMessageTaskLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, MessageTaskLogService_CountMessageTaskLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskLogServiceClient) ListMessageTaskLogs(ctx context.Context, in *ListMessageTaskLogsRequest, opts ...grpc.CallOption) (*ListMessageTaskLogsResponse, error) {
	out := new(ListMessageTaskLogsResponse)
	err := c.cc.Invoke(ctx, MessageTaskLogService_ListMessageTaskLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageTaskLogServiceServer is the server API for MessageTaskLogService service.
// All implementations should embed UnimplementedMessageTaskLogServiceServer
// for forward compatibility
type MessageTaskLogServiceServer interface {
	// 计算日志数量
	CountMessageTaskLogs(context.Context, *CountMessageTaskLogsRequest) (*RPCCountResponse, error)
	// 列出当页日志
	ListMessageTaskLogs(context.Context, *ListMessageTaskLogsRequest) (*ListMessageTaskLogsResponse, error)
}

// UnimplementedMessageTaskLogServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMessageTaskLogServiceServer struct {
}

func (UnimplementedMessageTaskLogServiceServer) CountMessageTaskLogs(context.Context, *CountMessageTaskLogsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountMessageTaskLogs not implemented")
}
func (UnimplementedMessageTaskLogServiceServer) ListMessageTaskLogs(context.Context, *ListMessageTaskLogsRequest) (*ListMessageTaskLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMessageTaskLogs not implemented")
}

// UnsafeMessageTaskLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageTaskLogServiceServer will
// result in compilation errors.
type UnsafeMessageTaskLogServiceServer interface {
	mustEmbedUnimplementedMessageTaskLogServiceServer()
}

func RegisterMessageTaskLogServiceServer(s grpc.ServiceRegistrar, srv MessageTaskLogServiceServer) {
	s.RegisterService(&MessageTaskLogService_ServiceDesc, srv)
}

func _MessageTaskLogService_CountMessageTaskLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountMessageTaskLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskLogServiceServer).CountMessageTaskLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskLogService_CountMessageTaskLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskLogServiceServer).CountMessageTaskLogs(ctx, req.(*CountMessageTaskLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskLogService_ListMessageTaskLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMessageTaskLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskLogServiceServer).ListMessageTaskLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskLogService_ListMessageTaskLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskLogServiceServer).ListMessageTaskLogs(ctx, req.(*ListMessageTaskLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageTaskLogService_ServiceDesc is the grpc.ServiceDesc for MessageTaskLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageTaskLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MessageTaskLogService",
	HandlerType: (*MessageTaskLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countMessageTaskLogs",
			Handler:    _MessageTaskLogService_CountMessageTaskLogs_Handler,
		},
		{
			MethodName: "listMessageTaskLogs",
			Handler:    _MessageTaskLogService_ListMessageTaskLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_message_task_log.proto",
}
