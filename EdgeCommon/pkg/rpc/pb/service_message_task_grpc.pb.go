// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_message_task.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MessageTaskService_CreateMessageTask_FullMethodName           = "/pb.MessageTaskService/createMessageTask"
	MessageTaskService_DeleteMessageTask_FullMethodName           = "/pb.MessageTaskService/deleteMessageTask"
	MessageTaskService_FindEnabledMessageTask_FullMethodName      = "/pb.MessageTaskService/findEnabledMessageTask"
	MessageTaskService_CountMessageTasksWithStatus_FullMethodName = "/pb.MessageTaskService/countMessageTasksWithStatus"
	MessageTaskService_ListMessageTasksWithStatus_FullMethodName  = "/pb.MessageTaskService/listMessageTasksWithStatus"
	MessageTaskService_SendMessageTask_FullMethodName             = "/pb.MessageTaskService/sendMessageTask"
	MessageTaskService_UpdateMessageTaskStatus_FullMethodName     = "/pb.MessageTaskService/updateMessageTaskStatus"
)

// MessageTaskServiceClient is the client API for MessageTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MessageTaskServiceClient interface {
	// 创建消息任务
	CreateMessageTask(ctx context.Context, in *CreateMessageTaskRequest, opts ...grpc.CallOption) (*CreateMessageTaskResponse, error)
	// 删除消息任务
	DeleteMessageTask(ctx context.Context, in *DeleteMessageTaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取消息任务状态
	FindEnabledMessageTask(ctx context.Context, in *FindEnabledMessageTaskRequest, opts ...grpc.CallOption) (*FindEnabledMessageTaskResponse, error)
	// 计算某个状态的消息任务数量
	CountMessageTasksWithStatus(ctx context.Context, in *CountMessageTasksWithStatusRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 根据状态列出某页任务
	ListMessageTasksWithStatus(ctx context.Context, in *ListMessageTasksWithStatusRequest, opts ...grpc.CallOption) (*ListMessageTasksWithStatusResponse, error)
	// 发送某个消息任务
	SendMessageTask(ctx context.Context, in *SendMessageTaskRequest, opts ...grpc.CallOption) (*SendMessageTaskResponse, error)
	// 修改消息任务状态
	UpdateMessageTaskStatus(ctx context.Context, in *UpdateMessageTaskStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type messageTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageTaskServiceClient(cc grpc.ClientConnInterface) MessageTaskServiceClient {
	return &messageTaskServiceClient{cc}
}

func (c *messageTaskServiceClient) CreateMessageTask(ctx context.Context, in *CreateMessageTaskRequest, opts ...grpc.CallOption) (*CreateMessageTaskResponse, error) {
	out := new(CreateMessageTaskResponse)
	err := c.cc.Invoke(ctx, MessageTaskService_CreateMessageTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskServiceClient) DeleteMessageTask(ctx context.Context, in *DeleteMessageTaskRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageTaskService_DeleteMessageTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskServiceClient) FindEnabledMessageTask(ctx context.Context, in *FindEnabledMessageTaskRequest, opts ...grpc.CallOption) (*FindEnabledMessageTaskResponse, error) {
	out := new(FindEnabledMessageTaskResponse)
	err := c.cc.Invoke(ctx, MessageTaskService_FindEnabledMessageTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskServiceClient) CountMessageTasksWithStatus(ctx context.Context, in *CountMessageTasksWithStatusRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, MessageTaskService_CountMessageTasksWithStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskServiceClient) ListMessageTasksWithStatus(ctx context.Context, in *ListMessageTasksWithStatusRequest, opts ...grpc.CallOption) (*ListMessageTasksWithStatusResponse, error) {
	out := new(ListMessageTasksWithStatusResponse)
	err := c.cc.Invoke(ctx, MessageTaskService_ListMessageTasksWithStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskServiceClient) SendMessageTask(ctx context.Context, in *SendMessageTaskRequest, opts ...grpc.CallOption) (*SendMessageTaskResponse, error) {
	out := new(SendMessageTaskResponse)
	err := c.cc.Invoke(ctx, MessageTaskService_SendMessageTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageTaskServiceClient) UpdateMessageTaskStatus(ctx context.Context, in *UpdateMessageTaskStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, MessageTaskService_UpdateMessageTaskStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageTaskServiceServer is the server API for MessageTaskService service.
// All implementations should embed UnimplementedMessageTaskServiceServer
// for forward compatibility
type MessageTaskServiceServer interface {
	// 创建消息任务
	CreateMessageTask(context.Context, *CreateMessageTaskRequest) (*CreateMessageTaskResponse, error)
	// 删除消息任务
	DeleteMessageTask(context.Context, *DeleteMessageTaskRequest) (*RPCSuccess, error)
	// 读取消息任务状态
	FindEnabledMessageTask(context.Context, *FindEnabledMessageTaskRequest) (*FindEnabledMessageTaskResponse, error)
	// 计算某个状态的消息任务数量
	CountMessageTasksWithStatus(context.Context, *CountMessageTasksWithStatusRequest) (*RPCCountResponse, error)
	// 根据状态列出某页任务
	ListMessageTasksWithStatus(context.Context, *ListMessageTasksWithStatusRequest) (*ListMessageTasksWithStatusResponse, error)
	// 发送某个消息任务
	SendMessageTask(context.Context, *SendMessageTaskRequest) (*SendMessageTaskResponse, error)
	// 修改消息任务状态
	UpdateMessageTaskStatus(context.Context, *UpdateMessageTaskStatusRequest) (*RPCSuccess, error)
}

// UnimplementedMessageTaskServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMessageTaskServiceServer struct {
}

func (UnimplementedMessageTaskServiceServer) CreateMessageTask(context.Context, *CreateMessageTaskRequest) (*CreateMessageTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMessageTask not implemented")
}
func (UnimplementedMessageTaskServiceServer) DeleteMessageTask(context.Context, *DeleteMessageTaskRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMessageTask not implemented")
}
func (UnimplementedMessageTaskServiceServer) FindEnabledMessageTask(context.Context, *FindEnabledMessageTaskRequest) (*FindEnabledMessageTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledMessageTask not implemented")
}
func (UnimplementedMessageTaskServiceServer) CountMessageTasksWithStatus(context.Context, *CountMessageTasksWithStatusRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountMessageTasksWithStatus not implemented")
}
func (UnimplementedMessageTaskServiceServer) ListMessageTasksWithStatus(context.Context, *ListMessageTasksWithStatusRequest) (*ListMessageTasksWithStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMessageTasksWithStatus not implemented")
}
func (UnimplementedMessageTaskServiceServer) SendMessageTask(context.Context, *SendMessageTaskRequest) (*SendMessageTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessageTask not implemented")
}
func (UnimplementedMessageTaskServiceServer) UpdateMessageTaskStatus(context.Context, *UpdateMessageTaskStatusRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMessageTaskStatus not implemented")
}

// UnsafeMessageTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageTaskServiceServer will
// result in compilation errors.
type UnsafeMessageTaskServiceServer interface {
	mustEmbedUnimplementedMessageTaskServiceServer()
}

func RegisterMessageTaskServiceServer(s grpc.ServiceRegistrar, srv MessageTaskServiceServer) {
	s.RegisterService(&MessageTaskService_ServiceDesc, srv)
}

func _MessageTaskService_CreateMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).CreateMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_CreateMessageTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).CreateMessageTask(ctx, req.(*CreateMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskService_DeleteMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).DeleteMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_DeleteMessageTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).DeleteMessageTask(ctx, req.(*DeleteMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskService_FindEnabledMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).FindEnabledMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_FindEnabledMessageTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).FindEnabledMessageTask(ctx, req.(*FindEnabledMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskService_CountMessageTasksWithStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountMessageTasksWithStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).CountMessageTasksWithStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_CountMessageTasksWithStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).CountMessageTasksWithStatus(ctx, req.(*CountMessageTasksWithStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskService_ListMessageTasksWithStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMessageTasksWithStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).ListMessageTasksWithStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_ListMessageTasksWithStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).ListMessageTasksWithStatus(ctx, req.(*ListMessageTasksWithStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskService_SendMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).SendMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_SendMessageTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).SendMessageTask(ctx, req.(*SendMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageTaskService_UpdateMessageTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMessageTaskStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageTaskServiceServer).UpdateMessageTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageTaskService_UpdateMessageTaskStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageTaskServiceServer).UpdateMessageTaskStatus(ctx, req.(*UpdateMessageTaskStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageTaskService_ServiceDesc is the grpc.ServiceDesc for MessageTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.MessageTaskService",
	HandlerType: (*MessageTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createMessageTask",
			Handler:    _MessageTaskService_CreateMessageTask_Handler,
		},
		{
			MethodName: "deleteMessageTask",
			Handler:    _MessageTaskService_DeleteMessageTask_Handler,
		},
		{
			MethodName: "findEnabledMessageTask",
			Handler:    _MessageTaskService_FindEnabledMessageTask_Handler,
		},
		{
			MethodName: "countMessageTasksWithStatus",
			Handler:    _MessageTaskService_CountMessageTasksWithStatus_Handler,
		},
		{
			MethodName: "listMessageTasksWithStatus",
			Handler:    _MessageTaskService_ListMessageTasksWithStatus_Handler,
		},
		{
			MethodName: "sendMessageTask",
			Handler:    _MessageTaskService_SendMessageTask_Handler,
		},
		{
			MethodName: "updateMessageTaskStatus",
			Handler:    _MessageTaskService_UpdateMessageTaskStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_message_task.proto",
}
