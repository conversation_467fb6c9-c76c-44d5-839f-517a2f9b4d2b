// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: api_method_stat_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	APIMethodStatService_FindAPIMethodStatsWithDay_FullMethodName  = "/pb.APIMethodStatService/findAPIMethodStatsWithDay"
	APIMethodStatService_CountAPIMethodStatsWithDay_FullMethodName = "/pb.APIMethodStatService/countAPIMethodStatsWithDay"
)

// APIMethodStatServiceClient is the client API for APIMethodStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type APIMethodStatServiceClient interface {
	// 查找某天的统计
	FindAPIMethodStatsWithDay(ctx context.Context, in *FindAPIMethodStatsWithDayRequest, opts ...grpc.CallOption) (*FindAPIMethodStatsWithDayResponse, error)
	// 检查是否有统计数据
	CountAPIMethodStatsWithDay(ctx context.Context, in *CountAPIMethodStatsWithDayRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
}

type aPIMethodStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAPIMethodStatServiceClient(cc grpc.ClientConnInterface) APIMethodStatServiceClient {
	return &aPIMethodStatServiceClient{cc}
}

func (c *aPIMethodStatServiceClient) FindAPIMethodStatsWithDay(ctx context.Context, in *FindAPIMethodStatsWithDayRequest, opts ...grpc.CallOption) (*FindAPIMethodStatsWithDayResponse, error) {
	out := new(FindAPIMethodStatsWithDayResponse)
	err := c.cc.Invoke(ctx, APIMethodStatService_FindAPIMethodStatsWithDay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPIMethodStatServiceClient) CountAPIMethodStatsWithDay(ctx context.Context, in *CountAPIMethodStatsWithDayRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, APIMethodStatService_CountAPIMethodStatsWithDay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// APIMethodStatServiceServer is the server API for APIMethodStatService service.
// All implementations should embed UnimplementedAPIMethodStatServiceServer
// for forward compatibility
type APIMethodStatServiceServer interface {
	// 查找某天的统计
	FindAPIMethodStatsWithDay(context.Context, *FindAPIMethodStatsWithDayRequest) (*FindAPIMethodStatsWithDayResponse, error)
	// 检查是否有统计数据
	CountAPIMethodStatsWithDay(context.Context, *CountAPIMethodStatsWithDayRequest) (*RPCCountResponse, error)
}

// UnimplementedAPIMethodStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedAPIMethodStatServiceServer struct {
}

func (UnimplementedAPIMethodStatServiceServer) FindAPIMethodStatsWithDay(context.Context, *FindAPIMethodStatsWithDayRequest) (*FindAPIMethodStatsWithDayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAPIMethodStatsWithDay not implemented")
}
func (UnimplementedAPIMethodStatServiceServer) CountAPIMethodStatsWithDay(context.Context, *CountAPIMethodStatsWithDayRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAPIMethodStatsWithDay not implemented")
}

// UnsafeAPIMethodStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to APIMethodStatServiceServer will
// result in compilation errors.
type UnsafeAPIMethodStatServiceServer interface {
	mustEmbedUnimplementedAPIMethodStatServiceServer()
}

func RegisterAPIMethodStatServiceServer(s grpc.ServiceRegistrar, srv APIMethodStatServiceServer) {
	s.RegisterService(&APIMethodStatService_ServiceDesc, srv)
}

func _APIMethodStatService_FindAPIMethodStatsWithDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAPIMethodStatsWithDayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APIMethodStatServiceServer).FindAPIMethodStatsWithDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APIMethodStatService_FindAPIMethodStatsWithDay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APIMethodStatServiceServer).FindAPIMethodStatsWithDay(ctx, req.(*FindAPIMethodStatsWithDayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APIMethodStatService_CountAPIMethodStatsWithDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAPIMethodStatsWithDayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APIMethodStatServiceServer).CountAPIMethodStatsWithDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APIMethodStatService_CountAPIMethodStatsWithDay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APIMethodStatServiceServer).CountAPIMethodStatsWithDay(ctx, req.(*CountAPIMethodStatsWithDayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// APIMethodStatService_ServiceDesc is the grpc.ServiceDesc for APIMethodStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var APIMethodStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.APIMethodStatService",
	HandlerType: (*APIMethodStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAPIMethodStatsWithDay",
			Handler:    _APIMethodStatService_FindAPIMethodStatsWithDay_Handler,
		},
		{
			MethodName: "countAPIMethodStatsWithDay",
			Handler:    _APIMethodStatService_CountAPIMethodStatsWithDay_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api_method_stat_service.proto",
}
