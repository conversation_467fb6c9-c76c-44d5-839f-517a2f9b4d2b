// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_access_log_policy.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPAccessLogPolicyService_CountAllHTTPAccessLogPolicies_FullMethodName = "/pb.HTTPAccessLogPolicyService/countAllHTTPAccessLogPolicies"
	HTTPAccessLogPolicyService_ListHTTPAccessLogPolicies_FullMethodName     = "/pb.HTTPAccessLogPolicyService/listHTTPAccessLogPolicies"
	HTTPAccessLogPolicyService_CreateHTTPAccessLogPolicy_FullMethodName     = "/pb.HTTPAccessLogPolicyService/createHTTPAccessLogPolicy"
	HTTPAccessLogPolicyService_UpdateHTTPAccessLogPolicy_FullMethodName     = "/pb.HTTPAccessLogPolicyService/updateHTTPAccessLogPolicy"
	HTTPAccessLogPolicyService_FindHTTPAccessLogPolicy_FullMethodName       = "/pb.HTTPAccessLogPolicyService/findHTTPAccessLogPolicy"
	HTTPAccessLogPolicyService_DeleteHTTPAccessLogPolicy_FullMethodName     = "/pb.HTTPAccessLogPolicyService/deleteHTTPAccessLogPolicy"
	HTTPAccessLogPolicyService_WriteHTTPAccessLogPolicy_FullMethodName      = "/pb.HTTPAccessLogPolicyService/writeHTTPAccessLogPolicy"
)

// HTTPAccessLogPolicyServiceClient is the client API for HTTPAccessLogPolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPAccessLogPolicyServiceClient interface {
	// 计算访问日志策略数量
	CountAllHTTPAccessLogPolicies(ctx context.Context, in *CountAllHTTPAccessLogPoliciesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页访问日志策略
	ListHTTPAccessLogPolicies(ctx context.Context, in *ListHTTPAccessLogPoliciesRequest, opts ...grpc.CallOption) (*ListHTTPAccessLogPoliciesResponse, error)
	// 创建访问日志策略
	CreateHTTPAccessLogPolicy(ctx context.Context, in *CreateHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*CreateHTTPAccessLogPolicyResponse, error)
	// 修改访问日志策略
	UpdateHTTPAccessLogPolicy(ctx context.Context, in *UpdateHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个访问日志策略
	FindHTTPAccessLogPolicy(ctx context.Context, in *FindHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*FindHTTPAccessLogPolicyResponse, error)
	// 删除访问日志策略
	DeleteHTTPAccessLogPolicy(ctx context.Context, in *DeleteHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 测试写入某个访问日志策略
	WriteHTTPAccessLogPolicy(ctx context.Context, in *WriteHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type hTTPAccessLogPolicyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPAccessLogPolicyServiceClient(cc grpc.ClientConnInterface) HTTPAccessLogPolicyServiceClient {
	return &hTTPAccessLogPolicyServiceClient{cc}
}

func (c *hTTPAccessLogPolicyServiceClient) CountAllHTTPAccessLogPolicies(ctx context.Context, in *CountAllHTTPAccessLogPoliciesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_CountAllHTTPAccessLogPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAccessLogPolicyServiceClient) ListHTTPAccessLogPolicies(ctx context.Context, in *ListHTTPAccessLogPoliciesRequest, opts ...grpc.CallOption) (*ListHTTPAccessLogPoliciesResponse, error) {
	out := new(ListHTTPAccessLogPoliciesResponse)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_ListHTTPAccessLogPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAccessLogPolicyServiceClient) CreateHTTPAccessLogPolicy(ctx context.Context, in *CreateHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*CreateHTTPAccessLogPolicyResponse, error) {
	out := new(CreateHTTPAccessLogPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_CreateHTTPAccessLogPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAccessLogPolicyServiceClient) UpdateHTTPAccessLogPolicy(ctx context.Context, in *UpdateHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_UpdateHTTPAccessLogPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAccessLogPolicyServiceClient) FindHTTPAccessLogPolicy(ctx context.Context, in *FindHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*FindHTTPAccessLogPolicyResponse, error) {
	out := new(FindHTTPAccessLogPolicyResponse)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_FindHTTPAccessLogPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAccessLogPolicyServiceClient) DeleteHTTPAccessLogPolicy(ctx context.Context, in *DeleteHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_DeleteHTTPAccessLogPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPAccessLogPolicyServiceClient) WriteHTTPAccessLogPolicy(ctx context.Context, in *WriteHTTPAccessLogPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPAccessLogPolicyService_WriteHTTPAccessLogPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPAccessLogPolicyServiceServer is the server API for HTTPAccessLogPolicyService service.
// All implementations should embed UnimplementedHTTPAccessLogPolicyServiceServer
// for forward compatibility
type HTTPAccessLogPolicyServiceServer interface {
	// 计算访问日志策略数量
	CountAllHTTPAccessLogPolicies(context.Context, *CountAllHTTPAccessLogPoliciesRequest) (*RPCCountResponse, error)
	// 列出单页访问日志策略
	ListHTTPAccessLogPolicies(context.Context, *ListHTTPAccessLogPoliciesRequest) (*ListHTTPAccessLogPoliciesResponse, error)
	// 创建访问日志策略
	CreateHTTPAccessLogPolicy(context.Context, *CreateHTTPAccessLogPolicyRequest) (*CreateHTTPAccessLogPolicyResponse, error)
	// 修改访问日志策略
	UpdateHTTPAccessLogPolicy(context.Context, *UpdateHTTPAccessLogPolicyRequest) (*RPCSuccess, error)
	// 查找单个访问日志策略
	FindHTTPAccessLogPolicy(context.Context, *FindHTTPAccessLogPolicyRequest) (*FindHTTPAccessLogPolicyResponse, error)
	// 删除访问日志策略
	DeleteHTTPAccessLogPolicy(context.Context, *DeleteHTTPAccessLogPolicyRequest) (*RPCSuccess, error)
	// 测试写入某个访问日志策略
	WriteHTTPAccessLogPolicy(context.Context, *WriteHTTPAccessLogPolicyRequest) (*RPCSuccess, error)
}

// UnimplementedHTTPAccessLogPolicyServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPAccessLogPolicyServiceServer struct {
}

func (UnimplementedHTTPAccessLogPolicyServiceServer) CountAllHTTPAccessLogPolicies(context.Context, *CountAllHTTPAccessLogPoliciesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllHTTPAccessLogPolicies not implemented")
}
func (UnimplementedHTTPAccessLogPolicyServiceServer) ListHTTPAccessLogPolicies(context.Context, *ListHTTPAccessLogPoliciesRequest) (*ListHTTPAccessLogPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHTTPAccessLogPolicies not implemented")
}
func (UnimplementedHTTPAccessLogPolicyServiceServer) CreateHTTPAccessLogPolicy(context.Context, *CreateHTTPAccessLogPolicyRequest) (*CreateHTTPAccessLogPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPAccessLogPolicy not implemented")
}
func (UnimplementedHTTPAccessLogPolicyServiceServer) UpdateHTTPAccessLogPolicy(context.Context, *UpdateHTTPAccessLogPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPAccessLogPolicy not implemented")
}
func (UnimplementedHTTPAccessLogPolicyServiceServer) FindHTTPAccessLogPolicy(context.Context, *FindHTTPAccessLogPolicyRequest) (*FindHTTPAccessLogPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHTTPAccessLogPolicy not implemented")
}
func (UnimplementedHTTPAccessLogPolicyServiceServer) DeleteHTTPAccessLogPolicy(context.Context, *DeleteHTTPAccessLogPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHTTPAccessLogPolicy not implemented")
}
func (UnimplementedHTTPAccessLogPolicyServiceServer) WriteHTTPAccessLogPolicy(context.Context, *WriteHTTPAccessLogPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteHTTPAccessLogPolicy not implemented")
}

// UnsafeHTTPAccessLogPolicyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPAccessLogPolicyServiceServer will
// result in compilation errors.
type UnsafeHTTPAccessLogPolicyServiceServer interface {
	mustEmbedUnimplementedHTTPAccessLogPolicyServiceServer()
}

func RegisterHTTPAccessLogPolicyServiceServer(s grpc.ServiceRegistrar, srv HTTPAccessLogPolicyServiceServer) {
	s.RegisterService(&HTTPAccessLogPolicyService_ServiceDesc, srv)
}

func _HTTPAccessLogPolicyService_CountAllHTTPAccessLogPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllHTTPAccessLogPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).CountAllHTTPAccessLogPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_CountAllHTTPAccessLogPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).CountAllHTTPAccessLogPolicies(ctx, req.(*CountAllHTTPAccessLogPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAccessLogPolicyService_ListHTTPAccessLogPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHTTPAccessLogPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).ListHTTPAccessLogPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_ListHTTPAccessLogPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).ListHTTPAccessLogPolicies(ctx, req.(*ListHTTPAccessLogPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAccessLogPolicyService_CreateHTTPAccessLogPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPAccessLogPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).CreateHTTPAccessLogPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_CreateHTTPAccessLogPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).CreateHTTPAccessLogPolicy(ctx, req.(*CreateHTTPAccessLogPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAccessLogPolicyService_UpdateHTTPAccessLogPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPAccessLogPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).UpdateHTTPAccessLogPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_UpdateHTTPAccessLogPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).UpdateHTTPAccessLogPolicy(ctx, req.(*UpdateHTTPAccessLogPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAccessLogPolicyService_FindHTTPAccessLogPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindHTTPAccessLogPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).FindHTTPAccessLogPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_FindHTTPAccessLogPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).FindHTTPAccessLogPolicy(ctx, req.(*FindHTTPAccessLogPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAccessLogPolicyService_DeleteHTTPAccessLogPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHTTPAccessLogPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).DeleteHTTPAccessLogPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_DeleteHTTPAccessLogPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).DeleteHTTPAccessLogPolicy(ctx, req.(*DeleteHTTPAccessLogPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPAccessLogPolicyService_WriteHTTPAccessLogPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteHTTPAccessLogPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPAccessLogPolicyServiceServer).WriteHTTPAccessLogPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPAccessLogPolicyService_WriteHTTPAccessLogPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPAccessLogPolicyServiceServer).WriteHTTPAccessLogPolicy(ctx, req.(*WriteHTTPAccessLogPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPAccessLogPolicyService_ServiceDesc is the grpc.ServiceDesc for HTTPAccessLogPolicyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPAccessLogPolicyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPAccessLogPolicyService",
	HandlerType: (*HTTPAccessLogPolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countAllHTTPAccessLogPolicies",
			Handler:    _HTTPAccessLogPolicyService_CountAllHTTPAccessLogPolicies_Handler,
		},
		{
			MethodName: "listHTTPAccessLogPolicies",
			Handler:    _HTTPAccessLogPolicyService_ListHTTPAccessLogPolicies_Handler,
		},
		{
			MethodName: "createHTTPAccessLogPolicy",
			Handler:    _HTTPAccessLogPolicyService_CreateHTTPAccessLogPolicy_Handler,
		},
		{
			MethodName: "updateHTTPAccessLogPolicy",
			Handler:    _HTTPAccessLogPolicyService_UpdateHTTPAccessLogPolicy_Handler,
		},
		{
			MethodName: "findHTTPAccessLogPolicy",
			Handler:    _HTTPAccessLogPolicyService_FindHTTPAccessLogPolicy_Handler,
		},
		{
			MethodName: "deleteHTTPAccessLogPolicy",
			Handler:    _HTTPAccessLogPolicyService_DeleteHTTPAccessLogPolicy_Handler,
		},
		{
			MethodName: "writeHTTPAccessLogPolicy",
			Handler:    _HTTPAccessLogPolicyService_WriteHTTPAccessLogPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_access_log_policy.proto",
}
