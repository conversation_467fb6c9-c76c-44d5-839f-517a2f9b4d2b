// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_size_capacity.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SizeCapacity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Unit          string                 `protobuf:"bytes,2,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SizeCapacity) Reset() {
	*x = SizeCapacity{}
	mi := &file_models_model_size_capacity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SizeCapacity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SizeCapacity) ProtoMessage() {}

func (x *SizeCapacity) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_size_capacity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SizeCapacity.ProtoReflect.Descriptor instead.
func (*SizeCapacity) Descriptor() ([]byte, []int) {
	return file_models_model_size_capacity_proto_rawDescGZIP(), []int{0}
}

func (x *SizeCapacity) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SizeCapacity) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

var File_models_model_size_capacity_proto protoreflect.FileDescriptor

const file_models_model_size_capacity_proto_rawDesc = "" +
	"\n" +
	" models/model_size_capacity.proto\x12\x02pb\"8\n" +
	"\fSizeCapacity\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\x12\x12\n" +
	"\x04unit\x18\x02 \x01(\tR\x04unitB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_size_capacity_proto_rawDescOnce sync.Once
	file_models_model_size_capacity_proto_rawDescData []byte
)

func file_models_model_size_capacity_proto_rawDescGZIP() []byte {
	file_models_model_size_capacity_proto_rawDescOnce.Do(func() {
		file_models_model_size_capacity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_size_capacity_proto_rawDesc), len(file_models_model_size_capacity_proto_rawDesc)))
	})
	return file_models_model_size_capacity_proto_rawDescData
}

var file_models_model_size_capacity_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_size_capacity_proto_goTypes = []any{
	(*SizeCapacity)(nil), // 0: pb.SizeCapacity
}
var file_models_model_size_capacity_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_size_capacity_proto_init() }
func file_models_model_size_capacity_proto_init() {
	if File_models_model_size_capacity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_size_capacity_proto_rawDesc), len(file_models_model_size_capacity_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_size_capacity_proto_goTypes,
		DependencyIndexes: file_models_model_size_capacity_proto_depIdxs,
		MessageInfos:      file_models_model_size_capacity_proto_msgTypes,
	}.Build()
	File_models_model_size_capacity_proto = out.File
	file_models_model_size_capacity_proto_goTypes = nil
	file_models_model_size_capacity_proto_depIdxs = nil
}
