// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_reverse_proxy.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReverseProxy struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SchedulingJSON     []byte                 `protobuf:"bytes,2,opt,name=schedulingJSON,proto3" json:"schedulingJSON,omitempty"`
	PrimaryOriginsJSON []byte                 `protobuf:"bytes,3,opt,name=primaryOriginsJSON,proto3" json:"primaryOriginsJSON,omitempty"`
	BackupOriginsJSON  []byte                 `protobuf:"bytes,4,opt,name=backupOriginsJSON,proto3" json:"backupOriginsJSON,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ReverseProxy) Reset() {
	*x = ReverseProxy{}
	mi := &file_models_model_reverse_proxy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReverseProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReverseProxy) ProtoMessage() {}

func (x *ReverseProxy) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_reverse_proxy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReverseProxy.ProtoReflect.Descriptor instead.
func (*ReverseProxy) Descriptor() ([]byte, []int) {
	return file_models_model_reverse_proxy_proto_rawDescGZIP(), []int{0}
}

func (x *ReverseProxy) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReverseProxy) GetSchedulingJSON() []byte {
	if x != nil {
		return x.SchedulingJSON
	}
	return nil
}

func (x *ReverseProxy) GetPrimaryOriginsJSON() []byte {
	if x != nil {
		return x.PrimaryOriginsJSON
	}
	return nil
}

func (x *ReverseProxy) GetBackupOriginsJSON() []byte {
	if x != nil {
		return x.BackupOriginsJSON
	}
	return nil
}

var File_models_model_reverse_proxy_proto protoreflect.FileDescriptor

const file_models_model_reverse_proxy_proto_rawDesc = "" +
	"\n" +
	" models/model_reverse_proxy.proto\x12\x02pb\"\xa4\x01\n" +
	"\fReverseProxy\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12&\n" +
	"\x0eschedulingJSON\x18\x02 \x01(\fR\x0eschedulingJSON\x12.\n" +
	"\x12primaryOriginsJSON\x18\x03 \x01(\fR\x12primaryOriginsJSON\x12,\n" +
	"\x11backupOriginsJSON\x18\x04 \x01(\fR\x11backupOriginsJSONB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_reverse_proxy_proto_rawDescOnce sync.Once
	file_models_model_reverse_proxy_proto_rawDescData []byte
)

func file_models_model_reverse_proxy_proto_rawDescGZIP() []byte {
	file_models_model_reverse_proxy_proto_rawDescOnce.Do(func() {
		file_models_model_reverse_proxy_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_reverse_proxy_proto_rawDesc), len(file_models_model_reverse_proxy_proto_rawDesc)))
	})
	return file_models_model_reverse_proxy_proto_rawDescData
}

var file_models_model_reverse_proxy_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_reverse_proxy_proto_goTypes = []any{
	(*ReverseProxy)(nil), // 0: pb.ReverseProxy
}
var file_models_model_reverse_proxy_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_reverse_proxy_proto_init() }
func file_models_model_reverse_proxy_proto_init() {
	if File_models_model_reverse_proxy_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_reverse_proxy_proto_rawDesc), len(file_models_model_reverse_proxy_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_reverse_proxy_proto_goTypes,
		DependencyIndexes: file_models_model_reverse_proxy_proto_depIdxs,
		MessageInfos:      file_models_model_reverse_proxy_proto_msgTypes,
	}.Build()
	File_models_model_reverse_proxy_proto = out.File
	file_models_model_reverse_proxy_proto_goTypes = nil
	file_models_model_reverse_proxy_proto_depIdxs = nil
}
