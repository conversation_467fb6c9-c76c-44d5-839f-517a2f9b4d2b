// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_report_node.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReportNode struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UniqueId         string                 `protobuf:"bytes,2,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
	Secret           string                 `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret,omitempty"`
	IsOn             bool                   `protobuf:"varint,4,opt,name=isOn,proto3" json:"isOn,omitempty"`
	Name             string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Location         string                 `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"`
	Isp              string                 `protobuf:"bytes,7,opt,name=isp,proto3" json:"isp,omitempty"`
	IsActive         bool                   `protobuf:"varint,8,opt,name=isActive,proto3" json:"isActive,omitempty"`
	StatusJSON       []byte                 `protobuf:"bytes,9,opt,name=statusJSON,proto3" json:"statusJSON,omitempty"`
	AllowIPs         []string               `protobuf:"bytes,10,rep,name=allowIPs,proto3" json:"allowIPs,omitempty"`
	ReportNodeGroups []*ReportNodeGroup     `protobuf:"bytes,11,rep,name=reportNodeGroups,proto3" json:"reportNodeGroups,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ReportNode) Reset() {
	*x = ReportNode{}
	mi := &file_models_model_report_node_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportNode) ProtoMessage() {}

func (x *ReportNode) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_report_node_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportNode.ProtoReflect.Descriptor instead.
func (*ReportNode) Descriptor() ([]byte, []int) {
	return file_models_model_report_node_proto_rawDescGZIP(), []int{0}
}

func (x *ReportNode) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReportNode) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

func (x *ReportNode) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *ReportNode) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *ReportNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReportNode) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ReportNode) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *ReportNode) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *ReportNode) GetStatusJSON() []byte {
	if x != nil {
		return x.StatusJSON
	}
	return nil
}

func (x *ReportNode) GetAllowIPs() []string {
	if x != nil {
		return x.AllowIPs
	}
	return nil
}

func (x *ReportNode) GetReportNodeGroups() []*ReportNodeGroup {
	if x != nil {
		return x.ReportNodeGroups
	}
	return nil
}

var File_models_model_report_node_proto protoreflect.FileDescriptor

const file_models_model_report_node_proto_rawDesc = "" +
	"\n" +
	"\x1emodels/model_report_node.proto\x12\x02pb\x1a$models/model_report_node_group.proto\"\xbf\x02\n" +
	"\n" +
	"ReportNode\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\buniqueId\x18\x02 \x01(\tR\buniqueId\x12\x16\n" +
	"\x06secret\x18\x03 \x01(\tR\x06secret\x12\x12\n" +
	"\x04isOn\x18\x04 \x01(\bR\x04isOn\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x1a\n" +
	"\blocation\x18\x06 \x01(\tR\blocation\x12\x10\n" +
	"\x03isp\x18\a \x01(\tR\x03isp\x12\x1a\n" +
	"\bisActive\x18\b \x01(\bR\bisActive\x12\x1e\n" +
	"\n" +
	"statusJSON\x18\t \x01(\fR\n" +
	"statusJSON\x12\x1a\n" +
	"\ballowIPs\x18\n" +
	" \x03(\tR\ballowIPs\x12?\n" +
	"\x10reportNodeGroups\x18\v \x03(\v2\x13.pb.ReportNodeGroupR\x10reportNodeGroupsB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_report_node_proto_rawDescOnce sync.Once
	file_models_model_report_node_proto_rawDescData []byte
)

func file_models_model_report_node_proto_rawDescGZIP() []byte {
	file_models_model_report_node_proto_rawDescOnce.Do(func() {
		file_models_model_report_node_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_report_node_proto_rawDesc), len(file_models_model_report_node_proto_rawDesc)))
	})
	return file_models_model_report_node_proto_rawDescData
}

var file_models_model_report_node_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_report_node_proto_goTypes = []any{
	(*ReportNode)(nil),      // 0: pb.ReportNode
	(*ReportNodeGroup)(nil), // 1: pb.ReportNodeGroup
}
var file_models_model_report_node_proto_depIdxs = []int32{
	1, // 0: pb.ReportNode.reportNodeGroups:type_name -> pb.ReportNodeGroup
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_report_node_proto_init() }
func file_models_model_report_node_proto_init() {
	if File_models_model_report_node_proto != nil {
		return
	}
	file_models_model_report_node_group_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_report_node_proto_rawDesc), len(file_models_model_report_node_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_report_node_proto_goTypes,
		DependencyIndexes: file_models_model_report_node_proto_depIdxs,
		MessageInfos:      file_models_model_report_node_proto_msgTypes,
	}.Build()
	File_models_model_report_node_proto = out.File
	file_models_model_report_node_proto_goTypes = nil
	file_models_model_report_node_proto_depIdxs = nil
}
