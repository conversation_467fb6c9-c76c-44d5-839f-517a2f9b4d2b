// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_ssl_cert.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SSLCert struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOn          bool                   `protobuf:"varint,2,opt,name=isOn,proto3" json:"isOn,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	TimeBeginAt   int64                  `protobuf:"varint,4,opt,name=timeBeginAt,proto3" json:"timeBeginAt,omitempty"`
	TimeEndAt     int64                  `protobuf:"varint,5,opt,name=timeEndAt,proto3" json:"timeEndAt,omitempty"`
	DnsNames      []string               `protobuf:"bytes,6,rep,name=dnsNames,proto3" json:"dnsNames,omitempty"`
	CommonNames   []string               `protobuf:"bytes,7,rep,name=commonNames,proto3" json:"commonNames,omitempty"`
	IsACME        bool                   `protobuf:"varint,8,opt,name=isACME,proto3" json:"isACME,omitempty"`
	AcmeTaskId    int64                  `protobuf:"varint,17,opt,name=acmeTaskId,proto3" json:"acmeTaskId,omitempty"`
	Ocsp          []byte                 `protobuf:"bytes,9,opt,name=ocsp,proto3" json:"ocsp,omitempty"`
	OcspIsUpdated bool                   `protobuf:"varint,10,opt,name=ocspIsUpdated,proto3" json:"ocspIsUpdated,omitempty"`
	OcspError     string                 `protobuf:"bytes,11,opt,name=ocspError,proto3" json:"ocspError,omitempty"`
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	IsCA          bool                   `protobuf:"varint,13,opt,name=isCA,proto3" json:"isCA,omitempty"`
	ServerName    string                 `protobuf:"bytes,14,opt,name=serverName,proto3" json:"serverName,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,15,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,16,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SSLCert) Reset() {
	*x = SSLCert{}
	mi := &file_models_model_ssl_cert_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SSLCert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSLCert) ProtoMessage() {}

func (x *SSLCert) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_ssl_cert_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSLCert.ProtoReflect.Descriptor instead.
func (*SSLCert) Descriptor() ([]byte, []int) {
	return file_models_model_ssl_cert_proto_rawDescGZIP(), []int{0}
}

func (x *SSLCert) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SSLCert) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *SSLCert) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SSLCert) GetTimeBeginAt() int64 {
	if x != nil {
		return x.TimeBeginAt
	}
	return 0
}

func (x *SSLCert) GetTimeEndAt() int64 {
	if x != nil {
		return x.TimeEndAt
	}
	return 0
}

func (x *SSLCert) GetDnsNames() []string {
	if x != nil {
		return x.DnsNames
	}
	return nil
}

func (x *SSLCert) GetCommonNames() []string {
	if x != nil {
		return x.CommonNames
	}
	return nil
}

func (x *SSLCert) GetIsACME() bool {
	if x != nil {
		return x.IsACME
	}
	return false
}

func (x *SSLCert) GetAcmeTaskId() int64 {
	if x != nil {
		return x.AcmeTaskId
	}
	return 0
}

func (x *SSLCert) GetOcsp() []byte {
	if x != nil {
		return x.Ocsp
	}
	return nil
}

func (x *SSLCert) GetOcspIsUpdated() bool {
	if x != nil {
		return x.OcspIsUpdated
	}
	return false
}

func (x *SSLCert) GetOcspError() string {
	if x != nil {
		return x.OcspError
	}
	return ""
}

func (x *SSLCert) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SSLCert) GetIsCA() bool {
	if x != nil {
		return x.IsCA
	}
	return false
}

func (x *SSLCert) GetServerName() string {
	if x != nil {
		return x.ServerName
	}
	return ""
}

func (x *SSLCert) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SSLCert) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_models_model_ssl_cert_proto protoreflect.FileDescriptor

const file_models_model_ssl_cert_proto_rawDesc = "" +
	"\n" +
	"\x1bmodels/model_ssl_cert.proto\x12\x02pb\"\xe1\x03\n" +
	"\aSSLCert\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04isOn\x18\x02 \x01(\bR\x04isOn\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vtimeBeginAt\x18\x04 \x01(\x03R\vtimeBeginAt\x12\x1c\n" +
	"\ttimeEndAt\x18\x05 \x01(\x03R\ttimeEndAt\x12\x1a\n" +
	"\bdnsNames\x18\x06 \x03(\tR\bdnsNames\x12 \n" +
	"\vcommonNames\x18\a \x03(\tR\vcommonNames\x12\x16\n" +
	"\x06isACME\x18\b \x01(\bR\x06isACME\x12\x1e\n" +
	"\n" +
	"acmeTaskId\x18\x11 \x01(\x03R\n" +
	"acmeTaskId\x12\x12\n" +
	"\x04ocsp\x18\t \x01(\fR\x04ocsp\x12$\n" +
	"\rocspIsUpdated\x18\n" +
	" \x01(\bR\rocspIsUpdated\x12\x1c\n" +
	"\tocspError\x18\v \x01(\tR\tocspError\x12 \n" +
	"\vdescription\x18\f \x01(\tR\vdescription\x12\x12\n" +
	"\x04isCA\x18\r \x01(\bR\x04isCA\x12\x1e\n" +
	"\n" +
	"serverName\x18\x0e \x01(\tR\n" +
	"serverName\x12\x1c\n" +
	"\tcreatedAt\x18\x0f \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\x10 \x01(\x03R\tupdatedAtB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_ssl_cert_proto_rawDescOnce sync.Once
	file_models_model_ssl_cert_proto_rawDescData []byte
)

func file_models_model_ssl_cert_proto_rawDescGZIP() []byte {
	file_models_model_ssl_cert_proto_rawDescOnce.Do(func() {
		file_models_model_ssl_cert_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_ssl_cert_proto_rawDesc), len(file_models_model_ssl_cert_proto_rawDesc)))
	})
	return file_models_model_ssl_cert_proto_rawDescData
}

var file_models_model_ssl_cert_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_ssl_cert_proto_goTypes = []any{
	(*SSLCert)(nil), // 0: pb.SSLCert
}
var file_models_model_ssl_cert_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_model_ssl_cert_proto_init() }
func file_models_model_ssl_cert_proto_init() {
	if File_models_model_ssl_cert_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_ssl_cert_proto_rawDesc), len(file_models_model_ssl_cert_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_ssl_cert_proto_goTypes,
		DependencyIndexes: file_models_model_ssl_cert_proto_depIdxs,
		MessageInfos:      file_models_model_ssl_cert_proto_msgTypes,
	}.Build()
	File_models_model_ssl_cert_proto = out.File
	file_models_model_ssl_cert_proto_goTypes = nil
	file_models_model_ssl_cert_proto_depIdxs = nil
}
