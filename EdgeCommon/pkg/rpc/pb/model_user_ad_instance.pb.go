// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_ad_instance.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 高防实例
type UserADInstance struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId               int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	AdPackageInstanceId  int64                  `protobuf:"varint,3,opt,name=adPackageInstanceId,proto3" json:"adPackageInstanceId,omitempty"`
	AdPackagePeriodId    int64                  `protobuf:"varint,4,opt,name=adPackagePeriodId,proto3" json:"adPackagePeriodId,omitempty"`
	AdPackagePeriodCount int32                  `protobuf:"varint,5,opt,name=adPackagePeriodCount,proto3" json:"adPackagePeriodCount,omitempty"`
	AdPackagePeriodUnit  string                 `protobuf:"bytes,6,opt,name=adPackagePeriodUnit,proto3" json:"adPackagePeriodUnit,omitempty"`
	DayFrom              string                 `protobuf:"bytes,7,opt,name=dayFrom,proto3" json:"dayFrom,omitempty"` // 开始日期，格式：YYYYMMDD
	DayTo                string                 `protobuf:"bytes,8,opt,name=dayTo,proto3" json:"dayTo,omitempty"`     // 结束日期，格式：YYYYMMDD
	CreatedAt            int64                  `protobuf:"varint,9,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	MaxObjects           int32                  `protobuf:"varint,10,opt,name=maxObjects,proto3" json:"maxObjects,omitempty"`
	ObjectCodes          []string               `protobuf:"bytes,11,rep,name=objectCodes,proto3" json:"objectCodes,omitempty"`
	AdPackageInstance    *ADPackageInstance     `protobuf:"bytes,30,opt,name=adPackageInstance,proto3" json:"adPackageInstance,omitempty"`
	User                 *User                  `protobuf:"bytes,31,opt,name=user,proto3" json:"user,omitempty"`
	CanDelete            bool                   `protobuf:"varint,32,opt,name=canDelete,proto3" json:"canDelete,omitempty"`
	IsAvailable          bool                   `protobuf:"varint,33,opt,name=isAvailable,proto3" json:"isAvailable,omitempty"`   // 当前是否在生效中
	CountObjects         int32                  `protobuf:"varint,34,opt,name=countObjects,proto3" json:"countObjects,omitempty"` // 防护对象数量
	ObjectsJSON          []byte                 `protobuf:"bytes,35,opt,name=objectsJSON,proto3" json:"objectsJSON,omitempty"`    // 对象JSON
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UserADInstance) Reset() {
	*x = UserADInstance{}
	mi := &file_models_model_user_ad_instance_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserADInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserADInstance) ProtoMessage() {}

func (x *UserADInstance) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_ad_instance_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserADInstance.ProtoReflect.Descriptor instead.
func (*UserADInstance) Descriptor() ([]byte, []int) {
	return file_models_model_user_ad_instance_proto_rawDescGZIP(), []int{0}
}

func (x *UserADInstance) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserADInstance) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserADInstance) GetAdPackageInstanceId() int64 {
	if x != nil {
		return x.AdPackageInstanceId
	}
	return 0
}

func (x *UserADInstance) GetAdPackagePeriodId() int64 {
	if x != nil {
		return x.AdPackagePeriodId
	}
	return 0
}

func (x *UserADInstance) GetAdPackagePeriodCount() int32 {
	if x != nil {
		return x.AdPackagePeriodCount
	}
	return 0
}

func (x *UserADInstance) GetAdPackagePeriodUnit() string {
	if x != nil {
		return x.AdPackagePeriodUnit
	}
	return ""
}

func (x *UserADInstance) GetDayFrom() string {
	if x != nil {
		return x.DayFrom
	}
	return ""
}

func (x *UserADInstance) GetDayTo() string {
	if x != nil {
		return x.DayTo
	}
	return ""
}

func (x *UserADInstance) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserADInstance) GetMaxObjects() int32 {
	if x != nil {
		return x.MaxObjects
	}
	return 0
}

func (x *UserADInstance) GetObjectCodes() []string {
	if x != nil {
		return x.ObjectCodes
	}
	return nil
}

func (x *UserADInstance) GetAdPackageInstance() *ADPackageInstance {
	if x != nil {
		return x.AdPackageInstance
	}
	return nil
}

func (x *UserADInstance) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserADInstance) GetCanDelete() bool {
	if x != nil {
		return x.CanDelete
	}
	return false
}

func (x *UserADInstance) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *UserADInstance) GetCountObjects() int32 {
	if x != nil {
		return x.CountObjects
	}
	return 0
}

func (x *UserADInstance) GetObjectsJSON() []byte {
	if x != nil {
		return x.ObjectsJSON
	}
	return nil
}

var File_models_model_user_ad_instance_proto protoreflect.FileDescriptor

const file_models_model_user_ad_instance_proto_rawDesc = "" +
	"\n" +
	"#models/model_user_ad_instance.proto\x12\x02pb\x1a&models/model_ad_package_instance.proto\x1a\x17models/model_user.proto\"\xf7\x04\n" +
	"\x0eUserADInstance\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x120\n" +
	"\x13adPackageInstanceId\x18\x03 \x01(\x03R\x13adPackageInstanceId\x12,\n" +
	"\x11adPackagePeriodId\x18\x04 \x01(\x03R\x11adPackagePeriodId\x122\n" +
	"\x14adPackagePeriodCount\x18\x05 \x01(\x05R\x14adPackagePeriodCount\x120\n" +
	"\x13adPackagePeriodUnit\x18\x06 \x01(\tR\x13adPackagePeriodUnit\x12\x18\n" +
	"\adayFrom\x18\a \x01(\tR\adayFrom\x12\x14\n" +
	"\x05dayTo\x18\b \x01(\tR\x05dayTo\x12\x1c\n" +
	"\tcreatedAt\x18\t \x01(\x03R\tcreatedAt\x12\x1e\n" +
	"\n" +
	"maxObjects\x18\n" +
	" \x01(\x05R\n" +
	"maxObjects\x12 \n" +
	"\vobjectCodes\x18\v \x03(\tR\vobjectCodes\x12C\n" +
	"\x11adPackageInstance\x18\x1e \x01(\v2\x15.pb.ADPackageInstanceR\x11adPackageInstance\x12\x1c\n" +
	"\x04user\x18\x1f \x01(\v2\b.pb.UserR\x04user\x12\x1c\n" +
	"\tcanDelete\x18  \x01(\bR\tcanDelete\x12 \n" +
	"\visAvailable\x18! \x01(\bR\visAvailable\x12\"\n" +
	"\fcountObjects\x18\" \x01(\x05R\fcountObjects\x12 \n" +
	"\vobjectsJSON\x18# \x01(\fR\vobjectsJSONB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_ad_instance_proto_rawDescOnce sync.Once
	file_models_model_user_ad_instance_proto_rawDescData []byte
)

func file_models_model_user_ad_instance_proto_rawDescGZIP() []byte {
	file_models_model_user_ad_instance_proto_rawDescOnce.Do(func() {
		file_models_model_user_ad_instance_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_ad_instance_proto_rawDesc), len(file_models_model_user_ad_instance_proto_rawDesc)))
	})
	return file_models_model_user_ad_instance_proto_rawDescData
}

var file_models_model_user_ad_instance_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_ad_instance_proto_goTypes = []any{
	(*UserADInstance)(nil),    // 0: pb.UserADInstance
	(*ADPackageInstance)(nil), // 1: pb.ADPackageInstance
	(*User)(nil),              // 2: pb.User
}
var file_models_model_user_ad_instance_proto_depIdxs = []int32{
	1, // 0: pb.UserADInstance.adPackageInstance:type_name -> pb.ADPackageInstance
	2, // 1: pb.UserADInstance.user:type_name -> pb.User
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_models_model_user_ad_instance_proto_init() }
func file_models_model_user_ad_instance_proto_init() {
	if File_models_model_user_ad_instance_proto != nil {
		return
	}
	file_models_model_ad_package_instance_proto_init()
	file_models_model_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_ad_instance_proto_rawDesc), len(file_models_model_user_ad_instance_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_ad_instance_proto_goTypes,
		DependencyIndexes: file_models_model_user_ad_instance_proto_depIdxs,
		MessageInfos:      file_models_model_user_ad_instance_proto_msgTypes,
	}.Build()
	File_models_model_user_ad_instance_proto = out.File
	file_models_model_user_ad_instance_proto_goTypes = nil
	file_models_model_user_ad_instance_proto_depIdxs = nil
}
