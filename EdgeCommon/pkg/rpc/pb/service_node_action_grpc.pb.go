// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_action.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeActionService_CreateNodeAction_FullMethodName       = "/pb.NodeActionService/createNodeAction"
	NodeActionService_DeleteNodeAction_FullMethodName       = "/pb.NodeActionService/deleteNodeAction"
	NodeActionService_UpdateNodeAction_FullMethodName       = "/pb.NodeActionService/updateNodeAction"
	NodeActionService_FindAllNodeActions_FullMethodName     = "/pb.NodeActionService/findAllNodeActions"
	NodeActionService_FindNodeAction_FullMethodName         = "/pb.NodeActionService/findNodeAction"
	NodeActionService_UpdateNodeActionOrders_FullMethodName = "/pb.NodeActionService/updateNodeActionOrders"
)

// NodeActionServiceClient is the client API for NodeActionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeActionServiceClient interface {
	// 添加动作
	CreateNodeAction(ctx context.Context, in *CreateNodeActionRequest, opts ...grpc.CallOption) (*CreateNodeActionResponse, error)
	// 删除动作
	DeleteNodeAction(ctx context.Context, in *DeleteNodeActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改动作
	UpdateNodeAction(ctx context.Context, in *UpdateNodeActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 列出某个节点的所有动作
	FindAllNodeActions(ctx context.Context, in *FindAllNodeActionsRequest, opts ...grpc.CallOption) (*FindAllNodeActionsResponse, error)
	// 查找单个节点动作
	FindNodeAction(ctx context.Context, in *FindNodeActionRequest, opts ...grpc.CallOption) (*FindNodeActionResponse, error)
	// 设置节点动作排序
	UpdateNodeActionOrders(ctx context.Context, in *UpdateNodeActionOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeActionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeActionServiceClient(cc grpc.ClientConnInterface) NodeActionServiceClient {
	return &nodeActionServiceClient{cc}
}

func (c *nodeActionServiceClient) CreateNodeAction(ctx context.Context, in *CreateNodeActionRequest, opts ...grpc.CallOption) (*CreateNodeActionResponse, error) {
	out := new(CreateNodeActionResponse)
	err := c.cc.Invoke(ctx, NodeActionService_CreateNodeAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeActionServiceClient) DeleteNodeAction(ctx context.Context, in *DeleteNodeActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeActionService_DeleteNodeAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeActionServiceClient) UpdateNodeAction(ctx context.Context, in *UpdateNodeActionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeActionService_UpdateNodeAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeActionServiceClient) FindAllNodeActions(ctx context.Context, in *FindAllNodeActionsRequest, opts ...grpc.CallOption) (*FindAllNodeActionsResponse, error) {
	out := new(FindAllNodeActionsResponse)
	err := c.cc.Invoke(ctx, NodeActionService_FindAllNodeActions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeActionServiceClient) FindNodeAction(ctx context.Context, in *FindNodeActionRequest, opts ...grpc.CallOption) (*FindNodeActionResponse, error) {
	out := new(FindNodeActionResponse)
	err := c.cc.Invoke(ctx, NodeActionService_FindNodeAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeActionServiceClient) UpdateNodeActionOrders(ctx context.Context, in *UpdateNodeActionOrdersRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeActionService_UpdateNodeActionOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeActionServiceServer is the server API for NodeActionService service.
// All implementations should embed UnimplementedNodeActionServiceServer
// for forward compatibility
type NodeActionServiceServer interface {
	// 添加动作
	CreateNodeAction(context.Context, *CreateNodeActionRequest) (*CreateNodeActionResponse, error)
	// 删除动作
	DeleteNodeAction(context.Context, *DeleteNodeActionRequest) (*RPCSuccess, error)
	// 修改动作
	UpdateNodeAction(context.Context, *UpdateNodeActionRequest) (*RPCSuccess, error)
	// 列出某个节点的所有动作
	FindAllNodeActions(context.Context, *FindAllNodeActionsRequest) (*FindAllNodeActionsResponse, error)
	// 查找单个节点动作
	FindNodeAction(context.Context, *FindNodeActionRequest) (*FindNodeActionResponse, error)
	// 设置节点动作排序
	UpdateNodeActionOrders(context.Context, *UpdateNodeActionOrdersRequest) (*RPCSuccess, error)
}

// UnimplementedNodeActionServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeActionServiceServer struct {
}

func (UnimplementedNodeActionServiceServer) CreateNodeAction(context.Context, *CreateNodeActionRequest) (*CreateNodeActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeAction not implemented")
}
func (UnimplementedNodeActionServiceServer) DeleteNodeAction(context.Context, *DeleteNodeActionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeAction not implemented")
}
func (UnimplementedNodeActionServiceServer) UpdateNodeAction(context.Context, *UpdateNodeActionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeAction not implemented")
}
func (UnimplementedNodeActionServiceServer) FindAllNodeActions(context.Context, *FindAllNodeActionsRequest) (*FindAllNodeActionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNodeActions not implemented")
}
func (UnimplementedNodeActionServiceServer) FindNodeAction(context.Context, *FindNodeActionRequest) (*FindNodeActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeAction not implemented")
}
func (UnimplementedNodeActionServiceServer) UpdateNodeActionOrders(context.Context, *UpdateNodeActionOrdersRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeActionOrders not implemented")
}

// UnsafeNodeActionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeActionServiceServer will
// result in compilation errors.
type UnsafeNodeActionServiceServer interface {
	mustEmbedUnimplementedNodeActionServiceServer()
}

func RegisterNodeActionServiceServer(s grpc.ServiceRegistrar, srv NodeActionServiceServer) {
	s.RegisterService(&NodeActionService_ServiceDesc, srv)
}

func _NodeActionService_CreateNodeAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeActionServiceServer).CreateNodeAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeActionService_CreateNodeAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeActionServiceServer).CreateNodeAction(ctx, req.(*CreateNodeActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeActionService_DeleteNodeAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeActionServiceServer).DeleteNodeAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeActionService_DeleteNodeAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeActionServiceServer).DeleteNodeAction(ctx, req.(*DeleteNodeActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeActionService_UpdateNodeAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeActionServiceServer).UpdateNodeAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeActionService_UpdateNodeAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeActionServiceServer).UpdateNodeAction(ctx, req.(*UpdateNodeActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeActionService_FindAllNodeActions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNodeActionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeActionServiceServer).FindAllNodeActions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeActionService_FindAllNodeActions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeActionServiceServer).FindAllNodeActions(ctx, req.(*FindAllNodeActionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeActionService_FindNodeAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeActionServiceServer).FindNodeAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeActionService_FindNodeAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeActionServiceServer).FindNodeAction(ctx, req.(*FindNodeActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeActionService_UpdateNodeActionOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeActionOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeActionServiceServer).UpdateNodeActionOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeActionService_UpdateNodeActionOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeActionServiceServer).UpdateNodeActionOrders(ctx, req.(*UpdateNodeActionOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeActionService_ServiceDesc is the grpc.ServiceDesc for NodeActionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeActionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeActionService",
	HandlerType: (*NodeActionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeAction",
			Handler:    _NodeActionService_CreateNodeAction_Handler,
		},
		{
			MethodName: "deleteNodeAction",
			Handler:    _NodeActionService_DeleteNodeAction_Handler,
		},
		{
			MethodName: "updateNodeAction",
			Handler:    _NodeActionService_UpdateNodeAction_Handler,
		},
		{
			MethodName: "findAllNodeActions",
			Handler:    _NodeActionService_FindAllNodeActions_Handler,
		},
		{
			MethodName: "findNodeAction",
			Handler:    _NodeActionService_FindNodeAction_Handler,
		},
		{
			MethodName: "updateNodeActionOrders",
			Handler:    _NodeActionService_UpdateNodeActionOrders_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_action.proto",
}
