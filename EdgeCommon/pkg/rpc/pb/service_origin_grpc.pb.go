// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_origin.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OriginService_CreateOrigin_FullMethodName            = "/pb.OriginService/createOrigin"
	OriginService_UpdateOrigin_FullMethodName            = "/pb.OriginService/updateOrigin"
	OriginService_FindEnabledOrigin_FullMethodName       = "/pb.OriginService/findEnabledOrigin"
	OriginService_FindEnabledOriginConfig_FullMethodName = "/pb.OriginService/findEnabledOriginConfig"
	OriginService_UpdateOriginIsOn_FullMethodName        = "/pb.OriginService/updateOriginIsOn"
)

// OriginServiceClient is the client API for OriginService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OriginServiceClient interface {
	// 创建源站
	CreateOrigin(ctx context.Context, in *CreateOriginRequest, opts ...grpc.CallOption) (*CreateOriginResponse, error)
	// 修改源站
	UpdateOrigin(ctx context.Context, in *UpdateOriginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个源站信息
	FindEnabledOrigin(ctx context.Context, in *FindEnabledOriginRequest, opts ...grpc.CallOption) (*FindEnabledOriginResponse, error)
	// 查找源站配置
	FindEnabledOriginConfig(ctx context.Context, in *FindEnabledOriginConfigRequest, opts ...grpc.CallOption) (*FindEnabledOriginConfigResponse, error)
	// 设置源站是否启用
	UpdateOriginIsOn(ctx context.Context, in *UpdateOriginIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type originServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOriginServiceClient(cc grpc.ClientConnInterface) OriginServiceClient {
	return &originServiceClient{cc}
}

func (c *originServiceClient) CreateOrigin(ctx context.Context, in *CreateOriginRequest, opts ...grpc.CallOption) (*CreateOriginResponse, error) {
	out := new(CreateOriginResponse)
	err := c.cc.Invoke(ctx, OriginService_CreateOrigin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *originServiceClient) UpdateOrigin(ctx context.Context, in *UpdateOriginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, OriginService_UpdateOrigin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *originServiceClient) FindEnabledOrigin(ctx context.Context, in *FindEnabledOriginRequest, opts ...grpc.CallOption) (*FindEnabledOriginResponse, error) {
	out := new(FindEnabledOriginResponse)
	err := c.cc.Invoke(ctx, OriginService_FindEnabledOrigin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *originServiceClient) FindEnabledOriginConfig(ctx context.Context, in *FindEnabledOriginConfigRequest, opts ...grpc.CallOption) (*FindEnabledOriginConfigResponse, error) {
	out := new(FindEnabledOriginConfigResponse)
	err := c.cc.Invoke(ctx, OriginService_FindEnabledOriginConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *originServiceClient) UpdateOriginIsOn(ctx context.Context, in *UpdateOriginIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, OriginService_UpdateOriginIsOn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OriginServiceServer is the server API for OriginService service.
// All implementations should embed UnimplementedOriginServiceServer
// for forward compatibility
type OriginServiceServer interface {
	// 创建源站
	CreateOrigin(context.Context, *CreateOriginRequest) (*CreateOriginResponse, error)
	// 修改源站
	UpdateOrigin(context.Context, *UpdateOriginRequest) (*RPCSuccess, error)
	// 查找单个源站信息
	FindEnabledOrigin(context.Context, *FindEnabledOriginRequest) (*FindEnabledOriginResponse, error)
	// 查找源站配置
	FindEnabledOriginConfig(context.Context, *FindEnabledOriginConfigRequest) (*FindEnabledOriginConfigResponse, error)
	// 设置源站是否启用
	UpdateOriginIsOn(context.Context, *UpdateOriginIsOnRequest) (*RPCSuccess, error)
}

// UnimplementedOriginServiceServer should be embedded to have forward compatible implementations.
type UnimplementedOriginServiceServer struct {
}

func (UnimplementedOriginServiceServer) CreateOrigin(context.Context, *CreateOriginRequest) (*CreateOriginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrigin not implemented")
}
func (UnimplementedOriginServiceServer) UpdateOrigin(context.Context, *UpdateOriginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrigin not implemented")
}
func (UnimplementedOriginServiceServer) FindEnabledOrigin(context.Context, *FindEnabledOriginRequest) (*FindEnabledOriginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledOrigin not implemented")
}
func (UnimplementedOriginServiceServer) FindEnabledOriginConfig(context.Context, *FindEnabledOriginConfigRequest) (*FindEnabledOriginConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledOriginConfig not implemented")
}
func (UnimplementedOriginServiceServer) UpdateOriginIsOn(context.Context, *UpdateOriginIsOnRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOriginIsOn not implemented")
}

// UnsafeOriginServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OriginServiceServer will
// result in compilation errors.
type UnsafeOriginServiceServer interface {
	mustEmbedUnimplementedOriginServiceServer()
}

func RegisterOriginServiceServer(s grpc.ServiceRegistrar, srv OriginServiceServer) {
	s.RegisterService(&OriginService_ServiceDesc, srv)
}

func _OriginService_CreateOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOriginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OriginServiceServer).CreateOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OriginService_CreateOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OriginServiceServer).CreateOrigin(ctx, req.(*CreateOriginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OriginService_UpdateOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOriginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OriginServiceServer).UpdateOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OriginService_UpdateOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OriginServiceServer).UpdateOrigin(ctx, req.(*UpdateOriginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OriginService_FindEnabledOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledOriginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OriginServiceServer).FindEnabledOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OriginService_FindEnabledOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OriginServiceServer).FindEnabledOrigin(ctx, req.(*FindEnabledOriginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OriginService_FindEnabledOriginConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledOriginConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OriginServiceServer).FindEnabledOriginConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OriginService_FindEnabledOriginConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OriginServiceServer).FindEnabledOriginConfig(ctx, req.(*FindEnabledOriginConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OriginService_UpdateOriginIsOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOriginIsOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OriginServiceServer).UpdateOriginIsOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OriginService_UpdateOriginIsOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OriginServiceServer).UpdateOriginIsOn(ctx, req.(*UpdateOriginIsOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OriginService_ServiceDesc is the grpc.ServiceDesc for OriginService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OriginService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.OriginService",
	HandlerType: (*OriginServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createOrigin",
			Handler:    _OriginService_CreateOrigin_Handler,
		},
		{
			MethodName: "updateOrigin",
			Handler:    _OriginService_UpdateOrigin_Handler,
		},
		{
			MethodName: "findEnabledOrigin",
			Handler:    _OriginService_FindEnabledOrigin_Handler,
		},
		{
			MethodName: "findEnabledOriginConfig",
			Handler:    _OriginService_FindEnabledOriginConfig_Handler,
		},
		{
			MethodName: "updateOriginIsOn",
			Handler:    _OriginService_UpdateOriginIsOn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_origin.proto",
}
