// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_cluster.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSClusterService_CreateNSCluster_FullMethodName                 = "/pb.NSClusterService/createNSCluster"
	NSClusterService_UpdateNSCluster_FullMethodName                 = "/pb.NSClusterService/updateNSCluster"
	NSClusterService_FindNSClusterAccessLog_FullMethodName          = "/pb.NSClusterService/findNSClusterAccessLog"
	NSClusterService_UpdateNSClusterAccessLog_FullMethodName        = "/pb.NSClusterService/updateNSClusterAccessLog"
	NSClusterService_DeleteNSCluster_FullMethodName                 = "/pb.NSClusterService/deleteNSCluster"
	NSClusterService_FindNSCluster_FullMethodName                   = "/pb.NSClusterService/findNSCluster"
	NSClusterService_CountAllNSClusters_FullMethodName              = "/pb.NSClusterService/countAllNSClusters"
	NSClusterService_ListNSClusters_FullMethodName                  = "/pb.NSClusterService/listNSClusters"
	NSClusterService_FindAllNSClusters_FullMethodName               = "/pb.NSClusterService/findAllNSClusters"
	NSClusterService_UpdateNSClusterRecursionConfig_FullMethodName  = "/pb.NSClusterService/updateNSClusterRecursionConfig"
	NSClusterService_FindNSClusterRecursionConfig_FullMethodName    = "/pb.NSClusterService/findNSClusterRecursionConfig"
	NSClusterService_FindNSClusterTCPConfig_FullMethodName          = "/pb.NSClusterService/findNSClusterTCPConfig"
	NSClusterService_UpdateNSClusterTCP_FullMethodName              = "/pb.NSClusterService/updateNSClusterTCP"
	NSClusterService_FindNSClusterTLSConfig_FullMethodName          = "/pb.NSClusterService/findNSClusterTLSConfig"
	NSClusterService_UpdateNSClusterTLS_FullMethodName              = "/pb.NSClusterService/updateNSClusterTLS"
	NSClusterService_FindNSClusterUDPConfig_FullMethodName          = "/pb.NSClusterService/findNSClusterUDPConfig"
	NSClusterService_UpdateNSClusterUDP_FullMethodName              = "/pb.NSClusterService/updateNSClusterUDP"
	NSClusterService_FindNSClusterDoHConfig_FullMethodName          = "/pb.NSClusterService/findNSClusterDoHConfig"
	NSClusterService_UpdateNSClusterDoH_FullMethodName              = "/pb.NSClusterService/updateNSClusterDoH"
	NSClusterService_CountAllNSClustersWithSSLCertId_FullMethodName = "/pb.NSClusterService/countAllNSClustersWithSSLCertId"
	NSClusterService_FindNSClusterDDoSProtection_FullMethodName     = "/pb.NSClusterService/findNSClusterDDoSProtection"
	NSClusterService_UpdateNSClusterDDoSProtection_FullMethodName   = "/pb.NSClusterService/updateNSClusterDDoSProtection"
	NSClusterService_FindNSClusterHosts_FullMethodName              = "/pb.NSClusterService/findNSClusterHosts"
	NSClusterService_FindAvailableNSHostsForUser_FullMethodName     = "/pb.NSClusterService/findAvailableNSHostsForUser"
	NSClusterService_FindNSClusterAnswerConfig_FullMethodName       = "/pb.NSClusterService/findNSClusterAnswerConfig"
	NSClusterService_UpdateNSClusterAnswerConfig_FullMethodName     = "/pb.NSClusterService/updateNSClusterAnswerConfig"
	NSClusterService_FindNSClusterSOAConfig_FullMethodName          = "/pb.NSClusterService/findNSClusterSOAConfig"
	NSClusterService_UpdateNSClusterSOAConfig_FullMethodName        = "/pb.NSClusterService/updateNSClusterSOAConfig"
)

// NSClusterServiceClient is the client API for NSClusterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSClusterServiceClient interface {
	// 创建集群
	CreateNSCluster(ctx context.Context, in *CreateNSClusterRequest, opts ...grpc.CallOption) (*CreateNSClusterResponse, error)
	// 修改集群
	UpdateNSCluster(ctx context.Context, in *UpdateNSClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找集群访问日志配置
	FindNSClusterAccessLog(ctx context.Context, in *FindNSClusterAccessLogRequest, opts ...grpc.CallOption) (*FindNSClusterAccessLogResponse, error)
	// 修改集群访问日志配置
	UpdateNSClusterAccessLog(ctx context.Context, in *UpdateNSClusterAccessLogRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除集群
	DeleteNSCluster(ctx context.Context, in *DeleteNSCluster, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个可用集群信息
	FindNSCluster(ctx context.Context, in *FindNSClusterRequest, opts ...grpc.CallOption) (*FindNSClusterResponse, error)
	// 计算所有可用集群的数量
	CountAllNSClusters(ctx context.Context, in *CountAllNSClustersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页可用集群
	ListNSClusters(ctx context.Context, in *ListNSClustersRequest, opts ...grpc.CallOption) (*ListNSClustersResponse, error)
	// 查找所有可用集群
	FindAllNSClusters(ctx context.Context, in *FindAllNSClustersRequest, opts ...grpc.CallOption) (*FindAllNSClustersResponse, error)
	// 设置递归DNS配置
	UpdateNSClusterRecursionConfig(ctx context.Context, in *UpdateNSClusterRecursionConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取递归DNS配置
	FindNSClusterRecursionConfig(ctx context.Context, in *FindNSClusterRecursionConfigRequest, opts ...grpc.CallOption) (*FindNSClusterRecursionConfigResponse, error)
	// 查找集群的TCP设置
	FindNSClusterTCPConfig(ctx context.Context, in *FindNSClusterTCPConfigRequest, opts ...grpc.CallOption) (*FindNSClusterTCPConfigResponse, error)
	// 修改集群的TCP设置
	UpdateNSClusterTCP(ctx context.Context, in *UpdateNSClusterTCPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找集群的TLS设置
	FindNSClusterTLSConfig(ctx context.Context, in *FindNSClusterTLSConfigRequest, opts ...grpc.CallOption) (*FindNSClusterTLSConfigResponse, error)
	// 修改集群的TLS设置
	UpdateNSClusterTLS(ctx context.Context, in *UpdateNSClusterTLSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找集群的UDP设置
	FindNSClusterUDPConfig(ctx context.Context, in *FindNSClusterUDPConfigRequest, opts ...grpc.CallOption) (*FindNSClusterUDPConfigResponse, error)
	// 修改集群的UDP设置
	UpdateNSClusterUDP(ctx context.Context, in *UpdateNSClusterUDPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找集群的DoH设置
	FindNSClusterDoHConfig(ctx context.Context, in *FindNSClusterDoHConfigRequest, opts ...grpc.CallOption) (*FindNSClusterDoHConfigResponse, error)
	// 修改集群的DoH设置
	UpdateNSClusterDoH(ctx context.Context, in *UpdateNSClusterDoHRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算使用某个SSL证书的集群数量
	CountAllNSClustersWithSSLCertId(ctx context.Context, in *CountAllNSClustersWithSSLCertIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 获取NS集群的DDoS设置
	FindNSClusterDDoSProtection(ctx context.Context, in *FindNSClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNSClusterDDoSProtectionResponse, error)
	// 修改NS集群的DDoS设置
	UpdateNSClusterDDoSProtection(ctx context.Context, in *UpdateNSClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找NS集群的主机地址
	FindNSClusterHosts(ctx context.Context, in *FindNSClusterHostsRequest, opts ...grpc.CallOption) (*FindNSClusterHostsResponse, error)
	// 查找用户可以使用的主机地址
	FindAvailableNSHostsForUser(ctx context.Context, in *FindAvailableNSHostsForUserRequest, opts ...grpc.CallOption) (*FindAvailableNSHostsForUserResponse, error)
	// 查找应答模式
	FindNSClusterAnswerConfig(ctx context.Context, in *FindNSClusterAnswerConfigRequest, opts ...grpc.CallOption) (*FindNSClusterAnswerConfigResponse, error)
	// 设置应答模式
	UpdateNSClusterAnswerConfig(ctx context.Context, in *UpdateNSClusterAnswerConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找SOA配置
	FindNSClusterSOAConfig(ctx context.Context, in *FindNSClusterSOAConfigRequest, opts ...grpc.CallOption) (*FindNSClusterSOAConfigResponse, error)
	// 设置SOA配置
	UpdateNSClusterSOAConfig(ctx context.Context, in *UpdateNSClusterSOAConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nSClusterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSClusterServiceClient(cc grpc.ClientConnInterface) NSClusterServiceClient {
	return &nSClusterServiceClient{cc}
}

func (c *nSClusterServiceClient) CreateNSCluster(ctx context.Context, in *CreateNSClusterRequest, opts ...grpc.CallOption) (*CreateNSClusterResponse, error) {
	out := new(CreateNSClusterResponse)
	err := c.cc.Invoke(ctx, NSClusterService_CreateNSCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSCluster(ctx context.Context, in *UpdateNSClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterAccessLog(ctx context.Context, in *FindNSClusterAccessLogRequest, opts ...grpc.CallOption) (*FindNSClusterAccessLogResponse, error) {
	out := new(FindNSClusterAccessLogResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterAccessLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterAccessLog(ctx context.Context, in *UpdateNSClusterAccessLogRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterAccessLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) DeleteNSCluster(ctx context.Context, in *DeleteNSCluster, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_DeleteNSCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSCluster(ctx context.Context, in *FindNSClusterRequest, opts ...grpc.CallOption) (*FindNSClusterResponse, error) {
	out := new(FindNSClusterResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) CountAllNSClusters(ctx context.Context, in *CountAllNSClustersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSClusterService_CountAllNSClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) ListNSClusters(ctx context.Context, in *ListNSClustersRequest, opts ...grpc.CallOption) (*ListNSClustersResponse, error) {
	out := new(ListNSClustersResponse)
	err := c.cc.Invoke(ctx, NSClusterService_ListNSClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindAllNSClusters(ctx context.Context, in *FindAllNSClustersRequest, opts ...grpc.CallOption) (*FindAllNSClustersResponse, error) {
	out := new(FindAllNSClustersResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindAllNSClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterRecursionConfig(ctx context.Context, in *UpdateNSClusterRecursionConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterRecursionConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterRecursionConfig(ctx context.Context, in *FindNSClusterRecursionConfigRequest, opts ...grpc.CallOption) (*FindNSClusterRecursionConfigResponse, error) {
	out := new(FindNSClusterRecursionConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterRecursionConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterTCPConfig(ctx context.Context, in *FindNSClusterTCPConfigRequest, opts ...grpc.CallOption) (*FindNSClusterTCPConfigResponse, error) {
	out := new(FindNSClusterTCPConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterTCPConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterTCP(ctx context.Context, in *UpdateNSClusterTCPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterTCP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterTLSConfig(ctx context.Context, in *FindNSClusterTLSConfigRequest, opts ...grpc.CallOption) (*FindNSClusterTLSConfigResponse, error) {
	out := new(FindNSClusterTLSConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterTLSConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterTLS(ctx context.Context, in *UpdateNSClusterTLSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterTLS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterUDPConfig(ctx context.Context, in *FindNSClusterUDPConfigRequest, opts ...grpc.CallOption) (*FindNSClusterUDPConfigResponse, error) {
	out := new(FindNSClusterUDPConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterUDPConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterUDP(ctx context.Context, in *UpdateNSClusterUDPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterUDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterDoHConfig(ctx context.Context, in *FindNSClusterDoHConfigRequest, opts ...grpc.CallOption) (*FindNSClusterDoHConfigResponse, error) {
	out := new(FindNSClusterDoHConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterDoHConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterDoH(ctx context.Context, in *UpdateNSClusterDoHRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterDoH_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) CountAllNSClustersWithSSLCertId(ctx context.Context, in *CountAllNSClustersWithSSLCertIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSClusterService_CountAllNSClustersWithSSLCertId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterDDoSProtection(ctx context.Context, in *FindNSClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNSClusterDDoSProtectionResponse, error) {
	out := new(FindNSClusterDDoSProtectionResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterDDoSProtection(ctx context.Context, in *UpdateNSClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterHosts(ctx context.Context, in *FindNSClusterHostsRequest, opts ...grpc.CallOption) (*FindNSClusterHostsResponse, error) {
	out := new(FindNSClusterHostsResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterHosts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindAvailableNSHostsForUser(ctx context.Context, in *FindAvailableNSHostsForUserRequest, opts ...grpc.CallOption) (*FindAvailableNSHostsForUserResponse, error) {
	out := new(FindAvailableNSHostsForUserResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindAvailableNSHostsForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterAnswerConfig(ctx context.Context, in *FindNSClusterAnswerConfigRequest, opts ...grpc.CallOption) (*FindNSClusterAnswerConfigResponse, error) {
	out := new(FindNSClusterAnswerConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterAnswerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterAnswerConfig(ctx context.Context, in *UpdateNSClusterAnswerConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterAnswerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) FindNSClusterSOAConfig(ctx context.Context, in *FindNSClusterSOAConfigRequest, opts ...grpc.CallOption) (*FindNSClusterSOAConfigResponse, error) {
	out := new(FindNSClusterSOAConfigResponse)
	err := c.cc.Invoke(ctx, NSClusterService_FindNSClusterSOAConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSClusterServiceClient) UpdateNSClusterSOAConfig(ctx context.Context, in *UpdateNSClusterSOAConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSClusterService_UpdateNSClusterSOAConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSClusterServiceServer is the server API for NSClusterService service.
// All implementations should embed UnimplementedNSClusterServiceServer
// for forward compatibility
type NSClusterServiceServer interface {
	// 创建集群
	CreateNSCluster(context.Context, *CreateNSClusterRequest) (*CreateNSClusterResponse, error)
	// 修改集群
	UpdateNSCluster(context.Context, *UpdateNSClusterRequest) (*RPCSuccess, error)
	// 查找集群访问日志配置
	FindNSClusterAccessLog(context.Context, *FindNSClusterAccessLogRequest) (*FindNSClusterAccessLogResponse, error)
	// 修改集群访问日志配置
	UpdateNSClusterAccessLog(context.Context, *UpdateNSClusterAccessLogRequest) (*RPCSuccess, error)
	// 删除集群
	DeleteNSCluster(context.Context, *DeleteNSCluster) (*RPCSuccess, error)
	// 查找单个可用集群信息
	FindNSCluster(context.Context, *FindNSClusterRequest) (*FindNSClusterResponse, error)
	// 计算所有可用集群的数量
	CountAllNSClusters(context.Context, *CountAllNSClustersRequest) (*RPCCountResponse, error)
	// 列出单页可用集群
	ListNSClusters(context.Context, *ListNSClustersRequest) (*ListNSClustersResponse, error)
	// 查找所有可用集群
	FindAllNSClusters(context.Context, *FindAllNSClustersRequest) (*FindAllNSClustersResponse, error)
	// 设置递归DNS配置
	UpdateNSClusterRecursionConfig(context.Context, *UpdateNSClusterRecursionConfigRequest) (*RPCSuccess, error)
	// 读取递归DNS配置
	FindNSClusterRecursionConfig(context.Context, *FindNSClusterRecursionConfigRequest) (*FindNSClusterRecursionConfigResponse, error)
	// 查找集群的TCP设置
	FindNSClusterTCPConfig(context.Context, *FindNSClusterTCPConfigRequest) (*FindNSClusterTCPConfigResponse, error)
	// 修改集群的TCP设置
	UpdateNSClusterTCP(context.Context, *UpdateNSClusterTCPRequest) (*RPCSuccess, error)
	// 查找集群的TLS设置
	FindNSClusterTLSConfig(context.Context, *FindNSClusterTLSConfigRequest) (*FindNSClusterTLSConfigResponse, error)
	// 修改集群的TLS设置
	UpdateNSClusterTLS(context.Context, *UpdateNSClusterTLSRequest) (*RPCSuccess, error)
	// 查找集群的UDP设置
	FindNSClusterUDPConfig(context.Context, *FindNSClusterUDPConfigRequest) (*FindNSClusterUDPConfigResponse, error)
	// 修改集群的UDP设置
	UpdateNSClusterUDP(context.Context, *UpdateNSClusterUDPRequest) (*RPCSuccess, error)
	// 查找集群的DoH设置
	FindNSClusterDoHConfig(context.Context, *FindNSClusterDoHConfigRequest) (*FindNSClusterDoHConfigResponse, error)
	// 修改集群的DoH设置
	UpdateNSClusterDoH(context.Context, *UpdateNSClusterDoHRequest) (*RPCSuccess, error)
	// 计算使用某个SSL证书的集群数量
	CountAllNSClustersWithSSLCertId(context.Context, *CountAllNSClustersWithSSLCertIdRequest) (*RPCCountResponse, error)
	// 获取NS集群的DDoS设置
	FindNSClusterDDoSProtection(context.Context, *FindNSClusterDDoSProtectionRequest) (*FindNSClusterDDoSProtectionResponse, error)
	// 修改NS集群的DDoS设置
	UpdateNSClusterDDoSProtection(context.Context, *UpdateNSClusterDDoSProtectionRequest) (*RPCSuccess, error)
	// 查找NS集群的主机地址
	FindNSClusterHosts(context.Context, *FindNSClusterHostsRequest) (*FindNSClusterHostsResponse, error)
	// 查找用户可以使用的主机地址
	FindAvailableNSHostsForUser(context.Context, *FindAvailableNSHostsForUserRequest) (*FindAvailableNSHostsForUserResponse, error)
	// 查找应答模式
	FindNSClusterAnswerConfig(context.Context, *FindNSClusterAnswerConfigRequest) (*FindNSClusterAnswerConfigResponse, error)
	// 设置应答模式
	UpdateNSClusterAnswerConfig(context.Context, *UpdateNSClusterAnswerConfigRequest) (*RPCSuccess, error)
	// 查找SOA配置
	FindNSClusterSOAConfig(context.Context, *FindNSClusterSOAConfigRequest) (*FindNSClusterSOAConfigResponse, error)
	// 设置SOA配置
	UpdateNSClusterSOAConfig(context.Context, *UpdateNSClusterSOAConfigRequest) (*RPCSuccess, error)
}

// UnimplementedNSClusterServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSClusterServiceServer struct {
}

func (UnimplementedNSClusterServiceServer) CreateNSCluster(context.Context, *CreateNSClusterRequest) (*CreateNSClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSCluster not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSCluster(context.Context, *UpdateNSClusterRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSCluster not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterAccessLog(context.Context, *FindNSClusterAccessLogRequest) (*FindNSClusterAccessLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterAccessLog not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterAccessLog(context.Context, *UpdateNSClusterAccessLogRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterAccessLog not implemented")
}
func (UnimplementedNSClusterServiceServer) DeleteNSCluster(context.Context, *DeleteNSCluster) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSCluster not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSCluster(context.Context, *FindNSClusterRequest) (*FindNSClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSCluster not implemented")
}
func (UnimplementedNSClusterServiceServer) CountAllNSClusters(context.Context, *CountAllNSClustersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSClusters not implemented")
}
func (UnimplementedNSClusterServiceServer) ListNSClusters(context.Context, *ListNSClustersRequest) (*ListNSClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSClusters not implemented")
}
func (UnimplementedNSClusterServiceServer) FindAllNSClusters(context.Context, *FindAllNSClustersRequest) (*FindAllNSClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNSClusters not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterRecursionConfig(context.Context, *UpdateNSClusterRecursionConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterRecursionConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterRecursionConfig(context.Context, *FindNSClusterRecursionConfigRequest) (*FindNSClusterRecursionConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterRecursionConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterTCPConfig(context.Context, *FindNSClusterTCPConfigRequest) (*FindNSClusterTCPConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterTCPConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterTCP(context.Context, *UpdateNSClusterTCPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterTCP not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterTLSConfig(context.Context, *FindNSClusterTLSConfigRequest) (*FindNSClusterTLSConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterTLSConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterTLS(context.Context, *UpdateNSClusterTLSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterTLS not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterUDPConfig(context.Context, *FindNSClusterUDPConfigRequest) (*FindNSClusterUDPConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterUDPConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterUDP(context.Context, *UpdateNSClusterUDPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterUDP not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterDoHConfig(context.Context, *FindNSClusterDoHConfigRequest) (*FindNSClusterDoHConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterDoHConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterDoH(context.Context, *UpdateNSClusterDoHRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterDoH not implemented")
}
func (UnimplementedNSClusterServiceServer) CountAllNSClustersWithSSLCertId(context.Context, *CountAllNSClustersWithSSLCertIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSClustersWithSSLCertId not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterDDoSProtection(context.Context, *FindNSClusterDDoSProtectionRequest) (*FindNSClusterDDoSProtectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterDDoSProtection not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterDDoSProtection(context.Context, *UpdateNSClusterDDoSProtectionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterDDoSProtection not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterHosts(context.Context, *FindNSClusterHostsRequest) (*FindNSClusterHostsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterHosts not implemented")
}
func (UnimplementedNSClusterServiceServer) FindAvailableNSHostsForUser(context.Context, *FindAvailableNSHostsForUserRequest) (*FindAvailableNSHostsForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAvailableNSHostsForUser not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterAnswerConfig(context.Context, *FindNSClusterAnswerConfigRequest) (*FindNSClusterAnswerConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterAnswerConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterAnswerConfig(context.Context, *UpdateNSClusterAnswerConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterAnswerConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) FindNSClusterSOAConfig(context.Context, *FindNSClusterSOAConfigRequest) (*FindNSClusterSOAConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSClusterSOAConfig not implemented")
}
func (UnimplementedNSClusterServiceServer) UpdateNSClusterSOAConfig(context.Context, *UpdateNSClusterSOAConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSClusterSOAConfig not implemented")
}

// UnsafeNSClusterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSClusterServiceServer will
// result in compilation errors.
type UnsafeNSClusterServiceServer interface {
	mustEmbedUnimplementedNSClusterServiceServer()
}

func RegisterNSClusterServiceServer(s grpc.ServiceRegistrar, srv NSClusterServiceServer) {
	s.RegisterService(&NSClusterService_ServiceDesc, srv)
}

func _NSClusterService_CreateNSCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).CreateNSCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_CreateNSCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).CreateNSCluster(ctx, req.(*CreateNSClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSCluster(ctx, req.(*UpdateNSClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterAccessLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterAccessLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterAccessLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterAccessLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterAccessLog(ctx, req.(*FindNSClusterAccessLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterAccessLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterAccessLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterAccessLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterAccessLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterAccessLog(ctx, req.(*UpdateNSClusterAccessLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_DeleteNSCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSCluster)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).DeleteNSCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_DeleteNSCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).DeleteNSCluster(ctx, req.(*DeleteNSCluster))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSCluster(ctx, req.(*FindNSClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_CountAllNSClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).CountAllNSClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_CountAllNSClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).CountAllNSClusters(ctx, req.(*CountAllNSClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_ListNSClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).ListNSClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_ListNSClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).ListNSClusters(ctx, req.(*ListNSClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindAllNSClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNSClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindAllNSClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindAllNSClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindAllNSClusters(ctx, req.(*FindAllNSClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterRecursionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterRecursionConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterRecursionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterRecursionConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterRecursionConfig(ctx, req.(*UpdateNSClusterRecursionConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterRecursionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterRecursionConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterRecursionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterRecursionConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterRecursionConfig(ctx, req.(*FindNSClusterRecursionConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterTCPConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterTCPConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterTCPConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterTCPConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterTCPConfig(ctx, req.(*FindNSClusterTCPConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterTCP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterTCPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterTCP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterTCP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterTCP(ctx, req.(*UpdateNSClusterTCPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterTLSConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterTLSConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterTLSConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterTLSConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterTLSConfig(ctx, req.(*FindNSClusterTLSConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterTLS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterTLSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterTLS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterTLS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterTLS(ctx, req.(*UpdateNSClusterTLSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterUDPConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterUDPConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterUDPConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterUDPConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterUDPConfig(ctx, req.(*FindNSClusterUDPConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterUDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterUDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterUDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterUDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterUDP(ctx, req.(*UpdateNSClusterUDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterDoHConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterDoHConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterDoHConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterDoHConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterDoHConfig(ctx, req.(*FindNSClusterDoHConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterDoH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterDoHRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterDoH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterDoH_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterDoH(ctx, req.(*UpdateNSClusterDoHRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_CountAllNSClustersWithSSLCertId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSClustersWithSSLCertIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).CountAllNSClustersWithSSLCertId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_CountAllNSClustersWithSSLCertId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).CountAllNSClustersWithSSLCertId(ctx, req.(*CountAllNSClustersWithSSLCertIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterDDoSProtection(ctx, req.(*FindNSClusterDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterDDoSProtection(ctx, req.(*UpdateNSClusterDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterHosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterHostsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterHosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterHosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterHosts(ctx, req.(*FindNSClusterHostsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindAvailableNSHostsForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAvailableNSHostsForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindAvailableNSHostsForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindAvailableNSHostsForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindAvailableNSHostsForUser(ctx, req.(*FindAvailableNSHostsForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterAnswerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterAnswerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterAnswerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterAnswerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterAnswerConfig(ctx, req.(*FindNSClusterAnswerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterAnswerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterAnswerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterAnswerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterAnswerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterAnswerConfig(ctx, req.(*UpdateNSClusterAnswerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_FindNSClusterSOAConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSClusterSOAConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).FindNSClusterSOAConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_FindNSClusterSOAConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).FindNSClusterSOAConfig(ctx, req.(*FindNSClusterSOAConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSClusterService_UpdateNSClusterSOAConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSClusterSOAConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSClusterServiceServer).UpdateNSClusterSOAConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSClusterService_UpdateNSClusterSOAConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSClusterServiceServer).UpdateNSClusterSOAConfig(ctx, req.(*UpdateNSClusterSOAConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSClusterService_ServiceDesc is the grpc.ServiceDesc for NSClusterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSClusterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSClusterService",
	HandlerType: (*NSClusterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSCluster",
			Handler:    _NSClusterService_CreateNSCluster_Handler,
		},
		{
			MethodName: "updateNSCluster",
			Handler:    _NSClusterService_UpdateNSCluster_Handler,
		},
		{
			MethodName: "findNSClusterAccessLog",
			Handler:    _NSClusterService_FindNSClusterAccessLog_Handler,
		},
		{
			MethodName: "updateNSClusterAccessLog",
			Handler:    _NSClusterService_UpdateNSClusterAccessLog_Handler,
		},
		{
			MethodName: "deleteNSCluster",
			Handler:    _NSClusterService_DeleteNSCluster_Handler,
		},
		{
			MethodName: "findNSCluster",
			Handler:    _NSClusterService_FindNSCluster_Handler,
		},
		{
			MethodName: "countAllNSClusters",
			Handler:    _NSClusterService_CountAllNSClusters_Handler,
		},
		{
			MethodName: "listNSClusters",
			Handler:    _NSClusterService_ListNSClusters_Handler,
		},
		{
			MethodName: "findAllNSClusters",
			Handler:    _NSClusterService_FindAllNSClusters_Handler,
		},
		{
			MethodName: "updateNSClusterRecursionConfig",
			Handler:    _NSClusterService_UpdateNSClusterRecursionConfig_Handler,
		},
		{
			MethodName: "findNSClusterRecursionConfig",
			Handler:    _NSClusterService_FindNSClusterRecursionConfig_Handler,
		},
		{
			MethodName: "findNSClusterTCPConfig",
			Handler:    _NSClusterService_FindNSClusterTCPConfig_Handler,
		},
		{
			MethodName: "updateNSClusterTCP",
			Handler:    _NSClusterService_UpdateNSClusterTCP_Handler,
		},
		{
			MethodName: "findNSClusterTLSConfig",
			Handler:    _NSClusterService_FindNSClusterTLSConfig_Handler,
		},
		{
			MethodName: "updateNSClusterTLS",
			Handler:    _NSClusterService_UpdateNSClusterTLS_Handler,
		},
		{
			MethodName: "findNSClusterUDPConfig",
			Handler:    _NSClusterService_FindNSClusterUDPConfig_Handler,
		},
		{
			MethodName: "updateNSClusterUDP",
			Handler:    _NSClusterService_UpdateNSClusterUDP_Handler,
		},
		{
			MethodName: "findNSClusterDoHConfig",
			Handler:    _NSClusterService_FindNSClusterDoHConfig_Handler,
		},
		{
			MethodName: "updateNSClusterDoH",
			Handler:    _NSClusterService_UpdateNSClusterDoH_Handler,
		},
		{
			MethodName: "countAllNSClustersWithSSLCertId",
			Handler:    _NSClusterService_CountAllNSClustersWithSSLCertId_Handler,
		},
		{
			MethodName: "findNSClusterDDoSProtection",
			Handler:    _NSClusterService_FindNSClusterDDoSProtection_Handler,
		},
		{
			MethodName: "updateNSClusterDDoSProtection",
			Handler:    _NSClusterService_UpdateNSClusterDDoSProtection_Handler,
		},
		{
			MethodName: "findNSClusterHosts",
			Handler:    _NSClusterService_FindNSClusterHosts_Handler,
		},
		{
			MethodName: "findAvailableNSHostsForUser",
			Handler:    _NSClusterService_FindAvailableNSHostsForUser_Handler,
		},
		{
			MethodName: "findNSClusterAnswerConfig",
			Handler:    _NSClusterService_FindNSClusterAnswerConfig_Handler,
		},
		{
			MethodName: "updateNSClusterAnswerConfig",
			Handler:    _NSClusterService_UpdateNSClusterAnswerConfig_Handler,
		},
		{
			MethodName: "findNSClusterSOAConfig",
			Handler:    _NSClusterService_FindNSClusterSOAConfig_Handler,
		},
		{
			MethodName: "updateNSClusterSOAConfig",
			Handler:    _NSClusterService_UpdateNSClusterSOAConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_cluster.proto",
}
