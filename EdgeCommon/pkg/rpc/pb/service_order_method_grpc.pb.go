// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_order_method.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OrderMethodService_CreateOrderMethod_FullMethodName              = "/pb.OrderMethodService/createOrderMethod"
	OrderMethodService_UpdateOrderMethod_FullMethodName              = "/pb.OrderMethodService/updateOrderMethod"
	OrderMethodService_DeleteOrderMethod_FullMethodName              = "/pb.OrderMethodService/deleteOrderMethod"
	OrderMethodService_FindEnabledOrderMethod_FullMethodName         = "/pb.OrderMethodService/findEnabledOrderMethod"
	OrderMethodService_FindEnabledOrderMethodWithCode_FullMethodName = "/pb.OrderMethodService/findEnabledOrderMethodWithCode"
	OrderMethodService_FindAllEnabledOrderMethods_FullMethodName     = "/pb.OrderMethodService/findAllEnabledOrderMethods"
	OrderMethodService_FindAllAvailableOrderMethods_FullMethodName   = "/pb.OrderMethodService/findAllAvailableOrderMethods"
)

// OrderMethodServiceClient is the client API for OrderMethodService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderMethodServiceClient interface {
	// 创建支付方式
	CreateOrderMethod(ctx context.Context, in *CreateOrderMethodRequest, opts ...grpc.CallOption) (*CreateOrderMethodResponse, error)
	// 修改支付方式
	UpdateOrderMethod(ctx context.Context, in *UpdateOrderMethodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除支付方式
	DeleteOrderMethod(ctx context.Context, in *DeleteOrderMethodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个支付方式
	FindEnabledOrderMethod(ctx context.Context, in *FindEnabledOrderMethodRequest, opts ...grpc.CallOption) (*FindEnabledOrderMethodResponse, error)
	// 根据代号查找支付方式
	FindEnabledOrderMethodWithCode(ctx context.Context, in *FindEnabledOrderMethodWithCodeRequest, opts ...grpc.CallOption) (*FindEnabledOrderMethodWithCodeResponse, error)
	// 查找所有支付方式
	FindAllEnabledOrderMethods(ctx context.Context, in *FindAllEnabledOrderMethodsRequest, opts ...grpc.CallOption) (*FindAllEnabledOrderMethodsResponse, error)
	// 查找所有已启用的支付方式
	FindAllAvailableOrderMethods(ctx context.Context, in *FindAllAvailableOrderMethodsRequest, opts ...grpc.CallOption) (*FindAllAvailableOrderMethodsResponse, error)
}

type orderMethodServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderMethodServiceClient(cc grpc.ClientConnInterface) OrderMethodServiceClient {
	return &orderMethodServiceClient{cc}
}

func (c *orderMethodServiceClient) CreateOrderMethod(ctx context.Context, in *CreateOrderMethodRequest, opts ...grpc.CallOption) (*CreateOrderMethodResponse, error) {
	out := new(CreateOrderMethodResponse)
	err := c.cc.Invoke(ctx, OrderMethodService_CreateOrderMethod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderMethodServiceClient) UpdateOrderMethod(ctx context.Context, in *UpdateOrderMethodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, OrderMethodService_UpdateOrderMethod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderMethodServiceClient) DeleteOrderMethod(ctx context.Context, in *DeleteOrderMethodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, OrderMethodService_DeleteOrderMethod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderMethodServiceClient) FindEnabledOrderMethod(ctx context.Context, in *FindEnabledOrderMethodRequest, opts ...grpc.CallOption) (*FindEnabledOrderMethodResponse, error) {
	out := new(FindEnabledOrderMethodResponse)
	err := c.cc.Invoke(ctx, OrderMethodService_FindEnabledOrderMethod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderMethodServiceClient) FindEnabledOrderMethodWithCode(ctx context.Context, in *FindEnabledOrderMethodWithCodeRequest, opts ...grpc.CallOption) (*FindEnabledOrderMethodWithCodeResponse, error) {
	out := new(FindEnabledOrderMethodWithCodeResponse)
	err := c.cc.Invoke(ctx, OrderMethodService_FindEnabledOrderMethodWithCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderMethodServiceClient) FindAllEnabledOrderMethods(ctx context.Context, in *FindAllEnabledOrderMethodsRequest, opts ...grpc.CallOption) (*FindAllEnabledOrderMethodsResponse, error) {
	out := new(FindAllEnabledOrderMethodsResponse)
	err := c.cc.Invoke(ctx, OrderMethodService_FindAllEnabledOrderMethods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderMethodServiceClient) FindAllAvailableOrderMethods(ctx context.Context, in *FindAllAvailableOrderMethodsRequest, opts ...grpc.CallOption) (*FindAllAvailableOrderMethodsResponse, error) {
	out := new(FindAllAvailableOrderMethodsResponse)
	err := c.cc.Invoke(ctx, OrderMethodService_FindAllAvailableOrderMethods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderMethodServiceServer is the server API for OrderMethodService service.
// All implementations should embed UnimplementedOrderMethodServiceServer
// for forward compatibility
type OrderMethodServiceServer interface {
	// 创建支付方式
	CreateOrderMethod(context.Context, *CreateOrderMethodRequest) (*CreateOrderMethodResponse, error)
	// 修改支付方式
	UpdateOrderMethod(context.Context, *UpdateOrderMethodRequest) (*RPCSuccess, error)
	// 删除支付方式
	DeleteOrderMethod(context.Context, *DeleteOrderMethodRequest) (*RPCSuccess, error)
	// 查找单个支付方式
	FindEnabledOrderMethod(context.Context, *FindEnabledOrderMethodRequest) (*FindEnabledOrderMethodResponse, error)
	// 根据代号查找支付方式
	FindEnabledOrderMethodWithCode(context.Context, *FindEnabledOrderMethodWithCodeRequest) (*FindEnabledOrderMethodWithCodeResponse, error)
	// 查找所有支付方式
	FindAllEnabledOrderMethods(context.Context, *FindAllEnabledOrderMethodsRequest) (*FindAllEnabledOrderMethodsResponse, error)
	// 查找所有已启用的支付方式
	FindAllAvailableOrderMethods(context.Context, *FindAllAvailableOrderMethodsRequest) (*FindAllAvailableOrderMethodsResponse, error)
}

// UnimplementedOrderMethodServiceServer should be embedded to have forward compatible implementations.
type UnimplementedOrderMethodServiceServer struct {
}

func (UnimplementedOrderMethodServiceServer) CreateOrderMethod(context.Context, *CreateOrderMethodRequest) (*CreateOrderMethodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrderMethod not implemented")
}
func (UnimplementedOrderMethodServiceServer) UpdateOrderMethod(context.Context, *UpdateOrderMethodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrderMethod not implemented")
}
func (UnimplementedOrderMethodServiceServer) DeleteOrderMethod(context.Context, *DeleteOrderMethodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOrderMethod not implemented")
}
func (UnimplementedOrderMethodServiceServer) FindEnabledOrderMethod(context.Context, *FindEnabledOrderMethodRequest) (*FindEnabledOrderMethodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledOrderMethod not implemented")
}
func (UnimplementedOrderMethodServiceServer) FindEnabledOrderMethodWithCode(context.Context, *FindEnabledOrderMethodWithCodeRequest) (*FindEnabledOrderMethodWithCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledOrderMethodWithCode not implemented")
}
func (UnimplementedOrderMethodServiceServer) FindAllEnabledOrderMethods(context.Context, *FindAllEnabledOrderMethodsRequest) (*FindAllEnabledOrderMethodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledOrderMethods not implemented")
}
func (UnimplementedOrderMethodServiceServer) FindAllAvailableOrderMethods(context.Context, *FindAllAvailableOrderMethodsRequest) (*FindAllAvailableOrderMethodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailableOrderMethods not implemented")
}

// UnsafeOrderMethodServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderMethodServiceServer will
// result in compilation errors.
type UnsafeOrderMethodServiceServer interface {
	mustEmbedUnimplementedOrderMethodServiceServer()
}

func RegisterOrderMethodServiceServer(s grpc.ServiceRegistrar, srv OrderMethodServiceServer) {
	s.RegisterService(&OrderMethodService_ServiceDesc, srv)
}

func _OrderMethodService_CreateOrderMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderMethodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).CreateOrderMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_CreateOrderMethod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).CreateOrderMethod(ctx, req.(*CreateOrderMethodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderMethodService_UpdateOrderMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrderMethodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).UpdateOrderMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_UpdateOrderMethod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).UpdateOrderMethod(ctx, req.(*UpdateOrderMethodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderMethodService_DeleteOrderMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteOrderMethodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).DeleteOrderMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_DeleteOrderMethod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).DeleteOrderMethod(ctx, req.(*DeleteOrderMethodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderMethodService_FindEnabledOrderMethod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledOrderMethodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).FindEnabledOrderMethod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_FindEnabledOrderMethod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).FindEnabledOrderMethod(ctx, req.(*FindEnabledOrderMethodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderMethodService_FindEnabledOrderMethodWithCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledOrderMethodWithCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).FindEnabledOrderMethodWithCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_FindEnabledOrderMethodWithCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).FindEnabledOrderMethodWithCode(ctx, req.(*FindEnabledOrderMethodWithCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderMethodService_FindAllEnabledOrderMethods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledOrderMethodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).FindAllEnabledOrderMethods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_FindAllEnabledOrderMethods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).FindAllEnabledOrderMethods(ctx, req.(*FindAllEnabledOrderMethodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderMethodService_FindAllAvailableOrderMethods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailableOrderMethodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMethodServiceServer).FindAllAvailableOrderMethods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrderMethodService_FindAllAvailableOrderMethods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMethodServiceServer).FindAllAvailableOrderMethods(ctx, req.(*FindAllAvailableOrderMethodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderMethodService_ServiceDesc is the grpc.ServiceDesc for OrderMethodService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderMethodService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.OrderMethodService",
	HandlerType: (*OrderMethodServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createOrderMethod",
			Handler:    _OrderMethodService_CreateOrderMethod_Handler,
		},
		{
			MethodName: "updateOrderMethod",
			Handler:    _OrderMethodService_UpdateOrderMethod_Handler,
		},
		{
			MethodName: "deleteOrderMethod",
			Handler:    _OrderMethodService_DeleteOrderMethod_Handler,
		},
		{
			MethodName: "findEnabledOrderMethod",
			Handler:    _OrderMethodService_FindEnabledOrderMethod_Handler,
		},
		{
			MethodName: "findEnabledOrderMethodWithCode",
			Handler:    _OrderMethodService_FindEnabledOrderMethodWithCode_Handler,
		},
		{
			MethodName: "findAllEnabledOrderMethods",
			Handler:    _OrderMethodService_FindAllEnabledOrderMethods_Handler,
		},
		{
			MethodName: "findAllAvailableOrderMethods",
			Handler:    _OrderMethodService_FindAllAvailableOrderMethods_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_order_method.proto",
}
