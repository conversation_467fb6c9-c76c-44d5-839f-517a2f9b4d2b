// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ad_package_price.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ADPackagePriceService_UpdateADPackagePrice_FullMethodName   = "/pb.ADPackagePriceService/updateADPackagePrice"
	ADPackagePriceService_FindADPackagePrice_FullMethodName     = "/pb.ADPackagePriceService/findADPackagePrice"
	ADPackagePriceService_CountADPackagePrices_FullMethodName   = "/pb.ADPackagePriceService/countADPackagePrices"
	ADPackagePriceService_FindADPackagePrices_FullMethodName    = "/pb.ADPackagePriceService/findADPackagePrices"
	ADPackagePriceService_FindAllADPackagePrices_FullMethodName = "/pb.ADPackagePriceService/findAllADPackagePrices"
)

// ADPackagePriceServiceClient is the client API for ADPackagePriceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ADPackagePriceServiceClient interface {
	// 设置高防产品价格
	UpdateADPackagePrice(ctx context.Context, in *UpdateADPackagePriceRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取单个高防产品具体价格
	FindADPackagePrice(ctx context.Context, in *FindADPackagePriceRequest, opts ...grpc.CallOption) (*FindADPackagePriceResponse, error)
	// 计算高防产品价格项数量
	CountADPackagePrices(ctx context.Context, in *CountADPackagePricesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找高防产品价格
	FindADPackagePrices(ctx context.Context, in *FindADPackagePricesRequest, opts ...grpc.CallOption) (*FindADPackagePricesResponse, error)
	// 查找所有高防产品价格
	FindAllADPackagePrices(ctx context.Context, in *FindAllADPackagePricesRequest, opts ...grpc.CallOption) (*FindAllADPackagePricesResponse, error)
}

type aDPackagePriceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewADPackagePriceServiceClient(cc grpc.ClientConnInterface) ADPackagePriceServiceClient {
	return &aDPackagePriceServiceClient{cc}
}

func (c *aDPackagePriceServiceClient) UpdateADPackagePrice(ctx context.Context, in *UpdateADPackagePriceRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ADPackagePriceService_UpdateADPackagePrice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePriceServiceClient) FindADPackagePrice(ctx context.Context, in *FindADPackagePriceRequest, opts ...grpc.CallOption) (*FindADPackagePriceResponse, error) {
	out := new(FindADPackagePriceResponse)
	err := c.cc.Invoke(ctx, ADPackagePriceService_FindADPackagePrice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePriceServiceClient) CountADPackagePrices(ctx context.Context, in *CountADPackagePricesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ADPackagePriceService_CountADPackagePrices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePriceServiceClient) FindADPackagePrices(ctx context.Context, in *FindADPackagePricesRequest, opts ...grpc.CallOption) (*FindADPackagePricesResponse, error) {
	out := new(FindADPackagePricesResponse)
	err := c.cc.Invoke(ctx, ADPackagePriceService_FindADPackagePrices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePriceServiceClient) FindAllADPackagePrices(ctx context.Context, in *FindAllADPackagePricesRequest, opts ...grpc.CallOption) (*FindAllADPackagePricesResponse, error) {
	out := new(FindAllADPackagePricesResponse)
	err := c.cc.Invoke(ctx, ADPackagePriceService_FindAllADPackagePrices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ADPackagePriceServiceServer is the server API for ADPackagePriceService service.
// All implementations should embed UnimplementedADPackagePriceServiceServer
// for forward compatibility
type ADPackagePriceServiceServer interface {
	// 设置高防产品价格
	UpdateADPackagePrice(context.Context, *UpdateADPackagePriceRequest) (*RPCSuccess, error)
	// 获取单个高防产品具体价格
	FindADPackagePrice(context.Context, *FindADPackagePriceRequest) (*FindADPackagePriceResponse, error)
	// 计算高防产品价格项数量
	CountADPackagePrices(context.Context, *CountADPackagePricesRequest) (*RPCCountResponse, error)
	// 查找高防产品价格
	FindADPackagePrices(context.Context, *FindADPackagePricesRequest) (*FindADPackagePricesResponse, error)
	// 查找所有高防产品价格
	FindAllADPackagePrices(context.Context, *FindAllADPackagePricesRequest) (*FindAllADPackagePricesResponse, error)
}

// UnimplementedADPackagePriceServiceServer should be embedded to have forward compatible implementations.
type UnimplementedADPackagePriceServiceServer struct {
}

func (UnimplementedADPackagePriceServiceServer) UpdateADPackagePrice(context.Context, *UpdateADPackagePriceRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateADPackagePrice not implemented")
}
func (UnimplementedADPackagePriceServiceServer) FindADPackagePrice(context.Context, *FindADPackagePriceRequest) (*FindADPackagePriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindADPackagePrice not implemented")
}
func (UnimplementedADPackagePriceServiceServer) CountADPackagePrices(context.Context, *CountADPackagePricesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountADPackagePrices not implemented")
}
func (UnimplementedADPackagePriceServiceServer) FindADPackagePrices(context.Context, *FindADPackagePricesRequest) (*FindADPackagePricesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindADPackagePrices not implemented")
}
func (UnimplementedADPackagePriceServiceServer) FindAllADPackagePrices(context.Context, *FindAllADPackagePricesRequest) (*FindAllADPackagePricesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllADPackagePrices not implemented")
}

// UnsafeADPackagePriceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ADPackagePriceServiceServer will
// result in compilation errors.
type UnsafeADPackagePriceServiceServer interface {
	mustEmbedUnimplementedADPackagePriceServiceServer()
}

func RegisterADPackagePriceServiceServer(s grpc.ServiceRegistrar, srv ADPackagePriceServiceServer) {
	s.RegisterService(&ADPackagePriceService_ServiceDesc, srv)
}

func _ADPackagePriceService_UpdateADPackagePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateADPackagePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePriceServiceServer).UpdateADPackagePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePriceService_UpdateADPackagePrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePriceServiceServer).UpdateADPackagePrice(ctx, req.(*UpdateADPackagePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePriceService_FindADPackagePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindADPackagePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePriceServiceServer).FindADPackagePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePriceService_FindADPackagePrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePriceServiceServer).FindADPackagePrice(ctx, req.(*FindADPackagePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePriceService_CountADPackagePrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountADPackagePricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePriceServiceServer).CountADPackagePrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePriceService_CountADPackagePrices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePriceServiceServer).CountADPackagePrices(ctx, req.(*CountADPackagePricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePriceService_FindADPackagePrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindADPackagePricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePriceServiceServer).FindADPackagePrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePriceService_FindADPackagePrices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePriceServiceServer).FindADPackagePrices(ctx, req.(*FindADPackagePricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePriceService_FindAllADPackagePrices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllADPackagePricesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePriceServiceServer).FindAllADPackagePrices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePriceService_FindAllADPackagePrices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePriceServiceServer).FindAllADPackagePrices(ctx, req.(*FindAllADPackagePricesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ADPackagePriceService_ServiceDesc is the grpc.ServiceDesc for ADPackagePriceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ADPackagePriceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ADPackagePriceService",
	HandlerType: (*ADPackagePriceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "updateADPackagePrice",
			Handler:    _ADPackagePriceService_UpdateADPackagePrice_Handler,
		},
		{
			MethodName: "findADPackagePrice",
			Handler:    _ADPackagePriceService_FindADPackagePrice_Handler,
		},
		{
			MethodName: "countADPackagePrices",
			Handler:    _ADPackagePriceService_CountADPackagePrices_Handler,
		},
		{
			MethodName: "findADPackagePrices",
			Handler:    _ADPackagePriceService_FindADPackagePrices_Handler,
		},
		{
			MethodName: "findAllADPackagePrices",
			Handler:    _ADPackagePriceService_FindAllADPackagePrices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ad_package_price.proto",
}
