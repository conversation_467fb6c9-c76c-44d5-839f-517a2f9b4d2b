// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_plan.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserPlan struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Id      int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`           // 套餐ID
	UserId  int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`   // 用户ID
	PlanId  int64                  `protobuf:"varint,3,opt,name=planId,proto3" json:"planId,omitempty"`   // 套餐定义ID
	IsOn    bool                   `protobuf:"varint,4,opt,name=isOn,proto3" json:"isOn,omitempty"`       // 是否启用
	DayTo   string                 `protobuf:"bytes,5,opt,name=dayTo,proto3" json:"dayTo,omitempty"`      // 到期日期Y-m-d
	Name    string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`        // 自定义备注名称
	User    *User                  `protobuf:"bytes,30,opt,name=user,proto3" json:"user,omitempty"`       // 用户信息
	Plan    *Plan                  `protobuf:"bytes,31,opt,name=plan,proto3" json:"plan,omitempty"`       // 套餐定义信息
	Servers []*Server              `protobuf:"bytes,33,rep,name=servers,proto3" json:"servers,omitempty"` // 绑定的网站列表
	// Deprecated: Marked as deprecated in models/model_user_plan.proto.
	Server        *Server `protobuf:"bytes,32,opt,name=server,proto3" json:"server,omitempty"` // 绑定的网站，已过期，使用 servers 代替
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserPlan) Reset() {
	*x = UserPlan{}
	mi := &file_models_model_user_plan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPlan) ProtoMessage() {}

func (x *UserPlan) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_plan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPlan.ProtoReflect.Descriptor instead.
func (*UserPlan) Descriptor() ([]byte, []int) {
	return file_models_model_user_plan_proto_rawDescGZIP(), []int{0}
}

func (x *UserPlan) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserPlan) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserPlan) GetPlanId() int64 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *UserPlan) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *UserPlan) GetDayTo() string {
	if x != nil {
		return x.DayTo
	}
	return ""
}

func (x *UserPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserPlan) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserPlan) GetPlan() *Plan {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *UserPlan) GetServers() []*Server {
	if x != nil {
		return x.Servers
	}
	return nil
}

// Deprecated: Marked as deprecated in models/model_user_plan.proto.
func (x *UserPlan) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

var File_models_model_user_plan_proto protoreflect.FileDescriptor

const file_models_model_user_plan_proto_rawDesc = "" +
	"\n" +
	"\x1cmodels/model_user_plan.proto\x12\x02pb\x1a\x17models/model_user.proto\x1a\x17models/model_plan.proto\x1a\x19models/model_server.proto\"\x92\x02\n" +
	"\bUserPlan\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x16\n" +
	"\x06planId\x18\x03 \x01(\x03R\x06planId\x12\x12\n" +
	"\x04isOn\x18\x04 \x01(\bR\x04isOn\x12\x14\n" +
	"\x05dayTo\x18\x05 \x01(\tR\x05dayTo\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12\x1c\n" +
	"\x04user\x18\x1e \x01(\v2\b.pb.UserR\x04user\x12\x1c\n" +
	"\x04plan\x18\x1f \x01(\v2\b.pb.PlanR\x04plan\x12$\n" +
	"\aservers\x18! \x03(\v2\n" +
	".pb.ServerR\aservers\x12&\n" +
	"\x06server\x18  \x01(\v2\n" +
	".pb.ServerB\x02\x18\x01R\x06serverB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_plan_proto_rawDescOnce sync.Once
	file_models_model_user_plan_proto_rawDescData []byte
)

func file_models_model_user_plan_proto_rawDescGZIP() []byte {
	file_models_model_user_plan_proto_rawDescOnce.Do(func() {
		file_models_model_user_plan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_plan_proto_rawDesc), len(file_models_model_user_plan_proto_rawDesc)))
	})
	return file_models_model_user_plan_proto_rawDescData
}

var file_models_model_user_plan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_plan_proto_goTypes = []any{
	(*UserPlan)(nil), // 0: pb.UserPlan
	(*User)(nil),     // 1: pb.User
	(*Plan)(nil),     // 2: pb.Plan
	(*Server)(nil),   // 3: pb.Server
}
var file_models_model_user_plan_proto_depIdxs = []int32{
	1, // 0: pb.UserPlan.user:type_name -> pb.User
	2, // 1: pb.UserPlan.plan:type_name -> pb.Plan
	3, // 2: pb.UserPlan.servers:type_name -> pb.Server
	3, // 3: pb.UserPlan.server:type_name -> pb.Server
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_models_model_user_plan_proto_init() }
func file_models_model_user_plan_proto_init() {
	if File_models_model_user_plan_proto != nil {
		return
	}
	file_models_model_user_proto_init()
	file_models_model_plan_proto_init()
	file_models_model_server_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_plan_proto_rawDesc), len(file_models_model_user_plan_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_plan_proto_goTypes,
		DependencyIndexes: file_models_model_user_plan_proto_depIdxs,
		MessageInfos:      file_models_model_user_plan_proto_msgTypes,
	}.Build()
	File_models_model_user_plan_proto = out.File
	file_models_model_user_plan_proto_goTypes = nil
	file_models_model_user_plan_proto_depIdxs = nil
}
