// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_account.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserAccount struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                    // 账户ID
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`            // 用户ID
	Total         float64                `protobuf:"fixed64,3,opt,name=total,proto3" json:"total,omitempty"`             // 总可用余额
	TotalFrozen   float64                `protobuf:"fixed64,4,opt,name=totalFrozen,proto3" json:"totalFrozen,omitempty"` // 总冻结余额
	User          *User                  `protobuf:"bytes,30,opt,name=user,proto3" json:"user,omitempty"`                // 用户信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAccount) Reset() {
	*x = UserAccount{}
	mi := &file_models_model_user_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccount) ProtoMessage() {}

func (x *UserAccount) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccount.ProtoReflect.Descriptor instead.
func (*UserAccount) Descriptor() ([]byte, []int) {
	return file_models_model_user_account_proto_rawDescGZIP(), []int{0}
}

func (x *UserAccount) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAccount) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserAccount) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *UserAccount) GetTotalFrozen() float64 {
	if x != nil {
		return x.TotalFrozen
	}
	return 0
}

func (x *UserAccount) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

var File_models_model_user_account_proto protoreflect.FileDescriptor

const file_models_model_user_account_proto_rawDesc = "" +
	"\n" +
	"\x1fmodels/model_user_account.proto\x12\x02pb\x1a\x17models/model_user.proto\"\x8b\x01\n" +
	"\vUserAccount\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x01R\x05total\x12 \n" +
	"\vtotalFrozen\x18\x04 \x01(\x01R\vtotalFrozen\x12\x1c\n" +
	"\x04user\x18\x1e \x01(\v2\b.pb.UserR\x04userB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_account_proto_rawDescOnce sync.Once
	file_models_model_user_account_proto_rawDescData []byte
)

func file_models_model_user_account_proto_rawDescGZIP() []byte {
	file_models_model_user_account_proto_rawDescOnce.Do(func() {
		file_models_model_user_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_account_proto_rawDesc), len(file_models_model_user_account_proto_rawDesc)))
	})
	return file_models_model_user_account_proto_rawDescData
}

var file_models_model_user_account_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_account_proto_goTypes = []any{
	(*UserAccount)(nil), // 0: pb.UserAccount
	(*User)(nil),        // 1: pb.User
}
var file_models_model_user_account_proto_depIdxs = []int32{
	1, // 0: pb.UserAccount.user:type_name -> pb.User
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_user_account_proto_init() }
func file_models_model_user_account_proto_init() {
	if File_models_model_user_account_proto != nil {
		return
	}
	file_models_model_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_account_proto_rawDesc), len(file_models_model_user_account_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_account_proto_goTypes,
		DependencyIndexes: file_models_model_user_account_proto_depIdxs,
		MessageInfos:      file_models_model_user_account_proto_msgTypes,
	}.Build()
	File_models_model_user_account_proto = out.File
	file_models_model_user_account_proto_goTypes = nil
	file_models_model_user_account_proto_depIdxs = nil
}
