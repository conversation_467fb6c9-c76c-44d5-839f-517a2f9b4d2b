// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_origin.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Origin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOn          bool                   `protobuf:"varint,2,opt,name=isOn,proto3" json:"isOn,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Addr          *NetworkAddress        `protobuf:"bytes,4,opt,name=addr,proto3" json:"addr,omitempty"`       // 源站网络地址
	OssJSON       []byte                 `protobuf:"bytes,9,opt,name=ossJSON,proto3" json:"ossJSON,omitempty"` // 源站网络地址为oss:开头时有此内容
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Domains       []string               `protobuf:"bytes,6,rep,name=domains,proto3" json:"domains,omitempty"`
	Host          string                 `protobuf:"bytes,7,opt,name=host,proto3" json:"host,omitempty"`
	FollowPort    bool                   `protobuf:"varint,8,opt,name=followPort,proto3" json:"followPort,omitempty"`
	Http2Enabled  bool                   `protobuf:"varint,10,opt,name=http2Enabled,proto3" json:"http2Enabled,omitempty"` // 是否支持HTTP/2，只在HTTPS源站时生效
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Origin) Reset() {
	*x = Origin{}
	mi := &file_models_model_origin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Origin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Origin) ProtoMessage() {}

func (x *Origin) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_origin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Origin.ProtoReflect.Descriptor instead.
func (*Origin) Descriptor() ([]byte, []int) {
	return file_models_model_origin_proto_rawDescGZIP(), []int{0}
}

func (x *Origin) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Origin) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *Origin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Origin) GetAddr() *NetworkAddress {
	if x != nil {
		return x.Addr
	}
	return nil
}

func (x *Origin) GetOssJSON() []byte {
	if x != nil {
		return x.OssJSON
	}
	return nil
}

func (x *Origin) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Origin) GetDomains() []string {
	if x != nil {
		return x.Domains
	}
	return nil
}

func (x *Origin) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Origin) GetFollowPort() bool {
	if x != nil {
		return x.FollowPort
	}
	return false
}

func (x *Origin) GetHttp2Enabled() bool {
	if x != nil {
		return x.Http2Enabled
	}
	return false
}

var File_models_model_origin_proto protoreflect.FileDescriptor

const file_models_model_origin_proto_rawDesc = "" +
	"\n" +
	"\x19models/model_origin.proto\x12\x02pb\x1a\"models/model_network_address.proto\"\x96\x02\n" +
	"\x06Origin\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04isOn\x18\x02 \x01(\bR\x04isOn\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12&\n" +
	"\x04addr\x18\x04 \x01(\v2\x12.pb.NetworkAddressR\x04addr\x12\x18\n" +
	"\aossJSON\x18\t \x01(\fR\aossJSON\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x18\n" +
	"\adomains\x18\x06 \x03(\tR\adomains\x12\x12\n" +
	"\x04host\x18\a \x01(\tR\x04host\x12\x1e\n" +
	"\n" +
	"followPort\x18\b \x01(\bR\n" +
	"followPort\x12\"\n" +
	"\fhttp2Enabled\x18\n" +
	" \x01(\bR\fhttp2EnabledB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_origin_proto_rawDescOnce sync.Once
	file_models_model_origin_proto_rawDescData []byte
)

func file_models_model_origin_proto_rawDescGZIP() []byte {
	file_models_model_origin_proto_rawDescOnce.Do(func() {
		file_models_model_origin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_origin_proto_rawDesc), len(file_models_model_origin_proto_rawDesc)))
	})
	return file_models_model_origin_proto_rawDescData
}

var file_models_model_origin_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_origin_proto_goTypes = []any{
	(*Origin)(nil),         // 0: pb.Origin
	(*NetworkAddress)(nil), // 1: pb.NetworkAddress
}
var file_models_model_origin_proto_depIdxs = []int32{
	1, // 0: pb.Origin.addr:type_name -> pb.NetworkAddress
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_origin_proto_init() }
func file_models_model_origin_proto_init() {
	if File_models_model_origin_proto != nil {
		return
	}
	file_models_model_network_address_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_origin_proto_rawDesc), len(file_models_model_origin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_origin_proto_goTypes,
		DependencyIndexes: file_models_model_origin_proto_depIdxs,
		MessageInfos:      file_models_model_origin_proto_msgTypes,
	}.Build()
	File_models_model_origin_proto = out.File
	file_models_model_origin_proto_goTypes = nil
	file_models_model_origin_proto_depIdxs = nil
}
