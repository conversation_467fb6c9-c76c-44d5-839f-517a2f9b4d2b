// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_report_node_group.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReportNodeGroupService_CreateReportNodeGroup_FullMethodName           = "/pb.ReportNodeGroupService/createReportNodeGroup"
	ReportNodeGroupService_UpdateReportNodeGroup_FullMethodName           = "/pb.ReportNodeGroupService/updateReportNodeGroup"
	ReportNodeGroupService_DeleteReportNodeGroup_FullMethodName           = "/pb.ReportNodeGroupService/deleteReportNodeGroup"
	ReportNodeGroupService_FindAllEnabledReportNodeGroups_FullMethodName  = "/pb.ReportNodeGroupService/findAllEnabledReportNodeGroups"
	ReportNodeGroupService_FindEnabledReportNodeGroup_FullMethodName      = "/pb.ReportNodeGroupService/findEnabledReportNodeGroup"
	ReportNodeGroupService_CountAllEnabledReportNodeGroups_FullMethodName = "/pb.ReportNodeGroupService/countAllEnabledReportNodeGroups"
)

// ReportNodeGroupServiceClient is the client API for ReportNodeGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportNodeGroupServiceClient interface {
	// 创建分组
	CreateReportNodeGroup(ctx context.Context, in *CreateReportNodeGroupRequest, opts ...grpc.CallOption) (*CreateReportNodeGroupResponse, error)
	// 修改分组
	UpdateReportNodeGroup(ctx context.Context, in *UpdateReportNodeGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除分组
	DeleteReportNodeGroup(ctx context.Context, in *DeleteReportNodeGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找所有分组
	FindAllEnabledReportNodeGroups(ctx context.Context, in *FindAllEnabledReportNodeGroupsRequest, opts ...grpc.CallOption) (*FindAllEnabledReportNodeGroupsResponse, error)
	// 查找单个分组
	FindEnabledReportNodeGroup(ctx context.Context, in *FindEnabledReportNodeGroupRequest, opts ...grpc.CallOption) (*FindEnabledReportNodeGroupResponse, error)
	// 计算所有分组数量
	CountAllEnabledReportNodeGroups(ctx context.Context, in *CountAllEnabledReportNodeGroupsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
}

type reportNodeGroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReportNodeGroupServiceClient(cc grpc.ClientConnInterface) ReportNodeGroupServiceClient {
	return &reportNodeGroupServiceClient{cc}
}

func (c *reportNodeGroupServiceClient) CreateReportNodeGroup(ctx context.Context, in *CreateReportNodeGroupRequest, opts ...grpc.CallOption) (*CreateReportNodeGroupResponse, error) {
	out := new(CreateReportNodeGroupResponse)
	err := c.cc.Invoke(ctx, ReportNodeGroupService_CreateReportNodeGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeGroupServiceClient) UpdateReportNodeGroup(ctx context.Context, in *UpdateReportNodeGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportNodeGroupService_UpdateReportNodeGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeGroupServiceClient) DeleteReportNodeGroup(ctx context.Context, in *DeleteReportNodeGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ReportNodeGroupService_DeleteReportNodeGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeGroupServiceClient) FindAllEnabledReportNodeGroups(ctx context.Context, in *FindAllEnabledReportNodeGroupsRequest, opts ...grpc.CallOption) (*FindAllEnabledReportNodeGroupsResponse, error) {
	out := new(FindAllEnabledReportNodeGroupsResponse)
	err := c.cc.Invoke(ctx, ReportNodeGroupService_FindAllEnabledReportNodeGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeGroupServiceClient) FindEnabledReportNodeGroup(ctx context.Context, in *FindEnabledReportNodeGroupRequest, opts ...grpc.CallOption) (*FindEnabledReportNodeGroupResponse, error) {
	out := new(FindEnabledReportNodeGroupResponse)
	err := c.cc.Invoke(ctx, ReportNodeGroupService_FindEnabledReportNodeGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportNodeGroupServiceClient) CountAllEnabledReportNodeGroups(ctx context.Context, in *CountAllEnabledReportNodeGroupsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ReportNodeGroupService_CountAllEnabledReportNodeGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportNodeGroupServiceServer is the server API for ReportNodeGroupService service.
// All implementations should embed UnimplementedReportNodeGroupServiceServer
// for forward compatibility
type ReportNodeGroupServiceServer interface {
	// 创建分组
	CreateReportNodeGroup(context.Context, *CreateReportNodeGroupRequest) (*CreateReportNodeGroupResponse, error)
	// 修改分组
	UpdateReportNodeGroup(context.Context, *UpdateReportNodeGroupRequest) (*RPCSuccess, error)
	// 删除分组
	DeleteReportNodeGroup(context.Context, *DeleteReportNodeGroupRequest) (*RPCSuccess, error)
	// 查找所有分组
	FindAllEnabledReportNodeGroups(context.Context, *FindAllEnabledReportNodeGroupsRequest) (*FindAllEnabledReportNodeGroupsResponse, error)
	// 查找单个分组
	FindEnabledReportNodeGroup(context.Context, *FindEnabledReportNodeGroupRequest) (*FindEnabledReportNodeGroupResponse, error)
	// 计算所有分组数量
	CountAllEnabledReportNodeGroups(context.Context, *CountAllEnabledReportNodeGroupsRequest) (*RPCCountResponse, error)
}

// UnimplementedReportNodeGroupServiceServer should be embedded to have forward compatible implementations.
type UnimplementedReportNodeGroupServiceServer struct {
}

func (UnimplementedReportNodeGroupServiceServer) CreateReportNodeGroup(context.Context, *CreateReportNodeGroupRequest) (*CreateReportNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReportNodeGroup not implemented")
}
func (UnimplementedReportNodeGroupServiceServer) UpdateReportNodeGroup(context.Context, *UpdateReportNodeGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReportNodeGroup not implemented")
}
func (UnimplementedReportNodeGroupServiceServer) DeleteReportNodeGroup(context.Context, *DeleteReportNodeGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteReportNodeGroup not implemented")
}
func (UnimplementedReportNodeGroupServiceServer) FindAllEnabledReportNodeGroups(context.Context, *FindAllEnabledReportNodeGroupsRequest) (*FindAllEnabledReportNodeGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledReportNodeGroups not implemented")
}
func (UnimplementedReportNodeGroupServiceServer) FindEnabledReportNodeGroup(context.Context, *FindEnabledReportNodeGroupRequest) (*FindEnabledReportNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledReportNodeGroup not implemented")
}
func (UnimplementedReportNodeGroupServiceServer) CountAllEnabledReportNodeGroups(context.Context, *CountAllEnabledReportNodeGroupsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledReportNodeGroups not implemented")
}

// UnsafeReportNodeGroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportNodeGroupServiceServer will
// result in compilation errors.
type UnsafeReportNodeGroupServiceServer interface {
	mustEmbedUnimplementedReportNodeGroupServiceServer()
}

func RegisterReportNodeGroupServiceServer(s grpc.ServiceRegistrar, srv ReportNodeGroupServiceServer) {
	s.RegisterService(&ReportNodeGroupService_ServiceDesc, srv)
}

func _ReportNodeGroupService_CreateReportNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReportNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeGroupServiceServer).CreateReportNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeGroupService_CreateReportNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeGroupServiceServer).CreateReportNodeGroup(ctx, req.(*CreateReportNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeGroupService_UpdateReportNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeGroupServiceServer).UpdateReportNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeGroupService_UpdateReportNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeGroupServiceServer).UpdateReportNodeGroup(ctx, req.(*UpdateReportNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeGroupService_DeleteReportNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteReportNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeGroupServiceServer).DeleteReportNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeGroupService_DeleteReportNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeGroupServiceServer).DeleteReportNodeGroup(ctx, req.(*DeleteReportNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeGroupService_FindAllEnabledReportNodeGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledReportNodeGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeGroupServiceServer).FindAllEnabledReportNodeGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeGroupService_FindAllEnabledReportNodeGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeGroupServiceServer).FindAllEnabledReportNodeGroups(ctx, req.(*FindAllEnabledReportNodeGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeGroupService_FindEnabledReportNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledReportNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeGroupServiceServer).FindEnabledReportNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeGroupService_FindEnabledReportNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeGroupServiceServer).FindEnabledReportNodeGroup(ctx, req.(*FindEnabledReportNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportNodeGroupService_CountAllEnabledReportNodeGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledReportNodeGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportNodeGroupServiceServer).CountAllEnabledReportNodeGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReportNodeGroupService_CountAllEnabledReportNodeGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportNodeGroupServiceServer).CountAllEnabledReportNodeGroups(ctx, req.(*CountAllEnabledReportNodeGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportNodeGroupService_ServiceDesc is the grpc.ServiceDesc for ReportNodeGroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportNodeGroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ReportNodeGroupService",
	HandlerType: (*ReportNodeGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createReportNodeGroup",
			Handler:    _ReportNodeGroupService_CreateReportNodeGroup_Handler,
		},
		{
			MethodName: "updateReportNodeGroup",
			Handler:    _ReportNodeGroupService_UpdateReportNodeGroup_Handler,
		},
		{
			MethodName: "deleteReportNodeGroup",
			Handler:    _ReportNodeGroupService_DeleteReportNodeGroup_Handler,
		},
		{
			MethodName: "findAllEnabledReportNodeGroups",
			Handler:    _ReportNodeGroupService_FindAllEnabledReportNodeGroups_Handler,
		},
		{
			MethodName: "findEnabledReportNodeGroup",
			Handler:    _ReportNodeGroupService_FindEnabledReportNodeGroup_Handler,
		},
		{
			MethodName: "countAllEnabledReportNodeGroups",
			Handler:    _ReportNodeGroupService_CountAllEnabledReportNodeGroups_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_report_node_group.proto",
}
