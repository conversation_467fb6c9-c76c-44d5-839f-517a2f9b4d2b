// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_traffic_package.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserTrafficPackageService_CreateUserTrafficPackage_FullMethodName = "/pb.UserTrafficPackageService/createUserTrafficPackage"
	UserTrafficPackageService_BuyUserTrafficPackage_FullMethodName    = "/pb.UserTrafficPackageService/buyUserTrafficPackage"
	UserTrafficPackageService_CountUserTrafficPackages_FullMethodName = "/pb.UserTrafficPackageService/countUserTrafficPackages"
	UserTrafficPackageService_ListUserTrafficPackages_FullMethodName  = "/pb.UserTrafficPackageService/listUserTrafficPackages"
	UserTrafficPackageService_DeleteUserTrafficPackage_FullMethodName = "/pb.UserTrafficPackageService/deleteUserTrafficPackage"
)

// UserTrafficPackageServiceClient is the client API for UserTrafficPackageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserTrafficPackageServiceClient interface {
	// 创建用户流量包
	CreateUserTrafficPackage(ctx context.Context, in *CreateUserTrafficPackageRequest, opts ...grpc.CallOption) (*CreateUserTrafficPackageResponse, error)
	// 购买用户流量包
	BuyUserTrafficPackage(ctx context.Context, in *BuyUserTrafficPackageRequest, opts ...grpc.CallOption) (*BuyUserTrafficPackageResponse, error)
	// 查询当前流量包数量
	CountUserTrafficPackages(ctx context.Context, in *CountUserTrafficPackagesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页流量包
	ListUserTrafficPackages(ctx context.Context, in *ListUserTrafficPackagesRequest, opts ...grpc.CallOption) (*ListUserTrafficPackagesResponse, error)
	// 删除流量包
	DeleteUserTrafficPackage(ctx context.Context, in *DeleteUserTrafficPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type userTrafficPackageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserTrafficPackageServiceClient(cc grpc.ClientConnInterface) UserTrafficPackageServiceClient {
	return &userTrafficPackageServiceClient{cc}
}

func (c *userTrafficPackageServiceClient) CreateUserTrafficPackage(ctx context.Context, in *CreateUserTrafficPackageRequest, opts ...grpc.CallOption) (*CreateUserTrafficPackageResponse, error) {
	out := new(CreateUserTrafficPackageResponse)
	err := c.cc.Invoke(ctx, UserTrafficPackageService_CreateUserTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTrafficPackageServiceClient) BuyUserTrafficPackage(ctx context.Context, in *BuyUserTrafficPackageRequest, opts ...grpc.CallOption) (*BuyUserTrafficPackageResponse, error) {
	out := new(BuyUserTrafficPackageResponse)
	err := c.cc.Invoke(ctx, UserTrafficPackageService_BuyUserTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTrafficPackageServiceClient) CountUserTrafficPackages(ctx context.Context, in *CountUserTrafficPackagesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserTrafficPackageService_CountUserTrafficPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTrafficPackageServiceClient) ListUserTrafficPackages(ctx context.Context, in *ListUserTrafficPackagesRequest, opts ...grpc.CallOption) (*ListUserTrafficPackagesResponse, error) {
	out := new(ListUserTrafficPackagesResponse)
	err := c.cc.Invoke(ctx, UserTrafficPackageService_ListUserTrafficPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTrafficPackageServiceClient) DeleteUserTrafficPackage(ctx context.Context, in *DeleteUserTrafficPackageRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserTrafficPackageService_DeleteUserTrafficPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTrafficPackageServiceServer is the server API for UserTrafficPackageService service.
// All implementations should embed UnimplementedUserTrafficPackageServiceServer
// for forward compatibility
type UserTrafficPackageServiceServer interface {
	// 创建用户流量包
	CreateUserTrafficPackage(context.Context, *CreateUserTrafficPackageRequest) (*CreateUserTrafficPackageResponse, error)
	// 购买用户流量包
	BuyUserTrafficPackage(context.Context, *BuyUserTrafficPackageRequest) (*BuyUserTrafficPackageResponse, error)
	// 查询当前流量包数量
	CountUserTrafficPackages(context.Context, *CountUserTrafficPackagesRequest) (*RPCCountResponse, error)
	// 列出单页流量包
	ListUserTrafficPackages(context.Context, *ListUserTrafficPackagesRequest) (*ListUserTrafficPackagesResponse, error)
	// 删除流量包
	DeleteUserTrafficPackage(context.Context, *DeleteUserTrafficPackageRequest) (*RPCSuccess, error)
}

// UnimplementedUserTrafficPackageServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserTrafficPackageServiceServer struct {
}

func (UnimplementedUserTrafficPackageServiceServer) CreateUserTrafficPackage(context.Context, *CreateUserTrafficPackageRequest) (*CreateUserTrafficPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserTrafficPackage not implemented")
}
func (UnimplementedUserTrafficPackageServiceServer) BuyUserTrafficPackage(context.Context, *BuyUserTrafficPackageRequest) (*BuyUserTrafficPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuyUserTrafficPackage not implemented")
}
func (UnimplementedUserTrafficPackageServiceServer) CountUserTrafficPackages(context.Context, *CountUserTrafficPackagesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserTrafficPackages not implemented")
}
func (UnimplementedUserTrafficPackageServiceServer) ListUserTrafficPackages(context.Context, *ListUserTrafficPackagesRequest) (*ListUserTrafficPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserTrafficPackages not implemented")
}
func (UnimplementedUserTrafficPackageServiceServer) DeleteUserTrafficPackage(context.Context, *DeleteUserTrafficPackageRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserTrafficPackage not implemented")
}

// UnsafeUserTrafficPackageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserTrafficPackageServiceServer will
// result in compilation errors.
type UnsafeUserTrafficPackageServiceServer interface {
	mustEmbedUnimplementedUserTrafficPackageServiceServer()
}

func RegisterUserTrafficPackageServiceServer(s grpc.ServiceRegistrar, srv UserTrafficPackageServiceServer) {
	s.RegisterService(&UserTrafficPackageService_ServiceDesc, srv)
}

func _UserTrafficPackageService_CreateUserTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTrafficPackageServiceServer).CreateUserTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTrafficPackageService_CreateUserTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTrafficPackageServiceServer).CreateUserTrafficPackage(ctx, req.(*CreateUserTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTrafficPackageService_BuyUserTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyUserTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTrafficPackageServiceServer).BuyUserTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTrafficPackageService_BuyUserTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTrafficPackageServiceServer).BuyUserTrafficPackage(ctx, req.(*BuyUserTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTrafficPackageService_CountUserTrafficPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserTrafficPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTrafficPackageServiceServer).CountUserTrafficPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTrafficPackageService_CountUserTrafficPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTrafficPackageServiceServer).CountUserTrafficPackages(ctx, req.(*CountUserTrafficPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTrafficPackageService_ListUserTrafficPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserTrafficPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTrafficPackageServiceServer).ListUserTrafficPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTrafficPackageService_ListUserTrafficPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTrafficPackageServiceServer).ListUserTrafficPackages(ctx, req.(*ListUserTrafficPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTrafficPackageService_DeleteUserTrafficPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserTrafficPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTrafficPackageServiceServer).DeleteUserTrafficPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTrafficPackageService_DeleteUserTrafficPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTrafficPackageServiceServer).DeleteUserTrafficPackage(ctx, req.(*DeleteUserTrafficPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserTrafficPackageService_ServiceDesc is the grpc.ServiceDesc for UserTrafficPackageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserTrafficPackageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserTrafficPackageService",
	HandlerType: (*UserTrafficPackageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createUserTrafficPackage",
			Handler:    _UserTrafficPackageService_CreateUserTrafficPackage_Handler,
		},
		{
			MethodName: "buyUserTrafficPackage",
			Handler:    _UserTrafficPackageService_BuyUserTrafficPackage_Handler,
		},
		{
			MethodName: "countUserTrafficPackages",
			Handler:    _UserTrafficPackageService_CountUserTrafficPackages_Handler,
		},
		{
			MethodName: "listUserTrafficPackages",
			Handler:    _UserTrafficPackageService_ListUserTrafficPackages_Handler,
		},
		{
			MethodName: "deleteUserTrafficPackage",
			Handler:    _UserTrafficPackageService_DeleteUserTrafficPackage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_traffic_package.proto",
}
