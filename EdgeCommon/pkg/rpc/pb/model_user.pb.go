// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Id                     int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                          // 用户ID
	Username               string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`                               // 用户名
	Fullname               string                 `protobuf:"bytes,3,opt,name=fullname,proto3" json:"fullname,omitempty"`                               // 全称
	Mobile                 string                 `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`                                   // 手机号码
	Tel                    string                 `protobuf:"bytes,5,opt,name=tel,proto3" json:"tel,omitempty"`                                         // 联系电话
	Email                  string                 `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`                                     // 联系邮箱
	VerifiedEmail          string                 `protobuf:"bytes,20,opt,name=verifiedEmail,proto3" json:"verifiedEmail,omitempty"`                    // 已验证邮箱
	VerifiedMobile         string                 `protobuf:"bytes,23,opt,name=verifiedMobile,proto3" json:"verifiedMobile,omitempty"`                  // 已验证手机号码
	Remark                 string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`                                   // 备注
	IsOn                   bool                   `protobuf:"varint,8,opt,name=isOn,proto3" json:"isOn,omitempty"`                                      // 是否启用
	CreatedAt              int64                  `protobuf:"varint,9,opt,name=createdAt,proto3" json:"createdAt,omitempty"`                            // 创建时间
	RegisteredIP           string                 `protobuf:"bytes,12,opt,name=registeredIP,proto3" json:"registeredIP,omitempty"`                      // 注册IP
	IsVerified             bool                   `protobuf:"varint,13,opt,name=isVerified,proto3" json:"isVerified,omitempty"`                         // 是否已实名认证
	IsRejected             bool                   `protobuf:"varint,14,opt,name=isRejected,proto3" json:"isRejected,omitempty"`                         // 实名认证是否已拒绝
	RejectReason           string                 `protobuf:"bytes,15,opt,name=rejectReason,proto3" json:"rejectReason,omitempty"`                      // 实名认证拒绝理由
	IsDeleted              bool                   `protobuf:"varint,16,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`                           // 是否已删除
	IsIndividualIdentified bool                   `protobuf:"varint,17,opt,name=isIndividualIdentified,proto3" json:"isIndividualIdentified,omitempty"` // 是否已通过个人验证
	IsEnterpriseIdentified bool                   `protobuf:"varint,18,opt,name=isEnterpriseIdentified,proto3" json:"isEnterpriseIdentified,omitempty"` // 是否已通过企业验证
	BandwidthAlgo          string                 `protobuf:"bytes,21,opt,name=bandwidthAlgo,proto3" json:"bandwidthAlgo,omitempty"`                    // 带宽算法
	Lang                   string                 `protobuf:"bytes,22,opt,name=lang,proto3" json:"lang,omitempty"`                                      // 语言代号
	OtpLogin               *Login                 `protobuf:"bytes,19,opt,name=otpLogin,proto3" json:"otpLogin,omitempty"`                              // OTP认证
	NodeCluster            *NodeCluster           `protobuf:"bytes,10,opt,name=nodeCluster,proto3" json:"nodeCluster,omitempty"`                        // 集群信息
	Features               []*UserFeature         `protobuf:"bytes,11,rep,name=features,proto3" json:"features,omitempty"`                              // 开通功能
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_models_model_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_models_model_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetFullname() string {
	if x != nil {
		return x.Fullname
	}
	return ""
}

func (x *User) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *User) GetTel() string {
	if x != nil {
		return x.Tel
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetVerifiedEmail() string {
	if x != nil {
		return x.VerifiedEmail
	}
	return ""
}

func (x *User) GetVerifiedMobile() string {
	if x != nil {
		return x.VerifiedMobile
	}
	return ""
}

func (x *User) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *User) GetIsOn() bool {
	if x != nil {
		return x.IsOn
	}
	return false
}

func (x *User) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *User) GetRegisteredIP() string {
	if x != nil {
		return x.RegisteredIP
	}
	return ""
}

func (x *User) GetIsVerified() bool {
	if x != nil {
		return x.IsVerified
	}
	return false
}

func (x *User) GetIsRejected() bool {
	if x != nil {
		return x.IsRejected
	}
	return false
}

func (x *User) GetRejectReason() string {
	if x != nil {
		return x.RejectReason
	}
	return ""
}

func (x *User) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *User) GetIsIndividualIdentified() bool {
	if x != nil {
		return x.IsIndividualIdentified
	}
	return false
}

func (x *User) GetIsEnterpriseIdentified() bool {
	if x != nil {
		return x.IsEnterpriseIdentified
	}
	return false
}

func (x *User) GetBandwidthAlgo() string {
	if x != nil {
		return x.BandwidthAlgo
	}
	return ""
}

func (x *User) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *User) GetOtpLogin() *Login {
	if x != nil {
		return x.OtpLogin
	}
	return nil
}

func (x *User) GetNodeCluster() *NodeCluster {
	if x != nil {
		return x.NodeCluster
	}
	return nil
}

func (x *User) GetFeatures() []*UserFeature {
	if x != nil {
		return x.Features
	}
	return nil
}

var File_models_model_user_proto protoreflect.FileDescriptor

const file_models_model_user_proto_rawDesc = "" +
	"\n" +
	"\x17models/model_user.proto\x12\x02pb\x1a\x1fmodels/model_node_cluster.proto\x1a\x1fmodels/model_user_feature.proto\x1a\x18models/model_login.proto\"\xfd\x05\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bfullname\x18\x03 \x01(\tR\bfullname\x12\x16\n" +
	"\x06mobile\x18\x04 \x01(\tR\x06mobile\x12\x10\n" +
	"\x03tel\x18\x05 \x01(\tR\x03tel\x12\x14\n" +
	"\x05email\x18\x06 \x01(\tR\x05email\x12$\n" +
	"\rverifiedEmail\x18\x14 \x01(\tR\rverifiedEmail\x12&\n" +
	"\x0everifiedMobile\x18\x17 \x01(\tR\x0everifiedMobile\x12\x16\n" +
	"\x06remark\x18\a \x01(\tR\x06remark\x12\x12\n" +
	"\x04isOn\x18\b \x01(\bR\x04isOn\x12\x1c\n" +
	"\tcreatedAt\x18\t \x01(\x03R\tcreatedAt\x12\"\n" +
	"\fregisteredIP\x18\f \x01(\tR\fregisteredIP\x12\x1e\n" +
	"\n" +
	"isVerified\x18\r \x01(\bR\n" +
	"isVerified\x12\x1e\n" +
	"\n" +
	"isRejected\x18\x0e \x01(\bR\n" +
	"isRejected\x12\"\n" +
	"\frejectReason\x18\x0f \x01(\tR\frejectReason\x12\x1c\n" +
	"\tisDeleted\x18\x10 \x01(\bR\tisDeleted\x126\n" +
	"\x16isIndividualIdentified\x18\x11 \x01(\bR\x16isIndividualIdentified\x126\n" +
	"\x16isEnterpriseIdentified\x18\x12 \x01(\bR\x16isEnterpriseIdentified\x12$\n" +
	"\rbandwidthAlgo\x18\x15 \x01(\tR\rbandwidthAlgo\x12\x12\n" +
	"\x04lang\x18\x16 \x01(\tR\x04lang\x12%\n" +
	"\botpLogin\x18\x13 \x01(\v2\t.pb.LoginR\botpLogin\x121\n" +
	"\vnodeCluster\x18\n" +
	" \x01(\v2\x0f.pb.NodeClusterR\vnodeCluster\x12+\n" +
	"\bfeatures\x18\v \x03(\v2\x0f.pb.UserFeatureR\bfeaturesB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_proto_rawDescOnce sync.Once
	file_models_model_user_proto_rawDescData []byte
)

func file_models_model_user_proto_rawDescGZIP() []byte {
	file_models_model_user_proto_rawDescOnce.Do(func() {
		file_models_model_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_proto_rawDesc), len(file_models_model_user_proto_rawDesc)))
	})
	return file_models_model_user_proto_rawDescData
}

var file_models_model_user_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_proto_goTypes = []any{
	(*User)(nil),        // 0: pb.User
	(*Login)(nil),       // 1: pb.Login
	(*NodeCluster)(nil), // 2: pb.NodeCluster
	(*UserFeature)(nil), // 3: pb.UserFeature
}
var file_models_model_user_proto_depIdxs = []int32{
	1, // 0: pb.User.otpLogin:type_name -> pb.Login
	2, // 1: pb.User.nodeCluster:type_name -> pb.NodeCluster
	3, // 2: pb.User.features:type_name -> pb.UserFeature
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_models_model_user_proto_init() }
func file_models_model_user_proto_init() {
	if File_models_model_user_proto != nil {
		return
	}
	file_models_model_node_cluster_proto_init()
	file_models_model_user_feature_proto_init()
	file_models_model_login_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_proto_rawDesc), len(file_models_model_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_proto_goTypes,
		DependencyIndexes: file_models_model_user_proto_depIdxs,
		MessageInfos:      file_models_model_user_proto_msgTypes,
	}.Build()
	File_models_model_user_proto = out.File
	file_models_model_user_proto_goTypes = nil
	file_models_model_user_proto_depIdxs = nil
}
