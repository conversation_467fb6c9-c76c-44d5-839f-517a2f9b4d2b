// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_log.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeLogService_CreateNodeLogs_FullMethodName         = "/pb.NodeLogService/createNodeLogs"
	NodeLogService_CountNodeLogs_FullMethodName          = "/pb.NodeLogService/countNodeLogs"
	NodeLogService_ListNodeLogs_FullMethodName           = "/pb.NodeLogService/listNodeLogs"
	NodeLogService_FixNodeLogs_FullMethodName            = "/pb.NodeLogService/fixNodeLogs"
	NodeLogService_FixAllNodeLogs_FullMethodName         = "/pb.NodeLogService/fixAllNodeLogs"
	NodeLogService_CountAllUnreadNodeLogs_FullMethodName = "/pb.NodeLogService/countAllUnreadNodeLogs"
	NodeLogService_UpdateNodeLogsRead_FullMethodName     = "/pb.NodeLogService/updateNodeLogsRead"
	NodeLogService_UpdateAllNodeLogsRead_FullMethodName  = "/pb.NodeLogService/updateAllNodeLogsRead"
	NodeLogService_DeleteNodeLogs_FullMethodName         = "/pb.NodeLogService/deleteNodeLogs"
)

// NodeLogServiceClient is the client API for NodeLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeLogServiceClient interface {
	// 创建日志
	CreateNodeLogs(ctx context.Context, in *CreateNodeLogsRequest, opts ...grpc.CallOption) (*CreateNodeLogsResponse, error)
	// 查询日志数量
	CountNodeLogs(ctx context.Context, in *CountNodeLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页日志
	ListNodeLogs(ctx context.Context, in *ListNodeLogsRequest, opts ...grpc.CallOption) (*ListNodeLogsResponse, error)
	// 设置日志为已修复
	FixNodeLogs(ctx context.Context, in *FixNodeLogsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 设置所有日志为已修复
	FixAllNodeLogs(ctx context.Context, in *FixAllNodeLogsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算未读的日志数量
	CountAllUnreadNodeLogs(ctx context.Context, in *CountAllUnreadNodeLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 设置日志为已读
	UpdateNodeLogsRead(ctx context.Context, in *UpdateNodeLogsReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 设置所有日志未已读
	UpdateAllNodeLogsRead(ctx context.Context, in *UpdateAllNodeLogsReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除日志
	DeleteNodeLogs(ctx context.Context, in *DeleteNodeLogsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeLogServiceClient(cc grpc.ClientConnInterface) NodeLogServiceClient {
	return &nodeLogServiceClient{cc}
}

func (c *nodeLogServiceClient) CreateNodeLogs(ctx context.Context, in *CreateNodeLogsRequest, opts ...grpc.CallOption) (*CreateNodeLogsResponse, error) {
	out := new(CreateNodeLogsResponse)
	err := c.cc.Invoke(ctx, NodeLogService_CreateNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) CountNodeLogs(ctx context.Context, in *CountNodeLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeLogService_CountNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) ListNodeLogs(ctx context.Context, in *ListNodeLogsRequest, opts ...grpc.CallOption) (*ListNodeLogsResponse, error) {
	out := new(ListNodeLogsResponse)
	err := c.cc.Invoke(ctx, NodeLogService_ListNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) FixNodeLogs(ctx context.Context, in *FixNodeLogsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeLogService_FixNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) FixAllNodeLogs(ctx context.Context, in *FixAllNodeLogsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeLogService_FixAllNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) CountAllUnreadNodeLogs(ctx context.Context, in *CountAllUnreadNodeLogsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeLogService_CountAllUnreadNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) UpdateNodeLogsRead(ctx context.Context, in *UpdateNodeLogsReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeLogService_UpdateNodeLogsRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) UpdateAllNodeLogsRead(ctx context.Context, in *UpdateAllNodeLogsReadRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeLogService_UpdateAllNodeLogsRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeLogServiceClient) DeleteNodeLogs(ctx context.Context, in *DeleteNodeLogsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeLogService_DeleteNodeLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeLogServiceServer is the server API for NodeLogService service.
// All implementations should embed UnimplementedNodeLogServiceServer
// for forward compatibility
type NodeLogServiceServer interface {
	// 创建日志
	CreateNodeLogs(context.Context, *CreateNodeLogsRequest) (*CreateNodeLogsResponse, error)
	// 查询日志数量
	CountNodeLogs(context.Context, *CountNodeLogsRequest) (*RPCCountResponse, error)
	// 列出单页日志
	ListNodeLogs(context.Context, *ListNodeLogsRequest) (*ListNodeLogsResponse, error)
	// 设置日志为已修复
	FixNodeLogs(context.Context, *FixNodeLogsRequest) (*RPCSuccess, error)
	// 设置所有日志为已修复
	FixAllNodeLogs(context.Context, *FixAllNodeLogsRequest) (*RPCSuccess, error)
	// 计算未读的日志数量
	CountAllUnreadNodeLogs(context.Context, *CountAllUnreadNodeLogsRequest) (*RPCCountResponse, error)
	// 设置日志为已读
	UpdateNodeLogsRead(context.Context, *UpdateNodeLogsReadRequest) (*RPCSuccess, error)
	// 设置所有日志未已读
	UpdateAllNodeLogsRead(context.Context, *UpdateAllNodeLogsReadRequest) (*RPCSuccess, error)
	// 删除日志
	DeleteNodeLogs(context.Context, *DeleteNodeLogsRequest) (*RPCSuccess, error)
}

// UnimplementedNodeLogServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeLogServiceServer struct {
}

func (UnimplementedNodeLogServiceServer) CreateNodeLogs(context.Context, *CreateNodeLogsRequest) (*CreateNodeLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeLogs not implemented")
}
func (UnimplementedNodeLogServiceServer) CountNodeLogs(context.Context, *CountNodeLogsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountNodeLogs not implemented")
}
func (UnimplementedNodeLogServiceServer) ListNodeLogs(context.Context, *ListNodeLogsRequest) (*ListNodeLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodeLogs not implemented")
}
func (UnimplementedNodeLogServiceServer) FixNodeLogs(context.Context, *FixNodeLogsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FixNodeLogs not implemented")
}
func (UnimplementedNodeLogServiceServer) FixAllNodeLogs(context.Context, *FixAllNodeLogsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FixAllNodeLogs not implemented")
}
func (UnimplementedNodeLogServiceServer) CountAllUnreadNodeLogs(context.Context, *CountAllUnreadNodeLogsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllUnreadNodeLogs not implemented")
}
func (UnimplementedNodeLogServiceServer) UpdateNodeLogsRead(context.Context, *UpdateNodeLogsReadRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeLogsRead not implemented")
}
func (UnimplementedNodeLogServiceServer) UpdateAllNodeLogsRead(context.Context, *UpdateAllNodeLogsReadRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAllNodeLogsRead not implemented")
}
func (UnimplementedNodeLogServiceServer) DeleteNodeLogs(context.Context, *DeleteNodeLogsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeLogs not implemented")
}

// UnsafeNodeLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeLogServiceServer will
// result in compilation errors.
type UnsafeNodeLogServiceServer interface {
	mustEmbedUnimplementedNodeLogServiceServer()
}

func RegisterNodeLogServiceServer(s grpc.ServiceRegistrar, srv NodeLogServiceServer) {
	s.RegisterService(&NodeLogService_ServiceDesc, srv)
}

func _NodeLogService_CreateNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).CreateNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_CreateNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).CreateNodeLogs(ctx, req.(*CreateNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_CountNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).CountNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_CountNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).CountNodeLogs(ctx, req.(*CountNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_ListNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).ListNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_ListNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).ListNodeLogs(ctx, req.(*ListNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_FixNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).FixNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_FixNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).FixNodeLogs(ctx, req.(*FixNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_FixAllNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixAllNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).FixAllNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_FixAllNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).FixAllNodeLogs(ctx, req.(*FixAllNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_CountAllUnreadNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllUnreadNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).CountAllUnreadNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_CountAllUnreadNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).CountAllUnreadNodeLogs(ctx, req.(*CountAllUnreadNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_UpdateNodeLogsRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeLogsReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).UpdateNodeLogsRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_UpdateNodeLogsRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).UpdateNodeLogsRead(ctx, req.(*UpdateNodeLogsReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_UpdateAllNodeLogsRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAllNodeLogsReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).UpdateAllNodeLogsRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_UpdateAllNodeLogsRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).UpdateAllNodeLogsRead(ctx, req.(*UpdateAllNodeLogsReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeLogService_DeleteNodeLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeLogServiceServer).DeleteNodeLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeLogService_DeleteNodeLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeLogServiceServer).DeleteNodeLogs(ctx, req.(*DeleteNodeLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeLogService_ServiceDesc is the grpc.ServiceDesc for NodeLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeLogService",
	HandlerType: (*NodeLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeLogs",
			Handler:    _NodeLogService_CreateNodeLogs_Handler,
		},
		{
			MethodName: "countNodeLogs",
			Handler:    _NodeLogService_CountNodeLogs_Handler,
		},
		{
			MethodName: "listNodeLogs",
			Handler:    _NodeLogService_ListNodeLogs_Handler,
		},
		{
			MethodName: "fixNodeLogs",
			Handler:    _NodeLogService_FixNodeLogs_Handler,
		},
		{
			MethodName: "fixAllNodeLogs",
			Handler:    _NodeLogService_FixAllNodeLogs_Handler,
		},
		{
			MethodName: "countAllUnreadNodeLogs",
			Handler:    _NodeLogService_CountAllUnreadNodeLogs_Handler,
		},
		{
			MethodName: "updateNodeLogsRead",
			Handler:    _NodeLogService_UpdateNodeLogsRead_Handler,
		},
		{
			MethodName: "updateAllNodeLogsRead",
			Handler:    _NodeLogService_UpdateAllNodeLogsRead_Handler,
		},
		{
			MethodName: "deleteNodeLogs",
			Handler:    _NodeLogService_DeleteNodeLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_log.proto",
}
