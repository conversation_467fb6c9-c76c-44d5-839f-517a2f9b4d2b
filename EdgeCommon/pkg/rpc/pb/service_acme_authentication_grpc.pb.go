// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_acme_authentication.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ACMEAuthenticationService_FindACMEAuthenticationKeyWithToken_FullMethodName = "/pb.ACMEAuthenticationService/findACMEAuthenticationKeyWithToken"
)

// ACMEAuthenticationServiceClient is the client API for ACMEAuthenticationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ACMEAuthenticationServiceClient interface {
	// 获取Key
	FindACMEAuthenticationKeyWithToken(ctx context.Context, in *FindACMEAuthenticationKeyWithTokenRequest, opts ...grpc.CallOption) (*FindACMEAuthenticationKeyWithTokenResponse, error)
}

type aCMEAuthenticationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewACMEAuthenticationServiceClient(cc grpc.ClientConnInterface) ACMEAuthenticationServiceClient {
	return &aCMEAuthenticationServiceClient{cc}
}

func (c *aCMEAuthenticationServiceClient) FindACMEAuthenticationKeyWithToken(ctx context.Context, in *FindACMEAuthenticationKeyWithTokenRequest, opts ...grpc.CallOption) (*FindACMEAuthenticationKeyWithTokenResponse, error) {
	out := new(FindACMEAuthenticationKeyWithTokenResponse)
	err := c.cc.Invoke(ctx, ACMEAuthenticationService_FindACMEAuthenticationKeyWithToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ACMEAuthenticationServiceServer is the server API for ACMEAuthenticationService service.
// All implementations should embed UnimplementedACMEAuthenticationServiceServer
// for forward compatibility
type ACMEAuthenticationServiceServer interface {
	// 获取Key
	FindACMEAuthenticationKeyWithToken(context.Context, *FindACMEAuthenticationKeyWithTokenRequest) (*FindACMEAuthenticationKeyWithTokenResponse, error)
}

// UnimplementedACMEAuthenticationServiceServer should be embedded to have forward compatible implementations.
type UnimplementedACMEAuthenticationServiceServer struct {
}

func (UnimplementedACMEAuthenticationServiceServer) FindACMEAuthenticationKeyWithToken(context.Context, *FindACMEAuthenticationKeyWithTokenRequest) (*FindACMEAuthenticationKeyWithTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindACMEAuthenticationKeyWithToken not implemented")
}

// UnsafeACMEAuthenticationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ACMEAuthenticationServiceServer will
// result in compilation errors.
type UnsafeACMEAuthenticationServiceServer interface {
	mustEmbedUnimplementedACMEAuthenticationServiceServer()
}

func RegisterACMEAuthenticationServiceServer(s grpc.ServiceRegistrar, srv ACMEAuthenticationServiceServer) {
	s.RegisterService(&ACMEAuthenticationService_ServiceDesc, srv)
}

func _ACMEAuthenticationService_FindACMEAuthenticationKeyWithToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindACMEAuthenticationKeyWithTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACMEAuthenticationServiceServer).FindACMEAuthenticationKeyWithToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ACMEAuthenticationService_FindACMEAuthenticationKeyWithToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACMEAuthenticationServiceServer).FindACMEAuthenticationKeyWithToken(ctx, req.(*FindACMEAuthenticationKeyWithTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ACMEAuthenticationService_ServiceDesc is the grpc.ServiceDesc for ACMEAuthenticationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ACMEAuthenticationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ACMEAuthenticationService",
	HandlerType: (*ACMEAuthenticationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findACMEAuthenticationKeyWithToken",
			Handler:    _ACMEAuthenticationService_FindACMEAuthenticationKeyWithToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_acme_authentication.proto",
}
