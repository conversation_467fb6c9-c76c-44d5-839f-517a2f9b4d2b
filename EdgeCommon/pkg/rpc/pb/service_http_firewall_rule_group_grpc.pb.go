// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_http_firewall_rule_group.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupIsOn_FullMethodName        = "/pb.HTTPFirewallRuleGroupService/updateHTTPFirewallRuleGroupIsOn"
	HTTPFirewallRuleGroupService_CreateHTTPFirewallRuleGroup_FullMethodName            = "/pb.HTTPFirewallRuleGroupService/createHTTPFirewallRuleGroup"
	HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroup_FullMethodName            = "/pb.HTTPFirewallRuleGroupService/updateHTTPFirewallRuleGroup"
	HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroupConfig_FullMethodName = "/pb.HTTPFirewallRuleGroupService/findEnabledHTTPFirewallRuleGroupConfig"
	HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroup_FullMethodName       = "/pb.HTTPFirewallRuleGroupService/findEnabledHTTPFirewallRuleGroup"
	HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupSets_FullMethodName        = "/pb.HTTPFirewallRuleGroupService/updateHTTPFirewallRuleGroupSets"
	HTTPFirewallRuleGroupService_AddHTTPFirewallRuleGroupSet_FullMethodName            = "/pb.HTTPFirewallRuleGroupService/addHTTPFirewallRuleGroupSet"
)

// HTTPFirewallRuleGroupServiceClient is the client API for HTTPFirewallRuleGroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HTTPFirewallRuleGroupServiceClient interface {
	// 设置是否启用分组
	UpdateHTTPFirewallRuleGroupIsOn(ctx context.Context, in *UpdateHTTPFirewallRuleGroupIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 创建分组
	CreateHTTPFirewallRuleGroup(ctx context.Context, in *CreateHTTPFirewallRuleGroupRequest, opts ...grpc.CallOption) (*CreateHTTPFirewallRuleGroupResponse, error)
	// 修改分组
	UpdateHTTPFirewallRuleGroup(ctx context.Context, in *UpdateHTTPFirewallRuleGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取分组配置
	FindEnabledHTTPFirewallRuleGroupConfig(ctx context.Context, in *FindEnabledHTTPFirewallRuleGroupConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleGroupConfigResponse, error)
	// 获取分组信息
	FindEnabledHTTPFirewallRuleGroup(ctx context.Context, in *FindEnabledHTTPFirewallRuleGroupRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleGroupResponse, error)
	// 修改分组的规则集
	UpdateHTTPFirewallRuleGroupSets(ctx context.Context, in *UpdateHTTPFirewallRuleGroupSetsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 添加规则集
	AddHTTPFirewallRuleGroupSet(ctx context.Context, in *AddHTTPFirewallRuleGroupSetRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type hTTPFirewallRuleGroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHTTPFirewallRuleGroupServiceClient(cc grpc.ClientConnInterface) HTTPFirewallRuleGroupServiceClient {
	return &hTTPFirewallRuleGroupServiceClient{cc}
}

func (c *hTTPFirewallRuleGroupServiceClient) UpdateHTTPFirewallRuleGroupIsOn(ctx context.Context, in *UpdateHTTPFirewallRuleGroupIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupIsOn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleGroupServiceClient) CreateHTTPFirewallRuleGroup(ctx context.Context, in *CreateHTTPFirewallRuleGroupRequest, opts ...grpc.CallOption) (*CreateHTTPFirewallRuleGroupResponse, error) {
	out := new(CreateHTTPFirewallRuleGroupResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_CreateHTTPFirewallRuleGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleGroupServiceClient) UpdateHTTPFirewallRuleGroup(ctx context.Context, in *UpdateHTTPFirewallRuleGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleGroupServiceClient) FindEnabledHTTPFirewallRuleGroupConfig(ctx context.Context, in *FindEnabledHTTPFirewallRuleGroupConfigRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleGroupConfigResponse, error) {
	out := new(FindEnabledHTTPFirewallRuleGroupConfigResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroupConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleGroupServiceClient) FindEnabledHTTPFirewallRuleGroup(ctx context.Context, in *FindEnabledHTTPFirewallRuleGroupRequest, opts ...grpc.CallOption) (*FindEnabledHTTPFirewallRuleGroupResponse, error) {
	out := new(FindEnabledHTTPFirewallRuleGroupResponse)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleGroupServiceClient) UpdateHTTPFirewallRuleGroupSets(ctx context.Context, in *UpdateHTTPFirewallRuleGroupSetsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupSets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hTTPFirewallRuleGroupServiceClient) AddHTTPFirewallRuleGroupSet(ctx context.Context, in *AddHTTPFirewallRuleGroupSetRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, HTTPFirewallRuleGroupService_AddHTTPFirewallRuleGroupSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HTTPFirewallRuleGroupServiceServer is the server API for HTTPFirewallRuleGroupService service.
// All implementations should embed UnimplementedHTTPFirewallRuleGroupServiceServer
// for forward compatibility
type HTTPFirewallRuleGroupServiceServer interface {
	// 设置是否启用分组
	UpdateHTTPFirewallRuleGroupIsOn(context.Context, *UpdateHTTPFirewallRuleGroupIsOnRequest) (*RPCSuccess, error)
	// 创建分组
	CreateHTTPFirewallRuleGroup(context.Context, *CreateHTTPFirewallRuleGroupRequest) (*CreateHTTPFirewallRuleGroupResponse, error)
	// 修改分组
	UpdateHTTPFirewallRuleGroup(context.Context, *UpdateHTTPFirewallRuleGroupRequest) (*RPCSuccess, error)
	// 获取分组配置
	FindEnabledHTTPFirewallRuleGroupConfig(context.Context, *FindEnabledHTTPFirewallRuleGroupConfigRequest) (*FindEnabledHTTPFirewallRuleGroupConfigResponse, error)
	// 获取分组信息
	FindEnabledHTTPFirewallRuleGroup(context.Context, *FindEnabledHTTPFirewallRuleGroupRequest) (*FindEnabledHTTPFirewallRuleGroupResponse, error)
	// 修改分组的规则集
	UpdateHTTPFirewallRuleGroupSets(context.Context, *UpdateHTTPFirewallRuleGroupSetsRequest) (*RPCSuccess, error)
	// 添加规则集
	AddHTTPFirewallRuleGroupSet(context.Context, *AddHTTPFirewallRuleGroupSetRequest) (*RPCSuccess, error)
}

// UnimplementedHTTPFirewallRuleGroupServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHTTPFirewallRuleGroupServiceServer struct {
}

func (UnimplementedHTTPFirewallRuleGroupServiceServer) UpdateHTTPFirewallRuleGroupIsOn(context.Context, *UpdateHTTPFirewallRuleGroupIsOnRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallRuleGroupIsOn not implemented")
}
func (UnimplementedHTTPFirewallRuleGroupServiceServer) CreateHTTPFirewallRuleGroup(context.Context, *CreateHTTPFirewallRuleGroupRequest) (*CreateHTTPFirewallRuleGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHTTPFirewallRuleGroup not implemented")
}
func (UnimplementedHTTPFirewallRuleGroupServiceServer) UpdateHTTPFirewallRuleGroup(context.Context, *UpdateHTTPFirewallRuleGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallRuleGroup not implemented")
}
func (UnimplementedHTTPFirewallRuleGroupServiceServer) FindEnabledHTTPFirewallRuleGroupConfig(context.Context, *FindEnabledHTTPFirewallRuleGroupConfigRequest) (*FindEnabledHTTPFirewallRuleGroupConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPFirewallRuleGroupConfig not implemented")
}
func (UnimplementedHTTPFirewallRuleGroupServiceServer) FindEnabledHTTPFirewallRuleGroup(context.Context, *FindEnabledHTTPFirewallRuleGroupRequest) (*FindEnabledHTTPFirewallRuleGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledHTTPFirewallRuleGroup not implemented")
}
func (UnimplementedHTTPFirewallRuleGroupServiceServer) UpdateHTTPFirewallRuleGroupSets(context.Context, *UpdateHTTPFirewallRuleGroupSetsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHTTPFirewallRuleGroupSets not implemented")
}
func (UnimplementedHTTPFirewallRuleGroupServiceServer) AddHTTPFirewallRuleGroupSet(context.Context, *AddHTTPFirewallRuleGroupSetRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHTTPFirewallRuleGroupSet not implemented")
}

// UnsafeHTTPFirewallRuleGroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HTTPFirewallRuleGroupServiceServer will
// result in compilation errors.
type UnsafeHTTPFirewallRuleGroupServiceServer interface {
	mustEmbedUnimplementedHTTPFirewallRuleGroupServiceServer()
}

func RegisterHTTPFirewallRuleGroupServiceServer(s grpc.ServiceRegistrar, srv HTTPFirewallRuleGroupServiceServer) {
	s.RegisterService(&HTTPFirewallRuleGroupService_ServiceDesc, srv)
}

func _HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupIsOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallRuleGroupIsOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).UpdateHTTPFirewallRuleGroupIsOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupIsOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).UpdateHTTPFirewallRuleGroupIsOn(ctx, req.(*UpdateHTTPFirewallRuleGroupIsOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleGroupService_CreateHTTPFirewallRuleGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHTTPFirewallRuleGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).CreateHTTPFirewallRuleGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_CreateHTTPFirewallRuleGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).CreateHTTPFirewallRuleGroup(ctx, req.(*CreateHTTPFirewallRuleGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallRuleGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).UpdateHTTPFirewallRuleGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).UpdateHTTPFirewallRuleGroup(ctx, req.(*UpdateHTTPFirewallRuleGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroupConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPFirewallRuleGroupConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).FindEnabledHTTPFirewallRuleGroupConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroupConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).FindEnabledHTTPFirewallRuleGroupConfig(ctx, req.(*FindEnabledHTTPFirewallRuleGroupConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledHTTPFirewallRuleGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).FindEnabledHTTPFirewallRuleGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).FindEnabledHTTPFirewallRuleGroup(ctx, req.(*FindEnabledHTTPFirewallRuleGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupSets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHTTPFirewallRuleGroupSetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).UpdateHTTPFirewallRuleGroupSets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupSets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).UpdateHTTPFirewallRuleGroupSets(ctx, req.(*UpdateHTTPFirewallRuleGroupSetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HTTPFirewallRuleGroupService_AddHTTPFirewallRuleGroupSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddHTTPFirewallRuleGroupSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HTTPFirewallRuleGroupServiceServer).AddHTTPFirewallRuleGroupSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HTTPFirewallRuleGroupService_AddHTTPFirewallRuleGroupSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HTTPFirewallRuleGroupServiceServer).AddHTTPFirewallRuleGroupSet(ctx, req.(*AddHTTPFirewallRuleGroupSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HTTPFirewallRuleGroupService_ServiceDesc is the grpc.ServiceDesc for HTTPFirewallRuleGroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HTTPFirewallRuleGroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HTTPFirewallRuleGroupService",
	HandlerType: (*HTTPFirewallRuleGroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "updateHTTPFirewallRuleGroupIsOn",
			Handler:    _HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupIsOn_Handler,
		},
		{
			MethodName: "createHTTPFirewallRuleGroup",
			Handler:    _HTTPFirewallRuleGroupService_CreateHTTPFirewallRuleGroup_Handler,
		},
		{
			MethodName: "updateHTTPFirewallRuleGroup",
			Handler:    _HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroup_Handler,
		},
		{
			MethodName: "findEnabledHTTPFirewallRuleGroupConfig",
			Handler:    _HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroupConfig_Handler,
		},
		{
			MethodName: "findEnabledHTTPFirewallRuleGroup",
			Handler:    _HTTPFirewallRuleGroupService_FindEnabledHTTPFirewallRuleGroup_Handler,
		},
		{
			MethodName: "updateHTTPFirewallRuleGroupSets",
			Handler:    _HTTPFirewallRuleGroupService_UpdateHTTPFirewallRuleGroupSets_Handler,
		},
		{
			MethodName: "addHTTPFirewallRuleGroupSet",
			Handler:    _HTTPFirewallRuleGroupService_AddHTTPFirewallRuleGroupSet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_http_firewall_rule_group.proto",
}
