// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeService_CreateNode_FullMethodName                                 = "/pb.NodeService/createNode"
	NodeService_RegisterClusterNode_FullMethodName                        = "/pb.NodeService/registerClusterNode"
	NodeService_CountAllEnabledNodes_FullMethodName                       = "/pb.NodeService/countAllEnabledNodes"
	NodeService_CountAllEnabledNodesMatch_FullMethodName                  = "/pb.NodeService/countAllEnabledNodesMatch"
	NodeService_ListEnabledNodesMatch_FullMethodName                      = "/pb.NodeService/listEnabledNodesMatch"
	NodeService_FindAllEnabledNodesWithNodeClusterId_FullMethodName       = "/pb.NodeService/findAllEnabledNodesWithNodeClusterId"
	NodeService_DeleteNode_FullMethodName                                 = "/pb.NodeService/deleteNode"
	NodeService_DeleteNodeFromNodeCluster_FullMethodName                  = "/pb.NodeService/deleteNodeFromNodeCluster"
	NodeService_UpdateNode_FullMethodName                                 = "/pb.NodeService/updateNode"
	NodeService_FindEnabledNode_FullMethodName                            = "/pb.NodeService/findEnabledNode"
	NodeService_FindEnabledBasicNode_FullMethodName                       = "/pb.NodeService/findEnabledBasicNode"
	NodeService_FindCurrentNodeConfig_FullMethodName                      = "/pb.NodeService/findCurrentNodeConfig"
	NodeService_NodeStream_FullMethodName                                 = "/pb.NodeService/nodeStream"
	NodeService_SendCommandToNode_FullMethodName                          = "/pb.NodeService/sendCommandToNode"
	NodeService_UpdateNodeStatus_FullMethodName                           = "/pb.NodeService/updateNodeStatus"
	NodeService_UpdateNodeIsInstalled_FullMethodName                      = "/pb.NodeService/updateNodeIsInstalled"
	NodeService_InstallNode_FullMethodName                                = "/pb.NodeService/installNode"
	NodeService_UpgradeNode_FullMethodName                                = "/pb.NodeService/upgradeNode"
	NodeService_StartNode_FullMethodName                                  = "/pb.NodeService/startNode"
	NodeService_StopNode_FullMethodName                                   = "/pb.NodeService/stopNode"
	NodeService_UninstallNode_FullMethodName                              = "/pb.NodeService/uninstallNode"
	NodeService_UpdateNodeConnectedAPINodes_FullMethodName                = "/pb.NodeService/updateNodeConnectedAPINodes"
	NodeService_CountAllEnabledNodesWithNodeGrantId_FullMethodName        = "/pb.NodeService/countAllEnabledNodesWithNodeGrantId"
	NodeService_FindAllEnabledNodesWithNodeGrantId_FullMethodName         = "/pb.NodeService/findAllEnabledNodesWithNodeGrantId"
	NodeService_CountAllNotInstalledNodesWithNodeClusterId_FullMethodName = "/pb.NodeService/countAllNotInstalledNodesWithNodeClusterId"
	NodeService_FindAllNotInstalledNodesWithNodeClusterId_FullMethodName  = "/pb.NodeService/findAllNotInstalledNodesWithNodeClusterId"
	NodeService_CountAllUpgradeNodesWithNodeClusterId_FullMethodName      = "/pb.NodeService/countAllUpgradeNodesWithNodeClusterId"
	NodeService_FindAllUpgradeNodesWithNodeClusterId_FullMethodName       = "/pb.NodeService/findAllUpgradeNodesWithNodeClusterId"
	NodeService_FindNodeInstallStatus_FullMethodName                      = "/pb.NodeService/findNodeInstallStatus"
	NodeService_UpdateNodeLogin_FullMethodName                            = "/pb.NodeService/updateNodeLogin"
	NodeService_CountAllEnabledNodesWithNodeGroupId_FullMethodName        = "/pb.NodeService/countAllEnabledNodesWithNodeGroupId"
	NodeService_FindAllEnabledNodesDNSWithNodeClusterId_FullMethodName    = "/pb.NodeService/findAllEnabledNodesDNSWithNodeClusterId"
	NodeService_FindEnabledNodeDNS_FullMethodName                         = "/pb.NodeService/findEnabledNodeDNS"
	NodeService_UpdateNodeDNS_FullMethodName                              = "/pb.NodeService/updateNodeDNS"
	NodeService_CountAllEnabledNodesWithNodeRegionId_FullMethodName       = "/pb.NodeService/countAllEnabledNodesWithNodeRegionId"
	NodeService_FindEnabledNodesWithIds_FullMethodName                    = "/pb.NodeService/findEnabledNodesWithIds"
	NodeService_CheckNodeLatestVersion_FullMethodName                     = "/pb.NodeService/checkNodeLatestVersion"
	NodeService_UpdateNodeUp_FullMethodName                               = "/pb.NodeService/updateNodeUp"
	NodeService_DownloadNodeInstallationFile_FullMethodName               = "/pb.NodeService/downloadNodeInstallationFile"
	NodeService_UpdateNodeSystem_FullMethodName                           = "/pb.NodeService/updateNodeSystem"
	NodeService_UpdateNodeCache_FullMethodName                            = "/pb.NodeService/updateNodeCache"
	NodeService_FindNodeLevelInfo_FullMethodName                          = "/pb.NodeService/findNodeLevelInfo"
	NodeService_FindNodeDNSResolver_FullMethodName                        = "/pb.NodeService/findNodeDNSResolver"
	NodeService_UpdateNodeDNSResolver_FullMethodName                      = "/pb.NodeService/updateNodeDNSResolver"
	NodeService_FindNodeDDoSProtection_FullMethodName                     = "/pb.NodeService/findNodeDDoSProtection"
	NodeService_UpdateNodeDDoSProtection_FullMethodName                   = "/pb.NodeService/updateNodeDDoSProtection"
	NodeService_FindNodeGlobalServerConfig_FullMethodName                 = "/pb.NodeService/findNodeGlobalServerConfig"
	NodeService_FindEnabledNodeConfigInfo_FullMethodName                  = "/pb.NodeService/findEnabledNodeConfigInfo"
	NodeService_CountAllNodeRegionInfo_FullMethodName                     = "/pb.NodeService/countAllNodeRegionInfo"
	NodeService_ListNodeRegionInfo_FullMethodName                         = "/pb.NodeService/listNodeRegionInfo"
	NodeService_UpdateNodeRegionInfo_FullMethodName                       = "/pb.NodeService/updateNodeRegionInfo"
	NodeService_FindNodeAPIConfig_FullMethodName                          = "/pb.NodeService/findNodeAPIConfig"
	NodeService_UpdateNodeAPIConfig_FullMethodName                        = "/pb.NodeService/updateNodeAPIConfig"
	NodeService_FindNodeUAMPolicies_FullMethodName                        = "/pb.NodeService/findNodeUAMPolicies"
	NodeService_FindNodeHTTPCCPolicies_FullMethodName                     = "/pb.NodeService/findNodeHTTPCCPolicies"
	NodeService_FindNodeHTTP3Policies_FullMethodName                      = "/pb.NodeService/findNodeHTTP3Policies"
	NodeService_FindNodeHTTPPagesPolicies_FullMethodName                  = "/pb.NodeService/findNodeHTTPPagesPolicies"
	NodeService_FindNodeScheduleInfo_FullMethodName                       = "/pb.NodeService/findNodeScheduleInfo"
	NodeService_UpdateNodeScheduleInfo_FullMethodName                     = "/pb.NodeService/updateNodeScheduleInfo"
	NodeService_ResetNodeActionStatus_FullMethodName                      = "/pb.NodeService/resetNodeActionStatus"
	NodeService_FindAllNodeScheduleInfoWithNodeClusterId_FullMethodName   = "/pb.NodeService/findAllNodeScheduleInfoWithNodeClusterId"
	NodeService_CopyNodeActionsToNodeGroup_FullMethodName                 = "/pb.NodeService/copyNodeActionsToNodeGroup"
	NodeService_CopyNodeActionsToNodeCluster_FullMethodName               = "/pb.NodeService/copyNodeActionsToNodeCluster"
	NodeService_FindNodeTOAConfig_FullMethodName                          = "/pb.NodeService/findNodeTOAConfig"
	NodeService_FindNodeNetworkSecurityPolicy_FullMethodName              = "/pb.NodeService/findNodeNetworkSecurityPolicy"
	NodeService_FindNodeWebPPolicies_FullMethodName                       = "/pb.NodeService/findNodeWebPPolicies"
	NodeService_UpdateNodeIsOn_FullMethodName                             = "/pb.NodeService/updateNodeIsOn"
)

// NodeServiceClient is the client API for NodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeServiceClient interface {
	// 创建节点
	CreateNode(ctx context.Context, in *CreateNodeRequest, opts ...grpc.CallOption) (*CreateNodeResponse, error)
	// 注册集群节点
	RegisterClusterNode(ctx context.Context, in *RegisterClusterNodeRequest, opts ...grpc.CallOption) (*RegisterClusterNodeResponse, error)
	// 所有可用的节点数量
	CountAllEnabledNodes(ctx context.Context, in *CountAllEnabledNodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算匹配的节点数量
	CountAllEnabledNodesMatch(ctx context.Context, in *CountAllEnabledNodesMatchRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页节点
	ListEnabledNodesMatch(ctx context.Context, in *ListEnabledNodesMatchRequest, opts ...grpc.CallOption) (*ListEnabledNodesMatchResponse, error)
	// 根据集群查找所有节点
	FindAllEnabledNodesWithNodeClusterId(ctx context.Context, in *FindAllEnabledNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodesWithNodeClusterIdResponse, error)
	// 删除节点
	DeleteNode(ctx context.Context, in *DeleteNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 从集群中删除节点
	DeleteNodeFromNodeCluster(ctx context.Context, in *DeleteNodeFromNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改节点
	UpdateNode(ctx context.Context, in *UpdateNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取单个节点信息
	FindEnabledNode(ctx context.Context, in *FindEnabledNodeRequest, opts ...grpc.CallOption) (*FindEnabledNodeResponse, error)
	// 获取单个节点基本信息
	FindEnabledBasicNode(ctx context.Context, in *FindEnabledBasicNodeRequest, opts ...grpc.CallOption) (*FindEnabledBasicNodeResponse, error)
	// 获取当前节点配置
	FindCurrentNodeConfig(ctx context.Context, in *FindCurrentNodeConfigRequest, opts ...grpc.CallOption) (*FindCurrentNodeConfigResponse, error)
	// 节点stream
	NodeStream(ctx context.Context, opts ...grpc.CallOption) (NodeService_NodeStreamClient, error)
	// 向节点发送命令
	SendCommandToNode(ctx context.Context, in *NodeStreamMessage, opts ...grpc.CallOption) (*NodeStreamMessage, error)
	// 更新节点状态
	UpdateNodeStatus(ctx context.Context, in *UpdateNodeStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改节点安装状态
	UpdateNodeIsInstalled(ctx context.Context, in *UpdateNodeIsInstalledRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 安装节点
	InstallNode(ctx context.Context, in *InstallNodeRequest, opts ...grpc.CallOption) (*InstallNodeResponse, error)
	// 升级节点
	UpgradeNode(ctx context.Context, in *UpgradeNodeRequest, opts ...grpc.CallOption) (*UpgradeNodeResponse, error)
	// 启动节点
	StartNode(ctx context.Context, in *StartNodeRequest, opts ...grpc.CallOption) (*StartNodeResponse, error)
	// 停止节点
	StopNode(ctx context.Context, in *StopNodeRequest, opts ...grpc.CallOption) (*StopNodeResponse, error)
	// 卸载节点
	UninstallNode(ctx context.Context, in *UninstallNodeRequest, opts ...grpc.CallOption) (*UninstallNodeResponse, error)
	// 更改节点连接的API节点信息
	UpdateNodeConnectedAPINodes(ctx context.Context, in *UpdateNodeConnectedAPINodesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算使用某个认证的节点数量
	CountAllEnabledNodesWithNodeGrantId(ctx context.Context, in *CountAllEnabledNodesWithNodeGrantIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找使用某个认证的所有节点
	FindAllEnabledNodesWithNodeGrantId(ctx context.Context, in *FindAllEnabledNodesWithNodeGrantIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodesWithNodeGrantIdResponse, error)
	// 计算没有安装的节点数量
	CountAllNotInstalledNodesWithNodeClusterId(ctx context.Context, in *CountAllNotInstalledNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出所有未安装的节点
	FindAllNotInstalledNodesWithNodeClusterId(ctx context.Context, in *FindAllNotInstalledNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllNotInstalledNodesWithNodeClusterIdResponse, error)
	// 计算需要升级的节点数量
	CountAllUpgradeNodesWithNodeClusterId(ctx context.Context, in *CountAllUpgradeNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出所有需要升级的节点
	FindAllUpgradeNodesWithNodeClusterId(ctx context.Context, in *FindAllUpgradeNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllUpgradeNodesWithNodeClusterIdResponse, error)
	// 读取节点安装状态
	FindNodeInstallStatus(ctx context.Context, in *FindNodeInstallStatusRequest, opts ...grpc.CallOption) (*FindNodeInstallStatusResponse, error)
	// 修改节点登录信息
	UpdateNodeLogin(ctx context.Context, in *UpdateNodeLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算某个节点分组内的节点数量
	CountAllEnabledNodesWithNodeGroupId(ctx context.Context, in *CountAllEnabledNodesWithNodeGroupIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 取得某个集群下的所有节点DNS信息
	FindAllEnabledNodesDNSWithNodeClusterId(ctx context.Context, in *FindAllEnabledNodesDNSWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodesDNSWithNodeClusterIdResponse, error)
	// 查找单个节点的域名解析信息
	FindEnabledNodeDNS(ctx context.Context, in *FindEnabledNodeDNSRequest, opts ...grpc.CallOption) (*FindEnabledNodeDNSResponse, error)
	// 修改节点的DNS解析信息
	UpdateNodeDNS(ctx context.Context, in *UpdateNodeDNSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算某个区域下的节点数量
	CountAllEnabledNodesWithNodeRegionId(ctx context.Context, in *CountAllEnabledNodesWithNodeRegionIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 根据一组ID获取节点信息
	FindEnabledNodesWithIds(ctx context.Context, in *FindEnabledNodesWithIdsRequest, opts ...grpc.CallOption) (*FindEnabledNodesWithIdsResponse, error)
	// 检查新版本
	CheckNodeLatestVersion(ctx context.Context, in *CheckNodeLatestVersionRequest, opts ...grpc.CallOption) (*CheckNodeLatestVersionResponse, error)
	// 设置节点上线状态
	UpdateNodeUp(ctx context.Context, in *UpdateNodeUpRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 下载最新边缘节点安装文件
	DownloadNodeInstallationFile(ctx context.Context, in *DownloadNodeInstallationFileRequest, opts ...grpc.CallOption) (*DownloadNodeInstallationFileResponse, error)
	// 修改节点系统信息
	UpdateNodeSystem(ctx context.Context, in *UpdateNodeSystemRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改节点缓存设置
	UpdateNodeCache(ctx context.Context, in *UpdateNodeCacheRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取节点级别信息
	FindNodeLevelInfo(ctx context.Context, in *FindNodeLevelInfoRequest, opts ...grpc.CallOption) (*FindNodeLevelInfoResponse, error)
	// 读取节点DNS Resolver
	FindNodeDNSResolver(ctx context.Context, in *FindNodeDNSResolverRequest, opts ...grpc.CallOption) (*FindNodeDNSResolverResponse, error)
	// 修改DNS Resolver
	UpdateNodeDNSResolver(ctx context.Context, in *UpdateNodeDNSResolverRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取节点的DDoS设置
	FindNodeDDoSProtection(ctx context.Context, in *FindNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNodeDDoSProtectionResponse, error)
	// 修改节点的DDoS设置
	UpdateNodeDDoSProtection(ctx context.Context, in *UpdateNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 取得节点的服务全局配置
	FindNodeGlobalServerConfig(ctx context.Context, in *FindNodeGlobalServerConfigRequest, opts ...grpc.CallOption) (*FindNodeGlobalServerConfigResponse, error)
	// 取得节点的配置概要信息
	FindEnabledNodeConfigInfo(ctx context.Context, in *FindEnabledNodeConfigInfoRequest, opts ...grpc.CallOption) (*FindEnabledNodeConfigInfoResponse, error)
	// 查找节点区域信息数量
	CountAllNodeRegionInfo(ctx context.Context, in *CountAllNodeRegionInfoRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页节点区域信息
	ListNodeRegionInfo(ctx context.Context, in *ListNodeRegionInfoRequest, opts ...grpc.CallOption) (*ListNodeRegionInfoResponse, error)
	// 修改节点区域信息
	UpdateNodeRegionInfo(ctx context.Context, in *UpdateNodeRegionInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个节点的API相关配置
	FindNodeAPIConfig(ctx context.Context, in *FindNodeAPIConfigRequest, opts ...grpc.CallOption) (*FindNodeAPIConfigResponse, error)
	// 修改某个节点的API相关配置
	UpdateNodeAPIConfig(ctx context.Context, in *UpdateNodeAPIConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找节点的UAM策略
	FindNodeUAMPolicies(ctx context.Context, in *FindNodeUAMPoliciesRequest, opts ...grpc.CallOption) (*FindNodeUAMPoliciesResponse, error)
	// 查找节点的HTTP CC策略
	FindNodeHTTPCCPolicies(ctx context.Context, in *FindNodeHTTPCCPoliciesRequest, opts ...grpc.CallOption) (*FindNodeHTTPCCPoliciesResponse, error)
	// 查找节点的HTTP3策略
	FindNodeHTTP3Policies(ctx context.Context, in *FindNodeHTTP3PoliciesRequest, opts ...grpc.CallOption) (*FindNodeHTTP3PoliciesResponse, error)
	// 查找节点的自定义页面策略
	FindNodeHTTPPagesPolicies(ctx context.Context, in *FindNodeHTTPPagesPoliciesRequest, opts ...grpc.CallOption) (*FindNodeHTTPPagesPoliciesResponse, error)
	// 查找节点调度信息
	FindNodeScheduleInfo(ctx context.Context, in *FindNodeScheduleInfoRequest, opts ...grpc.CallOption) (*FindNodeScheduleInfoResponse, error)
	// 修改节点调度信息
	UpdateNodeScheduleInfo(ctx context.Context, in *UpdateNodeScheduleInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 重置节点动作状态
	ResetNodeActionStatus(ctx context.Context, in *ResetNodeActionStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找集群的节点调度信息
	FindAllNodeScheduleInfoWithNodeClusterId(ctx context.Context, in *FindAllNodeScheduleInfoWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllNodeScheduleInfoWithNodeClusterIdResponse, error)
	// 复制动作设置到分组
	CopyNodeActionsToNodeGroup(ctx context.Context, in *CopyNodeActionsToNodeGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 复制动作设置到集群
	CopyNodeActionsToNodeCluster(ctx context.Context, in *CopyNodeActionsToNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找节点的TOA配置
	FindNodeTOAConfig(ctx context.Context, in *FindNodeTOAConfigRequest, opts ...grpc.CallOption) (*FindNodeTOAConfigResponse, error)
	// 查找节点的网络安全策略
	FindNodeNetworkSecurityPolicy(ctx context.Context, in *FindNodeNetworkSecurityPolicyRequest, opts ...grpc.CallOption) (*FindNodeNetworkSecurityPolicyResponse, error)
	// 查找节点的WebP策略
	FindNodeWebPPolicies(ctx context.Context, in *FindNodeWebPPoliciesRequest, opts ...grpc.CallOption) (*FindNodeWebPPoliciesResponse, error)
	// 修改节点的启用状态
	UpdateNodeIsOn(ctx context.Context, in *UpdateNodeIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeServiceClient(cc grpc.ClientConnInterface) NodeServiceClient {
	return &nodeServiceClient{cc}
}

func (c *nodeServiceClient) CreateNode(ctx context.Context, in *CreateNodeRequest, opts ...grpc.CallOption) (*CreateNodeResponse, error) {
	out := new(CreateNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_CreateNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) RegisterClusterNode(ctx context.Context, in *RegisterClusterNodeRequest, opts ...grpc.CallOption) (*RegisterClusterNodeResponse, error) {
	out := new(RegisterClusterNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_RegisterClusterNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllEnabledNodes(ctx context.Context, in *CountAllEnabledNodesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllEnabledNodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllEnabledNodesMatch(ctx context.Context, in *CountAllEnabledNodesMatchRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllEnabledNodesMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ListEnabledNodesMatch(ctx context.Context, in *ListEnabledNodesMatchRequest, opts ...grpc.CallOption) (*ListEnabledNodesMatchResponse, error) {
	out := new(ListEnabledNodesMatchResponse)
	err := c.cc.Invoke(ctx, NodeService_ListEnabledNodesMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindAllEnabledNodesWithNodeClusterId(ctx context.Context, in *FindAllEnabledNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodesWithNodeClusterIdResponse, error) {
	out := new(FindAllEnabledNodesWithNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, NodeService_FindAllEnabledNodesWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) DeleteNode(ctx context.Context, in *DeleteNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_DeleteNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) DeleteNodeFromNodeCluster(ctx context.Context, in *DeleteNodeFromNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_DeleteNodeFromNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNode(ctx context.Context, in *UpdateNodeRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindEnabledNode(ctx context.Context, in *FindEnabledNodeRequest, opts ...grpc.CallOption) (*FindEnabledNodeResponse, error) {
	out := new(FindEnabledNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_FindEnabledNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindEnabledBasicNode(ctx context.Context, in *FindEnabledBasicNodeRequest, opts ...grpc.CallOption) (*FindEnabledBasicNodeResponse, error) {
	out := new(FindEnabledBasicNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_FindEnabledBasicNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindCurrentNodeConfig(ctx context.Context, in *FindCurrentNodeConfigRequest, opts ...grpc.CallOption) (*FindCurrentNodeConfigResponse, error) {
	out := new(FindCurrentNodeConfigResponse)
	err := c.cc.Invoke(ctx, NodeService_FindCurrentNodeConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) NodeStream(ctx context.Context, opts ...grpc.CallOption) (NodeService_NodeStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &NodeService_ServiceDesc.Streams[0], NodeService_NodeStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &nodeServiceNodeStreamClient{stream}
	return x, nil
}

type NodeService_NodeStreamClient interface {
	Send(*NodeStreamMessage) error
	Recv() (*NodeStreamMessage, error)
	grpc.ClientStream
}

type nodeServiceNodeStreamClient struct {
	grpc.ClientStream
}

func (x *nodeServiceNodeStreamClient) Send(m *NodeStreamMessage) error {
	return x.ClientStream.SendMsg(m)
}

func (x *nodeServiceNodeStreamClient) Recv() (*NodeStreamMessage, error) {
	m := new(NodeStreamMessage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *nodeServiceClient) SendCommandToNode(ctx context.Context, in *NodeStreamMessage, opts ...grpc.CallOption) (*NodeStreamMessage, error) {
	out := new(NodeStreamMessage)
	err := c.cc.Invoke(ctx, NodeService_SendCommandToNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeStatus(ctx context.Context, in *UpdateNodeStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeIsInstalled(ctx context.Context, in *UpdateNodeIsInstalledRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeIsInstalled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) InstallNode(ctx context.Context, in *InstallNodeRequest, opts ...grpc.CallOption) (*InstallNodeResponse, error) {
	out := new(InstallNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_InstallNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpgradeNode(ctx context.Context, in *UpgradeNodeRequest, opts ...grpc.CallOption) (*UpgradeNodeResponse, error) {
	out := new(UpgradeNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_UpgradeNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) StartNode(ctx context.Context, in *StartNodeRequest, opts ...grpc.CallOption) (*StartNodeResponse, error) {
	out := new(StartNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_StartNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) StopNode(ctx context.Context, in *StopNodeRequest, opts ...grpc.CallOption) (*StopNodeResponse, error) {
	out := new(StopNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_StopNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UninstallNode(ctx context.Context, in *UninstallNodeRequest, opts ...grpc.CallOption) (*UninstallNodeResponse, error) {
	out := new(UninstallNodeResponse)
	err := c.cc.Invoke(ctx, NodeService_UninstallNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeConnectedAPINodes(ctx context.Context, in *UpdateNodeConnectedAPINodesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeConnectedAPINodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllEnabledNodesWithNodeGrantId(ctx context.Context, in *CountAllEnabledNodesWithNodeGrantIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllEnabledNodesWithNodeGrantId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindAllEnabledNodesWithNodeGrantId(ctx context.Context, in *FindAllEnabledNodesWithNodeGrantIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodesWithNodeGrantIdResponse, error) {
	out := new(FindAllEnabledNodesWithNodeGrantIdResponse)
	err := c.cc.Invoke(ctx, NodeService_FindAllEnabledNodesWithNodeGrantId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllNotInstalledNodesWithNodeClusterId(ctx context.Context, in *CountAllNotInstalledNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllNotInstalledNodesWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindAllNotInstalledNodesWithNodeClusterId(ctx context.Context, in *FindAllNotInstalledNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllNotInstalledNodesWithNodeClusterIdResponse, error) {
	out := new(FindAllNotInstalledNodesWithNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, NodeService_FindAllNotInstalledNodesWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllUpgradeNodesWithNodeClusterId(ctx context.Context, in *CountAllUpgradeNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllUpgradeNodesWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindAllUpgradeNodesWithNodeClusterId(ctx context.Context, in *FindAllUpgradeNodesWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllUpgradeNodesWithNodeClusterIdResponse, error) {
	out := new(FindAllUpgradeNodesWithNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, NodeService_FindAllUpgradeNodesWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeInstallStatus(ctx context.Context, in *FindNodeInstallStatusRequest, opts ...grpc.CallOption) (*FindNodeInstallStatusResponse, error) {
	out := new(FindNodeInstallStatusResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeInstallStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeLogin(ctx context.Context, in *UpdateNodeLoginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllEnabledNodesWithNodeGroupId(ctx context.Context, in *CountAllEnabledNodesWithNodeGroupIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllEnabledNodesWithNodeGroupId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindAllEnabledNodesDNSWithNodeClusterId(ctx context.Context, in *FindAllEnabledNodesDNSWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodesDNSWithNodeClusterIdResponse, error) {
	out := new(FindAllEnabledNodesDNSWithNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, NodeService_FindAllEnabledNodesDNSWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindEnabledNodeDNS(ctx context.Context, in *FindEnabledNodeDNSRequest, opts ...grpc.CallOption) (*FindEnabledNodeDNSResponse, error) {
	out := new(FindEnabledNodeDNSResponse)
	err := c.cc.Invoke(ctx, NodeService_FindEnabledNodeDNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeDNS(ctx context.Context, in *UpdateNodeDNSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeDNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllEnabledNodesWithNodeRegionId(ctx context.Context, in *CountAllEnabledNodesWithNodeRegionIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllEnabledNodesWithNodeRegionId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindEnabledNodesWithIds(ctx context.Context, in *FindEnabledNodesWithIdsRequest, opts ...grpc.CallOption) (*FindEnabledNodesWithIdsResponse, error) {
	out := new(FindEnabledNodesWithIdsResponse)
	err := c.cc.Invoke(ctx, NodeService_FindEnabledNodesWithIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CheckNodeLatestVersion(ctx context.Context, in *CheckNodeLatestVersionRequest, opts ...grpc.CallOption) (*CheckNodeLatestVersionResponse, error) {
	out := new(CheckNodeLatestVersionResponse)
	err := c.cc.Invoke(ctx, NodeService_CheckNodeLatestVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeUp(ctx context.Context, in *UpdateNodeUpRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeUp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) DownloadNodeInstallationFile(ctx context.Context, in *DownloadNodeInstallationFileRequest, opts ...grpc.CallOption) (*DownloadNodeInstallationFileResponse, error) {
	out := new(DownloadNodeInstallationFileResponse)
	err := c.cc.Invoke(ctx, NodeService_DownloadNodeInstallationFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeSystem(ctx context.Context, in *UpdateNodeSystemRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeSystem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeCache(ctx context.Context, in *UpdateNodeCacheRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeLevelInfo(ctx context.Context, in *FindNodeLevelInfoRequest, opts ...grpc.CallOption) (*FindNodeLevelInfoResponse, error) {
	out := new(FindNodeLevelInfoResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeLevelInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeDNSResolver(ctx context.Context, in *FindNodeDNSResolverRequest, opts ...grpc.CallOption) (*FindNodeDNSResolverResponse, error) {
	out := new(FindNodeDNSResolverResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeDNSResolver_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeDNSResolver(ctx context.Context, in *UpdateNodeDNSResolverRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeDNSResolver_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeDDoSProtection(ctx context.Context, in *FindNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNodeDDoSProtectionResponse, error) {
	out := new(FindNodeDDoSProtectionResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeDDoSProtection(ctx context.Context, in *UpdateNodeDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeGlobalServerConfig(ctx context.Context, in *FindNodeGlobalServerConfigRequest, opts ...grpc.CallOption) (*FindNodeGlobalServerConfigResponse, error) {
	out := new(FindNodeGlobalServerConfigResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeGlobalServerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindEnabledNodeConfigInfo(ctx context.Context, in *FindEnabledNodeConfigInfoRequest, opts ...grpc.CallOption) (*FindEnabledNodeConfigInfoResponse, error) {
	out := new(FindEnabledNodeConfigInfoResponse)
	err := c.cc.Invoke(ctx, NodeService_FindEnabledNodeConfigInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CountAllNodeRegionInfo(ctx context.Context, in *CountAllNodeRegionInfoRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeService_CountAllNodeRegionInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ListNodeRegionInfo(ctx context.Context, in *ListNodeRegionInfoRequest, opts ...grpc.CallOption) (*ListNodeRegionInfoResponse, error) {
	out := new(ListNodeRegionInfoResponse)
	err := c.cc.Invoke(ctx, NodeService_ListNodeRegionInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeRegionInfo(ctx context.Context, in *UpdateNodeRegionInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeRegionInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeAPIConfig(ctx context.Context, in *FindNodeAPIConfigRequest, opts ...grpc.CallOption) (*FindNodeAPIConfigResponse, error) {
	out := new(FindNodeAPIConfigResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeAPIConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeAPIConfig(ctx context.Context, in *UpdateNodeAPIConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeAPIConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeUAMPolicies(ctx context.Context, in *FindNodeUAMPoliciesRequest, opts ...grpc.CallOption) (*FindNodeUAMPoliciesResponse, error) {
	out := new(FindNodeUAMPoliciesResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeUAMPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeHTTPCCPolicies(ctx context.Context, in *FindNodeHTTPCCPoliciesRequest, opts ...grpc.CallOption) (*FindNodeHTTPCCPoliciesResponse, error) {
	out := new(FindNodeHTTPCCPoliciesResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeHTTPCCPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeHTTP3Policies(ctx context.Context, in *FindNodeHTTP3PoliciesRequest, opts ...grpc.CallOption) (*FindNodeHTTP3PoliciesResponse, error) {
	out := new(FindNodeHTTP3PoliciesResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeHTTP3Policies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeHTTPPagesPolicies(ctx context.Context, in *FindNodeHTTPPagesPoliciesRequest, opts ...grpc.CallOption) (*FindNodeHTTPPagesPoliciesResponse, error) {
	out := new(FindNodeHTTPPagesPoliciesResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeHTTPPagesPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeScheduleInfo(ctx context.Context, in *FindNodeScheduleInfoRequest, opts ...grpc.CallOption) (*FindNodeScheduleInfoResponse, error) {
	out := new(FindNodeScheduleInfoResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeScheduleInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeScheduleInfo(ctx context.Context, in *UpdateNodeScheduleInfoRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeScheduleInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ResetNodeActionStatus(ctx context.Context, in *ResetNodeActionStatusRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_ResetNodeActionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindAllNodeScheduleInfoWithNodeClusterId(ctx context.Context, in *FindAllNodeScheduleInfoWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllNodeScheduleInfoWithNodeClusterIdResponse, error) {
	out := new(FindAllNodeScheduleInfoWithNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, NodeService_FindAllNodeScheduleInfoWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CopyNodeActionsToNodeGroup(ctx context.Context, in *CopyNodeActionsToNodeGroupRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_CopyNodeActionsToNodeGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) CopyNodeActionsToNodeCluster(ctx context.Context, in *CopyNodeActionsToNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_CopyNodeActionsToNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeTOAConfig(ctx context.Context, in *FindNodeTOAConfigRequest, opts ...grpc.CallOption) (*FindNodeTOAConfigResponse, error) {
	out := new(FindNodeTOAConfigResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeTOAConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeNetworkSecurityPolicy(ctx context.Context, in *FindNodeNetworkSecurityPolicyRequest, opts ...grpc.CallOption) (*FindNodeNetworkSecurityPolicyResponse, error) {
	out := new(FindNodeNetworkSecurityPolicyResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeNetworkSecurityPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) FindNodeWebPPolicies(ctx context.Context, in *FindNodeWebPPoliciesRequest, opts ...grpc.CallOption) (*FindNodeWebPPoliciesResponse, error) {
	out := new(FindNodeWebPPoliciesResponse)
	err := c.cc.Invoke(ctx, NodeService_FindNodeWebPPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateNodeIsOn(ctx context.Context, in *UpdateNodeIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeService_UpdateNodeIsOn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeServiceServer is the server API for NodeService service.
// All implementations should embed UnimplementedNodeServiceServer
// for forward compatibility
type NodeServiceServer interface {
	// 创建节点
	CreateNode(context.Context, *CreateNodeRequest) (*CreateNodeResponse, error)
	// 注册集群节点
	RegisterClusterNode(context.Context, *RegisterClusterNodeRequest) (*RegisterClusterNodeResponse, error)
	// 所有可用的节点数量
	CountAllEnabledNodes(context.Context, *CountAllEnabledNodesRequest) (*RPCCountResponse, error)
	// 计算匹配的节点数量
	CountAllEnabledNodesMatch(context.Context, *CountAllEnabledNodesMatchRequest) (*RPCCountResponse, error)
	// 列出单页节点
	ListEnabledNodesMatch(context.Context, *ListEnabledNodesMatchRequest) (*ListEnabledNodesMatchResponse, error)
	// 根据集群查找所有节点
	FindAllEnabledNodesWithNodeClusterId(context.Context, *FindAllEnabledNodesWithNodeClusterIdRequest) (*FindAllEnabledNodesWithNodeClusterIdResponse, error)
	// 删除节点
	DeleteNode(context.Context, *DeleteNodeRequest) (*RPCSuccess, error)
	// 从集群中删除节点
	DeleteNodeFromNodeCluster(context.Context, *DeleteNodeFromNodeClusterRequest) (*RPCSuccess, error)
	// 修改节点
	UpdateNode(context.Context, *UpdateNodeRequest) (*RPCSuccess, error)
	// 获取单个节点信息
	FindEnabledNode(context.Context, *FindEnabledNodeRequest) (*FindEnabledNodeResponse, error)
	// 获取单个节点基本信息
	FindEnabledBasicNode(context.Context, *FindEnabledBasicNodeRequest) (*FindEnabledBasicNodeResponse, error)
	// 获取当前节点配置
	FindCurrentNodeConfig(context.Context, *FindCurrentNodeConfigRequest) (*FindCurrentNodeConfigResponse, error)
	// 节点stream
	NodeStream(NodeService_NodeStreamServer) error
	// 向节点发送命令
	SendCommandToNode(context.Context, *NodeStreamMessage) (*NodeStreamMessage, error)
	// 更新节点状态
	UpdateNodeStatus(context.Context, *UpdateNodeStatusRequest) (*RPCSuccess, error)
	// 修改节点安装状态
	UpdateNodeIsInstalled(context.Context, *UpdateNodeIsInstalledRequest) (*RPCSuccess, error)
	// 安装节点
	InstallNode(context.Context, *InstallNodeRequest) (*InstallNodeResponse, error)
	// 升级节点
	UpgradeNode(context.Context, *UpgradeNodeRequest) (*UpgradeNodeResponse, error)
	// 启动节点
	StartNode(context.Context, *StartNodeRequest) (*StartNodeResponse, error)
	// 停止节点
	StopNode(context.Context, *StopNodeRequest) (*StopNodeResponse, error)
	// 卸载节点
	UninstallNode(context.Context, *UninstallNodeRequest) (*UninstallNodeResponse, error)
	// 更改节点连接的API节点信息
	UpdateNodeConnectedAPINodes(context.Context, *UpdateNodeConnectedAPINodesRequest) (*RPCSuccess, error)
	// 计算使用某个认证的节点数量
	CountAllEnabledNodesWithNodeGrantId(context.Context, *CountAllEnabledNodesWithNodeGrantIdRequest) (*RPCCountResponse, error)
	// 查找使用某个认证的所有节点
	FindAllEnabledNodesWithNodeGrantId(context.Context, *FindAllEnabledNodesWithNodeGrantIdRequest) (*FindAllEnabledNodesWithNodeGrantIdResponse, error)
	// 计算没有安装的节点数量
	CountAllNotInstalledNodesWithNodeClusterId(context.Context, *CountAllNotInstalledNodesWithNodeClusterIdRequest) (*RPCCountResponse, error)
	// 列出所有未安装的节点
	FindAllNotInstalledNodesWithNodeClusterId(context.Context, *FindAllNotInstalledNodesWithNodeClusterIdRequest) (*FindAllNotInstalledNodesWithNodeClusterIdResponse, error)
	// 计算需要升级的节点数量
	CountAllUpgradeNodesWithNodeClusterId(context.Context, *CountAllUpgradeNodesWithNodeClusterIdRequest) (*RPCCountResponse, error)
	// 列出所有需要升级的节点
	FindAllUpgradeNodesWithNodeClusterId(context.Context, *FindAllUpgradeNodesWithNodeClusterIdRequest) (*FindAllUpgradeNodesWithNodeClusterIdResponse, error)
	// 读取节点安装状态
	FindNodeInstallStatus(context.Context, *FindNodeInstallStatusRequest) (*FindNodeInstallStatusResponse, error)
	// 修改节点登录信息
	UpdateNodeLogin(context.Context, *UpdateNodeLoginRequest) (*RPCSuccess, error)
	// 计算某个节点分组内的节点数量
	CountAllEnabledNodesWithNodeGroupId(context.Context, *CountAllEnabledNodesWithNodeGroupIdRequest) (*RPCCountResponse, error)
	// 取得某个集群下的所有节点DNS信息
	FindAllEnabledNodesDNSWithNodeClusterId(context.Context, *FindAllEnabledNodesDNSWithNodeClusterIdRequest) (*FindAllEnabledNodesDNSWithNodeClusterIdResponse, error)
	// 查找单个节点的域名解析信息
	FindEnabledNodeDNS(context.Context, *FindEnabledNodeDNSRequest) (*FindEnabledNodeDNSResponse, error)
	// 修改节点的DNS解析信息
	UpdateNodeDNS(context.Context, *UpdateNodeDNSRequest) (*RPCSuccess, error)
	// 计算某个区域下的节点数量
	CountAllEnabledNodesWithNodeRegionId(context.Context, *CountAllEnabledNodesWithNodeRegionIdRequest) (*RPCCountResponse, error)
	// 根据一组ID获取节点信息
	FindEnabledNodesWithIds(context.Context, *FindEnabledNodesWithIdsRequest) (*FindEnabledNodesWithIdsResponse, error)
	// 检查新版本
	CheckNodeLatestVersion(context.Context, *CheckNodeLatestVersionRequest) (*CheckNodeLatestVersionResponse, error)
	// 设置节点上线状态
	UpdateNodeUp(context.Context, *UpdateNodeUpRequest) (*RPCSuccess, error)
	// 下载最新边缘节点安装文件
	DownloadNodeInstallationFile(context.Context, *DownloadNodeInstallationFileRequest) (*DownloadNodeInstallationFileResponse, error)
	// 修改节点系统信息
	UpdateNodeSystem(context.Context, *UpdateNodeSystemRequest) (*RPCSuccess, error)
	// 修改节点缓存设置
	UpdateNodeCache(context.Context, *UpdateNodeCacheRequest) (*RPCSuccess, error)
	// 读取节点级别信息
	FindNodeLevelInfo(context.Context, *FindNodeLevelInfoRequest) (*FindNodeLevelInfoResponse, error)
	// 读取节点DNS Resolver
	FindNodeDNSResolver(context.Context, *FindNodeDNSResolverRequest) (*FindNodeDNSResolverResponse, error)
	// 修改DNS Resolver
	UpdateNodeDNSResolver(context.Context, *UpdateNodeDNSResolverRequest) (*RPCSuccess, error)
	// 获取节点的DDoS设置
	FindNodeDDoSProtection(context.Context, *FindNodeDDoSProtectionRequest) (*FindNodeDDoSProtectionResponse, error)
	// 修改节点的DDoS设置
	UpdateNodeDDoSProtection(context.Context, *UpdateNodeDDoSProtectionRequest) (*RPCSuccess, error)
	// 取得节点的服务全局配置
	FindNodeGlobalServerConfig(context.Context, *FindNodeGlobalServerConfigRequest) (*FindNodeGlobalServerConfigResponse, error)
	// 取得节点的配置概要信息
	FindEnabledNodeConfigInfo(context.Context, *FindEnabledNodeConfigInfoRequest) (*FindEnabledNodeConfigInfoResponse, error)
	// 查找节点区域信息数量
	CountAllNodeRegionInfo(context.Context, *CountAllNodeRegionInfoRequest) (*RPCCountResponse, error)
	// 列出单页节点区域信息
	ListNodeRegionInfo(context.Context, *ListNodeRegionInfoRequest) (*ListNodeRegionInfoResponse, error)
	// 修改节点区域信息
	UpdateNodeRegionInfo(context.Context, *UpdateNodeRegionInfoRequest) (*RPCSuccess, error)
	// 查找单个节点的API相关配置
	FindNodeAPIConfig(context.Context, *FindNodeAPIConfigRequest) (*FindNodeAPIConfigResponse, error)
	// 修改某个节点的API相关配置
	UpdateNodeAPIConfig(context.Context, *UpdateNodeAPIConfigRequest) (*RPCSuccess, error)
	// 查找节点的UAM策略
	FindNodeUAMPolicies(context.Context, *FindNodeUAMPoliciesRequest) (*FindNodeUAMPoliciesResponse, error)
	// 查找节点的HTTP CC策略
	FindNodeHTTPCCPolicies(context.Context, *FindNodeHTTPCCPoliciesRequest) (*FindNodeHTTPCCPoliciesResponse, error)
	// 查找节点的HTTP3策略
	FindNodeHTTP3Policies(context.Context, *FindNodeHTTP3PoliciesRequest) (*FindNodeHTTP3PoliciesResponse, error)
	// 查找节点的自定义页面策略
	FindNodeHTTPPagesPolicies(context.Context, *FindNodeHTTPPagesPoliciesRequest) (*FindNodeHTTPPagesPoliciesResponse, error)
	// 查找节点调度信息
	FindNodeScheduleInfo(context.Context, *FindNodeScheduleInfoRequest) (*FindNodeScheduleInfoResponse, error)
	// 修改节点调度信息
	UpdateNodeScheduleInfo(context.Context, *UpdateNodeScheduleInfoRequest) (*RPCSuccess, error)
	// 重置节点动作状态
	ResetNodeActionStatus(context.Context, *ResetNodeActionStatusRequest) (*RPCSuccess, error)
	// 查找集群的节点调度信息
	FindAllNodeScheduleInfoWithNodeClusterId(context.Context, *FindAllNodeScheduleInfoWithNodeClusterIdRequest) (*FindAllNodeScheduleInfoWithNodeClusterIdResponse, error)
	// 复制动作设置到分组
	CopyNodeActionsToNodeGroup(context.Context, *CopyNodeActionsToNodeGroupRequest) (*RPCSuccess, error)
	// 复制动作设置到集群
	CopyNodeActionsToNodeCluster(context.Context, *CopyNodeActionsToNodeClusterRequest) (*RPCSuccess, error)
	// 查找节点的TOA配置
	FindNodeTOAConfig(context.Context, *FindNodeTOAConfigRequest) (*FindNodeTOAConfigResponse, error)
	// 查找节点的网络安全策略
	FindNodeNetworkSecurityPolicy(context.Context, *FindNodeNetworkSecurityPolicyRequest) (*FindNodeNetworkSecurityPolicyResponse, error)
	// 查找节点的WebP策略
	FindNodeWebPPolicies(context.Context, *FindNodeWebPPoliciesRequest) (*FindNodeWebPPoliciesResponse, error)
	// 修改节点的启用状态
	UpdateNodeIsOn(context.Context, *UpdateNodeIsOnRequest) (*RPCSuccess, error)
}

// UnimplementedNodeServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeServiceServer struct {
}

func (UnimplementedNodeServiceServer) CreateNode(context.Context, *CreateNodeRequest) (*CreateNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNode not implemented")
}
func (UnimplementedNodeServiceServer) RegisterClusterNode(context.Context, *RegisterClusterNodeRequest) (*RegisterClusterNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterClusterNode not implemented")
}
func (UnimplementedNodeServiceServer) CountAllEnabledNodes(context.Context, *CountAllEnabledNodesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodes not implemented")
}
func (UnimplementedNodeServiceServer) CountAllEnabledNodesMatch(context.Context, *CountAllEnabledNodesMatchRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodesMatch not implemented")
}
func (UnimplementedNodeServiceServer) ListEnabledNodesMatch(context.Context, *ListEnabledNodesMatchRequest) (*ListEnabledNodesMatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledNodesMatch not implemented")
}
func (UnimplementedNodeServiceServer) FindAllEnabledNodesWithNodeClusterId(context.Context, *FindAllEnabledNodesWithNodeClusterIdRequest) (*FindAllEnabledNodesWithNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodesWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) DeleteNode(context.Context, *DeleteNodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNode not implemented")
}
func (UnimplementedNodeServiceServer) DeleteNodeFromNodeCluster(context.Context, *DeleteNodeFromNodeClusterRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeFromNodeCluster not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNode(context.Context, *UpdateNodeRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNode not implemented")
}
func (UnimplementedNodeServiceServer) FindEnabledNode(context.Context, *FindEnabledNodeRequest) (*FindEnabledNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNode not implemented")
}
func (UnimplementedNodeServiceServer) FindEnabledBasicNode(context.Context, *FindEnabledBasicNodeRequest) (*FindEnabledBasicNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledBasicNode not implemented")
}
func (UnimplementedNodeServiceServer) FindCurrentNodeConfig(context.Context, *FindCurrentNodeConfigRequest) (*FindCurrentNodeConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCurrentNodeConfig not implemented")
}
func (UnimplementedNodeServiceServer) NodeStream(NodeService_NodeStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method NodeStream not implemented")
}
func (UnimplementedNodeServiceServer) SendCommandToNode(context.Context, *NodeStreamMessage) (*NodeStreamMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCommandToNode not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeStatus(context.Context, *UpdateNodeStatusRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeStatus not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeIsInstalled(context.Context, *UpdateNodeIsInstalledRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeIsInstalled not implemented")
}
func (UnimplementedNodeServiceServer) InstallNode(context.Context, *InstallNodeRequest) (*InstallNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstallNode not implemented")
}
func (UnimplementedNodeServiceServer) UpgradeNode(context.Context, *UpgradeNodeRequest) (*UpgradeNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeNode not implemented")
}
func (UnimplementedNodeServiceServer) StartNode(context.Context, *StartNodeRequest) (*StartNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartNode not implemented")
}
func (UnimplementedNodeServiceServer) StopNode(context.Context, *StopNodeRequest) (*StopNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopNode not implemented")
}
func (UnimplementedNodeServiceServer) UninstallNode(context.Context, *UninstallNodeRequest) (*UninstallNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UninstallNode not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeConnectedAPINodes(context.Context, *UpdateNodeConnectedAPINodesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeConnectedAPINodes not implemented")
}
func (UnimplementedNodeServiceServer) CountAllEnabledNodesWithNodeGrantId(context.Context, *CountAllEnabledNodesWithNodeGrantIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodesWithNodeGrantId not implemented")
}
func (UnimplementedNodeServiceServer) FindAllEnabledNodesWithNodeGrantId(context.Context, *FindAllEnabledNodesWithNodeGrantIdRequest) (*FindAllEnabledNodesWithNodeGrantIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodesWithNodeGrantId not implemented")
}
func (UnimplementedNodeServiceServer) CountAllNotInstalledNodesWithNodeClusterId(context.Context, *CountAllNotInstalledNodesWithNodeClusterIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNotInstalledNodesWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) FindAllNotInstalledNodesWithNodeClusterId(context.Context, *FindAllNotInstalledNodesWithNodeClusterIdRequest) (*FindAllNotInstalledNodesWithNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNotInstalledNodesWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) CountAllUpgradeNodesWithNodeClusterId(context.Context, *CountAllUpgradeNodesWithNodeClusterIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllUpgradeNodesWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) FindAllUpgradeNodesWithNodeClusterId(context.Context, *FindAllUpgradeNodesWithNodeClusterIdRequest) (*FindAllUpgradeNodesWithNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllUpgradeNodesWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeInstallStatus(context.Context, *FindNodeInstallStatusRequest) (*FindNodeInstallStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeInstallStatus not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeLogin(context.Context, *UpdateNodeLoginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeLogin not implemented")
}
func (UnimplementedNodeServiceServer) CountAllEnabledNodesWithNodeGroupId(context.Context, *CountAllEnabledNodesWithNodeGroupIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodesWithNodeGroupId not implemented")
}
func (UnimplementedNodeServiceServer) FindAllEnabledNodesDNSWithNodeClusterId(context.Context, *FindAllEnabledNodesDNSWithNodeClusterIdRequest) (*FindAllEnabledNodesDNSWithNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodesDNSWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) FindEnabledNodeDNS(context.Context, *FindEnabledNodeDNSRequest) (*FindEnabledNodeDNSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeDNS not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeDNS(context.Context, *UpdateNodeDNSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeDNS not implemented")
}
func (UnimplementedNodeServiceServer) CountAllEnabledNodesWithNodeRegionId(context.Context, *CountAllEnabledNodesWithNodeRegionIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodesWithNodeRegionId not implemented")
}
func (UnimplementedNodeServiceServer) FindEnabledNodesWithIds(context.Context, *FindEnabledNodesWithIdsRequest) (*FindEnabledNodesWithIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodesWithIds not implemented")
}
func (UnimplementedNodeServiceServer) CheckNodeLatestVersion(context.Context, *CheckNodeLatestVersionRequest) (*CheckNodeLatestVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckNodeLatestVersion not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeUp(context.Context, *UpdateNodeUpRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeUp not implemented")
}
func (UnimplementedNodeServiceServer) DownloadNodeInstallationFile(context.Context, *DownloadNodeInstallationFileRequest) (*DownloadNodeInstallationFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadNodeInstallationFile not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeSystem(context.Context, *UpdateNodeSystemRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeSystem not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeCache(context.Context, *UpdateNodeCacheRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeCache not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeLevelInfo(context.Context, *FindNodeLevelInfoRequest) (*FindNodeLevelInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeLevelInfo not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeDNSResolver(context.Context, *FindNodeDNSResolverRequest) (*FindNodeDNSResolverResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeDNSResolver not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeDNSResolver(context.Context, *UpdateNodeDNSResolverRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeDNSResolver not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeDDoSProtection(context.Context, *FindNodeDDoSProtectionRequest) (*FindNodeDDoSProtectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeDDoSProtection not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeDDoSProtection(context.Context, *UpdateNodeDDoSProtectionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeDDoSProtection not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeGlobalServerConfig(context.Context, *FindNodeGlobalServerConfigRequest) (*FindNodeGlobalServerConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeGlobalServerConfig not implemented")
}
func (UnimplementedNodeServiceServer) FindEnabledNodeConfigInfo(context.Context, *FindEnabledNodeConfigInfoRequest) (*FindEnabledNodeConfigInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeConfigInfo not implemented")
}
func (UnimplementedNodeServiceServer) CountAllNodeRegionInfo(context.Context, *CountAllNodeRegionInfoRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNodeRegionInfo not implemented")
}
func (UnimplementedNodeServiceServer) ListNodeRegionInfo(context.Context, *ListNodeRegionInfoRequest) (*ListNodeRegionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodeRegionInfo not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeRegionInfo(context.Context, *UpdateNodeRegionInfoRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeRegionInfo not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeAPIConfig(context.Context, *FindNodeAPIConfigRequest) (*FindNodeAPIConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeAPIConfig not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeAPIConfig(context.Context, *UpdateNodeAPIConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeAPIConfig not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeUAMPolicies(context.Context, *FindNodeUAMPoliciesRequest) (*FindNodeUAMPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeUAMPolicies not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeHTTPCCPolicies(context.Context, *FindNodeHTTPCCPoliciesRequest) (*FindNodeHTTPCCPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeHTTPCCPolicies not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeHTTP3Policies(context.Context, *FindNodeHTTP3PoliciesRequest) (*FindNodeHTTP3PoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeHTTP3Policies not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeHTTPPagesPolicies(context.Context, *FindNodeHTTPPagesPoliciesRequest) (*FindNodeHTTPPagesPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeHTTPPagesPolicies not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeScheduleInfo(context.Context, *FindNodeScheduleInfoRequest) (*FindNodeScheduleInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeScheduleInfo not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeScheduleInfo(context.Context, *UpdateNodeScheduleInfoRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeScheduleInfo not implemented")
}
func (UnimplementedNodeServiceServer) ResetNodeActionStatus(context.Context, *ResetNodeActionStatusRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetNodeActionStatus not implemented")
}
func (UnimplementedNodeServiceServer) FindAllNodeScheduleInfoWithNodeClusterId(context.Context, *FindAllNodeScheduleInfoWithNodeClusterIdRequest) (*FindAllNodeScheduleInfoWithNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNodeScheduleInfoWithNodeClusterId not implemented")
}
func (UnimplementedNodeServiceServer) CopyNodeActionsToNodeGroup(context.Context, *CopyNodeActionsToNodeGroupRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyNodeActionsToNodeGroup not implemented")
}
func (UnimplementedNodeServiceServer) CopyNodeActionsToNodeCluster(context.Context, *CopyNodeActionsToNodeClusterRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyNodeActionsToNodeCluster not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeTOAConfig(context.Context, *FindNodeTOAConfigRequest) (*FindNodeTOAConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeTOAConfig not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeNetworkSecurityPolicy(context.Context, *FindNodeNetworkSecurityPolicyRequest) (*FindNodeNetworkSecurityPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeNetworkSecurityPolicy not implemented")
}
func (UnimplementedNodeServiceServer) FindNodeWebPPolicies(context.Context, *FindNodeWebPPoliciesRequest) (*FindNodeWebPPoliciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeWebPPolicies not implemented")
}
func (UnimplementedNodeServiceServer) UpdateNodeIsOn(context.Context, *UpdateNodeIsOnRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeIsOn not implemented")
}

// UnsafeNodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeServiceServer will
// result in compilation errors.
type UnsafeNodeServiceServer interface {
	mustEmbedUnimplementedNodeServiceServer()
}

func RegisterNodeServiceServer(s grpc.ServiceRegistrar, srv NodeServiceServer) {
	s.RegisterService(&NodeService_ServiceDesc, srv)
}

func _NodeService_CreateNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CreateNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CreateNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CreateNode(ctx, req.(*CreateNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_RegisterClusterNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterClusterNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).RegisterClusterNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_RegisterClusterNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).RegisterClusterNode(ctx, req.(*RegisterClusterNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllEnabledNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllEnabledNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllEnabledNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllEnabledNodes(ctx, req.(*CountAllEnabledNodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllEnabledNodesMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodesMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllEnabledNodesMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllEnabledNodesMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllEnabledNodesMatch(ctx, req.(*CountAllEnabledNodesMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ListEnabledNodesMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledNodesMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ListEnabledNodesMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ListEnabledNodesMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ListEnabledNodesMatch(ctx, req.(*ListEnabledNodesMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindAllEnabledNodesWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodesWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindAllEnabledNodesWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindAllEnabledNodesWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindAllEnabledNodesWithNodeClusterId(ctx, req.(*FindAllEnabledNodesWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_DeleteNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).DeleteNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_DeleteNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).DeleteNode(ctx, req.(*DeleteNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_DeleteNodeFromNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeFromNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).DeleteNodeFromNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_DeleteNodeFromNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).DeleteNodeFromNodeCluster(ctx, req.(*DeleteNodeFromNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNode(ctx, req.(*UpdateNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindEnabledNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindEnabledNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindEnabledNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindEnabledNode(ctx, req.(*FindEnabledNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindEnabledBasicNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledBasicNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindEnabledBasicNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindEnabledBasicNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindEnabledBasicNode(ctx, req.(*FindEnabledBasicNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindCurrentNodeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCurrentNodeConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindCurrentNodeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindCurrentNodeConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindCurrentNodeConfig(ctx, req.(*FindCurrentNodeConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_NodeStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(NodeServiceServer).NodeStream(&nodeServiceNodeStreamServer{stream})
}

type NodeService_NodeStreamServer interface {
	Send(*NodeStreamMessage) error
	Recv() (*NodeStreamMessage, error)
	grpc.ServerStream
}

type nodeServiceNodeStreamServer struct {
	grpc.ServerStream
}

func (x *nodeServiceNodeStreamServer) Send(m *NodeStreamMessage) error {
	return x.ServerStream.SendMsg(m)
}

func (x *nodeServiceNodeStreamServer) Recv() (*NodeStreamMessage, error) {
	m := new(NodeStreamMessage)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _NodeService_SendCommandToNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeStreamMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).SendCommandToNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_SendCommandToNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).SendCommandToNode(ctx, req.(*NodeStreamMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeStatus(ctx, req.(*UpdateNodeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeIsInstalled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeIsInstalledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeIsInstalled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeIsInstalled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeIsInstalled(ctx, req.(*UpdateNodeIsInstalledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_InstallNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).InstallNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_InstallNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).InstallNode(ctx, req.(*InstallNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpgradeNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpgradeNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpgradeNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpgradeNode(ctx, req.(*UpgradeNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_StartNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).StartNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_StartNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).StartNode(ctx, req.(*StartNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_StopNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).StopNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_StopNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).StopNode(ctx, req.(*StopNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UninstallNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UninstallNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UninstallNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UninstallNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UninstallNode(ctx, req.(*UninstallNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeConnectedAPINodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeConnectedAPINodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeConnectedAPINodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeConnectedAPINodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeConnectedAPINodes(ctx, req.(*UpdateNodeConnectedAPINodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllEnabledNodesWithNodeGrantId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodesWithNodeGrantIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllEnabledNodesWithNodeGrantId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllEnabledNodesWithNodeGrantId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllEnabledNodesWithNodeGrantId(ctx, req.(*CountAllEnabledNodesWithNodeGrantIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindAllEnabledNodesWithNodeGrantId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodesWithNodeGrantIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindAllEnabledNodesWithNodeGrantId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindAllEnabledNodesWithNodeGrantId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindAllEnabledNodesWithNodeGrantId(ctx, req.(*FindAllEnabledNodesWithNodeGrantIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllNotInstalledNodesWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNotInstalledNodesWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllNotInstalledNodesWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllNotInstalledNodesWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllNotInstalledNodesWithNodeClusterId(ctx, req.(*CountAllNotInstalledNodesWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindAllNotInstalledNodesWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNotInstalledNodesWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindAllNotInstalledNodesWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindAllNotInstalledNodesWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindAllNotInstalledNodesWithNodeClusterId(ctx, req.(*FindAllNotInstalledNodesWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllUpgradeNodesWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllUpgradeNodesWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllUpgradeNodesWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllUpgradeNodesWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllUpgradeNodesWithNodeClusterId(ctx, req.(*CountAllUpgradeNodesWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindAllUpgradeNodesWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllUpgradeNodesWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindAllUpgradeNodesWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindAllUpgradeNodesWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindAllUpgradeNodesWithNodeClusterId(ctx, req.(*FindAllUpgradeNodesWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeInstallStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeInstallStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeInstallStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeInstallStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeInstallStatus(ctx, req.(*FindNodeInstallStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeLogin(ctx, req.(*UpdateNodeLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllEnabledNodesWithNodeGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodesWithNodeGroupIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllEnabledNodesWithNodeGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllEnabledNodesWithNodeGroupId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllEnabledNodesWithNodeGroupId(ctx, req.(*CountAllEnabledNodesWithNodeGroupIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindAllEnabledNodesDNSWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodesDNSWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindAllEnabledNodesDNSWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindAllEnabledNodesDNSWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindAllEnabledNodesDNSWithNodeClusterId(ctx, req.(*FindAllEnabledNodesDNSWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindEnabledNodeDNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeDNSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindEnabledNodeDNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindEnabledNodeDNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindEnabledNodeDNS(ctx, req.(*FindEnabledNodeDNSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeDNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeDNSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeDNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeDNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeDNS(ctx, req.(*UpdateNodeDNSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllEnabledNodesWithNodeRegionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodesWithNodeRegionIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllEnabledNodesWithNodeRegionId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllEnabledNodesWithNodeRegionId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllEnabledNodesWithNodeRegionId(ctx, req.(*CountAllEnabledNodesWithNodeRegionIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindEnabledNodesWithIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodesWithIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindEnabledNodesWithIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindEnabledNodesWithIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindEnabledNodesWithIds(ctx, req.(*FindEnabledNodesWithIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CheckNodeLatestVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckNodeLatestVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CheckNodeLatestVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CheckNodeLatestVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CheckNodeLatestVersion(ctx, req.(*CheckNodeLatestVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeUp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeUp(ctx, req.(*UpdateNodeUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_DownloadNodeInstallationFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadNodeInstallationFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).DownloadNodeInstallationFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_DownloadNodeInstallationFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).DownloadNodeInstallationFile(ctx, req.(*DownloadNodeInstallationFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeSystem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeSystemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeSystem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeSystem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeSystem(ctx, req.(*UpdateNodeSystemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeCache(ctx, req.(*UpdateNodeCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeLevelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeLevelInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeLevelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeLevelInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeLevelInfo(ctx, req.(*FindNodeLevelInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeDNSResolver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeDNSResolverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeDNSResolver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeDNSResolver_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeDNSResolver(ctx, req.(*FindNodeDNSResolverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeDNSResolver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeDNSResolverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeDNSResolver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeDNSResolver_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeDNSResolver(ctx, req.(*UpdateNodeDNSResolverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeDDoSProtection(ctx, req.(*FindNodeDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeDDoSProtection(ctx, req.(*UpdateNodeDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeGlobalServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeGlobalServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeGlobalServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeGlobalServerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeGlobalServerConfig(ctx, req.(*FindNodeGlobalServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindEnabledNodeConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeConfigInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindEnabledNodeConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindEnabledNodeConfigInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindEnabledNodeConfigInfo(ctx, req.(*FindEnabledNodeConfigInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CountAllNodeRegionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNodeRegionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CountAllNodeRegionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CountAllNodeRegionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CountAllNodeRegionInfo(ctx, req.(*CountAllNodeRegionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ListNodeRegionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeRegionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ListNodeRegionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ListNodeRegionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ListNodeRegionInfo(ctx, req.(*ListNodeRegionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeRegionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeRegionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeRegionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeRegionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeRegionInfo(ctx, req.(*UpdateNodeRegionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeAPIConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeAPIConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeAPIConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeAPIConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeAPIConfig(ctx, req.(*FindNodeAPIConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeAPIConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeAPIConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeAPIConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeAPIConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeAPIConfig(ctx, req.(*UpdateNodeAPIConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeUAMPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeUAMPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeUAMPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeUAMPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeUAMPolicies(ctx, req.(*FindNodeUAMPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeHTTPCCPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeHTTPCCPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeHTTPCCPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeHTTPCCPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeHTTPCCPolicies(ctx, req.(*FindNodeHTTPCCPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeHTTP3Policies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeHTTP3PoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeHTTP3Policies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeHTTP3Policies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeHTTP3Policies(ctx, req.(*FindNodeHTTP3PoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeHTTPPagesPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeHTTPPagesPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeHTTPPagesPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeHTTPPagesPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeHTTPPagesPolicies(ctx, req.(*FindNodeHTTPPagesPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeScheduleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeScheduleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeScheduleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeScheduleInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeScheduleInfo(ctx, req.(*FindNodeScheduleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeScheduleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeScheduleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeScheduleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeScheduleInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeScheduleInfo(ctx, req.(*UpdateNodeScheduleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ResetNodeActionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetNodeActionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ResetNodeActionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ResetNodeActionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ResetNodeActionStatus(ctx, req.(*ResetNodeActionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindAllNodeScheduleInfoWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNodeScheduleInfoWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindAllNodeScheduleInfoWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindAllNodeScheduleInfoWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindAllNodeScheduleInfoWithNodeClusterId(ctx, req.(*FindAllNodeScheduleInfoWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CopyNodeActionsToNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyNodeActionsToNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CopyNodeActionsToNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CopyNodeActionsToNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CopyNodeActionsToNodeGroup(ctx, req.(*CopyNodeActionsToNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_CopyNodeActionsToNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyNodeActionsToNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CopyNodeActionsToNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CopyNodeActionsToNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CopyNodeActionsToNodeCluster(ctx, req.(*CopyNodeActionsToNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeTOAConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeTOAConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeTOAConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeTOAConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeTOAConfig(ctx, req.(*FindNodeTOAConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeNetworkSecurityPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeNetworkSecurityPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeNetworkSecurityPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeNetworkSecurityPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeNetworkSecurityPolicy(ctx, req.(*FindNodeNetworkSecurityPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_FindNodeWebPPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeWebPPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).FindNodeWebPPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_FindNodeWebPPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).FindNodeWebPPolicies(ctx, req.(*FindNodeWebPPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateNodeIsOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeIsOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateNodeIsOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateNodeIsOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateNodeIsOn(ctx, req.(*UpdateNodeIsOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeService_ServiceDesc is the grpc.ServiceDesc for NodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeService",
	HandlerType: (*NodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNode",
			Handler:    _NodeService_CreateNode_Handler,
		},
		{
			MethodName: "registerClusterNode",
			Handler:    _NodeService_RegisterClusterNode_Handler,
		},
		{
			MethodName: "countAllEnabledNodes",
			Handler:    _NodeService_CountAllEnabledNodes_Handler,
		},
		{
			MethodName: "countAllEnabledNodesMatch",
			Handler:    _NodeService_CountAllEnabledNodesMatch_Handler,
		},
		{
			MethodName: "listEnabledNodesMatch",
			Handler:    _NodeService_ListEnabledNodesMatch_Handler,
		},
		{
			MethodName: "findAllEnabledNodesWithNodeClusterId",
			Handler:    _NodeService_FindAllEnabledNodesWithNodeClusterId_Handler,
		},
		{
			MethodName: "deleteNode",
			Handler:    _NodeService_DeleteNode_Handler,
		},
		{
			MethodName: "deleteNodeFromNodeCluster",
			Handler:    _NodeService_DeleteNodeFromNodeCluster_Handler,
		},
		{
			MethodName: "updateNode",
			Handler:    _NodeService_UpdateNode_Handler,
		},
		{
			MethodName: "findEnabledNode",
			Handler:    _NodeService_FindEnabledNode_Handler,
		},
		{
			MethodName: "findEnabledBasicNode",
			Handler:    _NodeService_FindEnabledBasicNode_Handler,
		},
		{
			MethodName: "findCurrentNodeConfig",
			Handler:    _NodeService_FindCurrentNodeConfig_Handler,
		},
		{
			MethodName: "sendCommandToNode",
			Handler:    _NodeService_SendCommandToNode_Handler,
		},
		{
			MethodName: "updateNodeStatus",
			Handler:    _NodeService_UpdateNodeStatus_Handler,
		},
		{
			MethodName: "updateNodeIsInstalled",
			Handler:    _NodeService_UpdateNodeIsInstalled_Handler,
		},
		{
			MethodName: "installNode",
			Handler:    _NodeService_InstallNode_Handler,
		},
		{
			MethodName: "upgradeNode",
			Handler:    _NodeService_UpgradeNode_Handler,
		},
		{
			MethodName: "startNode",
			Handler:    _NodeService_StartNode_Handler,
		},
		{
			MethodName: "stopNode",
			Handler:    _NodeService_StopNode_Handler,
		},
		{
			MethodName: "uninstallNode",
			Handler:    _NodeService_UninstallNode_Handler,
		},
		{
			MethodName: "updateNodeConnectedAPINodes",
			Handler:    _NodeService_UpdateNodeConnectedAPINodes_Handler,
		},
		{
			MethodName: "countAllEnabledNodesWithNodeGrantId",
			Handler:    _NodeService_CountAllEnabledNodesWithNodeGrantId_Handler,
		},
		{
			MethodName: "findAllEnabledNodesWithNodeGrantId",
			Handler:    _NodeService_FindAllEnabledNodesWithNodeGrantId_Handler,
		},
		{
			MethodName: "countAllNotInstalledNodesWithNodeClusterId",
			Handler:    _NodeService_CountAllNotInstalledNodesWithNodeClusterId_Handler,
		},
		{
			MethodName: "findAllNotInstalledNodesWithNodeClusterId",
			Handler:    _NodeService_FindAllNotInstalledNodesWithNodeClusterId_Handler,
		},
		{
			MethodName: "countAllUpgradeNodesWithNodeClusterId",
			Handler:    _NodeService_CountAllUpgradeNodesWithNodeClusterId_Handler,
		},
		{
			MethodName: "findAllUpgradeNodesWithNodeClusterId",
			Handler:    _NodeService_FindAllUpgradeNodesWithNodeClusterId_Handler,
		},
		{
			MethodName: "findNodeInstallStatus",
			Handler:    _NodeService_FindNodeInstallStatus_Handler,
		},
		{
			MethodName: "updateNodeLogin",
			Handler:    _NodeService_UpdateNodeLogin_Handler,
		},
		{
			MethodName: "countAllEnabledNodesWithNodeGroupId",
			Handler:    _NodeService_CountAllEnabledNodesWithNodeGroupId_Handler,
		},
		{
			MethodName: "findAllEnabledNodesDNSWithNodeClusterId",
			Handler:    _NodeService_FindAllEnabledNodesDNSWithNodeClusterId_Handler,
		},
		{
			MethodName: "findEnabledNodeDNS",
			Handler:    _NodeService_FindEnabledNodeDNS_Handler,
		},
		{
			MethodName: "updateNodeDNS",
			Handler:    _NodeService_UpdateNodeDNS_Handler,
		},
		{
			MethodName: "countAllEnabledNodesWithNodeRegionId",
			Handler:    _NodeService_CountAllEnabledNodesWithNodeRegionId_Handler,
		},
		{
			MethodName: "findEnabledNodesWithIds",
			Handler:    _NodeService_FindEnabledNodesWithIds_Handler,
		},
		{
			MethodName: "checkNodeLatestVersion",
			Handler:    _NodeService_CheckNodeLatestVersion_Handler,
		},
		{
			MethodName: "updateNodeUp",
			Handler:    _NodeService_UpdateNodeUp_Handler,
		},
		{
			MethodName: "downloadNodeInstallationFile",
			Handler:    _NodeService_DownloadNodeInstallationFile_Handler,
		},
		{
			MethodName: "updateNodeSystem",
			Handler:    _NodeService_UpdateNodeSystem_Handler,
		},
		{
			MethodName: "updateNodeCache",
			Handler:    _NodeService_UpdateNodeCache_Handler,
		},
		{
			MethodName: "findNodeLevelInfo",
			Handler:    _NodeService_FindNodeLevelInfo_Handler,
		},
		{
			MethodName: "findNodeDNSResolver",
			Handler:    _NodeService_FindNodeDNSResolver_Handler,
		},
		{
			MethodName: "updateNodeDNSResolver",
			Handler:    _NodeService_UpdateNodeDNSResolver_Handler,
		},
		{
			MethodName: "findNodeDDoSProtection",
			Handler:    _NodeService_FindNodeDDoSProtection_Handler,
		},
		{
			MethodName: "updateNodeDDoSProtection",
			Handler:    _NodeService_UpdateNodeDDoSProtection_Handler,
		},
		{
			MethodName: "findNodeGlobalServerConfig",
			Handler:    _NodeService_FindNodeGlobalServerConfig_Handler,
		},
		{
			MethodName: "findEnabledNodeConfigInfo",
			Handler:    _NodeService_FindEnabledNodeConfigInfo_Handler,
		},
		{
			MethodName: "countAllNodeRegionInfo",
			Handler:    _NodeService_CountAllNodeRegionInfo_Handler,
		},
		{
			MethodName: "listNodeRegionInfo",
			Handler:    _NodeService_ListNodeRegionInfo_Handler,
		},
		{
			MethodName: "updateNodeRegionInfo",
			Handler:    _NodeService_UpdateNodeRegionInfo_Handler,
		},
		{
			MethodName: "findNodeAPIConfig",
			Handler:    _NodeService_FindNodeAPIConfig_Handler,
		},
		{
			MethodName: "updateNodeAPIConfig",
			Handler:    _NodeService_UpdateNodeAPIConfig_Handler,
		},
		{
			MethodName: "findNodeUAMPolicies",
			Handler:    _NodeService_FindNodeUAMPolicies_Handler,
		},
		{
			MethodName: "findNodeHTTPCCPolicies",
			Handler:    _NodeService_FindNodeHTTPCCPolicies_Handler,
		},
		{
			MethodName: "findNodeHTTP3Policies",
			Handler:    _NodeService_FindNodeHTTP3Policies_Handler,
		},
		{
			MethodName: "findNodeHTTPPagesPolicies",
			Handler:    _NodeService_FindNodeHTTPPagesPolicies_Handler,
		},
		{
			MethodName: "findNodeScheduleInfo",
			Handler:    _NodeService_FindNodeScheduleInfo_Handler,
		},
		{
			MethodName: "updateNodeScheduleInfo",
			Handler:    _NodeService_UpdateNodeScheduleInfo_Handler,
		},
		{
			MethodName: "resetNodeActionStatus",
			Handler:    _NodeService_ResetNodeActionStatus_Handler,
		},
		{
			MethodName: "findAllNodeScheduleInfoWithNodeClusterId",
			Handler:    _NodeService_FindAllNodeScheduleInfoWithNodeClusterId_Handler,
		},
		{
			MethodName: "copyNodeActionsToNodeGroup",
			Handler:    _NodeService_CopyNodeActionsToNodeGroup_Handler,
		},
		{
			MethodName: "copyNodeActionsToNodeCluster",
			Handler:    _NodeService_CopyNodeActionsToNodeCluster_Handler,
		},
		{
			MethodName: "findNodeTOAConfig",
			Handler:    _NodeService_FindNodeTOAConfig_Handler,
		},
		{
			MethodName: "findNodeNetworkSecurityPolicy",
			Handler:    _NodeService_FindNodeNetworkSecurityPolicy_Handler,
		},
		{
			MethodName: "findNodeWebPPolicies",
			Handler:    _NodeService_FindNodeWebPPolicies_Handler,
		},
		{
			MethodName: "updateNodeIsOn",
			Handler:    _NodeService_UpdateNodeIsOn_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "nodeStream",
			Handler:       _NodeService_NodeStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "service_node.proto",
}
