// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ip_library.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	IPLibraryService_CreateIPLibrary_FullMethodName                   = "/pb.IPLibraryService/createIPLibrary"
	IPLibraryService_FindLatestIPLibraryWithType_FullMethodName       = "/pb.IPLibraryService/findLatestIPLibraryWithType"
	IPLibraryService_FindEnabledIPLibrary_FullMethodName              = "/pb.IPLibraryService/findEnabledIPLibrary"
	IPLibraryService_FindAllEnabledIPLibrariesWithType_FullMethodName = "/pb.IPLibraryService/findAllEnabledIPLibrariesWithType"
	IPLibraryService_DeleteIPLibrary_FullMethodName                   = "/pb.IPLibraryService/deleteIPLibrary"
	IPLibraryService_LookupIPRegion_FullMethodName                    = "/pb.IPLibraryService/lookupIPRegion"
	IPLibraryService_LookupIPRegions_FullMethodName                   = "/pb.IPLibraryService/lookupIPRegions"
)

// IPLibraryServiceClient is the client API for IPLibraryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IPLibraryServiceClient interface {
	// Deprecated: Do not use.
	// 创建IP库
	CreateIPLibrary(ctx context.Context, in *CreateIPLibraryRequest, opts ...grpc.CallOption) (*CreateIPLibraryResponse, error)
	// Deprecated: Do not use.
	// 查找最新的IP库
	FindLatestIPLibraryWithType(ctx context.Context, in *FindLatestIPLibraryWithTypeRequest, opts ...grpc.CallOption) (*FindLatestIPLibraryWithTypeResponse, error)
	// Deprecated: Do not use.
	// 查找单个IP库
	FindEnabledIPLibrary(ctx context.Context, in *FindEnabledIPLibraryRequest, opts ...grpc.CallOption) (*FindEnabledIPLibraryResponse, error)
	// Deprecated: Do not use.
	// 列出某个类型的所有IP库
	FindAllEnabledIPLibrariesWithType(ctx context.Context, in *FindAllEnabledIPLibrariesWithTypeRequest, opts ...grpc.CallOption) (*FindAllEnabledIPLibrariesWithTypeResponse, error)
	// Deprecated: Do not use.
	// 删除IP库
	DeleteIPLibrary(ctx context.Context, in *DeleteIPLibraryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查询某个IP信息
	LookupIPRegion(ctx context.Context, in *LookupIPRegionRequest, opts ...grpc.CallOption) (*LookupIPRegionResponse, error)
	// 查询一组IP信息
	LookupIPRegions(ctx context.Context, in *LookupIPRegionsRequest, opts ...grpc.CallOption) (*LookupIPRegionsResponse, error)
}

type iPLibraryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewIPLibraryServiceClient(cc grpc.ClientConnInterface) IPLibraryServiceClient {
	return &iPLibraryServiceClient{cc}
}

// Deprecated: Do not use.
func (c *iPLibraryServiceClient) CreateIPLibrary(ctx context.Context, in *CreateIPLibraryRequest, opts ...grpc.CallOption) (*CreateIPLibraryResponse, error) {
	out := new(CreateIPLibraryResponse)
	err := c.cc.Invoke(ctx, IPLibraryService_CreateIPLibrary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *iPLibraryServiceClient) FindLatestIPLibraryWithType(ctx context.Context, in *FindLatestIPLibraryWithTypeRequest, opts ...grpc.CallOption) (*FindLatestIPLibraryWithTypeResponse, error) {
	out := new(FindLatestIPLibraryWithTypeResponse)
	err := c.cc.Invoke(ctx, IPLibraryService_FindLatestIPLibraryWithType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *iPLibraryServiceClient) FindEnabledIPLibrary(ctx context.Context, in *FindEnabledIPLibraryRequest, opts ...grpc.CallOption) (*FindEnabledIPLibraryResponse, error) {
	out := new(FindEnabledIPLibraryResponse)
	err := c.cc.Invoke(ctx, IPLibraryService_FindEnabledIPLibrary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *iPLibraryServiceClient) FindAllEnabledIPLibrariesWithType(ctx context.Context, in *FindAllEnabledIPLibrariesWithTypeRequest, opts ...grpc.CallOption) (*FindAllEnabledIPLibrariesWithTypeResponse, error) {
	out := new(FindAllEnabledIPLibrariesWithTypeResponse)
	err := c.cc.Invoke(ctx, IPLibraryService_FindAllEnabledIPLibrariesWithType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *iPLibraryServiceClient) DeleteIPLibrary(ctx context.Context, in *DeleteIPLibraryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, IPLibraryService_DeleteIPLibrary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPLibraryServiceClient) LookupIPRegion(ctx context.Context, in *LookupIPRegionRequest, opts ...grpc.CallOption) (*LookupIPRegionResponse, error) {
	out := new(LookupIPRegionResponse)
	err := c.cc.Invoke(ctx, IPLibraryService_LookupIPRegion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPLibraryServiceClient) LookupIPRegions(ctx context.Context, in *LookupIPRegionsRequest, opts ...grpc.CallOption) (*LookupIPRegionsResponse, error) {
	out := new(LookupIPRegionsResponse)
	err := c.cc.Invoke(ctx, IPLibraryService_LookupIPRegions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IPLibraryServiceServer is the server API for IPLibraryService service.
// All implementations should embed UnimplementedIPLibraryServiceServer
// for forward compatibility
type IPLibraryServiceServer interface {
	// Deprecated: Do not use.
	// 创建IP库
	CreateIPLibrary(context.Context, *CreateIPLibraryRequest) (*CreateIPLibraryResponse, error)
	// Deprecated: Do not use.
	// 查找最新的IP库
	FindLatestIPLibraryWithType(context.Context, *FindLatestIPLibraryWithTypeRequest) (*FindLatestIPLibraryWithTypeResponse, error)
	// Deprecated: Do not use.
	// 查找单个IP库
	FindEnabledIPLibrary(context.Context, *FindEnabledIPLibraryRequest) (*FindEnabledIPLibraryResponse, error)
	// Deprecated: Do not use.
	// 列出某个类型的所有IP库
	FindAllEnabledIPLibrariesWithType(context.Context, *FindAllEnabledIPLibrariesWithTypeRequest) (*FindAllEnabledIPLibrariesWithTypeResponse, error)
	// Deprecated: Do not use.
	// 删除IP库
	DeleteIPLibrary(context.Context, *DeleteIPLibraryRequest) (*RPCSuccess, error)
	// 查询某个IP信息
	LookupIPRegion(context.Context, *LookupIPRegionRequest) (*LookupIPRegionResponse, error)
	// 查询一组IP信息
	LookupIPRegions(context.Context, *LookupIPRegionsRequest) (*LookupIPRegionsResponse, error)
}

// UnimplementedIPLibraryServiceServer should be embedded to have forward compatible implementations.
type UnimplementedIPLibraryServiceServer struct {
}

func (UnimplementedIPLibraryServiceServer) CreateIPLibrary(context.Context, *CreateIPLibraryRequest) (*CreateIPLibraryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIPLibrary not implemented")
}
func (UnimplementedIPLibraryServiceServer) FindLatestIPLibraryWithType(context.Context, *FindLatestIPLibraryWithTypeRequest) (*FindLatestIPLibraryWithTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLatestIPLibraryWithType not implemented")
}
func (UnimplementedIPLibraryServiceServer) FindEnabledIPLibrary(context.Context, *FindEnabledIPLibraryRequest) (*FindEnabledIPLibraryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledIPLibrary not implemented")
}
func (UnimplementedIPLibraryServiceServer) FindAllEnabledIPLibrariesWithType(context.Context, *FindAllEnabledIPLibrariesWithTypeRequest) (*FindAllEnabledIPLibrariesWithTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledIPLibrariesWithType not implemented")
}
func (UnimplementedIPLibraryServiceServer) DeleteIPLibrary(context.Context, *DeleteIPLibraryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIPLibrary not implemented")
}
func (UnimplementedIPLibraryServiceServer) LookupIPRegion(context.Context, *LookupIPRegionRequest) (*LookupIPRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LookupIPRegion not implemented")
}
func (UnimplementedIPLibraryServiceServer) LookupIPRegions(context.Context, *LookupIPRegionsRequest) (*LookupIPRegionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LookupIPRegions not implemented")
}

// UnsafeIPLibraryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IPLibraryServiceServer will
// result in compilation errors.
type UnsafeIPLibraryServiceServer interface {
	mustEmbedUnimplementedIPLibraryServiceServer()
}

func RegisterIPLibraryServiceServer(s grpc.ServiceRegistrar, srv IPLibraryServiceServer) {
	s.RegisterService(&IPLibraryService_ServiceDesc, srv)
}

func _IPLibraryService_CreateIPLibrary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIPLibraryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).CreateIPLibrary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_CreateIPLibrary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).CreateIPLibrary(ctx, req.(*CreateIPLibraryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPLibraryService_FindLatestIPLibraryWithType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLatestIPLibraryWithTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).FindLatestIPLibraryWithType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_FindLatestIPLibraryWithType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).FindLatestIPLibraryWithType(ctx, req.(*FindLatestIPLibraryWithTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPLibraryService_FindEnabledIPLibrary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledIPLibraryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).FindEnabledIPLibrary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_FindEnabledIPLibrary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).FindEnabledIPLibrary(ctx, req.(*FindEnabledIPLibraryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPLibraryService_FindAllEnabledIPLibrariesWithType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledIPLibrariesWithTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).FindAllEnabledIPLibrariesWithType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_FindAllEnabledIPLibrariesWithType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).FindAllEnabledIPLibrariesWithType(ctx, req.(*FindAllEnabledIPLibrariesWithTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPLibraryService_DeleteIPLibrary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIPLibraryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).DeleteIPLibrary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_DeleteIPLibrary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).DeleteIPLibrary(ctx, req.(*DeleteIPLibraryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPLibraryService_LookupIPRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LookupIPRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).LookupIPRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_LookupIPRegion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).LookupIPRegion(ctx, req.(*LookupIPRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPLibraryService_LookupIPRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LookupIPRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPLibraryServiceServer).LookupIPRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IPLibraryService_LookupIPRegions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPLibraryServiceServer).LookupIPRegions(ctx, req.(*LookupIPRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// IPLibraryService_ServiceDesc is the grpc.ServiceDesc for IPLibraryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IPLibraryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.IPLibraryService",
	HandlerType: (*IPLibraryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createIPLibrary",
			Handler:    _IPLibraryService_CreateIPLibrary_Handler,
		},
		{
			MethodName: "findLatestIPLibraryWithType",
			Handler:    _IPLibraryService_FindLatestIPLibraryWithType_Handler,
		},
		{
			MethodName: "findEnabledIPLibrary",
			Handler:    _IPLibraryService_FindEnabledIPLibrary_Handler,
		},
		{
			MethodName: "findAllEnabledIPLibrariesWithType",
			Handler:    _IPLibraryService_FindAllEnabledIPLibrariesWithType_Handler,
		},
		{
			MethodName: "deleteIPLibrary",
			Handler:    _IPLibraryService_DeleteIPLibrary_Handler,
		},
		{
			MethodName: "lookupIPRegion",
			Handler:    _IPLibraryService_LookupIPRegion_Handler,
		},
		{
			MethodName: "lookupIPRegions",
			Handler:    _IPLibraryService_LookupIPRegions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ip_library.proto",
}
