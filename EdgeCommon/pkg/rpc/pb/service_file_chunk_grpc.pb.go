// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_file_chunk.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FileChunkService_CreateFileChunk_FullMethodName     = "/pb.FileChunkService/createFileChunk"
	FileChunkService_FindAllFileChunkIds_FullMethodName = "/pb.FileChunkService/findAllFileChunkIds"
	FileChunkService_DownloadFileChunk_FullMethodName   = "/pb.FileChunkService/downloadFileChunk"
)

// FileChunkServiceClient is the client API for FileChunkService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileChunkServiceClient interface {
	// 创建文件片段
	CreateFileChunk(ctx context.Context, in *CreateFileChunkRequest, opts ...grpc.CallOption) (*CreateFileChunkResponse, error)
	// 获取的一个文件的所有片段IDs
	FindAllFileChunkIds(ctx context.Context, in *FindAllFileChunkIdsRequest, opts ...grpc.CallOption) (*FindAllFileChunkIdsResponse, error)
	// 下载文件片段
	DownloadFileChunk(ctx context.Context, in *DownloadFileChunkRequest, opts ...grpc.CallOption) (*DownloadFileChunkResponse, error)
}

type fileChunkServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileChunkServiceClient(cc grpc.ClientConnInterface) FileChunkServiceClient {
	return &fileChunkServiceClient{cc}
}

func (c *fileChunkServiceClient) CreateFileChunk(ctx context.Context, in *CreateFileChunkRequest, opts ...grpc.CallOption) (*CreateFileChunkResponse, error) {
	out := new(CreateFileChunkResponse)
	err := c.cc.Invoke(ctx, FileChunkService_CreateFileChunk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileChunkServiceClient) FindAllFileChunkIds(ctx context.Context, in *FindAllFileChunkIdsRequest, opts ...grpc.CallOption) (*FindAllFileChunkIdsResponse, error) {
	out := new(FindAllFileChunkIdsResponse)
	err := c.cc.Invoke(ctx, FileChunkService_FindAllFileChunkIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileChunkServiceClient) DownloadFileChunk(ctx context.Context, in *DownloadFileChunkRequest, opts ...grpc.CallOption) (*DownloadFileChunkResponse, error) {
	out := new(DownloadFileChunkResponse)
	err := c.cc.Invoke(ctx, FileChunkService_DownloadFileChunk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileChunkServiceServer is the server API for FileChunkService service.
// All implementations should embed UnimplementedFileChunkServiceServer
// for forward compatibility
type FileChunkServiceServer interface {
	// 创建文件片段
	CreateFileChunk(context.Context, *CreateFileChunkRequest) (*CreateFileChunkResponse, error)
	// 获取的一个文件的所有片段IDs
	FindAllFileChunkIds(context.Context, *FindAllFileChunkIdsRequest) (*FindAllFileChunkIdsResponse, error)
	// 下载文件片段
	DownloadFileChunk(context.Context, *DownloadFileChunkRequest) (*DownloadFileChunkResponse, error)
}

// UnimplementedFileChunkServiceServer should be embedded to have forward compatible implementations.
type UnimplementedFileChunkServiceServer struct {
}

func (UnimplementedFileChunkServiceServer) CreateFileChunk(context.Context, *CreateFileChunkRequest) (*CreateFileChunkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFileChunk not implemented")
}
func (UnimplementedFileChunkServiceServer) FindAllFileChunkIds(context.Context, *FindAllFileChunkIdsRequest) (*FindAllFileChunkIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllFileChunkIds not implemented")
}
func (UnimplementedFileChunkServiceServer) DownloadFileChunk(context.Context, *DownloadFileChunkRequest) (*DownloadFileChunkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadFileChunk not implemented")
}

// UnsafeFileChunkServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileChunkServiceServer will
// result in compilation errors.
type UnsafeFileChunkServiceServer interface {
	mustEmbedUnimplementedFileChunkServiceServer()
}

func RegisterFileChunkServiceServer(s grpc.ServiceRegistrar, srv FileChunkServiceServer) {
	s.RegisterService(&FileChunkService_ServiceDesc, srv)
}

func _FileChunkService_CreateFileChunk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFileChunkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileChunkServiceServer).CreateFileChunk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileChunkService_CreateFileChunk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileChunkServiceServer).CreateFileChunk(ctx, req.(*CreateFileChunkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileChunkService_FindAllFileChunkIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllFileChunkIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileChunkServiceServer).FindAllFileChunkIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileChunkService_FindAllFileChunkIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileChunkServiceServer).FindAllFileChunkIds(ctx, req.(*FindAllFileChunkIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileChunkService_DownloadFileChunk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadFileChunkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileChunkServiceServer).DownloadFileChunk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileChunkService_DownloadFileChunk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileChunkServiceServer).DownloadFileChunk(ctx, req.(*DownloadFileChunkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FileChunkService_ServiceDesc is the grpc.ServiceDesc for FileChunkService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileChunkService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.FileChunkService",
	HandlerType: (*FileChunkServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createFileChunk",
			Handler:    _FileChunkService_CreateFileChunk_Handler,
		},
		{
			MethodName: "findAllFileChunkIds",
			Handler:    _FileChunkService_FindAllFileChunkIds_Handler,
		},
		{
			MethodName: "downloadFileChunk",
			Handler:    _FileChunkService_DownloadFileChunk_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_file_chunk.proto",
}
