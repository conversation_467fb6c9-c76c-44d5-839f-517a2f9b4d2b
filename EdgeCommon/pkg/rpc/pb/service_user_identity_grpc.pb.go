// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_identity.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserIdentityService_CreateUserIdentity_FullMethodName                 = "/pb.UserIdentityService/createUserIdentity"
	UserIdentityService_FindEnabledUserIdentity_FullMethodName            = "/pb.UserIdentityService/findEnabledUserIdentity"
	UserIdentityService_FindEnabledUserIdentityWithOrgType_FullMethodName = "/pb.UserIdentityService/findEnabledUserIdentityWithOrgType"
	UserIdentityService_CheckUserIdentityIsSubmitted_FullMethodName       = "/pb.UserIdentityService/checkUserIdentityIsSubmitted"
	UserIdentityService_UpdateUserIdentity_FullMethodName                 = "/pb.UserIdentityService/updateUserIdentity"
	UserIdentityService_SubmitUserIdentity_FullMethodName                 = "/pb.UserIdentityService/submitUserIdentity"
	UserIdentityService_CancelUserIdentity_FullMethodName                 = "/pb.UserIdentityService/cancelUserIdentity"
	UserIdentityService_ResetUserIdentity_FullMethodName                  = "/pb.UserIdentityService/resetUserIdentity"
	UserIdentityService_RejectUserIdentity_FullMethodName                 = "/pb.UserIdentityService/rejectUserIdentity"
	UserIdentityService_VerifyUserIdentity_FullMethodName                 = "/pb.UserIdentityService/verifyUserIdentity"
)

// UserIdentityServiceClient is the client API for UserIdentityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserIdentityServiceClient interface {
	// 创建实名认证信息
	CreateUserIdentity(ctx context.Context, in *CreateUserIdentityRequest, opts ...grpc.CallOption) (*CreateUserIdentityResponse, error)
	// 查找单个实名认证信息
	FindEnabledUserIdentity(ctx context.Context, in *FindEnabledUserIdentityRequest, opts ...grpc.CallOption) (*FindEnabledUserIdentityResponse, error)
	// 查看某个类型的实名认证信息
	FindEnabledUserIdentityWithOrgType(ctx context.Context, in *FindEnabledUserIdentityWithOrgTypeRequest, opts ...grpc.CallOption) (*FindEnabledUserIdentityWithOrgTypeResponse, error)
	// 检查是否正在审核中
	CheckUserIdentityIsSubmitted(ctx context.Context, in *CheckUserIdentityIsSubmittedRequest, opts ...grpc.CallOption) (*CheckUserIdentityIsSubmittedResponse, error)
	// 修改实名认证信息
	UpdateUserIdentity(ctx context.Context, in *UpdateUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 提交审核实名认证信息
	SubmitUserIdentity(ctx context.Context, in *SubmitUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 取消提交实名审核认证信息
	CancelUserIdentity(ctx context.Context, in *CancelUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 重置用户实名认证信息
	ResetUserIdentity(ctx context.Context, in *ResetUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 拒绝用户实名认证信息
	RejectUserIdentity(ctx context.Context, in *RejectUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 通过用户实名认证信息
	VerifyUserIdentity(ctx context.Context, in *VerifyUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type userIdentityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserIdentityServiceClient(cc grpc.ClientConnInterface) UserIdentityServiceClient {
	return &userIdentityServiceClient{cc}
}

func (c *userIdentityServiceClient) CreateUserIdentity(ctx context.Context, in *CreateUserIdentityRequest, opts ...grpc.CallOption) (*CreateUserIdentityResponse, error) {
	out := new(CreateUserIdentityResponse)
	err := c.cc.Invoke(ctx, UserIdentityService_CreateUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) FindEnabledUserIdentity(ctx context.Context, in *FindEnabledUserIdentityRequest, opts ...grpc.CallOption) (*FindEnabledUserIdentityResponse, error) {
	out := new(FindEnabledUserIdentityResponse)
	err := c.cc.Invoke(ctx, UserIdentityService_FindEnabledUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) FindEnabledUserIdentityWithOrgType(ctx context.Context, in *FindEnabledUserIdentityWithOrgTypeRequest, opts ...grpc.CallOption) (*FindEnabledUserIdentityWithOrgTypeResponse, error) {
	out := new(FindEnabledUserIdentityWithOrgTypeResponse)
	err := c.cc.Invoke(ctx, UserIdentityService_FindEnabledUserIdentityWithOrgType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) CheckUserIdentityIsSubmitted(ctx context.Context, in *CheckUserIdentityIsSubmittedRequest, opts ...grpc.CallOption) (*CheckUserIdentityIsSubmittedResponse, error) {
	out := new(CheckUserIdentityIsSubmittedResponse)
	err := c.cc.Invoke(ctx, UserIdentityService_CheckUserIdentityIsSubmitted_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) UpdateUserIdentity(ctx context.Context, in *UpdateUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserIdentityService_UpdateUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) SubmitUserIdentity(ctx context.Context, in *SubmitUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserIdentityService_SubmitUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) CancelUserIdentity(ctx context.Context, in *CancelUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserIdentityService_CancelUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) ResetUserIdentity(ctx context.Context, in *ResetUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserIdentityService_ResetUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) RejectUserIdentity(ctx context.Context, in *RejectUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserIdentityService_RejectUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userIdentityServiceClient) VerifyUserIdentity(ctx context.Context, in *VerifyUserIdentityRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserIdentityService_VerifyUserIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserIdentityServiceServer is the server API for UserIdentityService service.
// All implementations should embed UnimplementedUserIdentityServiceServer
// for forward compatibility
type UserIdentityServiceServer interface {
	// 创建实名认证信息
	CreateUserIdentity(context.Context, *CreateUserIdentityRequest) (*CreateUserIdentityResponse, error)
	// 查找单个实名认证信息
	FindEnabledUserIdentity(context.Context, *FindEnabledUserIdentityRequest) (*FindEnabledUserIdentityResponse, error)
	// 查看某个类型的实名认证信息
	FindEnabledUserIdentityWithOrgType(context.Context, *FindEnabledUserIdentityWithOrgTypeRequest) (*FindEnabledUserIdentityWithOrgTypeResponse, error)
	// 检查是否正在审核中
	CheckUserIdentityIsSubmitted(context.Context, *CheckUserIdentityIsSubmittedRequest) (*CheckUserIdentityIsSubmittedResponse, error)
	// 修改实名认证信息
	UpdateUserIdentity(context.Context, *UpdateUserIdentityRequest) (*RPCSuccess, error)
	// 提交审核实名认证信息
	SubmitUserIdentity(context.Context, *SubmitUserIdentityRequest) (*RPCSuccess, error)
	// 取消提交实名审核认证信息
	CancelUserIdentity(context.Context, *CancelUserIdentityRequest) (*RPCSuccess, error)
	// 重置用户实名认证信息
	ResetUserIdentity(context.Context, *ResetUserIdentityRequest) (*RPCSuccess, error)
	// 拒绝用户实名认证信息
	RejectUserIdentity(context.Context, *RejectUserIdentityRequest) (*RPCSuccess, error)
	// 通过用户实名认证信息
	VerifyUserIdentity(context.Context, *VerifyUserIdentityRequest) (*RPCSuccess, error)
}

// UnimplementedUserIdentityServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserIdentityServiceServer struct {
}

func (UnimplementedUserIdentityServiceServer) CreateUserIdentity(context.Context, *CreateUserIdentityRequest) (*CreateUserIdentityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) FindEnabledUserIdentity(context.Context, *FindEnabledUserIdentityRequest) (*FindEnabledUserIdentityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) FindEnabledUserIdentityWithOrgType(context.Context, *FindEnabledUserIdentityWithOrgTypeRequest) (*FindEnabledUserIdentityWithOrgTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUserIdentityWithOrgType not implemented")
}
func (UnimplementedUserIdentityServiceServer) CheckUserIdentityIsSubmitted(context.Context, *CheckUserIdentityIsSubmittedRequest) (*CheckUserIdentityIsSubmittedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserIdentityIsSubmitted not implemented")
}
func (UnimplementedUserIdentityServiceServer) UpdateUserIdentity(context.Context, *UpdateUserIdentityRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) SubmitUserIdentity(context.Context, *SubmitUserIdentityRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) CancelUserIdentity(context.Context, *CancelUserIdentityRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) ResetUserIdentity(context.Context, *ResetUserIdentityRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) RejectUserIdentity(context.Context, *RejectUserIdentityRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectUserIdentity not implemented")
}
func (UnimplementedUserIdentityServiceServer) VerifyUserIdentity(context.Context, *VerifyUserIdentityRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyUserIdentity not implemented")
}

// UnsafeUserIdentityServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserIdentityServiceServer will
// result in compilation errors.
type UnsafeUserIdentityServiceServer interface {
	mustEmbedUnimplementedUserIdentityServiceServer()
}

func RegisterUserIdentityServiceServer(s grpc.ServiceRegistrar, srv UserIdentityServiceServer) {
	s.RegisterService(&UserIdentityService_ServiceDesc, srv)
}

func _UserIdentityService_CreateUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).CreateUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_CreateUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).CreateUserIdentity(ctx, req.(*CreateUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_FindEnabledUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).FindEnabledUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_FindEnabledUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).FindEnabledUserIdentity(ctx, req.(*FindEnabledUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_FindEnabledUserIdentityWithOrgType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserIdentityWithOrgTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).FindEnabledUserIdentityWithOrgType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_FindEnabledUserIdentityWithOrgType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).FindEnabledUserIdentityWithOrgType(ctx, req.(*FindEnabledUserIdentityWithOrgTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_CheckUserIdentityIsSubmitted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIdentityIsSubmittedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).CheckUserIdentityIsSubmitted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_CheckUserIdentityIsSubmitted_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).CheckUserIdentityIsSubmitted(ctx, req.(*CheckUserIdentityIsSubmittedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_UpdateUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).UpdateUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_UpdateUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).UpdateUserIdentity(ctx, req.(*UpdateUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_SubmitUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).SubmitUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_SubmitUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).SubmitUserIdentity(ctx, req.(*SubmitUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_CancelUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).CancelUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_CancelUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).CancelUserIdentity(ctx, req.(*CancelUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_ResetUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).ResetUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_ResetUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).ResetUserIdentity(ctx, req.(*ResetUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_RejectUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).RejectUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_RejectUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).RejectUserIdentity(ctx, req.(*RejectUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserIdentityService_VerifyUserIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyUserIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserIdentityServiceServer).VerifyUserIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserIdentityService_VerifyUserIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserIdentityServiceServer).VerifyUserIdentity(ctx, req.(*VerifyUserIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserIdentityService_ServiceDesc is the grpc.ServiceDesc for UserIdentityService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserIdentityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserIdentityService",
	HandlerType: (*UserIdentityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createUserIdentity",
			Handler:    _UserIdentityService_CreateUserIdentity_Handler,
		},
		{
			MethodName: "findEnabledUserIdentity",
			Handler:    _UserIdentityService_FindEnabledUserIdentity_Handler,
		},
		{
			MethodName: "findEnabledUserIdentityWithOrgType",
			Handler:    _UserIdentityService_FindEnabledUserIdentityWithOrgType_Handler,
		},
		{
			MethodName: "checkUserIdentityIsSubmitted",
			Handler:    _UserIdentityService_CheckUserIdentityIsSubmitted_Handler,
		},
		{
			MethodName: "updateUserIdentity",
			Handler:    _UserIdentityService_UpdateUserIdentity_Handler,
		},
		{
			MethodName: "submitUserIdentity",
			Handler:    _UserIdentityService_SubmitUserIdentity_Handler,
		},
		{
			MethodName: "cancelUserIdentity",
			Handler:    _UserIdentityService_CancelUserIdentity_Handler,
		},
		{
			MethodName: "resetUserIdentity",
			Handler:    _UserIdentityService_ResetUserIdentity_Handler,
		},
		{
			MethodName: "rejectUserIdentity",
			Handler:    _UserIdentityService_RejectUserIdentity_Handler,
		},
		{
			MethodName: "verifyUserIdentity",
			Handler:    _UserIdentityService_VerifyUserIdentity_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_identity.proto",
}
