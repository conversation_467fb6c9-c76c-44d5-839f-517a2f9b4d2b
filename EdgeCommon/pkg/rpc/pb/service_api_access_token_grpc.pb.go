// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_api_access_token.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	APIAccessTokenService_GetAPIAccessToken_FullMethodName = "/pb.APIAccessTokenService/GetAPIAccessToken"
)

// APIAccessTokenServiceClient is the client API for APIAccessTokenService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type APIAccessTokenServiceClient interface {
	// 获取AccessToken
	GetAPIAccessToken(ctx context.Context, in *GetAPIAccessTokenRequest, opts ...grpc.CallOption) (*GetAPIAccessTokenResponse, error)
}

type aPIAccessTokenServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAPIAccessTokenServiceClient(cc grpc.ClientConnInterface) APIAccessTokenServiceClient {
	return &aPIAccessTokenServiceClient{cc}
}

func (c *aPIAccessTokenServiceClient) GetAPIAccessToken(ctx context.Context, in *GetAPIAccessTokenRequest, opts ...grpc.CallOption) (*GetAPIAccessTokenResponse, error) {
	out := new(GetAPIAccessTokenResponse)
	err := c.cc.Invoke(ctx, APIAccessTokenService_GetAPIAccessToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// APIAccessTokenServiceServer is the server API for APIAccessTokenService service.
// All implementations should embed UnimplementedAPIAccessTokenServiceServer
// for forward compatibility
type APIAccessTokenServiceServer interface {
	// 获取AccessToken
	GetAPIAccessToken(context.Context, *GetAPIAccessTokenRequest) (*GetAPIAccessTokenResponse, error)
}

// UnimplementedAPIAccessTokenServiceServer should be embedded to have forward compatible implementations.
type UnimplementedAPIAccessTokenServiceServer struct {
}

func (UnimplementedAPIAccessTokenServiceServer) GetAPIAccessToken(context.Context, *GetAPIAccessTokenRequest) (*GetAPIAccessTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAPIAccessToken not implemented")
}

// UnsafeAPIAccessTokenServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to APIAccessTokenServiceServer will
// result in compilation errors.
type UnsafeAPIAccessTokenServiceServer interface {
	mustEmbedUnimplementedAPIAccessTokenServiceServer()
}

func RegisterAPIAccessTokenServiceServer(s grpc.ServiceRegistrar, srv APIAccessTokenServiceServer) {
	s.RegisterService(&APIAccessTokenService_ServiceDesc, srv)
}

func _APIAccessTokenService_GetAPIAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAPIAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APIAccessTokenServiceServer).GetAPIAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APIAccessTokenService_GetAPIAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APIAccessTokenServiceServer).GetAPIAccessToken(ctx, req.(*GetAPIAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// APIAccessTokenService_ServiceDesc is the grpc.ServiceDesc for APIAccessTokenService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var APIAccessTokenService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.APIAccessTokenService",
	HandlerType: (*APIAccessTokenServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAPIAccessToken",
			Handler:    _APIAccessTokenService_GetAPIAccessToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_api_access_token.proto",
}
