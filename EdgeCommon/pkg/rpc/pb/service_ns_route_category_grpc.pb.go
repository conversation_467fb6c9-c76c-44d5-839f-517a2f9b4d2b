// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_route_category.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSRouteCategoryService_CreateNSRouteCategory_FullMethodName       = "/pb.NSRouteCategoryService/createNSRouteCategory"
	NSRouteCategoryService_UpdateNSRouteCategory_FullMethodName       = "/pb.NSRouteCategoryService/updateNSRouteCategory"
	NSRouteCategoryService_DeleteNSRouteCategory_FullMethodName       = "/pb.NSRouteCategoryService/deleteNSRouteCategory"
	NSRouteCategoryService_FindAllNSRouteCategories_FullMethodName    = "/pb.NSRouteCategoryService/findAllNSRouteCategories"
	NSRouteCategoryService_UpdateNSRouteCategoryOrders_FullMethodName = "/pb.NSRouteCategoryService/updateNSRouteCategoryOrders"
	NSRouteCategoryService_FindNSRouteCategory_FullMethodName         = "/pb.NSRouteCategoryService/findNSRouteCategory"
)

// NSRouteCategoryServiceClient is the client API for NSRouteCategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSRouteCategoryServiceClient interface {
	// 创建线路分类
	CreateNSRouteCategory(ctx context.Context, in *CreateNSRouteCategoryRequest, opts ...grpc.CallOption) (*CreateNSRouteCategoryResponse, error)
	// 修改线路分类
	UpdateNSRouteCategory(ctx context.Context, in *UpdateNSRouteCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除线路分类
	DeleteNSRouteCategory(ctx context.Context, in *DeleteNSRouteCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 列出所有线路分类
	FindAllNSRouteCategories(ctx context.Context, in *FindAllNSRouteCategoriesRequest, opts ...grpc.CallOption) (*FindAllNSRouteCategoriesResponse, error)
	// 对线路分类进行排序
	UpdateNSRouteCategoryOrders(ctx context.Context, in *UpdateNSRouteCategoryOrders, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个线路分类
	FindNSRouteCategory(ctx context.Context, in *FindNSRouteCategoryRequest, opts ...grpc.CallOption) (*FindNSRouteCategoryResponse, error)
}

type nSRouteCategoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSRouteCategoryServiceClient(cc grpc.ClientConnInterface) NSRouteCategoryServiceClient {
	return &nSRouteCategoryServiceClient{cc}
}

func (c *nSRouteCategoryServiceClient) CreateNSRouteCategory(ctx context.Context, in *CreateNSRouteCategoryRequest, opts ...grpc.CallOption) (*CreateNSRouteCategoryResponse, error) {
	out := new(CreateNSRouteCategoryResponse)
	err := c.cc.Invoke(ctx, NSRouteCategoryService_CreateNSRouteCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteCategoryServiceClient) UpdateNSRouteCategory(ctx context.Context, in *UpdateNSRouteCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRouteCategoryService_UpdateNSRouteCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteCategoryServiceClient) DeleteNSRouteCategory(ctx context.Context, in *DeleteNSRouteCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRouteCategoryService_DeleteNSRouteCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteCategoryServiceClient) FindAllNSRouteCategories(ctx context.Context, in *FindAllNSRouteCategoriesRequest, opts ...grpc.CallOption) (*FindAllNSRouteCategoriesResponse, error) {
	out := new(FindAllNSRouteCategoriesResponse)
	err := c.cc.Invoke(ctx, NSRouteCategoryService_FindAllNSRouteCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteCategoryServiceClient) UpdateNSRouteCategoryOrders(ctx context.Context, in *UpdateNSRouteCategoryOrders, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSRouteCategoryService_UpdateNSRouteCategoryOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSRouteCategoryServiceClient) FindNSRouteCategory(ctx context.Context, in *FindNSRouteCategoryRequest, opts ...grpc.CallOption) (*FindNSRouteCategoryResponse, error) {
	out := new(FindNSRouteCategoryResponse)
	err := c.cc.Invoke(ctx, NSRouteCategoryService_FindNSRouteCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSRouteCategoryServiceServer is the server API for NSRouteCategoryService service.
// All implementations should embed UnimplementedNSRouteCategoryServiceServer
// for forward compatibility
type NSRouteCategoryServiceServer interface {
	// 创建线路分类
	CreateNSRouteCategory(context.Context, *CreateNSRouteCategoryRequest) (*CreateNSRouteCategoryResponse, error)
	// 修改线路分类
	UpdateNSRouteCategory(context.Context, *UpdateNSRouteCategoryRequest) (*RPCSuccess, error)
	// 删除线路分类
	DeleteNSRouteCategory(context.Context, *DeleteNSRouteCategoryRequest) (*RPCSuccess, error)
	// 列出所有线路分类
	FindAllNSRouteCategories(context.Context, *FindAllNSRouteCategoriesRequest) (*FindAllNSRouteCategoriesResponse, error)
	// 对线路分类进行排序
	UpdateNSRouteCategoryOrders(context.Context, *UpdateNSRouteCategoryOrders) (*RPCSuccess, error)
	// 查找单个线路分类
	FindNSRouteCategory(context.Context, *FindNSRouteCategoryRequest) (*FindNSRouteCategoryResponse, error)
}

// UnimplementedNSRouteCategoryServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSRouteCategoryServiceServer struct {
}

func (UnimplementedNSRouteCategoryServiceServer) CreateNSRouteCategory(context.Context, *CreateNSRouteCategoryRequest) (*CreateNSRouteCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSRouteCategory not implemented")
}
func (UnimplementedNSRouteCategoryServiceServer) UpdateNSRouteCategory(context.Context, *UpdateNSRouteCategoryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRouteCategory not implemented")
}
func (UnimplementedNSRouteCategoryServiceServer) DeleteNSRouteCategory(context.Context, *DeleteNSRouteCategoryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSRouteCategory not implemented")
}
func (UnimplementedNSRouteCategoryServiceServer) FindAllNSRouteCategories(context.Context, *FindAllNSRouteCategoriesRequest) (*FindAllNSRouteCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllNSRouteCategories not implemented")
}
func (UnimplementedNSRouteCategoryServiceServer) UpdateNSRouteCategoryOrders(context.Context, *UpdateNSRouteCategoryOrders) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSRouteCategoryOrders not implemented")
}
func (UnimplementedNSRouteCategoryServiceServer) FindNSRouteCategory(context.Context, *FindNSRouteCategoryRequest) (*FindNSRouteCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSRouteCategory not implemented")
}

// UnsafeNSRouteCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSRouteCategoryServiceServer will
// result in compilation errors.
type UnsafeNSRouteCategoryServiceServer interface {
	mustEmbedUnimplementedNSRouteCategoryServiceServer()
}

func RegisterNSRouteCategoryServiceServer(s grpc.ServiceRegistrar, srv NSRouteCategoryServiceServer) {
	s.RegisterService(&NSRouteCategoryService_ServiceDesc, srv)
}

func _NSRouteCategoryService_CreateNSRouteCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSRouteCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteCategoryServiceServer).CreateNSRouteCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteCategoryService_CreateNSRouteCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteCategoryServiceServer).CreateNSRouteCategory(ctx, req.(*CreateNSRouteCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteCategoryService_UpdateNSRouteCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRouteCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteCategoryServiceServer).UpdateNSRouteCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteCategoryService_UpdateNSRouteCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteCategoryServiceServer).UpdateNSRouteCategory(ctx, req.(*UpdateNSRouteCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteCategoryService_DeleteNSRouteCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSRouteCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteCategoryServiceServer).DeleteNSRouteCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteCategoryService_DeleteNSRouteCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteCategoryServiceServer).DeleteNSRouteCategory(ctx, req.(*DeleteNSRouteCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteCategoryService_FindAllNSRouteCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllNSRouteCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteCategoryServiceServer).FindAllNSRouteCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteCategoryService_FindAllNSRouteCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteCategoryServiceServer).FindAllNSRouteCategories(ctx, req.(*FindAllNSRouteCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteCategoryService_UpdateNSRouteCategoryOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSRouteCategoryOrders)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteCategoryServiceServer).UpdateNSRouteCategoryOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteCategoryService_UpdateNSRouteCategoryOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteCategoryServiceServer).UpdateNSRouteCategoryOrders(ctx, req.(*UpdateNSRouteCategoryOrders))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSRouteCategoryService_FindNSRouteCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSRouteCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSRouteCategoryServiceServer).FindNSRouteCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSRouteCategoryService_FindNSRouteCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSRouteCategoryServiceServer).FindNSRouteCategory(ctx, req.(*FindNSRouteCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSRouteCategoryService_ServiceDesc is the grpc.ServiceDesc for NSRouteCategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSRouteCategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSRouteCategoryService",
	HandlerType: (*NSRouteCategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSRouteCategory",
			Handler:    _NSRouteCategoryService_CreateNSRouteCategory_Handler,
		},
		{
			MethodName: "updateNSRouteCategory",
			Handler:    _NSRouteCategoryService_UpdateNSRouteCategory_Handler,
		},
		{
			MethodName: "deleteNSRouteCategory",
			Handler:    _NSRouteCategoryService_DeleteNSRouteCategory_Handler,
		},
		{
			MethodName: "findAllNSRouteCategories",
			Handler:    _NSRouteCategoryService_FindAllNSRouteCategories_Handler,
		},
		{
			MethodName: "updateNSRouteCategoryOrders",
			Handler:    _NSRouteCategoryService_UpdateNSRouteCategoryOrders_Handler,
		},
		{
			MethodName: "findNSRouteCategory",
			Handler:    _NSRouteCategoryService_FindNSRouteCategory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_route_category.proto",
}
