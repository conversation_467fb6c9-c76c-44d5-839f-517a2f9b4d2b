// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ssl_policy.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SSLPolicyService_CreateSSLPolicy_FullMethodName            = "/pb.SSLPolicyService/createSSLPolicy"
	SSLPolicyService_UpdateSSLPolicy_FullMethodName            = "/pb.SSLPolicyService/updateSSLPolicy"
	SSLPolicyService_FindEnabledSSLPolicyConfig_FullMethodName = "/pb.SSLPolicyService/findEnabledSSLPolicyConfig"
)

// SSLPolicyServiceClient is the client API for SSLPolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SSLPolicyServiceClient interface {
	// 创建策略
	CreateSSLPolicy(ctx context.Context, in *CreateSSLPolicyRequest, opts ...grpc.CallOption) (*CreateSSLPolicyResponse, error)
	// 修改策略
	UpdateSSLPolicy(ctx context.Context, in *UpdateSSLPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找策略
	FindEnabledSSLPolicyConfig(ctx context.Context, in *FindEnabledSSLPolicyConfigRequest, opts ...grpc.CallOption) (*FindEnabledSSLPolicyConfigResponse, error)
}

type sSLPolicyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSSLPolicyServiceClient(cc grpc.ClientConnInterface) SSLPolicyServiceClient {
	return &sSLPolicyServiceClient{cc}
}

func (c *sSLPolicyServiceClient) CreateSSLPolicy(ctx context.Context, in *CreateSSLPolicyRequest, opts ...grpc.CallOption) (*CreateSSLPolicyResponse, error) {
	out := new(CreateSSLPolicyResponse)
	err := c.cc.Invoke(ctx, SSLPolicyService_CreateSSLPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sSLPolicyServiceClient) UpdateSSLPolicy(ctx context.Context, in *UpdateSSLPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, SSLPolicyService_UpdateSSLPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sSLPolicyServiceClient) FindEnabledSSLPolicyConfig(ctx context.Context, in *FindEnabledSSLPolicyConfigRequest, opts ...grpc.CallOption) (*FindEnabledSSLPolicyConfigResponse, error) {
	out := new(FindEnabledSSLPolicyConfigResponse)
	err := c.cc.Invoke(ctx, SSLPolicyService_FindEnabledSSLPolicyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SSLPolicyServiceServer is the server API for SSLPolicyService service.
// All implementations should embed UnimplementedSSLPolicyServiceServer
// for forward compatibility
type SSLPolicyServiceServer interface {
	// 创建策略
	CreateSSLPolicy(context.Context, *CreateSSLPolicyRequest) (*CreateSSLPolicyResponse, error)
	// 修改策略
	UpdateSSLPolicy(context.Context, *UpdateSSLPolicyRequest) (*RPCSuccess, error)
	// 查找策略
	FindEnabledSSLPolicyConfig(context.Context, *FindEnabledSSLPolicyConfigRequest) (*FindEnabledSSLPolicyConfigResponse, error)
}

// UnimplementedSSLPolicyServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSSLPolicyServiceServer struct {
}

func (UnimplementedSSLPolicyServiceServer) CreateSSLPolicy(context.Context, *CreateSSLPolicyRequest) (*CreateSSLPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSSLPolicy not implemented")
}
func (UnimplementedSSLPolicyServiceServer) UpdateSSLPolicy(context.Context, *UpdateSSLPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSSLPolicy not implemented")
}
func (UnimplementedSSLPolicyServiceServer) FindEnabledSSLPolicyConfig(context.Context, *FindEnabledSSLPolicyConfigRequest) (*FindEnabledSSLPolicyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledSSLPolicyConfig not implemented")
}

// UnsafeSSLPolicyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SSLPolicyServiceServer will
// result in compilation errors.
type UnsafeSSLPolicyServiceServer interface {
	mustEmbedUnimplementedSSLPolicyServiceServer()
}

func RegisterSSLPolicyServiceServer(s grpc.ServiceRegistrar, srv SSLPolicyServiceServer) {
	s.RegisterService(&SSLPolicyService_ServiceDesc, srv)
}

func _SSLPolicyService_CreateSSLPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSSLPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SSLPolicyServiceServer).CreateSSLPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SSLPolicyService_CreateSSLPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SSLPolicyServiceServer).CreateSSLPolicy(ctx, req.(*CreateSSLPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SSLPolicyService_UpdateSSLPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSSLPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SSLPolicyServiceServer).UpdateSSLPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SSLPolicyService_UpdateSSLPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SSLPolicyServiceServer).UpdateSSLPolicy(ctx, req.(*UpdateSSLPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SSLPolicyService_FindEnabledSSLPolicyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledSSLPolicyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SSLPolicyServiceServer).FindEnabledSSLPolicyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SSLPolicyService_FindEnabledSSLPolicyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SSLPolicyServiceServer).FindEnabledSSLPolicyConfig(ctx, req.(*FindEnabledSSLPolicyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SSLPolicyService_ServiceDesc is the grpc.ServiceDesc for SSLPolicyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SSLPolicyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.SSLPolicyService",
	HandlerType: (*SSLPolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createSSLPolicy",
			Handler:    _SSLPolicyService_CreateSSLPolicy_Handler,
		},
		{
			MethodName: "updateSSLPolicy",
			Handler:    _SSLPolicyService_UpdateSSLPolicy_Handler,
		},
		{
			MethodName: "findEnabledSSLPolicyConfig",
			Handler:    _SSLPolicyService_FindEnabledSSLPolicyConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ssl_policy.proto",
}
