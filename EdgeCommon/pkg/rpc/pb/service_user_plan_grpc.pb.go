// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_plan.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserPlanService_BuyUserPlan_FullMethodName                      = "/pb.UserPlanService/buyUserPlan"
	UserPlanService_RenewUserPlan_FullMethodName                    = "/pb.UserPlanService/renewUserPlan"
	UserPlanService_FindEnabledUserPlan_FullMethodName              = "/pb.UserPlanService/findEnabledUserPlan"
	UserPlanService_UpdateUserPlan_FullMethodName                   = "/pb.UserPlanService/updateUserPlan"
	UserPlanService_DeleteUserPlan_FullMethodName                   = "/pb.UserPlanService/deleteUserPlan"
	UserPlanService_CountAllEnabledUserPlans_FullMethodName         = "/pb.UserPlanService/countAllEnabledUserPlans"
	UserPlanService_ListEnabledUserPlans_FullMethodName             = "/pb.UserPlanService/listEnabledUserPlans"
	UserPlanService_FindAllEnabledUserPlansForServer_FullMethodName = "/pb.UserPlanService/findAllEnabledUserPlansForServer"
)

// UserPlanServiceClient is the client API for UserPlanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserPlanServiceClient interface {
	// 购买套餐
	BuyUserPlan(ctx context.Context, in *BuyUserPlanRequest, opts ...grpc.CallOption) (*BuyUserPlanResponse, error)
	// 续费套餐
	RenewUserPlan(ctx context.Context, in *RenewUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个已购套餐信息
	FindEnabledUserPlan(ctx context.Context, in *FindEnabledUserPlanRequest, opts ...grpc.CallOption) (*FindEnabledUserPlanResponse, error)
	// 修改已购套餐
	UpdateUserPlan(ctx context.Context, in *UpdateUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除已购套餐
	DeleteUserPlan(ctx context.Context, in *DeleteUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算已购套餐数
	CountAllEnabledUserPlans(ctx context.Context, in *CountAllEnabledUserPlansRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页已购套餐
	ListEnabledUserPlans(ctx context.Context, in *ListEnabledUserPlansRequest, opts ...grpc.CallOption) (*ListEnabledUserPlansResponse, error)
	// 查找所有服务可用的套餐
	FindAllEnabledUserPlansForServer(ctx context.Context, in *FindAllEnabledUserPlansForServerRequest, opts ...grpc.CallOption) (*FindAllEnabledUserPlansForServerResponse, error)
}

type userPlanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserPlanServiceClient(cc grpc.ClientConnInterface) UserPlanServiceClient {
	return &userPlanServiceClient{cc}
}

func (c *userPlanServiceClient) BuyUserPlan(ctx context.Context, in *BuyUserPlanRequest, opts ...grpc.CallOption) (*BuyUserPlanResponse, error) {
	out := new(BuyUserPlanResponse)
	err := c.cc.Invoke(ctx, UserPlanService_BuyUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) RenewUserPlan(ctx context.Context, in *RenewUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserPlanService_RenewUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) FindEnabledUserPlan(ctx context.Context, in *FindEnabledUserPlanRequest, opts ...grpc.CallOption) (*FindEnabledUserPlanResponse, error) {
	out := new(FindEnabledUserPlanResponse)
	err := c.cc.Invoke(ctx, UserPlanService_FindEnabledUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) UpdateUserPlan(ctx context.Context, in *UpdateUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserPlanService_UpdateUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) DeleteUserPlan(ctx context.Context, in *DeleteUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserPlanService_DeleteUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) CountAllEnabledUserPlans(ctx context.Context, in *CountAllEnabledUserPlansRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserPlanService_CountAllEnabledUserPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) ListEnabledUserPlans(ctx context.Context, in *ListEnabledUserPlansRequest, opts ...grpc.CallOption) (*ListEnabledUserPlansResponse, error) {
	out := new(ListEnabledUserPlansResponse)
	err := c.cc.Invoke(ctx, UserPlanService_ListEnabledUserPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPlanServiceClient) FindAllEnabledUserPlansForServer(ctx context.Context, in *FindAllEnabledUserPlansForServerRequest, opts ...grpc.CallOption) (*FindAllEnabledUserPlansForServerResponse, error) {
	out := new(FindAllEnabledUserPlansForServerResponse)
	err := c.cc.Invoke(ctx, UserPlanService_FindAllEnabledUserPlansForServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserPlanServiceServer is the server API for UserPlanService service.
// All implementations should embed UnimplementedUserPlanServiceServer
// for forward compatibility
type UserPlanServiceServer interface {
	// 购买套餐
	BuyUserPlan(context.Context, *BuyUserPlanRequest) (*BuyUserPlanResponse, error)
	// 续费套餐
	RenewUserPlan(context.Context, *RenewUserPlanRequest) (*RPCSuccess, error)
	// 查找单个已购套餐信息
	FindEnabledUserPlan(context.Context, *FindEnabledUserPlanRequest) (*FindEnabledUserPlanResponse, error)
	// 修改已购套餐
	UpdateUserPlan(context.Context, *UpdateUserPlanRequest) (*RPCSuccess, error)
	// 删除已购套餐
	DeleteUserPlan(context.Context, *DeleteUserPlanRequest) (*RPCSuccess, error)
	// 计算已购套餐数
	CountAllEnabledUserPlans(context.Context, *CountAllEnabledUserPlansRequest) (*RPCCountResponse, error)
	// 列出单页已购套餐
	ListEnabledUserPlans(context.Context, *ListEnabledUserPlansRequest) (*ListEnabledUserPlansResponse, error)
	// 查找所有服务可用的套餐
	FindAllEnabledUserPlansForServer(context.Context, *FindAllEnabledUserPlansForServerRequest) (*FindAllEnabledUserPlansForServerResponse, error)
}

// UnimplementedUserPlanServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserPlanServiceServer struct {
}

func (UnimplementedUserPlanServiceServer) BuyUserPlan(context.Context, *BuyUserPlanRequest) (*BuyUserPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuyUserPlan not implemented")
}
func (UnimplementedUserPlanServiceServer) RenewUserPlan(context.Context, *RenewUserPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewUserPlan not implemented")
}
func (UnimplementedUserPlanServiceServer) FindEnabledUserPlan(context.Context, *FindEnabledUserPlanRequest) (*FindEnabledUserPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUserPlan not implemented")
}
func (UnimplementedUserPlanServiceServer) UpdateUserPlan(context.Context, *UpdateUserPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPlan not implemented")
}
func (UnimplementedUserPlanServiceServer) DeleteUserPlan(context.Context, *DeleteUserPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserPlan not implemented")
}
func (UnimplementedUserPlanServiceServer) CountAllEnabledUserPlans(context.Context, *CountAllEnabledUserPlansRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledUserPlans not implemented")
}
func (UnimplementedUserPlanServiceServer) ListEnabledUserPlans(context.Context, *ListEnabledUserPlansRequest) (*ListEnabledUserPlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledUserPlans not implemented")
}
func (UnimplementedUserPlanServiceServer) FindAllEnabledUserPlansForServer(context.Context, *FindAllEnabledUserPlansForServerRequest) (*FindAllEnabledUserPlansForServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledUserPlansForServer not implemented")
}

// UnsafeUserPlanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserPlanServiceServer will
// result in compilation errors.
type UnsafeUserPlanServiceServer interface {
	mustEmbedUnimplementedUserPlanServiceServer()
}

func RegisterUserPlanServiceServer(s grpc.ServiceRegistrar, srv UserPlanServiceServer) {
	s.RegisterService(&UserPlanService_ServiceDesc, srv)
}

func _UserPlanService_BuyUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).BuyUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_BuyUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).BuyUserPlan(ctx, req.(*BuyUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_RenewUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).RenewUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_RenewUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).RenewUserPlan(ctx, req.(*RenewUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_FindEnabledUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).FindEnabledUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_FindEnabledUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).FindEnabledUserPlan(ctx, req.(*FindEnabledUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_UpdateUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).UpdateUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_UpdateUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).UpdateUserPlan(ctx, req.(*UpdateUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_DeleteUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).DeleteUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_DeleteUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).DeleteUserPlan(ctx, req.(*DeleteUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_CountAllEnabledUserPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledUserPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).CountAllEnabledUserPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_CountAllEnabledUserPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).CountAllEnabledUserPlans(ctx, req.(*CountAllEnabledUserPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_ListEnabledUserPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledUserPlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).ListEnabledUserPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_ListEnabledUserPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).ListEnabledUserPlans(ctx, req.(*ListEnabledUserPlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPlanService_FindAllEnabledUserPlansForServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledUserPlansForServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPlanServiceServer).FindAllEnabledUserPlansForServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPlanService_FindAllEnabledUserPlansForServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPlanServiceServer).FindAllEnabledUserPlansForServer(ctx, req.(*FindAllEnabledUserPlansForServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserPlanService_ServiceDesc is the grpc.ServiceDesc for UserPlanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserPlanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserPlanService",
	HandlerType: (*UserPlanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "buyUserPlan",
			Handler:    _UserPlanService_BuyUserPlan_Handler,
		},
		{
			MethodName: "renewUserPlan",
			Handler:    _UserPlanService_RenewUserPlan_Handler,
		},
		{
			MethodName: "findEnabledUserPlan",
			Handler:    _UserPlanService_FindEnabledUserPlan_Handler,
		},
		{
			MethodName: "updateUserPlan",
			Handler:    _UserPlanService_UpdateUserPlan_Handler,
		},
		{
			MethodName: "deleteUserPlan",
			Handler:    _UserPlanService_DeleteUserPlan_Handler,
		},
		{
			MethodName: "countAllEnabledUserPlans",
			Handler:    _UserPlanService_CountAllEnabledUserPlans_Handler,
		},
		{
			MethodName: "listEnabledUserPlans",
			Handler:    _UserPlanService_ListEnabledUserPlans_Handler,
		},
		{
			MethodName: "findAllEnabledUserPlansForServer",
			Handler:    _UserPlanService_FindAllEnabledUserPlansForServer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_plan.proto",
}
