// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerService_CreateServer_FullMethodName                              = "/pb.ServerService/createServer"
	ServerService_CreateBasicHTTPServer_FullMethodName                     = "/pb.ServerService/createBasicHTTPServer"
	ServerService_CreateBasicTCPServer_FullMethodName                      = "/pb.ServerService/createBasicTCPServer"
	ServerService_AddServerOrigin_FullMethodName                           = "/pb.ServerService/addServerOrigin"
	ServerService_DeleteServerOrigin_FullMethodName                        = "/pb.ServerService/deleteServerOrigin"
	ServerService_UpdateServerBasic_FullMethodName                         = "/pb.ServerService/updateServerBasic"
	ServerService_UpdateServerGroupIds_FullMethodName                      = "/pb.ServerService/updateServerGroupIds"
	ServerService_UpdateServerIsOn_FullMethodName                          = "/pb.ServerService/updateServerIsOn"
	ServerService_UpdateServerHTTP_FullMethodName                          = "/pb.ServerService/updateServerHTTP"
	ServerService_UpdateServerHTTPS_FullMethodName                         = "/pb.ServerService/updateServerHTTPS"
	ServerService_UpdateServerTCP_FullMethodName                           = "/pb.ServerService/updateServerTCP"
	ServerService_UpdateServerTLS_FullMethodName                           = "/pb.ServerService/updateServerTLS"
	ServerService_UpdateServerUDP_FullMethodName                           = "/pb.ServerService/updateServerUDP"
	ServerService_UpdateServerWeb_FullMethodName                           = "/pb.ServerService/updateServerWeb"
	ServerService_UpdateServerReverseProxy_FullMethodName                  = "/pb.ServerService/updateServerReverseProxy"
	ServerService_FindServerNames_FullMethodName                           = "/pb.ServerService/findServerNames"
	ServerService_UpdateServerNames_FullMethodName                         = "/pb.ServerService/updateServerNames"
	ServerService_UpdateServerNamesAuditing_FullMethodName                 = "/pb.ServerService/updateServerNamesAuditing"
	ServerService_UpdateServerDNS_FullMethodName                           = "/pb.ServerService/updateServerDNS"
	ServerService_RegenerateServerDNSName_FullMethodName                   = "/pb.ServerService/regenerateServerDNSName"
	ServerService_UpdateServerDNSName_FullMethodName                       = "/pb.ServerService/updateServerDNSName"
	ServerService_FindServerIdWithDNSName_FullMethodName                   = "/pb.ServerService/findServerIdWithDNSName"
	ServerService_CountAllEnabledServersMatch_FullMethodName               = "/pb.ServerService/countAllEnabledServersMatch"
	ServerService_ListEnabledServersMatch_FullMethodName                   = "/pb.ServerService/listEnabledServersMatch"
	ServerService_DeleteServer_FullMethodName                              = "/pb.ServerService/deleteServer"
	ServerService_DeleteServers_FullMethodName                             = "/pb.ServerService/deleteServers"
	ServerService_FindEnabledServer_FullMethodName                         = "/pb.ServerService/findEnabledServer"
	ServerService_FindEnabledServerConfig_FullMethodName                   = "/pb.ServerService/findEnabledServerConfig"
	ServerService_FindEnabledServerType_FullMethodName                     = "/pb.ServerService/findEnabledServerType"
	ServerService_FindAndInitServerReverseProxyConfig_FullMethodName       = "/pb.ServerService/findAndInitServerReverseProxyConfig"
	ServerService_FindAndInitServerWebConfig_FullMethodName                = "/pb.ServerService/findAndInitServerWebConfig"
	ServerService_CountAllEnabledServersWithSSLCertId_FullMethodName       = "/pb.ServerService/countAllEnabledServersWithSSLCertId"
	ServerService_FindAllEnabledServersWithSSLCertId_FullMethodName        = "/pb.ServerService/findAllEnabledServersWithSSLCertId"
	ServerService_CountAllEnabledServersWithNodeClusterId_FullMethodName   = "/pb.ServerService/countAllEnabledServersWithNodeClusterId"
	ServerService_CountAllEnabledServersWithServerGroupId_FullMethodName   = "/pb.ServerService/countAllEnabledServersWithServerGroupId"
	ServerService_NotifyServersChange_FullMethodName                       = "/pb.ServerService/notifyServersChange"
	ServerService_FindAllEnabledServersDNSWithNodeClusterId_FullMethodName = "/pb.ServerService/findAllEnabledServersDNSWithNodeClusterId"
	ServerService_FindEnabledServerDNS_FullMethodName                      = "/pb.ServerService/findEnabledServerDNS"
	ServerService_CheckUserServer_FullMethodName                           = "/pb.ServerService/checkUserServer"
	ServerService_FindAllEnabledServerNamesWithUserId_FullMethodName       = "/pb.ServerService/findAllEnabledServerNamesWithUserId"
	ServerService_CountAllServerNamesWithUserId_FullMethodName             = "/pb.ServerService/countAllServerNamesWithUserId"
	ServerService_CountServerNames_FullMethodName                          = "/pb.ServerService/countServerNames"
	ServerService_FindAllUserServers_FullMethodName                        = "/pb.ServerService/findAllUserServers"
	ServerService_CountAllUserServers_FullMethodName                       = "/pb.ServerService/countAllUserServers"
	ServerService_ComposeAllUserServersConfig_FullMethodName               = "/pb.ServerService/composeAllUserServersConfig"
	ServerService_FindEnabledUserServerBasic_FullMethodName                = "/pb.ServerService/findEnabledUserServerBasic"
	ServerService_UpdateEnabledUserServerBasic_FullMethodName              = "/pb.ServerService/updateEnabledUserServerBasic"
	ServerService_UploadServerHTTPRequestStat_FullMethodName               = "/pb.ServerService/uploadServerHTTPRequestStat"
	ServerService_CheckServerNameDuplicationInNodeCluster_FullMethodName   = "/pb.ServerService/checkServerNameDuplicationInNodeCluster"
	ServerService_CheckServerNameInServer_FullMethodName                   = "/pb.ServerService/checkServerNameInServer"
	ServerService_FindLatestServers_FullMethodName                         = "/pb.ServerService/findLatestServers"
	ServerService_FindNearbyServers_FullMethodName                         = "/pb.ServerService/findNearbyServers"
	ServerService_PurgeServerCache_FullMethodName                          = "/pb.ServerService/purgeServerCache"
	ServerService_FindEnabledServerTrafficLimit_FullMethodName             = "/pb.ServerService/findEnabledServerTrafficLimit"
	ServerService_UpdateServerTrafficLimit_FullMethodName                  = "/pb.ServerService/updateServerTrafficLimit"
	ServerService_UpdateServerUserPlan_FullMethodName                      = "/pb.ServerService/updateServerUserPlan"
	ServerService_FindServerUserPlan_FullMethodName                        = "/pb.ServerService/findServerUserPlan"
	ServerService_ComposeServerConfig_FullMethodName                       = "/pb.ServerService/composeServerConfig"
	ServerService_UpdateServerUAM_FullMethodName                           = "/pb.ServerService/updateServerUAM"
	ServerService_FindEnabledServerUAM_FullMethodName                      = "/pb.ServerService/findEnabledServerUAM"
	ServerService_UpdateServerUser_FullMethodName                          = "/pb.ServerService/updateServerUser"
	ServerService_UpdateServerName_FullMethodName                          = "/pb.ServerService/updateServerName"
	ServerService_CopyServerConfig_FullMethodName                          = "/pb.ServerService/copyServerConfig"
	ServerService_FindServerAuditingPrompt_FullMethodName                  = "/pb.ServerService/findServerAuditingPrompt"
)

// ServerServiceClient is the client API for ServerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerServiceClient interface {
	// 创建网站
	CreateServer(ctx context.Context, in *CreateServerRequest, opts ...grpc.CallOption) (*CreateServerResponse, error)
	// 快速创建基本的HTTP网站
	CreateBasicHTTPServer(ctx context.Context, in *CreateBasicHTTPServerRequest, opts ...grpc.CallOption) (*CreateBasicHTTPServerResponse, error)
	// 快速创建基本的TCP网站
	CreateBasicTCPServer(ctx context.Context, in *CreateBasicTCPServerRequest, opts ...grpc.CallOption) (*CreateBasicTCPServerResponse, error)
	// 为网站添加源站
	AddServerOrigin(ctx context.Context, in *AddServerOriginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 从网站中删除某个源站
	DeleteServerOrigin(ctx context.Context, in *DeleteServerOriginRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站基本信息
	UpdateServerBasic(ctx context.Context, in *UpdateServerBasicRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站所在分组
	UpdateServerGroupIds(ctx context.Context, in *UpdateServerGroupIdsRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站是否启用
	UpdateServerIsOn(ctx context.Context, in *UpdateServerIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的HTTP设置
	UpdateServerHTTP(ctx context.Context, in *UpdateServerHTTPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的HTTPS设置
	UpdateServerHTTPS(ctx context.Context, in *UpdateServerHTTPSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的TCP设置
	UpdateServerTCP(ctx context.Context, in *UpdateServerTCPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的TLS设置
	UpdateServerTLS(ctx context.Context, in *UpdateServerTLSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的UDP设置
	UpdateServerUDP(ctx context.Context, in *UpdateServerUDPRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的Web设置
	UpdateServerWeb(ctx context.Context, in *UpdateServerWebRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的反向代理设置
	UpdateServerReverseProxy(ctx context.Context, in *UpdateServerReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找网站的域名设置
	FindServerNames(ctx context.Context, in *FindServerNamesRequest, opts ...grpc.CallOption) (*FindServerNamesResponse, error)
	// 修改网站的域名设置
	UpdateServerNames(ctx context.Context, in *UpdateServerNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 审核网站的域名设置
	UpdateServerNamesAuditing(ctx context.Context, in *UpdateServerNamesAuditingRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的DNS相关设置
	UpdateServerDNS(ctx context.Context, in *UpdateServerDNSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 重新生成CNAME
	RegenerateServerDNSName(ctx context.Context, in *RegenerateServerDNSNameRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站的CNAME
	UpdateServerDNSName(ctx context.Context, in *UpdateServerDNSNameRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 使用CNAME查找网站
	FindServerIdWithDNSName(ctx context.Context, in *FindServerIdWithDNSNameRequest, opts ...grpc.CallOption) (*FindServerIdWithDNSNameResponse, error)
	// 计算匹配的网站数量
	CountAllEnabledServersMatch(ctx context.Context, in *CountAllEnabledServersMatchRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页网站
	ListEnabledServersMatch(ctx context.Context, in *ListEnabledServersMatchRequest, opts ...grpc.CallOption) (*ListEnabledServersMatchResponse, error)
	// 删除某网站
	DeleteServer(ctx context.Context, in *DeleteServerRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除一组网站
	DeleteServers(ctx context.Context, in *DeleteServersRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个网站
	FindEnabledServer(ctx context.Context, in *FindEnabledServerRequest, opts ...grpc.CallOption) (*FindEnabledServerResponse, error)
	// 查找网站配置
	FindEnabledServerConfig(ctx context.Context, in *FindEnabledServerConfigRequest, opts ...grpc.CallOption) (*FindEnabledServerConfigResponse, error)
	// 查找网站的网站类型
	FindEnabledServerType(ctx context.Context, in *FindEnabledServerTypeRequest, opts ...grpc.CallOption) (*FindEnabledServerTypeResponse, error)
	// 查找反向代理设置
	FindAndInitServerReverseProxyConfig(ctx context.Context, in *FindAndInitServerReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerReverseProxyConfigResponse, error)
	// 初始化Web设置
	FindAndInitServerWebConfig(ctx context.Context, in *FindAndInitServerWebConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerWebConfigResponse, error)
	// 计算使用某个SSL证书的网站数量
	CountAllEnabledServersWithSSLCertId(ctx context.Context, in *CountAllEnabledServersWithSSLCertIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找使用某个SSL证书的所有网站
	FindAllEnabledServersWithSSLCertId(ctx context.Context, in *FindAllEnabledServersWithSSLCertIdRequest, opts ...grpc.CallOption) (*FindAllEnabledServersWithSSLCertIdResponse, error)
	// 计算运行在某个集群上的所有网站数量
	CountAllEnabledServersWithNodeClusterId(ctx context.Context, in *CountAllEnabledServersWithNodeClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算使用某个分组的网站数量
	CountAllEnabledServersWithServerGroupId(ctx context.Context, in *CountAllEnabledServersWithServerGroupIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 通知更新
	NotifyServersChange(ctx context.Context, in *NotifyServersChangeRequest, opts ...grpc.CallOption) (*NotifyServersChangeResponse, error)
	// 取得某个集群下的所有网站相关的DNS
	FindAllEnabledServersDNSWithNodeClusterId(ctx context.Context, in *FindAllEnabledServersDNSWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllEnabledServersDNSWithNodeClusterIdResponse, error)
	// 查找单个网站的DNS信息
	FindEnabledServerDNS(ctx context.Context, in *FindEnabledServerDNSRequest, opts ...grpc.CallOption) (*FindEnabledServerDNSResponse, error)
	// 检查网站是否属于某个用户
	CheckUserServer(ctx context.Context, in *CheckUserServerRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找一个用户下的所有域名列表
	FindAllEnabledServerNamesWithUserId(ctx context.Context, in *FindAllEnabledServerNamesWithUserIdRequest, opts ...grpc.CallOption) (*FindAllEnabledServerNamesWithUserIdResponse, error)
	// 计算一个用户下的所有域名数量
	CountAllServerNamesWithUserId(ctx context.Context, in *CountAllServerNamesWithUserIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算某个网站下的域名数量
	CountServerNames(ctx context.Context, in *CountServerNamesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找一个用户下的所有网站
	FindAllUserServers(ctx context.Context, in *FindAllUserServersRequest, opts ...grpc.CallOption) (*FindAllUserServersResponse, error)
	// 计算一个用户下的所有网站数量
	CountAllUserServers(ctx context.Context, in *CountAllUserServersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找某个用户下的网站配置
	ComposeAllUserServersConfig(ctx context.Context, in *ComposeAllUserServersConfigRequest, opts ...grpc.CallOption) (*ComposeAllUserServersConfigResponse, error)
	// 查找用户网站基本信息
	FindEnabledUserServerBasic(ctx context.Context, in *FindEnabledUserServerBasicRequest, opts ...grpc.CallOption) (*FindEnabledUserServerBasicResponse, error)
	// 修改用户网站基本信息
	UpdateEnabledUserServerBasic(ctx context.Context, in *UpdateEnabledUserServerBasicRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 上传HTTP请求待统计数据
	UploadServerHTTPRequestStat(ctx context.Context, in *UploadServerHTTPRequestStatRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 检查域名是否在集群中已经存在
	CheckServerNameDuplicationInNodeCluster(ctx context.Context, in *CheckServerNameDuplicationInNodeClusterRequest, opts ...grpc.CallOption) (*CheckServerNameDuplicationInNodeClusterResponse, error)
	// 检查域名是否在网站中已经绑定
	CheckServerNameInServer(ctx context.Context, in *CheckServerNameInServerRequest, opts ...grpc.CallOption) (*CheckServerNameInServerResponse, error)
	// 查找最近访问的网站
	FindLatestServers(ctx context.Context, in *FindLatestServersRequest, opts ...grpc.CallOption) (*FindLatestServersResponse, error)
	// 查找某个网站附近的网站
	FindNearbyServers(ctx context.Context, in *FindNearbyServersRequest, opts ...grpc.CallOption) (*FindNearbyServersResponse, error)
	// 清除缓存
	PurgeServerCache(ctx context.Context, in *PurgeServerCacheRequest, opts ...grpc.CallOption) (*PurgeServerCacheResponse, error)
	// 查找流量限制
	FindEnabledServerTrafficLimit(ctx context.Context, in *FindEnabledServerTrafficLimitRequest, opts ...grpc.CallOption) (*FindEnabledServerTrafficLimitResponse, error)
	// 设置流量限制
	UpdateServerTrafficLimit(ctx context.Context, in *UpdateServerTrafficLimitRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站套餐
	UpdateServerUserPlan(ctx context.Context, in *UpdateServerUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取网站套餐信息
	FindServerUserPlan(ctx context.Context, in *FindServerUserPlanRequest, opts ...grpc.CallOption) (*FindServerUserPlanResponse, error)
	// 获取网站配置
	ComposeServerConfig(ctx context.Context, in *ComposeServerConfigRequest, opts ...grpc.CallOption) (*ComposeServerConfigResponse, error)
	// 修改网站UAM设置
	UpdateServerUAM(ctx context.Context, in *UpdateServerUAMRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找网站UAM设置
	FindEnabledServerUAM(ctx context.Context, in *FindEnabledServerUAMRequest, opts ...grpc.CallOption) (*FindEnabledServerUAMResponse, error)
	// 修改网站所属用户
	UpdateServerUser(ctx context.Context, in *UpdateServerUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改网站名称
	UpdateServerName(ctx context.Context, in *UpdateServerNameRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 在网站之间复制配置
	CopyServerConfig(ctx context.Context, in *CopyServerConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取域名审核时的提示文字
	FindServerAuditingPrompt(ctx context.Context, in *FindServerAuditingPromptRequest, opts ...grpc.CallOption) (*FindServerAuditingPromptResponse, error)
}

type serverServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerServiceClient(cc grpc.ClientConnInterface) ServerServiceClient {
	return &serverServiceClient{cc}
}

func (c *serverServiceClient) CreateServer(ctx context.Context, in *CreateServerRequest, opts ...grpc.CallOption) (*CreateServerResponse, error) {
	out := new(CreateServerResponse)
	err := c.cc.Invoke(ctx, ServerService_CreateServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CreateBasicHTTPServer(ctx context.Context, in *CreateBasicHTTPServerRequest, opts ...grpc.CallOption) (*CreateBasicHTTPServerResponse, error) {
	out := new(CreateBasicHTTPServerResponse)
	err := c.cc.Invoke(ctx, ServerService_CreateBasicHTTPServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CreateBasicTCPServer(ctx context.Context, in *CreateBasicTCPServerRequest, opts ...grpc.CallOption) (*CreateBasicTCPServerResponse, error) {
	out := new(CreateBasicTCPServerResponse)
	err := c.cc.Invoke(ctx, ServerService_CreateBasicTCPServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) AddServerOrigin(ctx context.Context, in *AddServerOriginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_AddServerOrigin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) DeleteServerOrigin(ctx context.Context, in *DeleteServerOriginRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_DeleteServerOrigin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerBasic(ctx context.Context, in *UpdateServerBasicRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerBasic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerGroupIds(ctx context.Context, in *UpdateServerGroupIdsRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerGroupIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerIsOn(ctx context.Context, in *UpdateServerIsOnRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerIsOn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerHTTP(ctx context.Context, in *UpdateServerHTTPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerHTTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerHTTPS(ctx context.Context, in *UpdateServerHTTPSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerHTTPS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerTCP(ctx context.Context, in *UpdateServerTCPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerTCP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerTLS(ctx context.Context, in *UpdateServerTLSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerTLS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerUDP(ctx context.Context, in *UpdateServerUDPRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerUDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerWeb(ctx context.Context, in *UpdateServerWebRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerWeb_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerReverseProxy(ctx context.Context, in *UpdateServerReverseProxyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerReverseProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindServerNames(ctx context.Context, in *FindServerNamesRequest, opts ...grpc.CallOption) (*FindServerNamesResponse, error) {
	out := new(FindServerNamesResponse)
	err := c.cc.Invoke(ctx, ServerService_FindServerNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerNames(ctx context.Context, in *UpdateServerNamesRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerNamesAuditing(ctx context.Context, in *UpdateServerNamesAuditingRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerNamesAuditing_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerDNS(ctx context.Context, in *UpdateServerDNSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerDNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) RegenerateServerDNSName(ctx context.Context, in *RegenerateServerDNSNameRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_RegenerateServerDNSName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerDNSName(ctx context.Context, in *UpdateServerDNSNameRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerDNSName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindServerIdWithDNSName(ctx context.Context, in *FindServerIdWithDNSNameRequest, opts ...grpc.CallOption) (*FindServerIdWithDNSNameResponse, error) {
	out := new(FindServerIdWithDNSNameResponse)
	err := c.cc.Invoke(ctx, ServerService_FindServerIdWithDNSName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountAllEnabledServersMatch(ctx context.Context, in *CountAllEnabledServersMatchRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountAllEnabledServersMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) ListEnabledServersMatch(ctx context.Context, in *ListEnabledServersMatchRequest, opts ...grpc.CallOption) (*ListEnabledServersMatchResponse, error) {
	out := new(ListEnabledServersMatchResponse)
	err := c.cc.Invoke(ctx, ServerService_ListEnabledServersMatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) DeleteServer(ctx context.Context, in *DeleteServerRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_DeleteServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) DeleteServers(ctx context.Context, in *DeleteServersRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_DeleteServers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledServer(ctx context.Context, in *FindEnabledServerRequest, opts ...grpc.CallOption) (*FindEnabledServerResponse, error) {
	out := new(FindEnabledServerResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledServerConfig(ctx context.Context, in *FindEnabledServerConfigRequest, opts ...grpc.CallOption) (*FindEnabledServerConfigResponse, error) {
	out := new(FindEnabledServerConfigResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledServerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledServerType(ctx context.Context, in *FindEnabledServerTypeRequest, opts ...grpc.CallOption) (*FindEnabledServerTypeResponse, error) {
	out := new(FindEnabledServerTypeResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledServerType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindAndInitServerReverseProxyConfig(ctx context.Context, in *FindAndInitServerReverseProxyConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerReverseProxyConfigResponse, error) {
	out := new(FindAndInitServerReverseProxyConfigResponse)
	err := c.cc.Invoke(ctx, ServerService_FindAndInitServerReverseProxyConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindAndInitServerWebConfig(ctx context.Context, in *FindAndInitServerWebConfigRequest, opts ...grpc.CallOption) (*FindAndInitServerWebConfigResponse, error) {
	out := new(FindAndInitServerWebConfigResponse)
	err := c.cc.Invoke(ctx, ServerService_FindAndInitServerWebConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountAllEnabledServersWithSSLCertId(ctx context.Context, in *CountAllEnabledServersWithSSLCertIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountAllEnabledServersWithSSLCertId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindAllEnabledServersWithSSLCertId(ctx context.Context, in *FindAllEnabledServersWithSSLCertIdRequest, opts ...grpc.CallOption) (*FindAllEnabledServersWithSSLCertIdResponse, error) {
	out := new(FindAllEnabledServersWithSSLCertIdResponse)
	err := c.cc.Invoke(ctx, ServerService_FindAllEnabledServersWithSSLCertId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountAllEnabledServersWithNodeClusterId(ctx context.Context, in *CountAllEnabledServersWithNodeClusterIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountAllEnabledServersWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountAllEnabledServersWithServerGroupId(ctx context.Context, in *CountAllEnabledServersWithServerGroupIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountAllEnabledServersWithServerGroupId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) NotifyServersChange(ctx context.Context, in *NotifyServersChangeRequest, opts ...grpc.CallOption) (*NotifyServersChangeResponse, error) {
	out := new(NotifyServersChangeResponse)
	err := c.cc.Invoke(ctx, ServerService_NotifyServersChange_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindAllEnabledServersDNSWithNodeClusterId(ctx context.Context, in *FindAllEnabledServersDNSWithNodeClusterIdRequest, opts ...grpc.CallOption) (*FindAllEnabledServersDNSWithNodeClusterIdResponse, error) {
	out := new(FindAllEnabledServersDNSWithNodeClusterIdResponse)
	err := c.cc.Invoke(ctx, ServerService_FindAllEnabledServersDNSWithNodeClusterId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledServerDNS(ctx context.Context, in *FindEnabledServerDNSRequest, opts ...grpc.CallOption) (*FindEnabledServerDNSResponse, error) {
	out := new(FindEnabledServerDNSResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledServerDNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CheckUserServer(ctx context.Context, in *CheckUserServerRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_CheckUserServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindAllEnabledServerNamesWithUserId(ctx context.Context, in *FindAllEnabledServerNamesWithUserIdRequest, opts ...grpc.CallOption) (*FindAllEnabledServerNamesWithUserIdResponse, error) {
	out := new(FindAllEnabledServerNamesWithUserIdResponse)
	err := c.cc.Invoke(ctx, ServerService_FindAllEnabledServerNamesWithUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountAllServerNamesWithUserId(ctx context.Context, in *CountAllServerNamesWithUserIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountAllServerNamesWithUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountServerNames(ctx context.Context, in *CountServerNamesRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountServerNames_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindAllUserServers(ctx context.Context, in *FindAllUserServersRequest, opts ...grpc.CallOption) (*FindAllUserServersResponse, error) {
	out := new(FindAllUserServersResponse)
	err := c.cc.Invoke(ctx, ServerService_FindAllUserServers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CountAllUserServers(ctx context.Context, in *CountAllUserServersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, ServerService_CountAllUserServers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) ComposeAllUserServersConfig(ctx context.Context, in *ComposeAllUserServersConfigRequest, opts ...grpc.CallOption) (*ComposeAllUserServersConfigResponse, error) {
	out := new(ComposeAllUserServersConfigResponse)
	err := c.cc.Invoke(ctx, ServerService_ComposeAllUserServersConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledUserServerBasic(ctx context.Context, in *FindEnabledUserServerBasicRequest, opts ...grpc.CallOption) (*FindEnabledUserServerBasicResponse, error) {
	out := new(FindEnabledUserServerBasicResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledUserServerBasic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateEnabledUserServerBasic(ctx context.Context, in *UpdateEnabledUserServerBasicRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateEnabledUserServerBasic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UploadServerHTTPRequestStat(ctx context.Context, in *UploadServerHTTPRequestStatRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UploadServerHTTPRequestStat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CheckServerNameDuplicationInNodeCluster(ctx context.Context, in *CheckServerNameDuplicationInNodeClusterRequest, opts ...grpc.CallOption) (*CheckServerNameDuplicationInNodeClusterResponse, error) {
	out := new(CheckServerNameDuplicationInNodeClusterResponse)
	err := c.cc.Invoke(ctx, ServerService_CheckServerNameDuplicationInNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CheckServerNameInServer(ctx context.Context, in *CheckServerNameInServerRequest, opts ...grpc.CallOption) (*CheckServerNameInServerResponse, error) {
	out := new(CheckServerNameInServerResponse)
	err := c.cc.Invoke(ctx, ServerService_CheckServerNameInServer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindLatestServers(ctx context.Context, in *FindLatestServersRequest, opts ...grpc.CallOption) (*FindLatestServersResponse, error) {
	out := new(FindLatestServersResponse)
	err := c.cc.Invoke(ctx, ServerService_FindLatestServers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindNearbyServers(ctx context.Context, in *FindNearbyServersRequest, opts ...grpc.CallOption) (*FindNearbyServersResponse, error) {
	out := new(FindNearbyServersResponse)
	err := c.cc.Invoke(ctx, ServerService_FindNearbyServers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) PurgeServerCache(ctx context.Context, in *PurgeServerCacheRequest, opts ...grpc.CallOption) (*PurgeServerCacheResponse, error) {
	out := new(PurgeServerCacheResponse)
	err := c.cc.Invoke(ctx, ServerService_PurgeServerCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledServerTrafficLimit(ctx context.Context, in *FindEnabledServerTrafficLimitRequest, opts ...grpc.CallOption) (*FindEnabledServerTrafficLimitResponse, error) {
	out := new(FindEnabledServerTrafficLimitResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledServerTrafficLimit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerTrafficLimit(ctx context.Context, in *UpdateServerTrafficLimitRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerTrafficLimit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerUserPlan(ctx context.Context, in *UpdateServerUserPlanRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindServerUserPlan(ctx context.Context, in *FindServerUserPlanRequest, opts ...grpc.CallOption) (*FindServerUserPlanResponse, error) {
	out := new(FindServerUserPlanResponse)
	err := c.cc.Invoke(ctx, ServerService_FindServerUserPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) ComposeServerConfig(ctx context.Context, in *ComposeServerConfigRequest, opts ...grpc.CallOption) (*ComposeServerConfigResponse, error) {
	out := new(ComposeServerConfigResponse)
	err := c.cc.Invoke(ctx, ServerService_ComposeServerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerUAM(ctx context.Context, in *UpdateServerUAMRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerUAM_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindEnabledServerUAM(ctx context.Context, in *FindEnabledServerUAMRequest, opts ...grpc.CallOption) (*FindEnabledServerUAMResponse, error) {
	out := new(FindEnabledServerUAMResponse)
	err := c.cc.Invoke(ctx, ServerService_FindEnabledServerUAM_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerUser(ctx context.Context, in *UpdateServerUserRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) UpdateServerName(ctx context.Context, in *UpdateServerNameRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_UpdateServerName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) CopyServerConfig(ctx context.Context, in *CopyServerConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ServerService_CopyServerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) FindServerAuditingPrompt(ctx context.Context, in *FindServerAuditingPromptRequest, opts ...grpc.CallOption) (*FindServerAuditingPromptResponse, error) {
	out := new(FindServerAuditingPromptResponse)
	err := c.cc.Invoke(ctx, ServerService_FindServerAuditingPrompt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerServiceServer is the server API for ServerService service.
// All implementations should embed UnimplementedServerServiceServer
// for forward compatibility
type ServerServiceServer interface {
	// 创建网站
	CreateServer(context.Context, *CreateServerRequest) (*CreateServerResponse, error)
	// 快速创建基本的HTTP网站
	CreateBasicHTTPServer(context.Context, *CreateBasicHTTPServerRequest) (*CreateBasicHTTPServerResponse, error)
	// 快速创建基本的TCP网站
	CreateBasicTCPServer(context.Context, *CreateBasicTCPServerRequest) (*CreateBasicTCPServerResponse, error)
	// 为网站添加源站
	AddServerOrigin(context.Context, *AddServerOriginRequest) (*RPCSuccess, error)
	// 从网站中删除某个源站
	DeleteServerOrigin(context.Context, *DeleteServerOriginRequest) (*RPCSuccess, error)
	// 修改网站基本信息
	UpdateServerBasic(context.Context, *UpdateServerBasicRequest) (*RPCSuccess, error)
	// 修改网站所在分组
	UpdateServerGroupIds(context.Context, *UpdateServerGroupIdsRequest) (*RPCSuccess, error)
	// 修改网站是否启用
	UpdateServerIsOn(context.Context, *UpdateServerIsOnRequest) (*RPCSuccess, error)
	// 修改网站的HTTP设置
	UpdateServerHTTP(context.Context, *UpdateServerHTTPRequest) (*RPCSuccess, error)
	// 修改网站的HTTPS设置
	UpdateServerHTTPS(context.Context, *UpdateServerHTTPSRequest) (*RPCSuccess, error)
	// 修改网站的TCP设置
	UpdateServerTCP(context.Context, *UpdateServerTCPRequest) (*RPCSuccess, error)
	// 修改网站的TLS设置
	UpdateServerTLS(context.Context, *UpdateServerTLSRequest) (*RPCSuccess, error)
	// 修改网站的UDP设置
	UpdateServerUDP(context.Context, *UpdateServerUDPRequest) (*RPCSuccess, error)
	// 修改网站的Web设置
	UpdateServerWeb(context.Context, *UpdateServerWebRequest) (*RPCSuccess, error)
	// 修改网站的反向代理设置
	UpdateServerReverseProxy(context.Context, *UpdateServerReverseProxyRequest) (*RPCSuccess, error)
	// 查找网站的域名设置
	FindServerNames(context.Context, *FindServerNamesRequest) (*FindServerNamesResponse, error)
	// 修改网站的域名设置
	UpdateServerNames(context.Context, *UpdateServerNamesRequest) (*RPCSuccess, error)
	// 审核网站的域名设置
	UpdateServerNamesAuditing(context.Context, *UpdateServerNamesAuditingRequest) (*RPCSuccess, error)
	// 修改网站的DNS相关设置
	UpdateServerDNS(context.Context, *UpdateServerDNSRequest) (*RPCSuccess, error)
	// 重新生成CNAME
	RegenerateServerDNSName(context.Context, *RegenerateServerDNSNameRequest) (*RPCSuccess, error)
	// 修改网站的CNAME
	UpdateServerDNSName(context.Context, *UpdateServerDNSNameRequest) (*RPCSuccess, error)
	// 使用CNAME查找网站
	FindServerIdWithDNSName(context.Context, *FindServerIdWithDNSNameRequest) (*FindServerIdWithDNSNameResponse, error)
	// 计算匹配的网站数量
	CountAllEnabledServersMatch(context.Context, *CountAllEnabledServersMatchRequest) (*RPCCountResponse, error)
	// 列出单页网站
	ListEnabledServersMatch(context.Context, *ListEnabledServersMatchRequest) (*ListEnabledServersMatchResponse, error)
	// 删除某网站
	DeleteServer(context.Context, *DeleteServerRequest) (*RPCSuccess, error)
	// 删除一组网站
	DeleteServers(context.Context, *DeleteServersRequest) (*RPCSuccess, error)
	// 查找单个网站
	FindEnabledServer(context.Context, *FindEnabledServerRequest) (*FindEnabledServerResponse, error)
	// 查找网站配置
	FindEnabledServerConfig(context.Context, *FindEnabledServerConfigRequest) (*FindEnabledServerConfigResponse, error)
	// 查找网站的网站类型
	FindEnabledServerType(context.Context, *FindEnabledServerTypeRequest) (*FindEnabledServerTypeResponse, error)
	// 查找反向代理设置
	FindAndInitServerReverseProxyConfig(context.Context, *FindAndInitServerReverseProxyConfigRequest) (*FindAndInitServerReverseProxyConfigResponse, error)
	// 初始化Web设置
	FindAndInitServerWebConfig(context.Context, *FindAndInitServerWebConfigRequest) (*FindAndInitServerWebConfigResponse, error)
	// 计算使用某个SSL证书的网站数量
	CountAllEnabledServersWithSSLCertId(context.Context, *CountAllEnabledServersWithSSLCertIdRequest) (*RPCCountResponse, error)
	// 查找使用某个SSL证书的所有网站
	FindAllEnabledServersWithSSLCertId(context.Context, *FindAllEnabledServersWithSSLCertIdRequest) (*FindAllEnabledServersWithSSLCertIdResponse, error)
	// 计算运行在某个集群上的所有网站数量
	CountAllEnabledServersWithNodeClusterId(context.Context, *CountAllEnabledServersWithNodeClusterIdRequest) (*RPCCountResponse, error)
	// 计算使用某个分组的网站数量
	CountAllEnabledServersWithServerGroupId(context.Context, *CountAllEnabledServersWithServerGroupIdRequest) (*RPCCountResponse, error)
	// 通知更新
	NotifyServersChange(context.Context, *NotifyServersChangeRequest) (*NotifyServersChangeResponse, error)
	// 取得某个集群下的所有网站相关的DNS
	FindAllEnabledServersDNSWithNodeClusterId(context.Context, *FindAllEnabledServersDNSWithNodeClusterIdRequest) (*FindAllEnabledServersDNSWithNodeClusterIdResponse, error)
	// 查找单个网站的DNS信息
	FindEnabledServerDNS(context.Context, *FindEnabledServerDNSRequest) (*FindEnabledServerDNSResponse, error)
	// 检查网站是否属于某个用户
	CheckUserServer(context.Context, *CheckUserServerRequest) (*RPCSuccess, error)
	// 查找一个用户下的所有域名列表
	FindAllEnabledServerNamesWithUserId(context.Context, *FindAllEnabledServerNamesWithUserIdRequest) (*FindAllEnabledServerNamesWithUserIdResponse, error)
	// 计算一个用户下的所有域名数量
	CountAllServerNamesWithUserId(context.Context, *CountAllServerNamesWithUserIdRequest) (*RPCCountResponse, error)
	// 计算某个网站下的域名数量
	CountServerNames(context.Context, *CountServerNamesRequest) (*RPCCountResponse, error)
	// 查找一个用户下的所有网站
	FindAllUserServers(context.Context, *FindAllUserServersRequest) (*FindAllUserServersResponse, error)
	// 计算一个用户下的所有网站数量
	CountAllUserServers(context.Context, *CountAllUserServersRequest) (*RPCCountResponse, error)
	// 查找某个用户下的网站配置
	ComposeAllUserServersConfig(context.Context, *ComposeAllUserServersConfigRequest) (*ComposeAllUserServersConfigResponse, error)
	// 查找用户网站基本信息
	FindEnabledUserServerBasic(context.Context, *FindEnabledUserServerBasicRequest) (*FindEnabledUserServerBasicResponse, error)
	// 修改用户网站基本信息
	UpdateEnabledUserServerBasic(context.Context, *UpdateEnabledUserServerBasicRequest) (*RPCSuccess, error)
	// 上传HTTP请求待统计数据
	UploadServerHTTPRequestStat(context.Context, *UploadServerHTTPRequestStatRequest) (*RPCSuccess, error)
	// 检查域名是否在集群中已经存在
	CheckServerNameDuplicationInNodeCluster(context.Context, *CheckServerNameDuplicationInNodeClusterRequest) (*CheckServerNameDuplicationInNodeClusterResponse, error)
	// 检查域名是否在网站中已经绑定
	CheckServerNameInServer(context.Context, *CheckServerNameInServerRequest) (*CheckServerNameInServerResponse, error)
	// 查找最近访问的网站
	FindLatestServers(context.Context, *FindLatestServersRequest) (*FindLatestServersResponse, error)
	// 查找某个网站附近的网站
	FindNearbyServers(context.Context, *FindNearbyServersRequest) (*FindNearbyServersResponse, error)
	// 清除缓存
	PurgeServerCache(context.Context, *PurgeServerCacheRequest) (*PurgeServerCacheResponse, error)
	// 查找流量限制
	FindEnabledServerTrafficLimit(context.Context, *FindEnabledServerTrafficLimitRequest) (*FindEnabledServerTrafficLimitResponse, error)
	// 设置流量限制
	UpdateServerTrafficLimit(context.Context, *UpdateServerTrafficLimitRequest) (*RPCSuccess, error)
	// 修改网站套餐
	UpdateServerUserPlan(context.Context, *UpdateServerUserPlanRequest) (*RPCSuccess, error)
	// 获取网站套餐信息
	FindServerUserPlan(context.Context, *FindServerUserPlanRequest) (*FindServerUserPlanResponse, error)
	// 获取网站配置
	ComposeServerConfig(context.Context, *ComposeServerConfigRequest) (*ComposeServerConfigResponse, error)
	// 修改网站UAM设置
	UpdateServerUAM(context.Context, *UpdateServerUAMRequest) (*RPCSuccess, error)
	// 查找网站UAM设置
	FindEnabledServerUAM(context.Context, *FindEnabledServerUAMRequest) (*FindEnabledServerUAMResponse, error)
	// 修改网站所属用户
	UpdateServerUser(context.Context, *UpdateServerUserRequest) (*RPCSuccess, error)
	// 修改网站名称
	UpdateServerName(context.Context, *UpdateServerNameRequest) (*RPCSuccess, error)
	// 在网站之间复制配置
	CopyServerConfig(context.Context, *CopyServerConfigRequest) (*RPCSuccess, error)
	// 获取域名审核时的提示文字
	FindServerAuditingPrompt(context.Context, *FindServerAuditingPromptRequest) (*FindServerAuditingPromptResponse, error)
}

// UnimplementedServerServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerServiceServer struct {
}

func (UnimplementedServerServiceServer) CreateServer(context.Context, *CreateServerRequest) (*CreateServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServer not implemented")
}
func (UnimplementedServerServiceServer) CreateBasicHTTPServer(context.Context, *CreateBasicHTTPServerRequest) (*CreateBasicHTTPServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBasicHTTPServer not implemented")
}
func (UnimplementedServerServiceServer) CreateBasicTCPServer(context.Context, *CreateBasicTCPServerRequest) (*CreateBasicTCPServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBasicTCPServer not implemented")
}
func (UnimplementedServerServiceServer) AddServerOrigin(context.Context, *AddServerOriginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddServerOrigin not implemented")
}
func (UnimplementedServerServiceServer) DeleteServerOrigin(context.Context, *DeleteServerOriginRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServerOrigin not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerBasic(context.Context, *UpdateServerBasicRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerBasic not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerGroupIds(context.Context, *UpdateServerGroupIdsRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerGroupIds not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerIsOn(context.Context, *UpdateServerIsOnRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerIsOn not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerHTTP(context.Context, *UpdateServerHTTPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerHTTP not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerHTTPS(context.Context, *UpdateServerHTTPSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerHTTPS not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerTCP(context.Context, *UpdateServerTCPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerTCP not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerTLS(context.Context, *UpdateServerTLSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerTLS not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerUDP(context.Context, *UpdateServerUDPRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerUDP not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerWeb(context.Context, *UpdateServerWebRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerWeb not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerReverseProxy(context.Context, *UpdateServerReverseProxyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerReverseProxy not implemented")
}
func (UnimplementedServerServiceServer) FindServerNames(context.Context, *FindServerNamesRequest) (*FindServerNamesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerNames not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerNames(context.Context, *UpdateServerNamesRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerNames not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerNamesAuditing(context.Context, *UpdateServerNamesAuditingRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerNamesAuditing not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerDNS(context.Context, *UpdateServerDNSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerDNS not implemented")
}
func (UnimplementedServerServiceServer) RegenerateServerDNSName(context.Context, *RegenerateServerDNSNameRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegenerateServerDNSName not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerDNSName(context.Context, *UpdateServerDNSNameRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerDNSName not implemented")
}
func (UnimplementedServerServiceServer) FindServerIdWithDNSName(context.Context, *FindServerIdWithDNSNameRequest) (*FindServerIdWithDNSNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerIdWithDNSName not implemented")
}
func (UnimplementedServerServiceServer) CountAllEnabledServersMatch(context.Context, *CountAllEnabledServersMatchRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledServersMatch not implemented")
}
func (UnimplementedServerServiceServer) ListEnabledServersMatch(context.Context, *ListEnabledServersMatchRequest) (*ListEnabledServersMatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledServersMatch not implemented")
}
func (UnimplementedServerServiceServer) DeleteServer(context.Context, *DeleteServerRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServer not implemented")
}
func (UnimplementedServerServiceServer) DeleteServers(context.Context, *DeleteServersRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServers not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledServer(context.Context, *FindEnabledServerRequest) (*FindEnabledServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServer not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledServerConfig(context.Context, *FindEnabledServerConfigRequest) (*FindEnabledServerConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerConfig not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledServerType(context.Context, *FindEnabledServerTypeRequest) (*FindEnabledServerTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerType not implemented")
}
func (UnimplementedServerServiceServer) FindAndInitServerReverseProxyConfig(context.Context, *FindAndInitServerReverseProxyConfigRequest) (*FindAndInitServerReverseProxyConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAndInitServerReverseProxyConfig not implemented")
}
func (UnimplementedServerServiceServer) FindAndInitServerWebConfig(context.Context, *FindAndInitServerWebConfigRequest) (*FindAndInitServerWebConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAndInitServerWebConfig not implemented")
}
func (UnimplementedServerServiceServer) CountAllEnabledServersWithSSLCertId(context.Context, *CountAllEnabledServersWithSSLCertIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledServersWithSSLCertId not implemented")
}
func (UnimplementedServerServiceServer) FindAllEnabledServersWithSSLCertId(context.Context, *FindAllEnabledServersWithSSLCertIdRequest) (*FindAllEnabledServersWithSSLCertIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledServersWithSSLCertId not implemented")
}
func (UnimplementedServerServiceServer) CountAllEnabledServersWithNodeClusterId(context.Context, *CountAllEnabledServersWithNodeClusterIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledServersWithNodeClusterId not implemented")
}
func (UnimplementedServerServiceServer) CountAllEnabledServersWithServerGroupId(context.Context, *CountAllEnabledServersWithServerGroupIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledServersWithServerGroupId not implemented")
}
func (UnimplementedServerServiceServer) NotifyServersChange(context.Context, *NotifyServersChangeRequest) (*NotifyServersChangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyServersChange not implemented")
}
func (UnimplementedServerServiceServer) FindAllEnabledServersDNSWithNodeClusterId(context.Context, *FindAllEnabledServersDNSWithNodeClusterIdRequest) (*FindAllEnabledServersDNSWithNodeClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledServersDNSWithNodeClusterId not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledServerDNS(context.Context, *FindEnabledServerDNSRequest) (*FindEnabledServerDNSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerDNS not implemented")
}
func (UnimplementedServerServiceServer) CheckUserServer(context.Context, *CheckUserServerRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserServer not implemented")
}
func (UnimplementedServerServiceServer) FindAllEnabledServerNamesWithUserId(context.Context, *FindAllEnabledServerNamesWithUserIdRequest) (*FindAllEnabledServerNamesWithUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledServerNamesWithUserId not implemented")
}
func (UnimplementedServerServiceServer) CountAllServerNamesWithUserId(context.Context, *CountAllServerNamesWithUserIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllServerNamesWithUserId not implemented")
}
func (UnimplementedServerServiceServer) CountServerNames(context.Context, *CountServerNamesRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountServerNames not implemented")
}
func (UnimplementedServerServiceServer) FindAllUserServers(context.Context, *FindAllUserServersRequest) (*FindAllUserServersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllUserServers not implemented")
}
func (UnimplementedServerServiceServer) CountAllUserServers(context.Context, *CountAllUserServersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllUserServers not implemented")
}
func (UnimplementedServerServiceServer) ComposeAllUserServersConfig(context.Context, *ComposeAllUserServersConfigRequest) (*ComposeAllUserServersConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeAllUserServersConfig not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledUserServerBasic(context.Context, *FindEnabledUserServerBasicRequest) (*FindEnabledUserServerBasicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUserServerBasic not implemented")
}
func (UnimplementedServerServiceServer) UpdateEnabledUserServerBasic(context.Context, *UpdateEnabledUserServerBasicRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEnabledUserServerBasic not implemented")
}
func (UnimplementedServerServiceServer) UploadServerHTTPRequestStat(context.Context, *UploadServerHTTPRequestStatRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadServerHTTPRequestStat not implemented")
}
func (UnimplementedServerServiceServer) CheckServerNameDuplicationInNodeCluster(context.Context, *CheckServerNameDuplicationInNodeClusterRequest) (*CheckServerNameDuplicationInNodeClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckServerNameDuplicationInNodeCluster not implemented")
}
func (UnimplementedServerServiceServer) CheckServerNameInServer(context.Context, *CheckServerNameInServerRequest) (*CheckServerNameInServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckServerNameInServer not implemented")
}
func (UnimplementedServerServiceServer) FindLatestServers(context.Context, *FindLatestServersRequest) (*FindLatestServersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLatestServers not implemented")
}
func (UnimplementedServerServiceServer) FindNearbyServers(context.Context, *FindNearbyServersRequest) (*FindNearbyServersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNearbyServers not implemented")
}
func (UnimplementedServerServiceServer) PurgeServerCache(context.Context, *PurgeServerCacheRequest) (*PurgeServerCacheResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurgeServerCache not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledServerTrafficLimit(context.Context, *FindEnabledServerTrafficLimitRequest) (*FindEnabledServerTrafficLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerTrafficLimit not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerTrafficLimit(context.Context, *UpdateServerTrafficLimitRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerTrafficLimit not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerUserPlan(context.Context, *UpdateServerUserPlanRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerUserPlan not implemented")
}
func (UnimplementedServerServiceServer) FindServerUserPlan(context.Context, *FindServerUserPlanRequest) (*FindServerUserPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerUserPlan not implemented")
}
func (UnimplementedServerServiceServer) ComposeServerConfig(context.Context, *ComposeServerConfigRequest) (*ComposeServerConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ComposeServerConfig not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerUAM(context.Context, *UpdateServerUAMRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerUAM not implemented")
}
func (UnimplementedServerServiceServer) FindEnabledServerUAM(context.Context, *FindEnabledServerUAMRequest) (*FindEnabledServerUAMResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledServerUAM not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerUser(context.Context, *UpdateServerUserRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerUser not implemented")
}
func (UnimplementedServerServiceServer) UpdateServerName(context.Context, *UpdateServerNameRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServerName not implemented")
}
func (UnimplementedServerServiceServer) CopyServerConfig(context.Context, *CopyServerConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyServerConfig not implemented")
}
func (UnimplementedServerServiceServer) FindServerAuditingPrompt(context.Context, *FindServerAuditingPromptRequest) (*FindServerAuditingPromptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindServerAuditingPrompt not implemented")
}

// UnsafeServerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerServiceServer will
// result in compilation errors.
type UnsafeServerServiceServer interface {
	mustEmbedUnimplementedServerServiceServer()
}

func RegisterServerServiceServer(s grpc.ServiceRegistrar, srv ServerServiceServer) {
	s.RegisterService(&ServerService_ServiceDesc, srv)
}

func _ServerService_CreateServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CreateServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CreateServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CreateServer(ctx, req.(*CreateServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CreateBasicHTTPServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBasicHTTPServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CreateBasicHTTPServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CreateBasicHTTPServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CreateBasicHTTPServer(ctx, req.(*CreateBasicHTTPServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CreateBasicTCPServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBasicTCPServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CreateBasicTCPServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CreateBasicTCPServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CreateBasicTCPServer(ctx, req.(*CreateBasicTCPServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_AddServerOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddServerOriginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).AddServerOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_AddServerOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).AddServerOrigin(ctx, req.(*AddServerOriginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_DeleteServerOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServerOriginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).DeleteServerOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_DeleteServerOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).DeleteServerOrigin(ctx, req.(*DeleteServerOriginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerBasic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerBasicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerBasic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerBasic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerBasic(ctx, req.(*UpdateServerBasicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerGroupIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerGroupIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerGroupIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerGroupIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerGroupIds(ctx, req.(*UpdateServerGroupIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerIsOn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerIsOnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerIsOn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerIsOn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerIsOn(ctx, req.(*UpdateServerIsOnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerHTTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerHTTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerHTTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerHTTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerHTTP(ctx, req.(*UpdateServerHTTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerHTTPS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerHTTPSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerHTTPS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerHTTPS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerHTTPS(ctx, req.(*UpdateServerHTTPSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerTCP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerTCPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerTCP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerTCP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerTCP(ctx, req.(*UpdateServerTCPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerTLS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerTLSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerTLS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerTLS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerTLS(ctx, req.(*UpdateServerTLSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerUDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerUDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerUDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerUDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerUDP(ctx, req.(*UpdateServerUDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerWebRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerWeb_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerWeb(ctx, req.(*UpdateServerWebRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerReverseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerReverseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerReverseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerReverseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerReverseProxy(ctx, req.(*UpdateServerReverseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindServerNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindServerNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindServerNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindServerNames(ctx, req.(*FindServerNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerNames(ctx, req.(*UpdateServerNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerNamesAuditing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerNamesAuditingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerNamesAuditing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerNamesAuditing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerNamesAuditing(ctx, req.(*UpdateServerNamesAuditingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerDNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerDNSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerDNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerDNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerDNS(ctx, req.(*UpdateServerDNSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_RegenerateServerDNSName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegenerateServerDNSNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).RegenerateServerDNSName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_RegenerateServerDNSName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).RegenerateServerDNSName(ctx, req.(*RegenerateServerDNSNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerDNSName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerDNSNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerDNSName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerDNSName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerDNSName(ctx, req.(*UpdateServerDNSNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindServerIdWithDNSName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerIdWithDNSNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindServerIdWithDNSName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindServerIdWithDNSName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindServerIdWithDNSName(ctx, req.(*FindServerIdWithDNSNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountAllEnabledServersMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledServersMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountAllEnabledServersMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountAllEnabledServersMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountAllEnabledServersMatch(ctx, req.(*CountAllEnabledServersMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_ListEnabledServersMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledServersMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).ListEnabledServersMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_ListEnabledServersMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).ListEnabledServersMatch(ctx, req.(*ListEnabledServersMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_DeleteServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).DeleteServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_DeleteServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).DeleteServer(ctx, req.(*DeleteServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_DeleteServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).DeleteServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_DeleteServers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).DeleteServers(ctx, req.(*DeleteServersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledServer(ctx, req.(*FindEnabledServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledServerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledServerConfig(ctx, req.(*FindEnabledServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledServerType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledServerType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledServerType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledServerType(ctx, req.(*FindEnabledServerTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindAndInitServerReverseProxyConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAndInitServerReverseProxyConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindAndInitServerReverseProxyConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindAndInitServerReverseProxyConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindAndInitServerReverseProxyConfig(ctx, req.(*FindAndInitServerReverseProxyConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindAndInitServerWebConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAndInitServerWebConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindAndInitServerWebConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindAndInitServerWebConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindAndInitServerWebConfig(ctx, req.(*FindAndInitServerWebConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountAllEnabledServersWithSSLCertId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledServersWithSSLCertIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountAllEnabledServersWithSSLCertId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountAllEnabledServersWithSSLCertId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountAllEnabledServersWithSSLCertId(ctx, req.(*CountAllEnabledServersWithSSLCertIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindAllEnabledServersWithSSLCertId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledServersWithSSLCertIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindAllEnabledServersWithSSLCertId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindAllEnabledServersWithSSLCertId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindAllEnabledServersWithSSLCertId(ctx, req.(*FindAllEnabledServersWithSSLCertIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountAllEnabledServersWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledServersWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountAllEnabledServersWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountAllEnabledServersWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountAllEnabledServersWithNodeClusterId(ctx, req.(*CountAllEnabledServersWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountAllEnabledServersWithServerGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledServersWithServerGroupIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountAllEnabledServersWithServerGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountAllEnabledServersWithServerGroupId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountAllEnabledServersWithServerGroupId(ctx, req.(*CountAllEnabledServersWithServerGroupIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_NotifyServersChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyServersChangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).NotifyServersChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_NotifyServersChange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).NotifyServersChange(ctx, req.(*NotifyServersChangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindAllEnabledServersDNSWithNodeClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledServersDNSWithNodeClusterIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindAllEnabledServersDNSWithNodeClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindAllEnabledServersDNSWithNodeClusterId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindAllEnabledServersDNSWithNodeClusterId(ctx, req.(*FindAllEnabledServersDNSWithNodeClusterIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledServerDNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerDNSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledServerDNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledServerDNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledServerDNS(ctx, req.(*FindEnabledServerDNSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CheckUserServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CheckUserServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CheckUserServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CheckUserServer(ctx, req.(*CheckUserServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindAllEnabledServerNamesWithUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledServerNamesWithUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindAllEnabledServerNamesWithUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindAllEnabledServerNamesWithUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindAllEnabledServerNamesWithUserId(ctx, req.(*FindAllEnabledServerNamesWithUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountAllServerNamesWithUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllServerNamesWithUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountAllServerNamesWithUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountAllServerNamesWithUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountAllServerNamesWithUserId(ctx, req.(*CountAllServerNamesWithUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountServerNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountServerNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountServerNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountServerNames_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountServerNames(ctx, req.(*CountServerNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindAllUserServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllUserServersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindAllUserServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindAllUserServers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindAllUserServers(ctx, req.(*FindAllUserServersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CountAllUserServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllUserServersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CountAllUserServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CountAllUserServers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CountAllUserServers(ctx, req.(*CountAllUserServersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_ComposeAllUserServersConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeAllUserServersConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).ComposeAllUserServersConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_ComposeAllUserServersConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).ComposeAllUserServersConfig(ctx, req.(*ComposeAllUserServersConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledUserServerBasic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserServerBasicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledUserServerBasic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledUserServerBasic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledUserServerBasic(ctx, req.(*FindEnabledUserServerBasicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateEnabledUserServerBasic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEnabledUserServerBasicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateEnabledUserServerBasic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateEnabledUserServerBasic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateEnabledUserServerBasic(ctx, req.(*UpdateEnabledUserServerBasicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UploadServerHTTPRequestStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadServerHTTPRequestStatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UploadServerHTTPRequestStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UploadServerHTTPRequestStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UploadServerHTTPRequestStat(ctx, req.(*UploadServerHTTPRequestStatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CheckServerNameDuplicationInNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckServerNameDuplicationInNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CheckServerNameDuplicationInNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CheckServerNameDuplicationInNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CheckServerNameDuplicationInNodeCluster(ctx, req.(*CheckServerNameDuplicationInNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CheckServerNameInServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckServerNameInServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CheckServerNameInServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CheckServerNameInServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CheckServerNameInServer(ctx, req.(*CheckServerNameInServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindLatestServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLatestServersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindLatestServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindLatestServers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindLatestServers(ctx, req.(*FindLatestServersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindNearbyServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNearbyServersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindNearbyServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindNearbyServers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindNearbyServers(ctx, req.(*FindNearbyServersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_PurgeServerCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurgeServerCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).PurgeServerCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_PurgeServerCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).PurgeServerCache(ctx, req.(*PurgeServerCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledServerTrafficLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerTrafficLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledServerTrafficLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledServerTrafficLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledServerTrafficLimit(ctx, req.(*FindEnabledServerTrafficLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerTrafficLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerTrafficLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerTrafficLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerTrafficLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerTrafficLimit(ctx, req.(*UpdateServerTrafficLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerUserPlan(ctx, req.(*UpdateServerUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindServerUserPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerUserPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindServerUserPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindServerUserPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindServerUserPlan(ctx, req.(*FindServerUserPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_ComposeServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComposeServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).ComposeServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_ComposeServerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).ComposeServerConfig(ctx, req.(*ComposeServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerUAM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerUAMRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerUAM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerUAM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerUAM(ctx, req.(*UpdateServerUAMRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindEnabledServerUAM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledServerUAMRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindEnabledServerUAM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindEnabledServerUAM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindEnabledServerUAM(ctx, req.(*FindEnabledServerUAMRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerUser(ctx, req.(*UpdateServerUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_UpdateServerName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServerNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).UpdateServerName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_UpdateServerName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).UpdateServerName(ctx, req.(*UpdateServerNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_CopyServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).CopyServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_CopyServerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).CopyServerConfig(ctx, req.(*CopyServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_FindServerAuditingPrompt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindServerAuditingPromptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).FindServerAuditingPrompt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_FindServerAuditingPrompt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).FindServerAuditingPrompt(ctx, req.(*FindServerAuditingPromptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerService_ServiceDesc is the grpc.ServiceDesc for ServerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerService",
	HandlerType: (*ServerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createServer",
			Handler:    _ServerService_CreateServer_Handler,
		},
		{
			MethodName: "createBasicHTTPServer",
			Handler:    _ServerService_CreateBasicHTTPServer_Handler,
		},
		{
			MethodName: "createBasicTCPServer",
			Handler:    _ServerService_CreateBasicTCPServer_Handler,
		},
		{
			MethodName: "addServerOrigin",
			Handler:    _ServerService_AddServerOrigin_Handler,
		},
		{
			MethodName: "deleteServerOrigin",
			Handler:    _ServerService_DeleteServerOrigin_Handler,
		},
		{
			MethodName: "updateServerBasic",
			Handler:    _ServerService_UpdateServerBasic_Handler,
		},
		{
			MethodName: "updateServerGroupIds",
			Handler:    _ServerService_UpdateServerGroupIds_Handler,
		},
		{
			MethodName: "updateServerIsOn",
			Handler:    _ServerService_UpdateServerIsOn_Handler,
		},
		{
			MethodName: "updateServerHTTP",
			Handler:    _ServerService_UpdateServerHTTP_Handler,
		},
		{
			MethodName: "updateServerHTTPS",
			Handler:    _ServerService_UpdateServerHTTPS_Handler,
		},
		{
			MethodName: "updateServerTCP",
			Handler:    _ServerService_UpdateServerTCP_Handler,
		},
		{
			MethodName: "updateServerTLS",
			Handler:    _ServerService_UpdateServerTLS_Handler,
		},
		{
			MethodName: "updateServerUDP",
			Handler:    _ServerService_UpdateServerUDP_Handler,
		},
		{
			MethodName: "updateServerWeb",
			Handler:    _ServerService_UpdateServerWeb_Handler,
		},
		{
			MethodName: "updateServerReverseProxy",
			Handler:    _ServerService_UpdateServerReverseProxy_Handler,
		},
		{
			MethodName: "findServerNames",
			Handler:    _ServerService_FindServerNames_Handler,
		},
		{
			MethodName: "updateServerNames",
			Handler:    _ServerService_UpdateServerNames_Handler,
		},
		{
			MethodName: "updateServerNamesAuditing",
			Handler:    _ServerService_UpdateServerNamesAuditing_Handler,
		},
		{
			MethodName: "updateServerDNS",
			Handler:    _ServerService_UpdateServerDNS_Handler,
		},
		{
			MethodName: "regenerateServerDNSName",
			Handler:    _ServerService_RegenerateServerDNSName_Handler,
		},
		{
			MethodName: "updateServerDNSName",
			Handler:    _ServerService_UpdateServerDNSName_Handler,
		},
		{
			MethodName: "findServerIdWithDNSName",
			Handler:    _ServerService_FindServerIdWithDNSName_Handler,
		},
		{
			MethodName: "countAllEnabledServersMatch",
			Handler:    _ServerService_CountAllEnabledServersMatch_Handler,
		},
		{
			MethodName: "listEnabledServersMatch",
			Handler:    _ServerService_ListEnabledServersMatch_Handler,
		},
		{
			MethodName: "deleteServer",
			Handler:    _ServerService_DeleteServer_Handler,
		},
		{
			MethodName: "deleteServers",
			Handler:    _ServerService_DeleteServers_Handler,
		},
		{
			MethodName: "findEnabledServer",
			Handler:    _ServerService_FindEnabledServer_Handler,
		},
		{
			MethodName: "findEnabledServerConfig",
			Handler:    _ServerService_FindEnabledServerConfig_Handler,
		},
		{
			MethodName: "findEnabledServerType",
			Handler:    _ServerService_FindEnabledServerType_Handler,
		},
		{
			MethodName: "findAndInitServerReverseProxyConfig",
			Handler:    _ServerService_FindAndInitServerReverseProxyConfig_Handler,
		},
		{
			MethodName: "findAndInitServerWebConfig",
			Handler:    _ServerService_FindAndInitServerWebConfig_Handler,
		},
		{
			MethodName: "countAllEnabledServersWithSSLCertId",
			Handler:    _ServerService_CountAllEnabledServersWithSSLCertId_Handler,
		},
		{
			MethodName: "findAllEnabledServersWithSSLCertId",
			Handler:    _ServerService_FindAllEnabledServersWithSSLCertId_Handler,
		},
		{
			MethodName: "countAllEnabledServersWithNodeClusterId",
			Handler:    _ServerService_CountAllEnabledServersWithNodeClusterId_Handler,
		},
		{
			MethodName: "countAllEnabledServersWithServerGroupId",
			Handler:    _ServerService_CountAllEnabledServersWithServerGroupId_Handler,
		},
		{
			MethodName: "notifyServersChange",
			Handler:    _ServerService_NotifyServersChange_Handler,
		},
		{
			MethodName: "findAllEnabledServersDNSWithNodeClusterId",
			Handler:    _ServerService_FindAllEnabledServersDNSWithNodeClusterId_Handler,
		},
		{
			MethodName: "findEnabledServerDNS",
			Handler:    _ServerService_FindEnabledServerDNS_Handler,
		},
		{
			MethodName: "checkUserServer",
			Handler:    _ServerService_CheckUserServer_Handler,
		},
		{
			MethodName: "findAllEnabledServerNamesWithUserId",
			Handler:    _ServerService_FindAllEnabledServerNamesWithUserId_Handler,
		},
		{
			MethodName: "countAllServerNamesWithUserId",
			Handler:    _ServerService_CountAllServerNamesWithUserId_Handler,
		},
		{
			MethodName: "countServerNames",
			Handler:    _ServerService_CountServerNames_Handler,
		},
		{
			MethodName: "findAllUserServers",
			Handler:    _ServerService_FindAllUserServers_Handler,
		},
		{
			MethodName: "countAllUserServers",
			Handler:    _ServerService_CountAllUserServers_Handler,
		},
		{
			MethodName: "composeAllUserServersConfig",
			Handler:    _ServerService_ComposeAllUserServersConfig_Handler,
		},
		{
			MethodName: "findEnabledUserServerBasic",
			Handler:    _ServerService_FindEnabledUserServerBasic_Handler,
		},
		{
			MethodName: "updateEnabledUserServerBasic",
			Handler:    _ServerService_UpdateEnabledUserServerBasic_Handler,
		},
		{
			MethodName: "uploadServerHTTPRequestStat",
			Handler:    _ServerService_UploadServerHTTPRequestStat_Handler,
		},
		{
			MethodName: "checkServerNameDuplicationInNodeCluster",
			Handler:    _ServerService_CheckServerNameDuplicationInNodeCluster_Handler,
		},
		{
			MethodName: "checkServerNameInServer",
			Handler:    _ServerService_CheckServerNameInServer_Handler,
		},
		{
			MethodName: "findLatestServers",
			Handler:    _ServerService_FindLatestServers_Handler,
		},
		{
			MethodName: "findNearbyServers",
			Handler:    _ServerService_FindNearbyServers_Handler,
		},
		{
			MethodName: "purgeServerCache",
			Handler:    _ServerService_PurgeServerCache_Handler,
		},
		{
			MethodName: "findEnabledServerTrafficLimit",
			Handler:    _ServerService_FindEnabledServerTrafficLimit_Handler,
		},
		{
			MethodName: "updateServerTrafficLimit",
			Handler:    _ServerService_UpdateServerTrafficLimit_Handler,
		},
		{
			MethodName: "updateServerUserPlan",
			Handler:    _ServerService_UpdateServerUserPlan_Handler,
		},
		{
			MethodName: "findServerUserPlan",
			Handler:    _ServerService_FindServerUserPlan_Handler,
		},
		{
			MethodName: "composeServerConfig",
			Handler:    _ServerService_ComposeServerConfig_Handler,
		},
		{
			MethodName: "updateServerUAM",
			Handler:    _ServerService_UpdateServerUAM_Handler,
		},
		{
			MethodName: "findEnabledServerUAM",
			Handler:    _ServerService_FindEnabledServerUAM_Handler,
		},
		{
			MethodName: "updateServerUser",
			Handler:    _ServerService_UpdateServerUser_Handler,
		},
		{
			MethodName: "updateServerName",
			Handler:    _ServerService_UpdateServerName_Handler,
		},
		{
			MethodName: "copyServerConfig",
			Handler:    _ServerService_CopyServerConfig_Handler,
		},
		{
			MethodName: "findServerAuditingPrompt",
			Handler:    _ServerService_FindServerAuditingPrompt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server.proto",
}
