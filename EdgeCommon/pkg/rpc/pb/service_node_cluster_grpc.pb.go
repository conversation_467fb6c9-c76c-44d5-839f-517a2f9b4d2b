// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_node_cluster.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NodeClusterService_CreateNodeCluster_FullMethodName                                   = "/pb.NodeClusterService/createNodeCluster"
	NodeClusterService_UpdateNodeCluster_FullMethodName                                   = "/pb.NodeClusterService/updateNodeCluster"
	NodeClusterService_DeleteNodeCluster_FullMethodName                                   = "/pb.NodeClusterService/deleteNodeCluster"
	NodeClusterService_FindEnabledNodeCluster_FullMethodName                              = "/pb.NodeClusterService/findEnabledNodeCluster"
	NodeClusterService_FindAPINodesWithNodeCluster_FullMethodName                         = "/pb.NodeClusterService/findAPINodesWithNodeCluster"
	NodeClusterService_FindAllEnabledNodeClusters_FullMethodName                          = "/pb.NodeClusterService/findAllEnabledNodeClusters"
	NodeClusterService_CountAllEnabledNodeClusters_FullMethodName                         = "/pb.NodeClusterService/countAllEnabledNodeClusters"
	NodeClusterService_ListEnabledNodeClusters_FullMethodName                             = "/pb.NodeClusterService/listEnabledNodeClusters"
	NodeClusterService_FindNodeClusterHealthCheckConfig_FullMethodName                    = "/pb.NodeClusterService/findNodeClusterHealthCheckConfig"
	NodeClusterService_UpdateNodeClusterHealthCheck_FullMethodName                        = "/pb.NodeClusterService/updateNodeClusterHealthCheck"
	NodeClusterService_ExecuteNodeClusterHealthCheck_FullMethodName                       = "/pb.NodeClusterService/executeNodeClusterHealthCheck"
	NodeClusterService_CountAllEnabledNodeClustersWithNodeGrantId_FullMethodName          = "/pb.NodeClusterService/countAllEnabledNodeClustersWithNodeGrantId"
	NodeClusterService_FindAllEnabledNodeClustersWithNodeGrantId_FullMethodName           = "/pb.NodeClusterService/findAllEnabledNodeClustersWithNodeGrantId"
	NodeClusterService_FindEnabledNodeClusterDNS_FullMethodName                           = "/pb.NodeClusterService/findEnabledNodeClusterDNS"
	NodeClusterService_CountAllEnabledNodeClustersWithDNSProviderId_FullMethodName        = "/pb.NodeClusterService/countAllEnabledNodeClustersWithDNSProviderId"
	NodeClusterService_CountAllEnabledNodeClustersWithDNSDomainId_FullMethodName          = "/pb.NodeClusterService/countAllEnabledNodeClustersWithDNSDomainId"
	NodeClusterService_FindAllEnabledNodeClustersWithDNSDomainId_FullMethodName           = "/pb.NodeClusterService/findAllEnabledNodeClustersWithDNSDomainId"
	NodeClusterService_CheckNodeClusterDNSName_FullMethodName                             = "/pb.NodeClusterService/checkNodeClusterDNSName"
	NodeClusterService_UpdateNodeClusterDNS_FullMethodName                                = "/pb.NodeClusterService/updateNodeClusterDNS"
	NodeClusterService_CheckNodeClusterDNSChanges_FullMethodName                          = "/pb.NodeClusterService/checkNodeClusterDNSChanges"
	NodeClusterService_FindEnabledNodeClusterTOA_FullMethodName                           = "/pb.NodeClusterService/findEnabledNodeClusterTOA"
	NodeClusterService_UpdateNodeClusterTOA_FullMethodName                                = "/pb.NodeClusterService/updateNodeClusterTOA"
	NodeClusterService_CountAllEnabledNodeClustersWithHTTPCachePolicyId_FullMethodName    = "/pb.NodeClusterService/countAllEnabledNodeClustersWithHTTPCachePolicyId"
	NodeClusterService_FindAllEnabledNodeClustersWithHTTPCachePolicyId_FullMethodName     = "/pb.NodeClusterService/findAllEnabledNodeClustersWithHTTPCachePolicyId"
	NodeClusterService_CountAllEnabledNodeClustersWithHTTPFirewallPolicyId_FullMethodName = "/pb.NodeClusterService/countAllEnabledNodeClustersWithHTTPFirewallPolicyId"
	NodeClusterService_FindAllEnabledNodeClustersWithHTTPFirewallPolicyId_FullMethodName  = "/pb.NodeClusterService/findAllEnabledNodeClustersWithHTTPFirewallPolicyId"
	NodeClusterService_UpdateNodeClusterHTTPCachePolicyId_FullMethodName                  = "/pb.NodeClusterService/updateNodeClusterHTTPCachePolicyId"
	NodeClusterService_UpdateNodeClusterHTTPFirewallPolicyId_FullMethodName               = "/pb.NodeClusterService/updateNodeClusterHTTPFirewallPolicyId"
	NodeClusterService_UpdateNodeClusterSystemService_FullMethodName                      = "/pb.NodeClusterService/updateNodeClusterSystemService"
	NodeClusterService_FindNodeClusterSystemService_FullMethodName                        = "/pb.NodeClusterService/findNodeClusterSystemService"
	NodeClusterService_FindFreePortInNodeCluster_FullMethodName                           = "/pb.NodeClusterService/findFreePortInNodeCluster"
	NodeClusterService_CheckPortIsUsingInNodeCluster_FullMethodName                       = "/pb.NodeClusterService/checkPortIsUsingInNodeCluster"
	NodeClusterService_FindLatestNodeClusters_FullMethodName                              = "/pb.NodeClusterService/findLatestNodeClusters"
	NodeClusterService_FindEnabledNodeClusterConfigInfo_FullMethodName                    = "/pb.NodeClusterService/findEnabledNodeClusterConfigInfo"
	NodeClusterService_UpdateNodeClusterPinned_FullMethodName                             = "/pb.NodeClusterService/updateNodeClusterPinned"
	NodeClusterService_FindEnabledNodeClusterWebPPolicy_FullMethodName                    = "/pb.NodeClusterService/findEnabledNodeClusterWebPPolicy"
	NodeClusterService_UpdateNodeClusterWebPPolicy_FullMethodName                         = "/pb.NodeClusterService/updateNodeClusterWebPPolicy"
	NodeClusterService_FindEnabledNodeClusterUAMPolicy_FullMethodName                     = "/pb.NodeClusterService/findEnabledNodeClusterUAMPolicy"
	NodeClusterService_UpdateNodeClusterUAMPolicy_FullMethodName                          = "/pb.NodeClusterService/updateNodeClusterUAMPolicy"
	NodeClusterService_FindEnabledNodeClusterHTTPCCPolicy_FullMethodName                  = "/pb.NodeClusterService/findEnabledNodeClusterHTTPCCPolicy"
	NodeClusterService_UpdateNodeClusterHTTPCCPolicy_FullMethodName                       = "/pb.NodeClusterService/updateNodeClusterHTTPCCPolicy"
	NodeClusterService_FindNodeClusterDDoSProtection_FullMethodName                       = "/pb.NodeClusterService/findNodeClusterDDoSProtection"
	NodeClusterService_UpdateNodeClusterDDoSProtection_FullMethodName                     = "/pb.NodeClusterService/updateNodeClusterDDoSProtection"
	NodeClusterService_FindNodeClusterGlobalServerConfig_FullMethodName                   = "/pb.NodeClusterService/findNodeClusterGlobalServerConfig"
	NodeClusterService_UpdateNodeClusterGlobalServerConfig_FullMethodName                 = "/pb.NodeClusterService/updateNodeClusterGlobalServerConfig"
	NodeClusterService_FindNodeClusterHTTPPagesPolicy_FullMethodName                      = "/pb.NodeClusterService/findNodeClusterHTTPPagesPolicy"
	NodeClusterService_UpdateNodeClusterHTTPPagesPolicy_FullMethodName                    = "/pb.NodeClusterService/updateNodeClusterHTTPPagesPolicy"
	NodeClusterService_FindNodeClusterHTTP3Policy_FullMethodName                          = "/pb.NodeClusterService/findNodeClusterHTTP3Policy"
	NodeClusterService_UpdateNodeClusterHTTP3Policy_FullMethodName                        = "/pb.NodeClusterService/updateNodeClusterHTTP3Policy"
	NodeClusterService_FindNodeClusterNetworkSecurityPolicy_FullMethodName                = "/pb.NodeClusterService/findNodeClusterNetworkSecurityPolicy"
	NodeClusterService_UpdateNodeClusterNetworkSecurityPolicy_FullMethodName              = "/pb.NodeClusterService/updateNodeClusterNetworkSecurityPolicy"
)

// NodeClusterServiceClient is the client API for NodeClusterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NodeClusterServiceClient interface {
	// 创建集群
	CreateNodeCluster(ctx context.Context, in *CreateNodeClusterRequest, opts ...grpc.CallOption) (*CreateNodeClusterResponse, error)
	// 修改集群
	UpdateNodeCluster(ctx context.Context, in *UpdateNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除集群
	DeleteNodeCluster(ctx context.Context, in *DeleteNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个集群信息
	FindEnabledNodeCluster(ctx context.Context, in *FindEnabledNodeClusterRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterResponse, error)
	// 查找集群的API节点信息
	FindAPINodesWithNodeCluster(ctx context.Context, in *FindAPINodesWithNodeClusterRequest, opts ...grpc.CallOption) (*FindAPINodesWithNodeClusterResponse, error)
	// 获取所有可用集群
	FindAllEnabledNodeClusters(ctx context.Context, in *FindAllEnabledNodeClustersRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersResponse, error)
	// 计算所有集群数量
	CountAllEnabledNodeClusters(ctx context.Context, in *CountAllEnabledNodeClustersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页集群
	ListEnabledNodeClusters(ctx context.Context, in *ListEnabledNodeClustersRequest, opts ...grpc.CallOption) (*ListEnabledNodeClustersResponse, error)
	// 查找集群的健康检查配置
	FindNodeClusterHealthCheckConfig(ctx context.Context, in *FindNodeClusterHealthCheckConfigRequest, opts ...grpc.CallOption) (*FindNodeClusterHealthCheckConfigResponse, error)
	// 修改集群健康检查设置
	UpdateNodeClusterHealthCheck(ctx context.Context, in *UpdateNodeClusterHealthCheckRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 执行健康检查
	ExecuteNodeClusterHealthCheck(ctx context.Context, in *ExecuteNodeClusterHealthCheckRequest, opts ...grpc.CallOption) (*ExecuteNodeClusterHealthCheckResponse, error)
	// 计算使用某个认证的集群数量
	CountAllEnabledNodeClustersWithNodeGrantId(ctx context.Context, in *CountAllEnabledNodeClustersWithNodeGrantIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找使用某个认证的所有集群
	FindAllEnabledNodeClustersWithNodeGrantId(ctx context.Context, in *FindAllEnabledNodeClustersWithNodeGrantIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithNodeGrantIdResponse, error)
	// 查找集群的DNS配置
	FindEnabledNodeClusterDNS(ctx context.Context, in *FindEnabledNodeClusterDNSRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterDNSResponse, error)
	// 计算使用某个DNS服务商的集群数量
	CountAllEnabledNodeClustersWithDNSProviderId(ctx context.Context, in *CountAllEnabledNodeClustersWithDNSProviderIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 计算使用某个DNS域名的集群数量
	CountAllEnabledNodeClustersWithDNSDomainId(ctx context.Context, in *CountAllEnabledNodeClustersWithDNSDomainIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找使用某个域名的所有集群
	FindAllEnabledNodeClustersWithDNSDomainId(ctx context.Context, in *FindAllEnabledNodeClustersWithDNSDomainIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithDNSDomainIdResponse, error)
	// 检查集群域名是否已经被使用
	CheckNodeClusterDNSName(ctx context.Context, in *CheckNodeClusterDNSNameRequest, opts ...grpc.CallOption) (*CheckNodeClusterDNSNameResponse, error)
	// 修改集群的域名设置
	UpdateNodeClusterDNS(ctx context.Context, in *UpdateNodeClusterDNSRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 检查集群的DNS是否有变化
	CheckNodeClusterDNSChanges(ctx context.Context, in *CheckNodeClusterDNSChangesRequest, opts ...grpc.CallOption) (*CheckNodeClusterDNSChangesResponse, error)
	// 查找集群的TOA配置
	FindEnabledNodeClusterTOA(ctx context.Context, in *FindEnabledNodeClusterTOARequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterTOAResponse, error)
	// 修改集群的TOA设置
	UpdateNodeClusterTOA(ctx context.Context, in *UpdateNodeClusterTOARequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 计算使用某个缓存策略的集群数量
	CountAllEnabledNodeClustersWithHTTPCachePolicyId(ctx context.Context, in *CountAllEnabledNodeClustersWithHTTPCachePolicyIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找使用缓存策略的所有集群
	FindAllEnabledNodeClustersWithHTTPCachePolicyId(ctx context.Context, in *FindAllEnabledNodeClustersWithHTTPCachePolicyIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithHTTPCachePolicyIdResponse, error)
	// 计算使用某个WAF策略的集群数量
	CountAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx context.Context, in *CountAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 查找使用某个WAF策略的所有集群
	FindAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx context.Context, in *FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdResponse, error)
	// 修改集群的缓存策略
	UpdateNodeClusterHTTPCachePolicyId(ctx context.Context, in *UpdateNodeClusterHTTPCachePolicyIdRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改集群的WAF策略
	UpdateNodeClusterHTTPFirewallPolicyId(ctx context.Context, in *UpdateNodeClusterHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 修改集群的系统服务设置
	UpdateNodeClusterSystemService(ctx context.Context, in *UpdateNodeClusterSystemServiceRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找集群的系统服务设置
	FindNodeClusterSystemService(ctx context.Context, in *FindNodeClusterSystemServiceRequest, opts ...grpc.CallOption) (*FindNodeClusterSystemServiceResponse, error)
	// 获取集群中可以使用的端口
	FindFreePortInNodeCluster(ctx context.Context, in *FindFreePortInNodeClusterRequest, opts ...grpc.CallOption) (*FindFreePortInNodeClusterResponse, error)
	// 检查端口是否已经被使用
	CheckPortIsUsingInNodeCluster(ctx context.Context, in *CheckPortIsUsingInNodeClusterRequest, opts ...grpc.CallOption) (*CheckPortIsUsingInNodeClusterResponse, error)
	// 查找最近访问的集群
	FindLatestNodeClusters(ctx context.Context, in *FindLatestNodeClustersRequest, opts ...grpc.CallOption) (*FindLatestNodeClustersResponse, error)
	// 取得集群的配置概要信息
	FindEnabledNodeClusterConfigInfo(ctx context.Context, in *FindEnabledNodeClusterConfigInfoRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterConfigInfoResponse, error)
	// 设置集群是否置顶
	UpdateNodeClusterPinned(ctx context.Context, in *UpdateNodeClusterPinnedRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取集群WebP策略
	FindEnabledNodeClusterWebPPolicy(ctx context.Context, in *FindEnabledNodeClusterWebPPolicyRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterWebPPolicyResponse, error)
	// 设置集群WebP策略
	UpdateNodeClusterWebPPolicy(ctx context.Context, in *UpdateNodeClusterWebPPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取集群的UAM策略
	FindEnabledNodeClusterUAMPolicy(ctx context.Context, in *FindEnabledNodeClusterUAMPolicyRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterUAMPolicyResponse, error)
	// 设置集群的UAM策略
	UpdateNodeClusterUAMPolicy(ctx context.Context, in *UpdateNodeClusterUAMPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 读取集群的HTTP CC策略
	FindEnabledNodeClusterHTTPCCPolicy(ctx context.Context, in *FindEnabledNodeClusterHTTPCCPolicyRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterHTTPCCPolicyResponse, error)
	// 设置集群的HTTP CC策略
	UpdateNodeClusterHTTPCCPolicy(ctx context.Context, in *UpdateNodeClusterHTTPCCPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取集群的DDoS设置
	FindNodeClusterDDoSProtection(ctx context.Context, in *FindNodeClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNodeClusterDDoSProtectionResponse, error)
	// 修改集群的DDoS设置
	UpdateNodeClusterDDoSProtection(ctx context.Context, in *UpdateNodeClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取集群的全局服务设置
	FindNodeClusterGlobalServerConfig(ctx context.Context, in *FindNodeClusterGlobalServerConfigRequest, opts ...grpc.CallOption) (*FindNodeClusterGlobalServerConfigResponse, error)
	// 修改集群的全局服务设置
	UpdateNodeClusterGlobalServerConfig(ctx context.Context, in *UpdateNodeClusterGlobalServerConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取集群的自定义页面设置
	FindNodeClusterHTTPPagesPolicy(ctx context.Context, in *FindNodeClusterHTTPPagesPolicyRequest, opts ...grpc.CallOption) (*FindNodeClusterHTTPPagesPolicyResponse, error)
	// 修改集群的自定义页面设置
	UpdateNodeClusterHTTPPagesPolicy(ctx context.Context, in *UpdateNodeClusterHTTPPagesPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取集群的HTTP3设置
	FindNodeClusterHTTP3Policy(ctx context.Context, in *FindNodeClusterHTTP3PolicyRequest, opts ...grpc.CallOption) (*FindNodeClusterHTTP3PolicyResponse, error)
	// 修改集群的HTTP3设置
	UpdateNodeClusterHTTP3Policy(ctx context.Context, in *UpdateNodeClusterHTTP3PolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 获取集群的网络安全策略
	FindNodeClusterNetworkSecurityPolicy(ctx context.Context, in *FindNodeClusterNetworkSecurityPolicyRequest, opts ...grpc.CallOption) (*FindNodeClusterNetworkSecurityPolicyResponse, error)
	// 修改集群的网络安全策略
	UpdateNodeClusterNetworkSecurityPolicy(ctx context.Context, in *UpdateNodeClusterNetworkSecurityPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type nodeClusterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeClusterServiceClient(cc grpc.ClientConnInterface) NodeClusterServiceClient {
	return &nodeClusterServiceClient{cc}
}

func (c *nodeClusterServiceClient) CreateNodeCluster(ctx context.Context, in *CreateNodeClusterRequest, opts ...grpc.CallOption) (*CreateNodeClusterResponse, error) {
	out := new(CreateNodeClusterResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CreateNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeCluster(ctx context.Context, in *UpdateNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) DeleteNodeCluster(ctx context.Context, in *DeleteNodeClusterRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_DeleteNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeCluster(ctx context.Context, in *FindEnabledNodeClusterRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterResponse, error) {
	out := new(FindEnabledNodeClusterResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindAPINodesWithNodeCluster(ctx context.Context, in *FindAPINodesWithNodeClusterRequest, opts ...grpc.CallOption) (*FindAPINodesWithNodeClusterResponse, error) {
	out := new(FindAPINodesWithNodeClusterResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindAPINodesWithNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindAllEnabledNodeClusters(ctx context.Context, in *FindAllEnabledNodeClustersRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersResponse, error) {
	out := new(FindAllEnabledNodeClustersResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindAllEnabledNodeClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CountAllEnabledNodeClusters(ctx context.Context, in *CountAllEnabledNodeClustersRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CountAllEnabledNodeClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) ListEnabledNodeClusters(ctx context.Context, in *ListEnabledNodeClustersRequest, opts ...grpc.CallOption) (*ListEnabledNodeClustersResponse, error) {
	out := new(ListEnabledNodeClustersResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_ListEnabledNodeClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterHealthCheckConfig(ctx context.Context, in *FindNodeClusterHealthCheckConfigRequest, opts ...grpc.CallOption) (*FindNodeClusterHealthCheckConfigResponse, error) {
	out := new(FindNodeClusterHealthCheckConfigResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterHealthCheckConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterHealthCheck(ctx context.Context, in *UpdateNodeClusterHealthCheckRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterHealthCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) ExecuteNodeClusterHealthCheck(ctx context.Context, in *ExecuteNodeClusterHealthCheckRequest, opts ...grpc.CallOption) (*ExecuteNodeClusterHealthCheckResponse, error) {
	out := new(ExecuteNodeClusterHealthCheckResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_ExecuteNodeClusterHealthCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CountAllEnabledNodeClustersWithNodeGrantId(ctx context.Context, in *CountAllEnabledNodeClustersWithNodeGrantIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CountAllEnabledNodeClustersWithNodeGrantId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindAllEnabledNodeClustersWithNodeGrantId(ctx context.Context, in *FindAllEnabledNodeClustersWithNodeGrantIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithNodeGrantIdResponse, error) {
	out := new(FindAllEnabledNodeClustersWithNodeGrantIdResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindAllEnabledNodeClustersWithNodeGrantId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeClusterDNS(ctx context.Context, in *FindEnabledNodeClusterDNSRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterDNSResponse, error) {
	out := new(FindEnabledNodeClusterDNSResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeClusterDNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CountAllEnabledNodeClustersWithDNSProviderId(ctx context.Context, in *CountAllEnabledNodeClustersWithDNSProviderIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CountAllEnabledNodeClustersWithDNSProviderId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CountAllEnabledNodeClustersWithDNSDomainId(ctx context.Context, in *CountAllEnabledNodeClustersWithDNSDomainIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CountAllEnabledNodeClustersWithDNSDomainId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindAllEnabledNodeClustersWithDNSDomainId(ctx context.Context, in *FindAllEnabledNodeClustersWithDNSDomainIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithDNSDomainIdResponse, error) {
	out := new(FindAllEnabledNodeClustersWithDNSDomainIdResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindAllEnabledNodeClustersWithDNSDomainId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CheckNodeClusterDNSName(ctx context.Context, in *CheckNodeClusterDNSNameRequest, opts ...grpc.CallOption) (*CheckNodeClusterDNSNameResponse, error) {
	out := new(CheckNodeClusterDNSNameResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CheckNodeClusterDNSName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterDNS(ctx context.Context, in *UpdateNodeClusterDNSRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterDNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CheckNodeClusterDNSChanges(ctx context.Context, in *CheckNodeClusterDNSChangesRequest, opts ...grpc.CallOption) (*CheckNodeClusterDNSChangesResponse, error) {
	out := new(CheckNodeClusterDNSChangesResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CheckNodeClusterDNSChanges_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeClusterTOA(ctx context.Context, in *FindEnabledNodeClusterTOARequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterTOAResponse, error) {
	out := new(FindEnabledNodeClusterTOAResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeClusterTOA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterTOA(ctx context.Context, in *UpdateNodeClusterTOARequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterTOA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CountAllEnabledNodeClustersWithHTTPCachePolicyId(ctx context.Context, in *CountAllEnabledNodeClustersWithHTTPCachePolicyIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CountAllEnabledNodeClustersWithHTTPCachePolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindAllEnabledNodeClustersWithHTTPCachePolicyId(ctx context.Context, in *FindAllEnabledNodeClustersWithHTTPCachePolicyIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithHTTPCachePolicyIdResponse, error) {
	out := new(FindAllEnabledNodeClustersWithHTTPCachePolicyIdResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindAllEnabledNodeClustersWithHTTPCachePolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CountAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx context.Context, in *CountAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CountAllEnabledNodeClustersWithHTTPFirewallPolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx context.Context, in *FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdResponse, error) {
	out := new(FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindAllEnabledNodeClustersWithHTTPFirewallPolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterHTTPCachePolicyId(ctx context.Context, in *UpdateNodeClusterHTTPCachePolicyIdRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterHTTPCachePolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterHTTPFirewallPolicyId(ctx context.Context, in *UpdateNodeClusterHTTPFirewallPolicyIdRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterHTTPFirewallPolicyId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterSystemService(ctx context.Context, in *UpdateNodeClusterSystemServiceRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterSystemService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterSystemService(ctx context.Context, in *FindNodeClusterSystemServiceRequest, opts ...grpc.CallOption) (*FindNodeClusterSystemServiceResponse, error) {
	out := new(FindNodeClusterSystemServiceResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterSystemService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindFreePortInNodeCluster(ctx context.Context, in *FindFreePortInNodeClusterRequest, opts ...grpc.CallOption) (*FindFreePortInNodeClusterResponse, error) {
	out := new(FindFreePortInNodeClusterResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindFreePortInNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) CheckPortIsUsingInNodeCluster(ctx context.Context, in *CheckPortIsUsingInNodeClusterRequest, opts ...grpc.CallOption) (*CheckPortIsUsingInNodeClusterResponse, error) {
	out := new(CheckPortIsUsingInNodeClusterResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_CheckPortIsUsingInNodeCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindLatestNodeClusters(ctx context.Context, in *FindLatestNodeClustersRequest, opts ...grpc.CallOption) (*FindLatestNodeClustersResponse, error) {
	out := new(FindLatestNodeClustersResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindLatestNodeClusters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeClusterConfigInfo(ctx context.Context, in *FindEnabledNodeClusterConfigInfoRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterConfigInfoResponse, error) {
	out := new(FindEnabledNodeClusterConfigInfoResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeClusterConfigInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterPinned(ctx context.Context, in *UpdateNodeClusterPinnedRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterPinned_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeClusterWebPPolicy(ctx context.Context, in *FindEnabledNodeClusterWebPPolicyRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterWebPPolicyResponse, error) {
	out := new(FindEnabledNodeClusterWebPPolicyResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeClusterWebPPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterWebPPolicy(ctx context.Context, in *UpdateNodeClusterWebPPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterWebPPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeClusterUAMPolicy(ctx context.Context, in *FindEnabledNodeClusterUAMPolicyRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterUAMPolicyResponse, error) {
	out := new(FindEnabledNodeClusterUAMPolicyResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeClusterUAMPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterUAMPolicy(ctx context.Context, in *UpdateNodeClusterUAMPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterUAMPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindEnabledNodeClusterHTTPCCPolicy(ctx context.Context, in *FindEnabledNodeClusterHTTPCCPolicyRequest, opts ...grpc.CallOption) (*FindEnabledNodeClusterHTTPCCPolicyResponse, error) {
	out := new(FindEnabledNodeClusterHTTPCCPolicyResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindEnabledNodeClusterHTTPCCPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterHTTPCCPolicy(ctx context.Context, in *UpdateNodeClusterHTTPCCPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterHTTPCCPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterDDoSProtection(ctx context.Context, in *FindNodeClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*FindNodeClusterDDoSProtectionResponse, error) {
	out := new(FindNodeClusterDDoSProtectionResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterDDoSProtection(ctx context.Context, in *UpdateNodeClusterDDoSProtectionRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterDDoSProtection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterGlobalServerConfig(ctx context.Context, in *FindNodeClusterGlobalServerConfigRequest, opts ...grpc.CallOption) (*FindNodeClusterGlobalServerConfigResponse, error) {
	out := new(FindNodeClusterGlobalServerConfigResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterGlobalServerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterGlobalServerConfig(ctx context.Context, in *UpdateNodeClusterGlobalServerConfigRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterGlobalServerConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterHTTPPagesPolicy(ctx context.Context, in *FindNodeClusterHTTPPagesPolicyRequest, opts ...grpc.CallOption) (*FindNodeClusterHTTPPagesPolicyResponse, error) {
	out := new(FindNodeClusterHTTPPagesPolicyResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterHTTPPagesPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterHTTPPagesPolicy(ctx context.Context, in *UpdateNodeClusterHTTPPagesPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterHTTPPagesPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterHTTP3Policy(ctx context.Context, in *FindNodeClusterHTTP3PolicyRequest, opts ...grpc.CallOption) (*FindNodeClusterHTTP3PolicyResponse, error) {
	out := new(FindNodeClusterHTTP3PolicyResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterHTTP3Policy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterHTTP3Policy(ctx context.Context, in *UpdateNodeClusterHTTP3PolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterHTTP3Policy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) FindNodeClusterNetworkSecurityPolicy(ctx context.Context, in *FindNodeClusterNetworkSecurityPolicyRequest, opts ...grpc.CallOption) (*FindNodeClusterNetworkSecurityPolicyResponse, error) {
	out := new(FindNodeClusterNetworkSecurityPolicyResponse)
	err := c.cc.Invoke(ctx, NodeClusterService_FindNodeClusterNetworkSecurityPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeClusterServiceClient) UpdateNodeClusterNetworkSecurityPolicy(ctx context.Context, in *UpdateNodeClusterNetworkSecurityPolicyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NodeClusterService_UpdateNodeClusterNetworkSecurityPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeClusterServiceServer is the server API for NodeClusterService service.
// All implementations should embed UnimplementedNodeClusterServiceServer
// for forward compatibility
type NodeClusterServiceServer interface {
	// 创建集群
	CreateNodeCluster(context.Context, *CreateNodeClusterRequest) (*CreateNodeClusterResponse, error)
	// 修改集群
	UpdateNodeCluster(context.Context, *UpdateNodeClusterRequest) (*RPCSuccess, error)
	// 删除集群
	DeleteNodeCluster(context.Context, *DeleteNodeClusterRequest) (*RPCSuccess, error)
	// 查找单个集群信息
	FindEnabledNodeCluster(context.Context, *FindEnabledNodeClusterRequest) (*FindEnabledNodeClusterResponse, error)
	// 查找集群的API节点信息
	FindAPINodesWithNodeCluster(context.Context, *FindAPINodesWithNodeClusterRequest) (*FindAPINodesWithNodeClusterResponse, error)
	// 获取所有可用集群
	FindAllEnabledNodeClusters(context.Context, *FindAllEnabledNodeClustersRequest) (*FindAllEnabledNodeClustersResponse, error)
	// 计算所有集群数量
	CountAllEnabledNodeClusters(context.Context, *CountAllEnabledNodeClustersRequest) (*RPCCountResponse, error)
	// 列出单页集群
	ListEnabledNodeClusters(context.Context, *ListEnabledNodeClustersRequest) (*ListEnabledNodeClustersResponse, error)
	// 查找集群的健康检查配置
	FindNodeClusterHealthCheckConfig(context.Context, *FindNodeClusterHealthCheckConfigRequest) (*FindNodeClusterHealthCheckConfigResponse, error)
	// 修改集群健康检查设置
	UpdateNodeClusterHealthCheck(context.Context, *UpdateNodeClusterHealthCheckRequest) (*RPCSuccess, error)
	// 执行健康检查
	ExecuteNodeClusterHealthCheck(context.Context, *ExecuteNodeClusterHealthCheckRequest) (*ExecuteNodeClusterHealthCheckResponse, error)
	// 计算使用某个认证的集群数量
	CountAllEnabledNodeClustersWithNodeGrantId(context.Context, *CountAllEnabledNodeClustersWithNodeGrantIdRequest) (*RPCCountResponse, error)
	// 查找使用某个认证的所有集群
	FindAllEnabledNodeClustersWithNodeGrantId(context.Context, *FindAllEnabledNodeClustersWithNodeGrantIdRequest) (*FindAllEnabledNodeClustersWithNodeGrantIdResponse, error)
	// 查找集群的DNS配置
	FindEnabledNodeClusterDNS(context.Context, *FindEnabledNodeClusterDNSRequest) (*FindEnabledNodeClusterDNSResponse, error)
	// 计算使用某个DNS服务商的集群数量
	CountAllEnabledNodeClustersWithDNSProviderId(context.Context, *CountAllEnabledNodeClustersWithDNSProviderIdRequest) (*RPCCountResponse, error)
	// 计算使用某个DNS域名的集群数量
	CountAllEnabledNodeClustersWithDNSDomainId(context.Context, *CountAllEnabledNodeClustersWithDNSDomainIdRequest) (*RPCCountResponse, error)
	// 查找使用某个域名的所有集群
	FindAllEnabledNodeClustersWithDNSDomainId(context.Context, *FindAllEnabledNodeClustersWithDNSDomainIdRequest) (*FindAllEnabledNodeClustersWithDNSDomainIdResponse, error)
	// 检查集群域名是否已经被使用
	CheckNodeClusterDNSName(context.Context, *CheckNodeClusterDNSNameRequest) (*CheckNodeClusterDNSNameResponse, error)
	// 修改集群的域名设置
	UpdateNodeClusterDNS(context.Context, *UpdateNodeClusterDNSRequest) (*RPCSuccess, error)
	// 检查集群的DNS是否有变化
	CheckNodeClusterDNSChanges(context.Context, *CheckNodeClusterDNSChangesRequest) (*CheckNodeClusterDNSChangesResponse, error)
	// 查找集群的TOA配置
	FindEnabledNodeClusterTOA(context.Context, *FindEnabledNodeClusterTOARequest) (*FindEnabledNodeClusterTOAResponse, error)
	// 修改集群的TOA设置
	UpdateNodeClusterTOA(context.Context, *UpdateNodeClusterTOARequest) (*RPCSuccess, error)
	// 计算使用某个缓存策略的集群数量
	CountAllEnabledNodeClustersWithHTTPCachePolicyId(context.Context, *CountAllEnabledNodeClustersWithHTTPCachePolicyIdRequest) (*RPCCountResponse, error)
	// 查找使用缓存策略的所有集群
	FindAllEnabledNodeClustersWithHTTPCachePolicyId(context.Context, *FindAllEnabledNodeClustersWithHTTPCachePolicyIdRequest) (*FindAllEnabledNodeClustersWithHTTPCachePolicyIdResponse, error)
	// 计算使用某个WAF策略的集群数量
	CountAllEnabledNodeClustersWithHTTPFirewallPolicyId(context.Context, *CountAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest) (*RPCCountResponse, error)
	// 查找使用某个WAF策略的所有集群
	FindAllEnabledNodeClustersWithHTTPFirewallPolicyId(context.Context, *FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest) (*FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdResponse, error)
	// 修改集群的缓存策略
	UpdateNodeClusterHTTPCachePolicyId(context.Context, *UpdateNodeClusterHTTPCachePolicyIdRequest) (*RPCSuccess, error)
	// 修改集群的WAF策略
	UpdateNodeClusterHTTPFirewallPolicyId(context.Context, *UpdateNodeClusterHTTPFirewallPolicyIdRequest) (*RPCSuccess, error)
	// 修改集群的系统服务设置
	UpdateNodeClusterSystemService(context.Context, *UpdateNodeClusterSystemServiceRequest) (*RPCSuccess, error)
	// 查找集群的系统服务设置
	FindNodeClusterSystemService(context.Context, *FindNodeClusterSystemServiceRequest) (*FindNodeClusterSystemServiceResponse, error)
	// 获取集群中可以使用的端口
	FindFreePortInNodeCluster(context.Context, *FindFreePortInNodeClusterRequest) (*FindFreePortInNodeClusterResponse, error)
	// 检查端口是否已经被使用
	CheckPortIsUsingInNodeCluster(context.Context, *CheckPortIsUsingInNodeClusterRequest) (*CheckPortIsUsingInNodeClusterResponse, error)
	// 查找最近访问的集群
	FindLatestNodeClusters(context.Context, *FindLatestNodeClustersRequest) (*FindLatestNodeClustersResponse, error)
	// 取得集群的配置概要信息
	FindEnabledNodeClusterConfigInfo(context.Context, *FindEnabledNodeClusterConfigInfoRequest) (*FindEnabledNodeClusterConfigInfoResponse, error)
	// 设置集群是否置顶
	UpdateNodeClusterPinned(context.Context, *UpdateNodeClusterPinnedRequest) (*RPCSuccess, error)
	// 读取集群WebP策略
	FindEnabledNodeClusterWebPPolicy(context.Context, *FindEnabledNodeClusterWebPPolicyRequest) (*FindEnabledNodeClusterWebPPolicyResponse, error)
	// 设置集群WebP策略
	UpdateNodeClusterWebPPolicy(context.Context, *UpdateNodeClusterWebPPolicyRequest) (*RPCSuccess, error)
	// 读取集群的UAM策略
	FindEnabledNodeClusterUAMPolicy(context.Context, *FindEnabledNodeClusterUAMPolicyRequest) (*FindEnabledNodeClusterUAMPolicyResponse, error)
	// 设置集群的UAM策略
	UpdateNodeClusterUAMPolicy(context.Context, *UpdateNodeClusterUAMPolicyRequest) (*RPCSuccess, error)
	// 读取集群的HTTP CC策略
	FindEnabledNodeClusterHTTPCCPolicy(context.Context, *FindEnabledNodeClusterHTTPCCPolicyRequest) (*FindEnabledNodeClusterHTTPCCPolicyResponse, error)
	// 设置集群的HTTP CC策略
	UpdateNodeClusterHTTPCCPolicy(context.Context, *UpdateNodeClusterHTTPCCPolicyRequest) (*RPCSuccess, error)
	// 获取集群的DDoS设置
	FindNodeClusterDDoSProtection(context.Context, *FindNodeClusterDDoSProtectionRequest) (*FindNodeClusterDDoSProtectionResponse, error)
	// 修改集群的DDoS设置
	UpdateNodeClusterDDoSProtection(context.Context, *UpdateNodeClusterDDoSProtectionRequest) (*RPCSuccess, error)
	// 获取集群的全局服务设置
	FindNodeClusterGlobalServerConfig(context.Context, *FindNodeClusterGlobalServerConfigRequest) (*FindNodeClusterGlobalServerConfigResponse, error)
	// 修改集群的全局服务设置
	UpdateNodeClusterGlobalServerConfig(context.Context, *UpdateNodeClusterGlobalServerConfigRequest) (*RPCSuccess, error)
	// 获取集群的自定义页面设置
	FindNodeClusterHTTPPagesPolicy(context.Context, *FindNodeClusterHTTPPagesPolicyRequest) (*FindNodeClusterHTTPPagesPolicyResponse, error)
	// 修改集群的自定义页面设置
	UpdateNodeClusterHTTPPagesPolicy(context.Context, *UpdateNodeClusterHTTPPagesPolicyRequest) (*RPCSuccess, error)
	// 获取集群的HTTP3设置
	FindNodeClusterHTTP3Policy(context.Context, *FindNodeClusterHTTP3PolicyRequest) (*FindNodeClusterHTTP3PolicyResponse, error)
	// 修改集群的HTTP3设置
	UpdateNodeClusterHTTP3Policy(context.Context, *UpdateNodeClusterHTTP3PolicyRequest) (*RPCSuccess, error)
	// 获取集群的网络安全策略
	FindNodeClusterNetworkSecurityPolicy(context.Context, *FindNodeClusterNetworkSecurityPolicyRequest) (*FindNodeClusterNetworkSecurityPolicyResponse, error)
	// 修改集群的网络安全策略
	UpdateNodeClusterNetworkSecurityPolicy(context.Context, *UpdateNodeClusterNetworkSecurityPolicyRequest) (*RPCSuccess, error)
}

// UnimplementedNodeClusterServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNodeClusterServiceServer struct {
}

func (UnimplementedNodeClusterServiceServer) CreateNodeCluster(context.Context, *CreateNodeClusterRequest) (*CreateNodeClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeCluster(context.Context, *UpdateNodeClusterRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) DeleteNodeCluster(context.Context, *DeleteNodeClusterRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeCluster(context.Context, *FindEnabledNodeClusterRequest) (*FindEnabledNodeClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindAPINodesWithNodeCluster(context.Context, *FindAPINodesWithNodeClusterRequest) (*FindAPINodesWithNodeClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAPINodesWithNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindAllEnabledNodeClusters(context.Context, *FindAllEnabledNodeClustersRequest) (*FindAllEnabledNodeClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeClusters not implemented")
}
func (UnimplementedNodeClusterServiceServer) CountAllEnabledNodeClusters(context.Context, *CountAllEnabledNodeClustersRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClusters not implemented")
}
func (UnimplementedNodeClusterServiceServer) ListEnabledNodeClusters(context.Context, *ListEnabledNodeClustersRequest) (*ListEnabledNodeClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnabledNodeClusters not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterHealthCheckConfig(context.Context, *FindNodeClusterHealthCheckConfigRequest) (*FindNodeClusterHealthCheckConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterHealthCheckConfig not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterHealthCheck(context.Context, *UpdateNodeClusterHealthCheckRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterHealthCheck not implemented")
}
func (UnimplementedNodeClusterServiceServer) ExecuteNodeClusterHealthCheck(context.Context, *ExecuteNodeClusterHealthCheckRequest) (*ExecuteNodeClusterHealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteNodeClusterHealthCheck not implemented")
}
func (UnimplementedNodeClusterServiceServer) CountAllEnabledNodeClustersWithNodeGrantId(context.Context, *CountAllEnabledNodeClustersWithNodeGrantIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClustersWithNodeGrantId not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindAllEnabledNodeClustersWithNodeGrantId(context.Context, *FindAllEnabledNodeClustersWithNodeGrantIdRequest) (*FindAllEnabledNodeClustersWithNodeGrantIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeClustersWithNodeGrantId not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeClusterDNS(context.Context, *FindEnabledNodeClusterDNSRequest) (*FindEnabledNodeClusterDNSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterDNS not implemented")
}
func (UnimplementedNodeClusterServiceServer) CountAllEnabledNodeClustersWithDNSProviderId(context.Context, *CountAllEnabledNodeClustersWithDNSProviderIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClustersWithDNSProviderId not implemented")
}
func (UnimplementedNodeClusterServiceServer) CountAllEnabledNodeClustersWithDNSDomainId(context.Context, *CountAllEnabledNodeClustersWithDNSDomainIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClustersWithDNSDomainId not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindAllEnabledNodeClustersWithDNSDomainId(context.Context, *FindAllEnabledNodeClustersWithDNSDomainIdRequest) (*FindAllEnabledNodeClustersWithDNSDomainIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeClustersWithDNSDomainId not implemented")
}
func (UnimplementedNodeClusterServiceServer) CheckNodeClusterDNSName(context.Context, *CheckNodeClusterDNSNameRequest) (*CheckNodeClusterDNSNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckNodeClusterDNSName not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterDNS(context.Context, *UpdateNodeClusterDNSRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterDNS not implemented")
}
func (UnimplementedNodeClusterServiceServer) CheckNodeClusterDNSChanges(context.Context, *CheckNodeClusterDNSChangesRequest) (*CheckNodeClusterDNSChangesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckNodeClusterDNSChanges not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeClusterTOA(context.Context, *FindEnabledNodeClusterTOARequest) (*FindEnabledNodeClusterTOAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterTOA not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterTOA(context.Context, *UpdateNodeClusterTOARequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterTOA not implemented")
}
func (UnimplementedNodeClusterServiceServer) CountAllEnabledNodeClustersWithHTTPCachePolicyId(context.Context, *CountAllEnabledNodeClustersWithHTTPCachePolicyIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClustersWithHTTPCachePolicyId not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindAllEnabledNodeClustersWithHTTPCachePolicyId(context.Context, *FindAllEnabledNodeClustersWithHTTPCachePolicyIdRequest) (*FindAllEnabledNodeClustersWithHTTPCachePolicyIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeClustersWithHTTPCachePolicyId not implemented")
}
func (UnimplementedNodeClusterServiceServer) CountAllEnabledNodeClustersWithHTTPFirewallPolicyId(context.Context, *CountAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllEnabledNodeClustersWithHTTPFirewallPolicyId not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindAllEnabledNodeClustersWithHTTPFirewallPolicyId(context.Context, *FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest) (*FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEnabledNodeClustersWithHTTPFirewallPolicyId not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterHTTPCachePolicyId(context.Context, *UpdateNodeClusterHTTPCachePolicyIdRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterHTTPCachePolicyId not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterHTTPFirewallPolicyId(context.Context, *UpdateNodeClusterHTTPFirewallPolicyIdRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterHTTPFirewallPolicyId not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterSystemService(context.Context, *UpdateNodeClusterSystemServiceRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterSystemService not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterSystemService(context.Context, *FindNodeClusterSystemServiceRequest) (*FindNodeClusterSystemServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterSystemService not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindFreePortInNodeCluster(context.Context, *FindFreePortInNodeClusterRequest) (*FindFreePortInNodeClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindFreePortInNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) CheckPortIsUsingInNodeCluster(context.Context, *CheckPortIsUsingInNodeClusterRequest) (*CheckPortIsUsingInNodeClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPortIsUsingInNodeCluster not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindLatestNodeClusters(context.Context, *FindLatestNodeClustersRequest) (*FindLatestNodeClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLatestNodeClusters not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeClusterConfigInfo(context.Context, *FindEnabledNodeClusterConfigInfoRequest) (*FindEnabledNodeClusterConfigInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterConfigInfo not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterPinned(context.Context, *UpdateNodeClusterPinnedRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterPinned not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeClusterWebPPolicy(context.Context, *FindEnabledNodeClusterWebPPolicyRequest) (*FindEnabledNodeClusterWebPPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterWebPPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterWebPPolicy(context.Context, *UpdateNodeClusterWebPPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterWebPPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeClusterUAMPolicy(context.Context, *FindEnabledNodeClusterUAMPolicyRequest) (*FindEnabledNodeClusterUAMPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterUAMPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterUAMPolicy(context.Context, *UpdateNodeClusterUAMPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterUAMPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindEnabledNodeClusterHTTPCCPolicy(context.Context, *FindEnabledNodeClusterHTTPCCPolicyRequest) (*FindEnabledNodeClusterHTTPCCPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledNodeClusterHTTPCCPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterHTTPCCPolicy(context.Context, *UpdateNodeClusterHTTPCCPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterHTTPCCPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterDDoSProtection(context.Context, *FindNodeClusterDDoSProtectionRequest) (*FindNodeClusterDDoSProtectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterDDoSProtection not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterDDoSProtection(context.Context, *UpdateNodeClusterDDoSProtectionRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterDDoSProtection not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterGlobalServerConfig(context.Context, *FindNodeClusterGlobalServerConfigRequest) (*FindNodeClusterGlobalServerConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterGlobalServerConfig not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterGlobalServerConfig(context.Context, *UpdateNodeClusterGlobalServerConfigRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterGlobalServerConfig not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterHTTPPagesPolicy(context.Context, *FindNodeClusterHTTPPagesPolicyRequest) (*FindNodeClusterHTTPPagesPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterHTTPPagesPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterHTTPPagesPolicy(context.Context, *UpdateNodeClusterHTTPPagesPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterHTTPPagesPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterHTTP3Policy(context.Context, *FindNodeClusterHTTP3PolicyRequest) (*FindNodeClusterHTTP3PolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterHTTP3Policy not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterHTTP3Policy(context.Context, *UpdateNodeClusterHTTP3PolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterHTTP3Policy not implemented")
}
func (UnimplementedNodeClusterServiceServer) FindNodeClusterNetworkSecurityPolicy(context.Context, *FindNodeClusterNetworkSecurityPolicyRequest) (*FindNodeClusterNetworkSecurityPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNodeClusterNetworkSecurityPolicy not implemented")
}
func (UnimplementedNodeClusterServiceServer) UpdateNodeClusterNetworkSecurityPolicy(context.Context, *UpdateNodeClusterNetworkSecurityPolicyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeClusterNetworkSecurityPolicy not implemented")
}

// UnsafeNodeClusterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeClusterServiceServer will
// result in compilation errors.
type UnsafeNodeClusterServiceServer interface {
	mustEmbedUnimplementedNodeClusterServiceServer()
}

func RegisterNodeClusterServiceServer(s grpc.ServiceRegistrar, srv NodeClusterServiceServer) {
	s.RegisterService(&NodeClusterService_ServiceDesc, srv)
}

func _NodeClusterService_CreateNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CreateNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CreateNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CreateNodeCluster(ctx, req.(*CreateNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeCluster(ctx, req.(*UpdateNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_DeleteNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).DeleteNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_DeleteNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).DeleteNodeCluster(ctx, req.(*DeleteNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeCluster(ctx, req.(*FindEnabledNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindAPINodesWithNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAPINodesWithNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindAPINodesWithNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindAPINodesWithNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindAPINodesWithNodeCluster(ctx, req.(*FindAPINodesWithNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindAllEnabledNodeClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindAllEnabledNodeClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClusters(ctx, req.(*FindAllEnabledNodeClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CountAllEnabledNodeClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CountAllEnabledNodeClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClusters(ctx, req.(*CountAllEnabledNodeClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_ListEnabledNodeClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnabledNodeClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).ListEnabledNodeClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_ListEnabledNodeClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).ListEnabledNodeClusters(ctx, req.(*ListEnabledNodeClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterHealthCheckConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterHealthCheckConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterHealthCheckConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterHealthCheckConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterHealthCheckConfig(ctx, req.(*FindNodeClusterHealthCheckConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterHealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterHealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterHealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHealthCheck(ctx, req.(*UpdateNodeClusterHealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_ExecuteNodeClusterHealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteNodeClusterHealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).ExecuteNodeClusterHealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_ExecuteNodeClusterHealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).ExecuteNodeClusterHealthCheck(ctx, req.(*ExecuteNodeClusterHealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CountAllEnabledNodeClustersWithNodeGrantId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClustersWithNodeGrantIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithNodeGrantId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CountAllEnabledNodeClustersWithNodeGrantId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithNodeGrantId(ctx, req.(*CountAllEnabledNodeClustersWithNodeGrantIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindAllEnabledNodeClustersWithNodeGrantId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeClustersWithNodeGrantIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithNodeGrantId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindAllEnabledNodeClustersWithNodeGrantId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithNodeGrantId(ctx, req.(*FindAllEnabledNodeClustersWithNodeGrantIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeClusterDNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterDNSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterDNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeClusterDNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterDNS(ctx, req.(*FindEnabledNodeClusterDNSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CountAllEnabledNodeClustersWithDNSProviderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClustersWithDNSProviderIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithDNSProviderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CountAllEnabledNodeClustersWithDNSProviderId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithDNSProviderId(ctx, req.(*CountAllEnabledNodeClustersWithDNSProviderIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CountAllEnabledNodeClustersWithDNSDomainId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClustersWithDNSDomainIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithDNSDomainId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CountAllEnabledNodeClustersWithDNSDomainId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithDNSDomainId(ctx, req.(*CountAllEnabledNodeClustersWithDNSDomainIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindAllEnabledNodeClustersWithDNSDomainId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeClustersWithDNSDomainIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithDNSDomainId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindAllEnabledNodeClustersWithDNSDomainId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithDNSDomainId(ctx, req.(*FindAllEnabledNodeClustersWithDNSDomainIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CheckNodeClusterDNSName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckNodeClusterDNSNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CheckNodeClusterDNSName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CheckNodeClusterDNSName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CheckNodeClusterDNSName(ctx, req.(*CheckNodeClusterDNSNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterDNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterDNSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterDNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterDNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterDNS(ctx, req.(*UpdateNodeClusterDNSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CheckNodeClusterDNSChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckNodeClusterDNSChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CheckNodeClusterDNSChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CheckNodeClusterDNSChanges_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CheckNodeClusterDNSChanges(ctx, req.(*CheckNodeClusterDNSChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeClusterTOA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterTOARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterTOA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeClusterTOA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterTOA(ctx, req.(*FindEnabledNodeClusterTOARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterTOA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterTOARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterTOA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterTOA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterTOA(ctx, req.(*UpdateNodeClusterTOARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CountAllEnabledNodeClustersWithHTTPCachePolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClustersWithHTTPCachePolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithHTTPCachePolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CountAllEnabledNodeClustersWithHTTPCachePolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithHTTPCachePolicyId(ctx, req.(*CountAllEnabledNodeClustersWithHTTPCachePolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindAllEnabledNodeClustersWithHTTPCachePolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeClustersWithHTTPCachePolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithHTTPCachePolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindAllEnabledNodeClustersWithHTTPCachePolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithHTTPCachePolicyId(ctx, req.(*FindAllEnabledNodeClustersWithHTTPCachePolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CountAllEnabledNodeClustersWithHTTPFirewallPolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CountAllEnabledNodeClustersWithHTTPFirewallPolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CountAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx, req.(*CountAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindAllEnabledNodeClustersWithHTTPFirewallPolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindAllEnabledNodeClustersWithHTTPFirewallPolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindAllEnabledNodeClustersWithHTTPFirewallPolicyId(ctx, req.(*FindAllEnabledNodeClustersWithHTTPFirewallPolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterHTTPCachePolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterHTTPCachePolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPCachePolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterHTTPCachePolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPCachePolicyId(ctx, req.(*UpdateNodeClusterHTTPCachePolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterHTTPFirewallPolicyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterHTTPFirewallPolicyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPFirewallPolicyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterHTTPFirewallPolicyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPFirewallPolicyId(ctx, req.(*UpdateNodeClusterHTTPFirewallPolicyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterSystemService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterSystemServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterSystemService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterSystemService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterSystemService(ctx, req.(*UpdateNodeClusterSystemServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterSystemService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterSystemServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterSystemService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterSystemService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterSystemService(ctx, req.(*FindNodeClusterSystemServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindFreePortInNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindFreePortInNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindFreePortInNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindFreePortInNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindFreePortInNodeCluster(ctx, req.(*FindFreePortInNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_CheckPortIsUsingInNodeCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPortIsUsingInNodeClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).CheckPortIsUsingInNodeCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_CheckPortIsUsingInNodeCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).CheckPortIsUsingInNodeCluster(ctx, req.(*CheckPortIsUsingInNodeClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindLatestNodeClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLatestNodeClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindLatestNodeClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindLatestNodeClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindLatestNodeClusters(ctx, req.(*FindLatestNodeClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeClusterConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterConfigInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeClusterConfigInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterConfigInfo(ctx, req.(*FindEnabledNodeClusterConfigInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterPinned_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterPinnedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterPinned(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterPinned_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterPinned(ctx, req.(*UpdateNodeClusterPinnedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeClusterWebPPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterWebPPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterWebPPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeClusterWebPPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterWebPPolicy(ctx, req.(*FindEnabledNodeClusterWebPPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterWebPPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterWebPPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterWebPPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterWebPPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterWebPPolicy(ctx, req.(*UpdateNodeClusterWebPPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeClusterUAMPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterUAMPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterUAMPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeClusterUAMPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterUAMPolicy(ctx, req.(*FindEnabledNodeClusterUAMPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterUAMPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterUAMPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterUAMPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterUAMPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterUAMPolicy(ctx, req.(*UpdateNodeClusterUAMPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindEnabledNodeClusterHTTPCCPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledNodeClusterHTTPCCPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterHTTPCCPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindEnabledNodeClusterHTTPCCPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindEnabledNodeClusterHTTPCCPolicy(ctx, req.(*FindEnabledNodeClusterHTTPCCPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterHTTPCCPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterHTTPCCPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPCCPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterHTTPCCPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPCCPolicy(ctx, req.(*UpdateNodeClusterHTTPCCPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterDDoSProtection(ctx, req.(*FindNodeClusterDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterDDoSProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterDDoSProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterDDoSProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterDDoSProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterDDoSProtection(ctx, req.(*UpdateNodeClusterDDoSProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterGlobalServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterGlobalServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterGlobalServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterGlobalServerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterGlobalServerConfig(ctx, req.(*FindNodeClusterGlobalServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterGlobalServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterGlobalServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterGlobalServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterGlobalServerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterGlobalServerConfig(ctx, req.(*UpdateNodeClusterGlobalServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterHTTPPagesPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterHTTPPagesPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterHTTPPagesPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterHTTPPagesPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterHTTPPagesPolicy(ctx, req.(*FindNodeClusterHTTPPagesPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterHTTPPagesPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterHTTPPagesPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPPagesPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterHTTPPagesPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTPPagesPolicy(ctx, req.(*UpdateNodeClusterHTTPPagesPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterHTTP3Policy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterHTTP3PolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterHTTP3Policy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterHTTP3Policy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterHTTP3Policy(ctx, req.(*FindNodeClusterHTTP3PolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterHTTP3Policy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterHTTP3PolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTP3Policy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterHTTP3Policy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterHTTP3Policy(ctx, req.(*UpdateNodeClusterHTTP3PolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_FindNodeClusterNetworkSecurityPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNodeClusterNetworkSecurityPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).FindNodeClusterNetworkSecurityPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_FindNodeClusterNetworkSecurityPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).FindNodeClusterNetworkSecurityPolicy(ctx, req.(*FindNodeClusterNetworkSecurityPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeClusterService_UpdateNodeClusterNetworkSecurityPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeClusterNetworkSecurityPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterNetworkSecurityPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeClusterService_UpdateNodeClusterNetworkSecurityPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeClusterServiceServer).UpdateNodeClusterNetworkSecurityPolicy(ctx, req.(*UpdateNodeClusterNetworkSecurityPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeClusterService_ServiceDesc is the grpc.ServiceDesc for NodeClusterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeClusterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NodeClusterService",
	HandlerType: (*NodeClusterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNodeCluster",
			Handler:    _NodeClusterService_CreateNodeCluster_Handler,
		},
		{
			MethodName: "updateNodeCluster",
			Handler:    _NodeClusterService_UpdateNodeCluster_Handler,
		},
		{
			MethodName: "deleteNodeCluster",
			Handler:    _NodeClusterService_DeleteNodeCluster_Handler,
		},
		{
			MethodName: "findEnabledNodeCluster",
			Handler:    _NodeClusterService_FindEnabledNodeCluster_Handler,
		},
		{
			MethodName: "findAPINodesWithNodeCluster",
			Handler:    _NodeClusterService_FindAPINodesWithNodeCluster_Handler,
		},
		{
			MethodName: "findAllEnabledNodeClusters",
			Handler:    _NodeClusterService_FindAllEnabledNodeClusters_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClusters",
			Handler:    _NodeClusterService_CountAllEnabledNodeClusters_Handler,
		},
		{
			MethodName: "listEnabledNodeClusters",
			Handler:    _NodeClusterService_ListEnabledNodeClusters_Handler,
		},
		{
			MethodName: "findNodeClusterHealthCheckConfig",
			Handler:    _NodeClusterService_FindNodeClusterHealthCheckConfig_Handler,
		},
		{
			MethodName: "updateNodeClusterHealthCheck",
			Handler:    _NodeClusterService_UpdateNodeClusterHealthCheck_Handler,
		},
		{
			MethodName: "executeNodeClusterHealthCheck",
			Handler:    _NodeClusterService_ExecuteNodeClusterHealthCheck_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClustersWithNodeGrantId",
			Handler:    _NodeClusterService_CountAllEnabledNodeClustersWithNodeGrantId_Handler,
		},
		{
			MethodName: "findAllEnabledNodeClustersWithNodeGrantId",
			Handler:    _NodeClusterService_FindAllEnabledNodeClustersWithNodeGrantId_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterDNS",
			Handler:    _NodeClusterService_FindEnabledNodeClusterDNS_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClustersWithDNSProviderId",
			Handler:    _NodeClusterService_CountAllEnabledNodeClustersWithDNSProviderId_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClustersWithDNSDomainId",
			Handler:    _NodeClusterService_CountAllEnabledNodeClustersWithDNSDomainId_Handler,
		},
		{
			MethodName: "findAllEnabledNodeClustersWithDNSDomainId",
			Handler:    _NodeClusterService_FindAllEnabledNodeClustersWithDNSDomainId_Handler,
		},
		{
			MethodName: "checkNodeClusterDNSName",
			Handler:    _NodeClusterService_CheckNodeClusterDNSName_Handler,
		},
		{
			MethodName: "updateNodeClusterDNS",
			Handler:    _NodeClusterService_UpdateNodeClusterDNS_Handler,
		},
		{
			MethodName: "checkNodeClusterDNSChanges",
			Handler:    _NodeClusterService_CheckNodeClusterDNSChanges_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterTOA",
			Handler:    _NodeClusterService_FindEnabledNodeClusterTOA_Handler,
		},
		{
			MethodName: "updateNodeClusterTOA",
			Handler:    _NodeClusterService_UpdateNodeClusterTOA_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClustersWithHTTPCachePolicyId",
			Handler:    _NodeClusterService_CountAllEnabledNodeClustersWithHTTPCachePolicyId_Handler,
		},
		{
			MethodName: "findAllEnabledNodeClustersWithHTTPCachePolicyId",
			Handler:    _NodeClusterService_FindAllEnabledNodeClustersWithHTTPCachePolicyId_Handler,
		},
		{
			MethodName: "countAllEnabledNodeClustersWithHTTPFirewallPolicyId",
			Handler:    _NodeClusterService_CountAllEnabledNodeClustersWithHTTPFirewallPolicyId_Handler,
		},
		{
			MethodName: "findAllEnabledNodeClustersWithHTTPFirewallPolicyId",
			Handler:    _NodeClusterService_FindAllEnabledNodeClustersWithHTTPFirewallPolicyId_Handler,
		},
		{
			MethodName: "updateNodeClusterHTTPCachePolicyId",
			Handler:    _NodeClusterService_UpdateNodeClusterHTTPCachePolicyId_Handler,
		},
		{
			MethodName: "updateNodeClusterHTTPFirewallPolicyId",
			Handler:    _NodeClusterService_UpdateNodeClusterHTTPFirewallPolicyId_Handler,
		},
		{
			MethodName: "updateNodeClusterSystemService",
			Handler:    _NodeClusterService_UpdateNodeClusterSystemService_Handler,
		},
		{
			MethodName: "findNodeClusterSystemService",
			Handler:    _NodeClusterService_FindNodeClusterSystemService_Handler,
		},
		{
			MethodName: "findFreePortInNodeCluster",
			Handler:    _NodeClusterService_FindFreePortInNodeCluster_Handler,
		},
		{
			MethodName: "checkPortIsUsingInNodeCluster",
			Handler:    _NodeClusterService_CheckPortIsUsingInNodeCluster_Handler,
		},
		{
			MethodName: "findLatestNodeClusters",
			Handler:    _NodeClusterService_FindLatestNodeClusters_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterConfigInfo",
			Handler:    _NodeClusterService_FindEnabledNodeClusterConfigInfo_Handler,
		},
		{
			MethodName: "updateNodeClusterPinned",
			Handler:    _NodeClusterService_UpdateNodeClusterPinned_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterWebPPolicy",
			Handler:    _NodeClusterService_FindEnabledNodeClusterWebPPolicy_Handler,
		},
		{
			MethodName: "updateNodeClusterWebPPolicy",
			Handler:    _NodeClusterService_UpdateNodeClusterWebPPolicy_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterUAMPolicy",
			Handler:    _NodeClusterService_FindEnabledNodeClusterUAMPolicy_Handler,
		},
		{
			MethodName: "updateNodeClusterUAMPolicy",
			Handler:    _NodeClusterService_UpdateNodeClusterUAMPolicy_Handler,
		},
		{
			MethodName: "findEnabledNodeClusterHTTPCCPolicy",
			Handler:    _NodeClusterService_FindEnabledNodeClusterHTTPCCPolicy_Handler,
		},
		{
			MethodName: "updateNodeClusterHTTPCCPolicy",
			Handler:    _NodeClusterService_UpdateNodeClusterHTTPCCPolicy_Handler,
		},
		{
			MethodName: "findNodeClusterDDoSProtection",
			Handler:    _NodeClusterService_FindNodeClusterDDoSProtection_Handler,
		},
		{
			MethodName: "updateNodeClusterDDoSProtection",
			Handler:    _NodeClusterService_UpdateNodeClusterDDoSProtection_Handler,
		},
		{
			MethodName: "findNodeClusterGlobalServerConfig",
			Handler:    _NodeClusterService_FindNodeClusterGlobalServerConfig_Handler,
		},
		{
			MethodName: "updateNodeClusterGlobalServerConfig",
			Handler:    _NodeClusterService_UpdateNodeClusterGlobalServerConfig_Handler,
		},
		{
			MethodName: "findNodeClusterHTTPPagesPolicy",
			Handler:    _NodeClusterService_FindNodeClusterHTTPPagesPolicy_Handler,
		},
		{
			MethodName: "updateNodeClusterHTTPPagesPolicy",
			Handler:    _NodeClusterService_UpdateNodeClusterHTTPPagesPolicy_Handler,
		},
		{
			MethodName: "findNodeClusterHTTP3Policy",
			Handler:    _NodeClusterService_FindNodeClusterHTTP3Policy_Handler,
		},
		{
			MethodName: "updateNodeClusterHTTP3Policy",
			Handler:    _NodeClusterService_UpdateNodeClusterHTTP3Policy_Handler,
		},
		{
			MethodName: "findNodeClusterNetworkSecurityPolicy",
			Handler:    _NodeClusterService_FindNodeClusterNetworkSecurityPolicy_Handler,
		},
		{
			MethodName: "updateNodeClusterNetworkSecurityPolicy",
			Handler:    _NodeClusterService_UpdateNodeClusterNetworkSecurityPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_node_cluster.proto",
}
