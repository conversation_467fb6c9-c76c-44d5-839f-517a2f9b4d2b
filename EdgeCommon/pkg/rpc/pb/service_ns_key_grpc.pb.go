// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ns_key.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NSKeyService_CreateNSKey_FullMethodName            = "/pb.NSKeyService/createNSKey"
	NSKeyService_UpdateNSKey_FullMethodName            = "/pb.NSKeyService/updateNSKey"
	NSKeyService_DeleteNSKey_FullMethodName            = "/pb.NSKeyService/deleteNSKey"
	NSKeyService_FindNSKey_FullMethodName              = "/pb.NSKeyService/findNSKey"
	NSKeyService_CountAllNSKeys_FullMethodName         = "/pb.NSKeyService/countAllNSKeys"
	NSKeyService_ListNSKeys_FullMethodName             = "/pb.NSKeyService/listNSKeys"
	NSKeyService_ListNSKeysAfterVersion_FullMethodName = "/pb.NSKeyService/listNSKeysAfterVersion"
)

// NSKeyServiceClient is the client API for NSKeyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NSKeyServiceClient interface {
	// 创建密钥
	CreateNSKey(ctx context.Context, in *CreateNSKeyRequest, opts ...grpc.CallOption) (*CreateNSKeyResponse, error)
	// 修改密钥
	UpdateNSKey(ctx context.Context, in *UpdateNSKeyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除密钥
	DeleteNSKey(ctx context.Context, in *DeleteNSKeyRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找单个密钥
	FindNSKey(ctx context.Context, in *FindNSKeyRequest, opts ...grpc.CallOption) (*FindNSKeyResponse, error)
	// 计算密钥数量
	CountAllNSKeys(ctx context.Context, in *CountAllNSKeysRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页密钥
	ListNSKeys(ctx context.Context, in *ListNSKeysRequest, opts ...grpc.CallOption) (*ListNSKeysResponse, error)
	// 根据版本列出一组密钥
	ListNSKeysAfterVersion(ctx context.Context, in *ListNSKeysAfterVersionRequest, opts ...grpc.CallOption) (*ListNSKeysAfterVersionResponse, error)
}

type nSKeyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNSKeyServiceClient(cc grpc.ClientConnInterface) NSKeyServiceClient {
	return &nSKeyServiceClient{cc}
}

func (c *nSKeyServiceClient) CreateNSKey(ctx context.Context, in *CreateNSKeyRequest, opts ...grpc.CallOption) (*CreateNSKeyResponse, error) {
	out := new(CreateNSKeyResponse)
	err := c.cc.Invoke(ctx, NSKeyService_CreateNSKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSKeyServiceClient) UpdateNSKey(ctx context.Context, in *UpdateNSKeyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSKeyService_UpdateNSKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSKeyServiceClient) DeleteNSKey(ctx context.Context, in *DeleteNSKeyRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, NSKeyService_DeleteNSKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSKeyServiceClient) FindNSKey(ctx context.Context, in *FindNSKeyRequest, opts ...grpc.CallOption) (*FindNSKeyResponse, error) {
	out := new(FindNSKeyResponse)
	err := c.cc.Invoke(ctx, NSKeyService_FindNSKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSKeyServiceClient) CountAllNSKeys(ctx context.Context, in *CountAllNSKeysRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, NSKeyService_CountAllNSKeys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSKeyServiceClient) ListNSKeys(ctx context.Context, in *ListNSKeysRequest, opts ...grpc.CallOption) (*ListNSKeysResponse, error) {
	out := new(ListNSKeysResponse)
	err := c.cc.Invoke(ctx, NSKeyService_ListNSKeys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nSKeyServiceClient) ListNSKeysAfterVersion(ctx context.Context, in *ListNSKeysAfterVersionRequest, opts ...grpc.CallOption) (*ListNSKeysAfterVersionResponse, error) {
	out := new(ListNSKeysAfterVersionResponse)
	err := c.cc.Invoke(ctx, NSKeyService_ListNSKeysAfterVersion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NSKeyServiceServer is the server API for NSKeyService service.
// All implementations should embed UnimplementedNSKeyServiceServer
// for forward compatibility
type NSKeyServiceServer interface {
	// 创建密钥
	CreateNSKey(context.Context, *CreateNSKeyRequest) (*CreateNSKeyResponse, error)
	// 修改密钥
	UpdateNSKey(context.Context, *UpdateNSKeyRequest) (*RPCSuccess, error)
	// 删除密钥
	DeleteNSKey(context.Context, *DeleteNSKeyRequest) (*RPCSuccess, error)
	// 查找单个密钥
	FindNSKey(context.Context, *FindNSKeyRequest) (*FindNSKeyResponse, error)
	// 计算密钥数量
	CountAllNSKeys(context.Context, *CountAllNSKeysRequest) (*RPCCountResponse, error)
	// 列出单页密钥
	ListNSKeys(context.Context, *ListNSKeysRequest) (*ListNSKeysResponse, error)
	// 根据版本列出一组密钥
	ListNSKeysAfterVersion(context.Context, *ListNSKeysAfterVersionRequest) (*ListNSKeysAfterVersionResponse, error)
}

// UnimplementedNSKeyServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNSKeyServiceServer struct {
}

func (UnimplementedNSKeyServiceServer) CreateNSKey(context.Context, *CreateNSKeyRequest) (*CreateNSKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNSKey not implemented")
}
func (UnimplementedNSKeyServiceServer) UpdateNSKey(context.Context, *UpdateNSKeyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNSKey not implemented")
}
func (UnimplementedNSKeyServiceServer) DeleteNSKey(context.Context, *DeleteNSKeyRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNSKey not implemented")
}
func (UnimplementedNSKeyServiceServer) FindNSKey(context.Context, *FindNSKeyRequest) (*FindNSKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNSKey not implemented")
}
func (UnimplementedNSKeyServiceServer) CountAllNSKeys(context.Context, *CountAllNSKeysRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountAllNSKeys not implemented")
}
func (UnimplementedNSKeyServiceServer) ListNSKeys(context.Context, *ListNSKeysRequest) (*ListNSKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSKeys not implemented")
}
func (UnimplementedNSKeyServiceServer) ListNSKeysAfterVersion(context.Context, *ListNSKeysAfterVersionRequest) (*ListNSKeysAfterVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNSKeysAfterVersion not implemented")
}

// UnsafeNSKeyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NSKeyServiceServer will
// result in compilation errors.
type UnsafeNSKeyServiceServer interface {
	mustEmbedUnimplementedNSKeyServiceServer()
}

func RegisterNSKeyServiceServer(s grpc.ServiceRegistrar, srv NSKeyServiceServer) {
	s.RegisterService(&NSKeyService_ServiceDesc, srv)
}

func _NSKeyService_CreateNSKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNSKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).CreateNSKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_CreateNSKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).CreateNSKey(ctx, req.(*CreateNSKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSKeyService_UpdateNSKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNSKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).UpdateNSKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_UpdateNSKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).UpdateNSKey(ctx, req.(*UpdateNSKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSKeyService_DeleteNSKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNSKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).DeleteNSKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_DeleteNSKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).DeleteNSKey(ctx, req.(*DeleteNSKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSKeyService_FindNSKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNSKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).FindNSKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_FindNSKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).FindNSKey(ctx, req.(*FindNSKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSKeyService_CountAllNSKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountAllNSKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).CountAllNSKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_CountAllNSKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).CountAllNSKeys(ctx, req.(*CountAllNSKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSKeyService_ListNSKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).ListNSKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_ListNSKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).ListNSKeys(ctx, req.(*ListNSKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NSKeyService_ListNSKeysAfterVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNSKeysAfterVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NSKeyServiceServer).ListNSKeysAfterVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NSKeyService_ListNSKeysAfterVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NSKeyServiceServer).ListNSKeysAfterVersion(ctx, req.(*ListNSKeysAfterVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NSKeyService_ServiceDesc is the grpc.ServiceDesc for NSKeyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NSKeyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.NSKeyService",
	HandlerType: (*NSKeyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createNSKey",
			Handler:    _NSKeyService_CreateNSKey_Handler,
		},
		{
			MethodName: "updateNSKey",
			Handler:    _NSKeyService_UpdateNSKey_Handler,
		},
		{
			MethodName: "deleteNSKey",
			Handler:    _NSKeyService_DeleteNSKey_Handler,
		},
		{
			MethodName: "findNSKey",
			Handler:    _NSKeyService_FindNSKey_Handler,
		},
		{
			MethodName: "countAllNSKeys",
			Handler:    _NSKeyService_CountAllNSKeys_Handler,
		},
		{
			MethodName: "listNSKeys",
			Handler:    _NSKeyService_ListNSKeys_Handler,
		},
		{
			MethodName: "listNSKeysAfterVersion",
			Handler:    _NSKeyService_ListNSKeysAfterVersion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ns_key.proto",
}
