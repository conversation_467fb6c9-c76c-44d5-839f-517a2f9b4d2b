// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_traffic_package.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户流量包
type UserTrafficPackage struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	Id                        int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId                    int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	TrafficPackageId          int64                  `protobuf:"varint,3,opt,name=trafficPackageId,proto3" json:"trafficPackageId,omitempty"`
	TotalBytes                int64                  `protobuf:"varint,4,opt,name=totalBytes,proto3" json:"totalBytes,omitempty"`
	UsedBytes                 int64                  `protobuf:"varint,5,opt,name=usedBytes,proto3" json:"usedBytes,omitempty"`
	NodeRegionId              int64                  `protobuf:"varint,6,opt,name=nodeRegionId,proto3" json:"nodeRegionId,omitempty"`
	TrafficPackagePeriodId    int64                  `protobuf:"varint,7,opt,name=trafficPackagePeriodId,proto3" json:"trafficPackagePeriodId,omitempty"`
	TrafficPackagePeriodCount int32                  `protobuf:"varint,8,opt,name=trafficPackagePeriodCount,proto3" json:"trafficPackagePeriodCount,omitempty"`
	TrafficPackagePeriodUnit  string                 `protobuf:"bytes,9,opt,name=trafficPackagePeriodUnit,proto3" json:"trafficPackagePeriodUnit,omitempty"`
	DayFrom                   string                 `protobuf:"bytes,10,opt,name=dayFrom,proto3" json:"dayFrom,omitempty"`
	DayTo                     string                 `protobuf:"bytes,11,opt,name=dayTo,proto3" json:"dayTo,omitempty"`
	CreatedAt                 int64                  `protobuf:"varint,12,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	TrafficPackage            *TrafficPackage        `protobuf:"bytes,30,opt,name=trafficPackage,proto3" json:"trafficPackage,omitempty"`
	NodeRegion                *NodeRegion            `protobuf:"bytes,31,opt,name=nodeRegion,proto3" json:"nodeRegion,omitempty"`
	User                      *User                  `protobuf:"bytes,32,opt,name=user,proto3" json:"user,omitempty"`
	CanDelete                 bool                   `protobuf:"varint,33,opt,name=canDelete,proto3" json:"canDelete,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *UserTrafficPackage) Reset() {
	*x = UserTrafficPackage{}
	mi := &file_models_model_user_traffic_package_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTrafficPackage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTrafficPackage) ProtoMessage() {}

func (x *UserTrafficPackage) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_traffic_package_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTrafficPackage.ProtoReflect.Descriptor instead.
func (*UserTrafficPackage) Descriptor() ([]byte, []int) {
	return file_models_model_user_traffic_package_proto_rawDescGZIP(), []int{0}
}

func (x *UserTrafficPackage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserTrafficPackage) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserTrafficPackage) GetTrafficPackageId() int64 {
	if x != nil {
		return x.TrafficPackageId
	}
	return 0
}

func (x *UserTrafficPackage) GetTotalBytes() int64 {
	if x != nil {
		return x.TotalBytes
	}
	return 0
}

func (x *UserTrafficPackage) GetUsedBytes() int64 {
	if x != nil {
		return x.UsedBytes
	}
	return 0
}

func (x *UserTrafficPackage) GetNodeRegionId() int64 {
	if x != nil {
		return x.NodeRegionId
	}
	return 0
}

func (x *UserTrafficPackage) GetTrafficPackagePeriodId() int64 {
	if x != nil {
		return x.TrafficPackagePeriodId
	}
	return 0
}

func (x *UserTrafficPackage) GetTrafficPackagePeriodCount() int32 {
	if x != nil {
		return x.TrafficPackagePeriodCount
	}
	return 0
}

func (x *UserTrafficPackage) GetTrafficPackagePeriodUnit() string {
	if x != nil {
		return x.TrafficPackagePeriodUnit
	}
	return ""
}

func (x *UserTrafficPackage) GetDayFrom() string {
	if x != nil {
		return x.DayFrom
	}
	return ""
}

func (x *UserTrafficPackage) GetDayTo() string {
	if x != nil {
		return x.DayTo
	}
	return ""
}

func (x *UserTrafficPackage) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserTrafficPackage) GetTrafficPackage() *TrafficPackage {
	if x != nil {
		return x.TrafficPackage
	}
	return nil
}

func (x *UserTrafficPackage) GetNodeRegion() *NodeRegion {
	if x != nil {
		return x.NodeRegion
	}
	return nil
}

func (x *UserTrafficPackage) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserTrafficPackage) GetCanDelete() bool {
	if x != nil {
		return x.CanDelete
	}
	return false
}

var File_models_model_user_traffic_package_proto protoreflect.FileDescriptor

const file_models_model_user_traffic_package_proto_rawDesc = "" +
	"\n" +
	"'models/model_user_traffic_package.proto\x12\x02pb\x1a\x1emodels/model_node_region.proto\x1a\"models/model_traffic_package.proto\x1a\x17models/model_user.proto\"\xf2\x04\n" +
	"\x12UserTrafficPackage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12*\n" +
	"\x10trafficPackageId\x18\x03 \x01(\x03R\x10trafficPackageId\x12\x1e\n" +
	"\n" +
	"totalBytes\x18\x04 \x01(\x03R\n" +
	"totalBytes\x12\x1c\n" +
	"\tusedBytes\x18\x05 \x01(\x03R\tusedBytes\x12\"\n" +
	"\fnodeRegionId\x18\x06 \x01(\x03R\fnodeRegionId\x126\n" +
	"\x16trafficPackagePeriodId\x18\a \x01(\x03R\x16trafficPackagePeriodId\x12<\n" +
	"\x19trafficPackagePeriodCount\x18\b \x01(\x05R\x19trafficPackagePeriodCount\x12:\n" +
	"\x18trafficPackagePeriodUnit\x18\t \x01(\tR\x18trafficPackagePeriodUnit\x12\x18\n" +
	"\adayFrom\x18\n" +
	" \x01(\tR\adayFrom\x12\x14\n" +
	"\x05dayTo\x18\v \x01(\tR\x05dayTo\x12\x1c\n" +
	"\tcreatedAt\x18\f \x01(\x03R\tcreatedAt\x12:\n" +
	"\x0etrafficPackage\x18\x1e \x01(\v2\x12.pb.TrafficPackageR\x0etrafficPackage\x12.\n" +
	"\n" +
	"nodeRegion\x18\x1f \x01(\v2\x0e.pb.NodeRegionR\n" +
	"nodeRegion\x12\x1c\n" +
	"\x04user\x18  \x01(\v2\b.pb.UserR\x04user\x12\x1c\n" +
	"\tcanDelete\x18! \x01(\bR\tcanDeleteB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_traffic_package_proto_rawDescOnce sync.Once
	file_models_model_user_traffic_package_proto_rawDescData []byte
)

func file_models_model_user_traffic_package_proto_rawDescGZIP() []byte {
	file_models_model_user_traffic_package_proto_rawDescOnce.Do(func() {
		file_models_model_user_traffic_package_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_traffic_package_proto_rawDesc), len(file_models_model_user_traffic_package_proto_rawDesc)))
	})
	return file_models_model_user_traffic_package_proto_rawDescData
}

var file_models_model_user_traffic_package_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_traffic_package_proto_goTypes = []any{
	(*UserTrafficPackage)(nil), // 0: pb.UserTrafficPackage
	(*TrafficPackage)(nil),     // 1: pb.TrafficPackage
	(*NodeRegion)(nil),         // 2: pb.NodeRegion
	(*User)(nil),               // 3: pb.User
}
var file_models_model_user_traffic_package_proto_depIdxs = []int32{
	1, // 0: pb.UserTrafficPackage.trafficPackage:type_name -> pb.TrafficPackage
	2, // 1: pb.UserTrafficPackage.nodeRegion:type_name -> pb.NodeRegion
	3, // 2: pb.UserTrafficPackage.user:type_name -> pb.User
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_models_model_user_traffic_package_proto_init() }
func file_models_model_user_traffic_package_proto_init() {
	if File_models_model_user_traffic_package_proto != nil {
		return
	}
	file_models_model_node_region_proto_init()
	file_models_model_traffic_package_proto_init()
	file_models_model_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_traffic_package_proto_rawDesc), len(file_models_model_user_traffic_package_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_traffic_package_proto_goTypes,
		DependencyIndexes: file_models_model_user_traffic_package_proto_depIdxs,
		MessageInfos:      file_models_model_user_traffic_package_proto_msgTypes,
	}.Build()
	File_models_model_user_traffic_package_proto = out.File
	file_models_model_user_traffic_package_proto_goTypes = nil
	file_models_model_user_traffic_package_proto_depIdxs = nil
}
