// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_db.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DBService_FindAllDBTables_FullMethodName = "/pb.DBService/findAllDBTables"
	DBService_DeleteDBTable_FullMethodName   = "/pb.DBService/deleteDBTable"
	DBService_TruncateDBTable_FullMethodName = "/pb.DBService/truncateDBTable"
)

// DBServiceClient is the client API for DBService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DBServiceClient interface {
	// 获取所有表信息
	FindAllDBTables(ctx context.Context, in *FindAllDBTablesRequest, opts ...grpc.CallOption) (*FindAllDBTablesResponse, error)
	// 删除表
	DeleteDBTable(ctx context.Context, in *DeleteDBTableRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 清空表
	TruncateDBTable(ctx context.Context, in *TruncateDBTableRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type dBServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDBServiceClient(cc grpc.ClientConnInterface) DBServiceClient {
	return &dBServiceClient{cc}
}

func (c *dBServiceClient) FindAllDBTables(ctx context.Context, in *FindAllDBTablesRequest, opts ...grpc.CallOption) (*FindAllDBTablesResponse, error) {
	out := new(FindAllDBTablesResponse)
	err := c.cc.Invoke(ctx, DBService_FindAllDBTables_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dBServiceClient) DeleteDBTable(ctx context.Context, in *DeleteDBTableRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, DBService_DeleteDBTable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dBServiceClient) TruncateDBTable(ctx context.Context, in *TruncateDBTableRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, DBService_TruncateDBTable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DBServiceServer is the server API for DBService service.
// All implementations should embed UnimplementedDBServiceServer
// for forward compatibility
type DBServiceServer interface {
	// 获取所有表信息
	FindAllDBTables(context.Context, *FindAllDBTablesRequest) (*FindAllDBTablesResponse, error)
	// 删除表
	DeleteDBTable(context.Context, *DeleteDBTableRequest) (*RPCSuccess, error)
	// 清空表
	TruncateDBTable(context.Context, *TruncateDBTableRequest) (*RPCSuccess, error)
}

// UnimplementedDBServiceServer should be embedded to have forward compatible implementations.
type UnimplementedDBServiceServer struct {
}

func (UnimplementedDBServiceServer) FindAllDBTables(context.Context, *FindAllDBTablesRequest) (*FindAllDBTablesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllDBTables not implemented")
}
func (UnimplementedDBServiceServer) DeleteDBTable(context.Context, *DeleteDBTableRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDBTable not implemented")
}
func (UnimplementedDBServiceServer) TruncateDBTable(context.Context, *TruncateDBTableRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TruncateDBTable not implemented")
}

// UnsafeDBServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DBServiceServer will
// result in compilation errors.
type UnsafeDBServiceServer interface {
	mustEmbedUnimplementedDBServiceServer()
}

func RegisterDBServiceServer(s grpc.ServiceRegistrar, srv DBServiceServer) {
	s.RegisterService(&DBService_ServiceDesc, srv)
}

func _DBService_FindAllDBTables_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllDBTablesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DBServiceServer).FindAllDBTables(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DBService_FindAllDBTables_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DBServiceServer).FindAllDBTables(ctx, req.(*FindAllDBTablesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DBService_DeleteDBTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDBTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DBServiceServer).DeleteDBTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DBService_DeleteDBTable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DBServiceServer).DeleteDBTable(ctx, req.(*DeleteDBTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DBService_TruncateDBTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TruncateDBTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DBServiceServer).TruncateDBTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DBService_TruncateDBTable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DBServiceServer).TruncateDBTable(ctx, req.(*TruncateDBTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DBService_ServiceDesc is the grpc.ServiceDesc for DBService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DBService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.DBService",
	HandlerType: (*DBServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findAllDBTables",
			Handler:    _DBService_FindAllDBTables_Handler,
		},
		{
			MethodName: "deleteDBTable",
			Handler:    _DBService_DeleteDBTable_Handler,
		},
		{
			MethodName: "truncateDBTable",
			Handler:    _DBService_TruncateDBTable_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_db.proto",
}
