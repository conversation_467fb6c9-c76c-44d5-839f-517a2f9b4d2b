// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_account.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserAccountService_CountUserAccounts_FullMethodName                = "/pb.UserAccountService/countUserAccounts"
	UserAccountService_ListUserAccounts_FullMethodName                 = "/pb.UserAccountService/listUserAccounts"
	UserAccountService_FindEnabledUserAccountWithUserId_FullMethodName = "/pb.UserAccountService/findEnabledUserAccountWithUserId"
	UserAccountService_FindEnabledUserAccount_FullMethodName           = "/pb.UserAccountService/findEnabledUserAccount"
	UserAccountService_UpdateUserAccount_FullMethodName                = "/pb.UserAccountService/updateUserAccount"
)

// UserAccountServiceClient is the client API for UserAccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserAccountServiceClient interface {
	// 计算账户数量
	CountUserAccounts(ctx context.Context, in *CountUserAccountsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error)
	// 列出单页账户
	ListUserAccounts(ctx context.Context, in *ListUserAccountsRequest, opts ...grpc.CallOption) (*ListUserAccountsResponse, error)
	// 根据用户ID查找单个账户
	FindEnabledUserAccountWithUserId(ctx context.Context, in *FindEnabledUserAccountWithUserIdRequest, opts ...grpc.CallOption) (*FindEnabledUserAccountWithUserIdResponse, error)
	// 查找单个账户
	FindEnabledUserAccount(ctx context.Context, in *FindEnabledUserAccountRequest, opts ...grpc.CallOption) (*FindEnabledUserAccountResponse, error)
	// 修改用户账户
	UpdateUserAccount(ctx context.Context, in *UpdateUserAccountRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
}

type userAccountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserAccountServiceClient(cc grpc.ClientConnInterface) UserAccountServiceClient {
	return &userAccountServiceClient{cc}
}

func (c *userAccountServiceClient) CountUserAccounts(ctx context.Context, in *CountUserAccountsRequest, opts ...grpc.CallOption) (*RPCCountResponse, error) {
	out := new(RPCCountResponse)
	err := c.cc.Invoke(ctx, UserAccountService_CountUserAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAccountServiceClient) ListUserAccounts(ctx context.Context, in *ListUserAccountsRequest, opts ...grpc.CallOption) (*ListUserAccountsResponse, error) {
	out := new(ListUserAccountsResponse)
	err := c.cc.Invoke(ctx, UserAccountService_ListUserAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAccountServiceClient) FindEnabledUserAccountWithUserId(ctx context.Context, in *FindEnabledUserAccountWithUserIdRequest, opts ...grpc.CallOption) (*FindEnabledUserAccountWithUserIdResponse, error) {
	out := new(FindEnabledUserAccountWithUserIdResponse)
	err := c.cc.Invoke(ctx, UserAccountService_FindEnabledUserAccountWithUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAccountServiceClient) FindEnabledUserAccount(ctx context.Context, in *FindEnabledUserAccountRequest, opts ...grpc.CallOption) (*FindEnabledUserAccountResponse, error) {
	out := new(FindEnabledUserAccountResponse)
	err := c.cc.Invoke(ctx, UserAccountService_FindEnabledUserAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAccountServiceClient) UpdateUserAccount(ctx context.Context, in *UpdateUserAccountRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserAccountService_UpdateUserAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserAccountServiceServer is the server API for UserAccountService service.
// All implementations should embed UnimplementedUserAccountServiceServer
// for forward compatibility
type UserAccountServiceServer interface {
	// 计算账户数量
	CountUserAccounts(context.Context, *CountUserAccountsRequest) (*RPCCountResponse, error)
	// 列出单页账户
	ListUserAccounts(context.Context, *ListUserAccountsRequest) (*ListUserAccountsResponse, error)
	// 根据用户ID查找单个账户
	FindEnabledUserAccountWithUserId(context.Context, *FindEnabledUserAccountWithUserIdRequest) (*FindEnabledUserAccountWithUserIdResponse, error)
	// 查找单个账户
	FindEnabledUserAccount(context.Context, *FindEnabledUserAccountRequest) (*FindEnabledUserAccountResponse, error)
	// 修改用户账户
	UpdateUserAccount(context.Context, *UpdateUserAccountRequest) (*RPCSuccess, error)
}

// UnimplementedUserAccountServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserAccountServiceServer struct {
}

func (UnimplementedUserAccountServiceServer) CountUserAccounts(context.Context, *CountUserAccountsRequest) (*RPCCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountUserAccounts not implemented")
}
func (UnimplementedUserAccountServiceServer) ListUserAccounts(context.Context, *ListUserAccountsRequest) (*ListUserAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserAccounts not implemented")
}
func (UnimplementedUserAccountServiceServer) FindEnabledUserAccountWithUserId(context.Context, *FindEnabledUserAccountWithUserIdRequest) (*FindEnabledUserAccountWithUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUserAccountWithUserId not implemented")
}
func (UnimplementedUserAccountServiceServer) FindEnabledUserAccount(context.Context, *FindEnabledUserAccountRequest) (*FindEnabledUserAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindEnabledUserAccount not implemented")
}
func (UnimplementedUserAccountServiceServer) UpdateUserAccount(context.Context, *UpdateUserAccountRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserAccount not implemented")
}

// UnsafeUserAccountServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserAccountServiceServer will
// result in compilation errors.
type UnsafeUserAccountServiceServer interface {
	mustEmbedUnimplementedUserAccountServiceServer()
}

func RegisterUserAccountServiceServer(s grpc.ServiceRegistrar, srv UserAccountServiceServer) {
	s.RegisterService(&UserAccountService_ServiceDesc, srv)
}

func _UserAccountService_CountUserAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountUserAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountServiceServer).CountUserAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountService_CountUserAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountServiceServer).CountUserAccounts(ctx, req.(*CountUserAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAccountService_ListUserAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountServiceServer).ListUserAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountService_ListUserAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountServiceServer).ListUserAccounts(ctx, req.(*ListUserAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAccountService_FindEnabledUserAccountWithUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserAccountWithUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountServiceServer).FindEnabledUserAccountWithUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountService_FindEnabledUserAccountWithUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountServiceServer).FindEnabledUserAccountWithUserId(ctx, req.(*FindEnabledUserAccountWithUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAccountService_FindEnabledUserAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindEnabledUserAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountServiceServer).FindEnabledUserAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountService_FindEnabledUserAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountServiceServer).FindEnabledUserAccount(ctx, req.(*FindEnabledUserAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAccountService_UpdateUserAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAccountServiceServer).UpdateUserAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAccountService_UpdateUserAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAccountServiceServer).UpdateUserAccount(ctx, req.(*UpdateUserAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserAccountService_ServiceDesc is the grpc.ServiceDesc for UserAccountService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserAccountService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserAccountService",
	HandlerType: (*UserAccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "countUserAccounts",
			Handler:    _UserAccountService_CountUserAccounts_Handler,
		},
		{
			MethodName: "listUserAccounts",
			Handler:    _UserAccountService_ListUserAccounts_Handler,
		},
		{
			MethodName: "findEnabledUserAccountWithUserId",
			Handler:    _UserAccountService_FindEnabledUserAccountWithUserId_Handler,
		},
		{
			MethodName: "findEnabledUserAccount",
			Handler:    _UserAccountService_FindEnabledUserAccount_Handler,
		},
		{
			MethodName: "updateUserAccount",
			Handler:    _UserAccountService_UpdateUserAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_account.proto",
}
