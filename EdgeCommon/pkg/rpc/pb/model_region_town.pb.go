// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_region_town.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RegionTown struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Codes         []string               `protobuf:"bytes,3,rep,name=codes,proto3" json:"codes,omitempty"`
	RegionCityId  int64                  `protobuf:"varint,4,opt,name=regionCityId,proto3" json:"regionCityId,omitempty"`
	CustomName    string                 `protobuf:"bytes,5,opt,name=customName,proto3" json:"customName,omitempty"`
	CustomCodes   []string               `protobuf:"bytes,6,rep,name=customCodes,proto3" json:"customCodes,omitempty"`
	DisplayName   string                 `protobuf:"bytes,7,opt,name=displayName,proto3" json:"displayName,omitempty"`
	RegionCity    *RegionCity            `protobuf:"bytes,30,opt,name=regionCity,proto3" json:"regionCity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegionTown) Reset() {
	*x = RegionTown{}
	mi := &file_models_model_region_town_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionTown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionTown) ProtoMessage() {}

func (x *RegionTown) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_region_town_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionTown.ProtoReflect.Descriptor instead.
func (*RegionTown) Descriptor() ([]byte, []int) {
	return file_models_model_region_town_proto_rawDescGZIP(), []int{0}
}

func (x *RegionTown) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegionTown) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegionTown) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *RegionTown) GetRegionCityId() int64 {
	if x != nil {
		return x.RegionCityId
	}
	return 0
}

func (x *RegionTown) GetCustomName() string {
	if x != nil {
		return x.CustomName
	}
	return ""
}

func (x *RegionTown) GetCustomCodes() []string {
	if x != nil {
		return x.CustomCodes
	}
	return nil
}

func (x *RegionTown) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *RegionTown) GetRegionCity() *RegionCity {
	if x != nil {
		return x.RegionCity
	}
	return nil
}

var File_models_model_region_town_proto protoreflect.FileDescriptor

const file_models_model_region_town_proto_rawDesc = "" +
	"\n" +
	"\x1emodels/model_region_town.proto\x12\x02pb\x1a\x1emodels/model_region_city.proto\"\xfe\x01\n" +
	"\n" +
	"RegionTown\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05codes\x18\x03 \x03(\tR\x05codes\x12\"\n" +
	"\fregionCityId\x18\x04 \x01(\x03R\fregionCityId\x12\x1e\n" +
	"\n" +
	"customName\x18\x05 \x01(\tR\n" +
	"customName\x12 \n" +
	"\vcustomCodes\x18\x06 \x03(\tR\vcustomCodes\x12 \n" +
	"\vdisplayName\x18\a \x01(\tR\vdisplayName\x12.\n" +
	"\n" +
	"regionCity\x18\x1e \x01(\v2\x0e.pb.RegionCityR\n" +
	"regionCityB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_region_town_proto_rawDescOnce sync.Once
	file_models_model_region_town_proto_rawDescData []byte
)

func file_models_model_region_town_proto_rawDescGZIP() []byte {
	file_models_model_region_town_proto_rawDescOnce.Do(func() {
		file_models_model_region_town_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_region_town_proto_rawDesc), len(file_models_model_region_town_proto_rawDesc)))
	})
	return file_models_model_region_town_proto_rawDescData
}

var file_models_model_region_town_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_region_town_proto_goTypes = []any{
	(*RegionTown)(nil), // 0: pb.RegionTown
	(*RegionCity)(nil), // 1: pb.RegionCity
}
var file_models_model_region_town_proto_depIdxs = []int32{
	1, // 0: pb.RegionTown.regionCity:type_name -> pb.RegionCity
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_region_town_proto_init() }
func file_models_model_region_town_proto_init() {
	if File_models_model_region_town_proto != nil {
		return
	}
	file_models_model_region_city_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_region_town_proto_rawDesc), len(file_models_model_region_town_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_region_town_proto_goTypes,
		DependencyIndexes: file_models_model_region_town_proto_depIdxs,
		MessageInfos:      file_models_model_region_town_proto_msgTypes,
	}.Build()
	File_models_model_region_town_proto = out.File
	file_models_model_region_town_proto_goTypes = nil
	file_models_model_region_town_proto_depIdxs = nil
}
