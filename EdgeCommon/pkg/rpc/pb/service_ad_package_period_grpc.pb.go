// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_ad_package_period.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ADPackagePeriodService_CreateADPackagePeriod_FullMethodName            = "/pb.ADPackagePeriodService/createADPackagePeriod"
	ADPackagePeriodService_UpdateADPackagePeriod_FullMethodName            = "/pb.ADPackagePeriodService/updateADPackagePeriod"
	ADPackagePeriodService_DeleteADPackagePeriod_FullMethodName            = "/pb.ADPackagePeriodService/deleteADPackagePeriod"
	ADPackagePeriodService_FindADPackagePeriod_FullMethodName              = "/pb.ADPackagePeriodService/findADPackagePeriod"
	ADPackagePeriodService_FindAllADPackagePeriods_FullMethodName          = "/pb.ADPackagePeriodService/findAllADPackagePeriods"
	ADPackagePeriodService_FindAllAvailableADPackagePeriods_FullMethodName = "/pb.ADPackagePeriodService/findAllAvailableADPackagePeriods"
)

// ADPackagePeriodServiceClient is the client API for ADPackagePeriodService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ADPackagePeriodServiceClient interface {
	// 创建有效期
	CreateADPackagePeriod(ctx context.Context, in *CreateADPackagePeriodRequest, opts ...grpc.CallOption) (*CreateADPackagePeriodResponse, error)
	// 修改有效期
	UpdateADPackagePeriod(ctx context.Context, in *UpdateADPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除有效期
	DeleteADPackagePeriod(ctx context.Context, in *DeleteADPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找有效期
	FindADPackagePeriod(ctx context.Context, in *FindADPackagePeriodRequest, opts ...grpc.CallOption) (*FindADPackagePeriodResponse, error)
	// 列出所有有效期
	FindAllADPackagePeriods(ctx context.Context, in *FindAllADPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllADPackagePeriodsResponse, error)
	// 列出所有可用有效期
	FindAllAvailableADPackagePeriods(ctx context.Context, in *FindAllAvailableADPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllAvailableADPackagePeriodsResponse, error)
}

type aDPackagePeriodServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewADPackagePeriodServiceClient(cc grpc.ClientConnInterface) ADPackagePeriodServiceClient {
	return &aDPackagePeriodServiceClient{cc}
}

func (c *aDPackagePeriodServiceClient) CreateADPackagePeriod(ctx context.Context, in *CreateADPackagePeriodRequest, opts ...grpc.CallOption) (*CreateADPackagePeriodResponse, error) {
	out := new(CreateADPackagePeriodResponse)
	err := c.cc.Invoke(ctx, ADPackagePeriodService_CreateADPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePeriodServiceClient) UpdateADPackagePeriod(ctx context.Context, in *UpdateADPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ADPackagePeriodService_UpdateADPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePeriodServiceClient) DeleteADPackagePeriod(ctx context.Context, in *DeleteADPackagePeriodRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, ADPackagePeriodService_DeleteADPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePeriodServiceClient) FindADPackagePeriod(ctx context.Context, in *FindADPackagePeriodRequest, opts ...grpc.CallOption) (*FindADPackagePeriodResponse, error) {
	out := new(FindADPackagePeriodResponse)
	err := c.cc.Invoke(ctx, ADPackagePeriodService_FindADPackagePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePeriodServiceClient) FindAllADPackagePeriods(ctx context.Context, in *FindAllADPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllADPackagePeriodsResponse, error) {
	out := new(FindAllADPackagePeriodsResponse)
	err := c.cc.Invoke(ctx, ADPackagePeriodService_FindAllADPackagePeriods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aDPackagePeriodServiceClient) FindAllAvailableADPackagePeriods(ctx context.Context, in *FindAllAvailableADPackagePeriodsRequest, opts ...grpc.CallOption) (*FindAllAvailableADPackagePeriodsResponse, error) {
	out := new(FindAllAvailableADPackagePeriodsResponse)
	err := c.cc.Invoke(ctx, ADPackagePeriodService_FindAllAvailableADPackagePeriods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ADPackagePeriodServiceServer is the server API for ADPackagePeriodService service.
// All implementations should embed UnimplementedADPackagePeriodServiceServer
// for forward compatibility
type ADPackagePeriodServiceServer interface {
	// 创建有效期
	CreateADPackagePeriod(context.Context, *CreateADPackagePeriodRequest) (*CreateADPackagePeriodResponse, error)
	// 修改有效期
	UpdateADPackagePeriod(context.Context, *UpdateADPackagePeriodRequest) (*RPCSuccess, error)
	// 删除有效期
	DeleteADPackagePeriod(context.Context, *DeleteADPackagePeriodRequest) (*RPCSuccess, error)
	// 查找有效期
	FindADPackagePeriod(context.Context, *FindADPackagePeriodRequest) (*FindADPackagePeriodResponse, error)
	// 列出所有有效期
	FindAllADPackagePeriods(context.Context, *FindAllADPackagePeriodsRequest) (*FindAllADPackagePeriodsResponse, error)
	// 列出所有可用有效期
	FindAllAvailableADPackagePeriods(context.Context, *FindAllAvailableADPackagePeriodsRequest) (*FindAllAvailableADPackagePeriodsResponse, error)
}

// UnimplementedADPackagePeriodServiceServer should be embedded to have forward compatible implementations.
type UnimplementedADPackagePeriodServiceServer struct {
}

func (UnimplementedADPackagePeriodServiceServer) CreateADPackagePeriod(context.Context, *CreateADPackagePeriodRequest) (*CreateADPackagePeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateADPackagePeriod not implemented")
}
func (UnimplementedADPackagePeriodServiceServer) UpdateADPackagePeriod(context.Context, *UpdateADPackagePeriodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateADPackagePeriod not implemented")
}
func (UnimplementedADPackagePeriodServiceServer) DeleteADPackagePeriod(context.Context, *DeleteADPackagePeriodRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteADPackagePeriod not implemented")
}
func (UnimplementedADPackagePeriodServiceServer) FindADPackagePeriod(context.Context, *FindADPackagePeriodRequest) (*FindADPackagePeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindADPackagePeriod not implemented")
}
func (UnimplementedADPackagePeriodServiceServer) FindAllADPackagePeriods(context.Context, *FindAllADPackagePeriodsRequest) (*FindAllADPackagePeriodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllADPackagePeriods not implemented")
}
func (UnimplementedADPackagePeriodServiceServer) FindAllAvailableADPackagePeriods(context.Context, *FindAllAvailableADPackagePeriodsRequest) (*FindAllAvailableADPackagePeriodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailableADPackagePeriods not implemented")
}

// UnsafeADPackagePeriodServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ADPackagePeriodServiceServer will
// result in compilation errors.
type UnsafeADPackagePeriodServiceServer interface {
	mustEmbedUnimplementedADPackagePeriodServiceServer()
}

func RegisterADPackagePeriodServiceServer(s grpc.ServiceRegistrar, srv ADPackagePeriodServiceServer) {
	s.RegisterService(&ADPackagePeriodService_ServiceDesc, srv)
}

func _ADPackagePeriodService_CreateADPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateADPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePeriodServiceServer).CreateADPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePeriodService_CreateADPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePeriodServiceServer).CreateADPackagePeriod(ctx, req.(*CreateADPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePeriodService_UpdateADPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateADPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePeriodServiceServer).UpdateADPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePeriodService_UpdateADPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePeriodServiceServer).UpdateADPackagePeriod(ctx, req.(*UpdateADPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePeriodService_DeleteADPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteADPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePeriodServiceServer).DeleteADPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePeriodService_DeleteADPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePeriodServiceServer).DeleteADPackagePeriod(ctx, req.(*DeleteADPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePeriodService_FindADPackagePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindADPackagePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePeriodServiceServer).FindADPackagePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePeriodService_FindADPackagePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePeriodServiceServer).FindADPackagePeriod(ctx, req.(*FindADPackagePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePeriodService_FindAllADPackagePeriods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllADPackagePeriodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePeriodServiceServer).FindAllADPackagePeriods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePeriodService_FindAllADPackagePeriods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePeriodServiceServer).FindAllADPackagePeriods(ctx, req.(*FindAllADPackagePeriodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ADPackagePeriodService_FindAllAvailableADPackagePeriods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailableADPackagePeriodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ADPackagePeriodServiceServer).FindAllAvailableADPackagePeriods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ADPackagePeriodService_FindAllAvailableADPackagePeriods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ADPackagePeriodServiceServer).FindAllAvailableADPackagePeriods(ctx, req.(*FindAllAvailableADPackagePeriodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ADPackagePeriodService_ServiceDesc is the grpc.ServiceDesc for ADPackagePeriodService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ADPackagePeriodService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ADPackagePeriodService",
	HandlerType: (*ADPackagePeriodServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createADPackagePeriod",
			Handler:    _ADPackagePeriodService_CreateADPackagePeriod_Handler,
		},
		{
			MethodName: "updateADPackagePeriod",
			Handler:    _ADPackagePeriodService_UpdateADPackagePeriod_Handler,
		},
		{
			MethodName: "deleteADPackagePeriod",
			Handler:    _ADPackagePeriodService_DeleteADPackagePeriod_Handler,
		},
		{
			MethodName: "findADPackagePeriod",
			Handler:    _ADPackagePeriodService_FindADPackagePeriod_Handler,
		},
		{
			MethodName: "findAllADPackagePeriods",
			Handler:    _ADPackagePeriodService_FindAllADPackagePeriods_Handler,
		},
		{
			MethodName: "findAllAvailableADPackagePeriods",
			Handler:    _ADPackagePeriodService_FindAllAvailableADPackagePeriods_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_ad_package_period.proto",
}
