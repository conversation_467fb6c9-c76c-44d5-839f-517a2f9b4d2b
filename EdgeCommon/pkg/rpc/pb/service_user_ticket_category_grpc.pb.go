// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_user_ticket_category.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserTicketCategoryService_CreateUserTicketCategory_FullMethodName             = "/pb.UserTicketCategoryService/createUserTicketCategory"
	UserTicketCategoryService_UpdateUserTicketCategory_FullMethodName             = "/pb.UserTicketCategoryService/updateUserTicketCategory"
	UserTicketCategoryService_DeleteUserTicketCategory_FullMethodName             = "/pb.UserTicketCategoryService/deleteUserTicketCategory"
	UserTicketCategoryService_FindAllUserTicketCategories_FullMethodName          = "/pb.UserTicketCategoryService/findAllUserTicketCategories"
	UserTicketCategoryService_FindAllAvailableUserTicketCategories_FullMethodName = "/pb.UserTicketCategoryService/findAllAvailableUserTicketCategories"
	UserTicketCategoryService_FindUserTicketCategory_FullMethodName               = "/pb.UserTicketCategoryService/findUserTicketCategory"
)

// UserTicketCategoryServiceClient is the client API for UserTicketCategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserTicketCategoryServiceClient interface {
	// 创建分类
	CreateUserTicketCategory(ctx context.Context, in *CreateUserTicketCategoryRequest, opts ...grpc.CallOption) (*CreateUserTicketCategoryResponse, error)
	// 修改分类
	UpdateUserTicketCategory(ctx context.Context, in *UpdateUserTicketCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 删除分类
	DeleteUserTicketCategory(ctx context.Context, in *DeleteUserTicketCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error)
	// 查找所有分类
	FindAllUserTicketCategories(ctx context.Context, in *FindAllUserTicketCategoriesRequest, opts ...grpc.CallOption) (*FindAllUserTicketCategoriesResponse, error)
	// 查找所有启用中的分类
	FindAllAvailableUserTicketCategories(ctx context.Context, in *FindAllAvailableUserTicketCategoriesRequest, opts ...grpc.CallOption) (*FindAllAvailableUserTicketCategoriesResponse, error)
	// 查询单个分类
	FindUserTicketCategory(ctx context.Context, in *FindUserTicketCategoryRequest, opts ...grpc.CallOption) (*FindUserTicketCategoryResponse, error)
}

type userTicketCategoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserTicketCategoryServiceClient(cc grpc.ClientConnInterface) UserTicketCategoryServiceClient {
	return &userTicketCategoryServiceClient{cc}
}

func (c *userTicketCategoryServiceClient) CreateUserTicketCategory(ctx context.Context, in *CreateUserTicketCategoryRequest, opts ...grpc.CallOption) (*CreateUserTicketCategoryResponse, error) {
	out := new(CreateUserTicketCategoryResponse)
	err := c.cc.Invoke(ctx, UserTicketCategoryService_CreateUserTicketCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketCategoryServiceClient) UpdateUserTicketCategory(ctx context.Context, in *UpdateUserTicketCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserTicketCategoryService_UpdateUserTicketCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketCategoryServiceClient) DeleteUserTicketCategory(ctx context.Context, in *DeleteUserTicketCategoryRequest, opts ...grpc.CallOption) (*RPCSuccess, error) {
	out := new(RPCSuccess)
	err := c.cc.Invoke(ctx, UserTicketCategoryService_DeleteUserTicketCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketCategoryServiceClient) FindAllUserTicketCategories(ctx context.Context, in *FindAllUserTicketCategoriesRequest, opts ...grpc.CallOption) (*FindAllUserTicketCategoriesResponse, error) {
	out := new(FindAllUserTicketCategoriesResponse)
	err := c.cc.Invoke(ctx, UserTicketCategoryService_FindAllUserTicketCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketCategoryServiceClient) FindAllAvailableUserTicketCategories(ctx context.Context, in *FindAllAvailableUserTicketCategoriesRequest, opts ...grpc.CallOption) (*FindAllAvailableUserTicketCategoriesResponse, error) {
	out := new(FindAllAvailableUserTicketCategoriesResponse)
	err := c.cc.Invoke(ctx, UserTicketCategoryService_FindAllAvailableUserTicketCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTicketCategoryServiceClient) FindUserTicketCategory(ctx context.Context, in *FindUserTicketCategoryRequest, opts ...grpc.CallOption) (*FindUserTicketCategoryResponse, error) {
	out := new(FindUserTicketCategoryResponse)
	err := c.cc.Invoke(ctx, UserTicketCategoryService_FindUserTicketCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTicketCategoryServiceServer is the server API for UserTicketCategoryService service.
// All implementations should embed UnimplementedUserTicketCategoryServiceServer
// for forward compatibility
type UserTicketCategoryServiceServer interface {
	// 创建分类
	CreateUserTicketCategory(context.Context, *CreateUserTicketCategoryRequest) (*CreateUserTicketCategoryResponse, error)
	// 修改分类
	UpdateUserTicketCategory(context.Context, *UpdateUserTicketCategoryRequest) (*RPCSuccess, error)
	// 删除分类
	DeleteUserTicketCategory(context.Context, *DeleteUserTicketCategoryRequest) (*RPCSuccess, error)
	// 查找所有分类
	FindAllUserTicketCategories(context.Context, *FindAllUserTicketCategoriesRequest) (*FindAllUserTicketCategoriesResponse, error)
	// 查找所有启用中的分类
	FindAllAvailableUserTicketCategories(context.Context, *FindAllAvailableUserTicketCategoriesRequest) (*FindAllAvailableUserTicketCategoriesResponse, error)
	// 查询单个分类
	FindUserTicketCategory(context.Context, *FindUserTicketCategoryRequest) (*FindUserTicketCategoryResponse, error)
}

// UnimplementedUserTicketCategoryServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserTicketCategoryServiceServer struct {
}

func (UnimplementedUserTicketCategoryServiceServer) CreateUserTicketCategory(context.Context, *CreateUserTicketCategoryRequest) (*CreateUserTicketCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserTicketCategory not implemented")
}
func (UnimplementedUserTicketCategoryServiceServer) UpdateUserTicketCategory(context.Context, *UpdateUserTicketCategoryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserTicketCategory not implemented")
}
func (UnimplementedUserTicketCategoryServiceServer) DeleteUserTicketCategory(context.Context, *DeleteUserTicketCategoryRequest) (*RPCSuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserTicketCategory not implemented")
}
func (UnimplementedUserTicketCategoryServiceServer) FindAllUserTicketCategories(context.Context, *FindAllUserTicketCategoriesRequest) (*FindAllUserTicketCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllUserTicketCategories not implemented")
}
func (UnimplementedUserTicketCategoryServiceServer) FindAllAvailableUserTicketCategories(context.Context, *FindAllAvailableUserTicketCategoriesRequest) (*FindAllAvailableUserTicketCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllAvailableUserTicketCategories not implemented")
}
func (UnimplementedUserTicketCategoryServiceServer) FindUserTicketCategory(context.Context, *FindUserTicketCategoryRequest) (*FindUserTicketCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserTicketCategory not implemented")
}

// UnsafeUserTicketCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserTicketCategoryServiceServer will
// result in compilation errors.
type UnsafeUserTicketCategoryServiceServer interface {
	mustEmbedUnimplementedUserTicketCategoryServiceServer()
}

func RegisterUserTicketCategoryServiceServer(s grpc.ServiceRegistrar, srv UserTicketCategoryServiceServer) {
	s.RegisterService(&UserTicketCategoryService_ServiceDesc, srv)
}

func _UserTicketCategoryService_CreateUserTicketCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserTicketCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketCategoryServiceServer).CreateUserTicketCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketCategoryService_CreateUserTicketCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketCategoryServiceServer).CreateUserTicketCategory(ctx, req.(*CreateUserTicketCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketCategoryService_UpdateUserTicketCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserTicketCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketCategoryServiceServer).UpdateUserTicketCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketCategoryService_UpdateUserTicketCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketCategoryServiceServer).UpdateUserTicketCategory(ctx, req.(*UpdateUserTicketCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketCategoryService_DeleteUserTicketCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserTicketCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketCategoryServiceServer).DeleteUserTicketCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketCategoryService_DeleteUserTicketCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketCategoryServiceServer).DeleteUserTicketCategory(ctx, req.(*DeleteUserTicketCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketCategoryService_FindAllUserTicketCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllUserTicketCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketCategoryServiceServer).FindAllUserTicketCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketCategoryService_FindAllUserTicketCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketCategoryServiceServer).FindAllUserTicketCategories(ctx, req.(*FindAllUserTicketCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketCategoryService_FindAllAvailableUserTicketCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAllAvailableUserTicketCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketCategoryServiceServer).FindAllAvailableUserTicketCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketCategoryService_FindAllAvailableUserTicketCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketCategoryServiceServer).FindAllAvailableUserTicketCategories(ctx, req.(*FindAllAvailableUserTicketCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTicketCategoryService_FindUserTicketCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUserTicketCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTicketCategoryServiceServer).FindUserTicketCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserTicketCategoryService_FindUserTicketCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTicketCategoryServiceServer).FindUserTicketCategory(ctx, req.(*FindUserTicketCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserTicketCategoryService_ServiceDesc is the grpc.ServiceDesc for UserTicketCategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserTicketCategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserTicketCategoryService",
	HandlerType: (*UserTicketCategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createUserTicketCategory",
			Handler:    _UserTicketCategoryService_CreateUserTicketCategory_Handler,
		},
		{
			MethodName: "updateUserTicketCategory",
			Handler:    _UserTicketCategoryService_UpdateUserTicketCategory_Handler,
		},
		{
			MethodName: "deleteUserTicketCategory",
			Handler:    _UserTicketCategoryService_DeleteUserTicketCategory_Handler,
		},
		{
			MethodName: "findAllUserTicketCategories",
			Handler:    _UserTicketCategoryService_FindAllUserTicketCategories_Handler,
		},
		{
			MethodName: "findAllAvailableUserTicketCategories",
			Handler:    _UserTicketCategoryService_FindAllAvailableUserTicketCategories_Handler,
		},
		{
			MethodName: "findUserTicketCategory",
			Handler:    _UserTicketCategoryService_FindUserTicketCategory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_user_ticket_category.proto",
}
