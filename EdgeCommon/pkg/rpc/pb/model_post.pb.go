// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_post.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 文章
type Post struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                         // ID
	PostCategoryId int64                  `protobuf:"varint,2,opt,name=postCategoryId,proto3" json:"postCategoryId,omitempty"` // 分类ID
	ProductCode    string                 `protobuf:"bytes,3,opt,name=productCode,proto3" json:"productCode,omitempty"`        // 产品代号
	Type           string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                      // 类型：normal, url
	Subject        string                 `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`                // 标题
	Url            string                 `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`                        // URL
	Body           string                 `protobuf:"bytes,7,opt,name=body,proto3" json:"body,omitempty"`                      // 内容
	CreatedAt      int64                  `protobuf:"varint,8,opt,name=createdAt,proto3" json:"createdAt,omitempty"`           // 创建时间
	IsPublished    bool                   `protobuf:"varint,9,opt,name=isPublished,proto3" json:"isPublished,omitempty"`       // 是否已发布
	PublishedAt    int64                  `protobuf:"varint,10,opt,name=publishedAt,proto3" json:"publishedAt,omitempty"`      // 发布时间
	PostCategory   *PostCategory          `protobuf:"bytes,30,opt,name=postCategory,proto3" json:"postCategory,omitempty"`     // 分类信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Post) Reset() {
	*x = Post{}
	mi := &file_models_model_post_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Post) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Post) ProtoMessage() {}

func (x *Post) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_post_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Post.ProtoReflect.Descriptor instead.
func (*Post) Descriptor() ([]byte, []int) {
	return file_models_model_post_proto_rawDescGZIP(), []int{0}
}

func (x *Post) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Post) GetPostCategoryId() int64 {
	if x != nil {
		return x.PostCategoryId
	}
	return 0
}

func (x *Post) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *Post) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Post) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *Post) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Post) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *Post) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Post) GetIsPublished() bool {
	if x != nil {
		return x.IsPublished
	}
	return false
}

func (x *Post) GetPublishedAt() int64 {
	if x != nil {
		return x.PublishedAt
	}
	return 0
}

func (x *Post) GetPostCategory() *PostCategory {
	if x != nil {
		return x.PostCategory
	}
	return nil
}

var File_models_model_post_proto protoreflect.FileDescriptor

const file_models_model_post_proto_rawDesc = "" +
	"\n" +
	"\x17models/model_post.proto\x12\x02pb\x1a models/model_post_category.proto\"\xcc\x02\n" +
	"\x04Post\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12&\n" +
	"\x0epostCategoryId\x18\x02 \x01(\x03R\x0epostCategoryId\x12 \n" +
	"\vproductCode\x18\x03 \x01(\tR\vproductCode\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x18\n" +
	"\asubject\x18\x05 \x01(\tR\asubject\x12\x10\n" +
	"\x03url\x18\x06 \x01(\tR\x03url\x12\x12\n" +
	"\x04body\x18\a \x01(\tR\x04body\x12\x1c\n" +
	"\tcreatedAt\x18\b \x01(\x03R\tcreatedAt\x12 \n" +
	"\visPublished\x18\t \x01(\bR\visPublished\x12 \n" +
	"\vpublishedAt\x18\n" +
	" \x01(\x03R\vpublishedAt\x124\n" +
	"\fpostCategory\x18\x1e \x01(\v2\x10.pb.PostCategoryR\fpostCategoryB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_post_proto_rawDescOnce sync.Once
	file_models_model_post_proto_rawDescData []byte
)

func file_models_model_post_proto_rawDescGZIP() []byte {
	file_models_model_post_proto_rawDescOnce.Do(func() {
		file_models_model_post_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_post_proto_rawDesc), len(file_models_model_post_proto_rawDesc)))
	})
	return file_models_model_post_proto_rawDescData
}

var file_models_model_post_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_post_proto_goTypes = []any{
	(*Post)(nil),         // 0: pb.Post
	(*PostCategory)(nil), // 1: pb.PostCategory
}
var file_models_model_post_proto_depIdxs = []int32{
	1, // 0: pb.Post.postCategory:type_name -> pb.PostCategory
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_post_proto_init() }
func file_models_model_post_proto_init() {
	if File_models_model_post_proto != nil {
		return
	}
	file_models_model_post_category_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_post_proto_rawDesc), len(file_models_model_post_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_post_proto_goTypes,
		DependencyIndexes: file_models_model_post_proto_depIdxs,
		MessageInfos:      file_models_model_post_proto_msgTypes,
	}.Build()
	File_models_model_post_proto = out.File
	file_models_model_post_proto_goTypes = nil
	file_models_model_post_proto_depIdxs = nil
}
