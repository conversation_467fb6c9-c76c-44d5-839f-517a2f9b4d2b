// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_traffic_bill.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户流量带宽子账单
type UserTrafficBill struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BillId                int64                  `protobuf:"varint,2,opt,name=billId,proto3" json:"billId,omitempty"`
	NodeRegionId          int64                  `protobuf:"varint,3,opt,name=nodeRegionId,proto3" json:"nodeRegionId,omitempty"`
	Amount                float64                `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty"`
	BandwidthMB           float64                `protobuf:"fixed64,5,opt,name=bandwidthMB,proto3" json:"bandwidthMB,omitempty"`
	BandwidthPercentile   int32                  `protobuf:"varint,6,opt,name=bandwidthPercentile,proto3" json:"bandwidthPercentile,omitempty"`
	TrafficGB             float64                `protobuf:"fixed64,7,opt,name=trafficGB,proto3" json:"trafficGB,omitempty"`
	TrafficPackageGB      float64                `protobuf:"fixed64,8,opt,name=trafficPackageGB,proto3" json:"trafficPackageGB,omitempty"`
	UserTrafficPackageIds []int64                `protobuf:"varint,9,rep,packed,name=userTrafficPackageIds,proto3" json:"userTrafficPackageIds,omitempty"`
	PricePerUnit          float64                `protobuf:"fixed64,10,opt,name=pricePerUnit,proto3" json:"pricePerUnit,omitempty"`
	PriceType             string                 `protobuf:"bytes,11,opt,name=priceType,proto3" json:"priceType,omitempty"`
	NodeRegion            *NodeRegion            `protobuf:"bytes,30,opt,name=nodeRegion,proto3" json:"nodeRegion,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UserTrafficBill) Reset() {
	*x = UserTrafficBill{}
	mi := &file_models_model_user_traffic_bill_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTrafficBill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTrafficBill) ProtoMessage() {}

func (x *UserTrafficBill) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_traffic_bill_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTrafficBill.ProtoReflect.Descriptor instead.
func (*UserTrafficBill) Descriptor() ([]byte, []int) {
	return file_models_model_user_traffic_bill_proto_rawDescGZIP(), []int{0}
}

func (x *UserTrafficBill) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserTrafficBill) GetBillId() int64 {
	if x != nil {
		return x.BillId
	}
	return 0
}

func (x *UserTrafficBill) GetNodeRegionId() int64 {
	if x != nil {
		return x.NodeRegionId
	}
	return 0
}

func (x *UserTrafficBill) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UserTrafficBill) GetBandwidthMB() float64 {
	if x != nil {
		return x.BandwidthMB
	}
	return 0
}

func (x *UserTrafficBill) GetBandwidthPercentile() int32 {
	if x != nil {
		return x.BandwidthPercentile
	}
	return 0
}

func (x *UserTrafficBill) GetTrafficGB() float64 {
	if x != nil {
		return x.TrafficGB
	}
	return 0
}

func (x *UserTrafficBill) GetTrafficPackageGB() float64 {
	if x != nil {
		return x.TrafficPackageGB
	}
	return 0
}

func (x *UserTrafficBill) GetUserTrafficPackageIds() []int64 {
	if x != nil {
		return x.UserTrafficPackageIds
	}
	return nil
}

func (x *UserTrafficBill) GetPricePerUnit() float64 {
	if x != nil {
		return x.PricePerUnit
	}
	return 0
}

func (x *UserTrafficBill) GetPriceType() string {
	if x != nil {
		return x.PriceType
	}
	return ""
}

func (x *UserTrafficBill) GetNodeRegion() *NodeRegion {
	if x != nil {
		return x.NodeRegion
	}
	return nil
}

var File_models_model_user_traffic_bill_proto protoreflect.FileDescriptor

const file_models_model_user_traffic_bill_proto_rawDesc = "" +
	"\n" +
	"$models/model_user_traffic_bill.proto\x12\x02pb\x1a\x1emodels/model_node_region.proto\"\xbb\x03\n" +
	"\x0fUserTrafficBill\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06billId\x18\x02 \x01(\x03R\x06billId\x12\"\n" +
	"\fnodeRegionId\x18\x03 \x01(\x03R\fnodeRegionId\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x01R\x06amount\x12 \n" +
	"\vbandwidthMB\x18\x05 \x01(\x01R\vbandwidthMB\x120\n" +
	"\x13bandwidthPercentile\x18\x06 \x01(\x05R\x13bandwidthPercentile\x12\x1c\n" +
	"\ttrafficGB\x18\a \x01(\x01R\ttrafficGB\x12*\n" +
	"\x10trafficPackageGB\x18\b \x01(\x01R\x10trafficPackageGB\x124\n" +
	"\x15userTrafficPackageIds\x18\t \x03(\x03R\x15userTrafficPackageIds\x12\"\n" +
	"\fpricePerUnit\x18\n" +
	" \x01(\x01R\fpricePerUnit\x12\x1c\n" +
	"\tpriceType\x18\v \x01(\tR\tpriceType\x12.\n" +
	"\n" +
	"nodeRegion\x18\x1e \x01(\v2\x0e.pb.NodeRegionR\n" +
	"nodeRegionB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_traffic_bill_proto_rawDescOnce sync.Once
	file_models_model_user_traffic_bill_proto_rawDescData []byte
)

func file_models_model_user_traffic_bill_proto_rawDescGZIP() []byte {
	file_models_model_user_traffic_bill_proto_rawDescOnce.Do(func() {
		file_models_model_user_traffic_bill_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_traffic_bill_proto_rawDesc), len(file_models_model_user_traffic_bill_proto_rawDesc)))
	})
	return file_models_model_user_traffic_bill_proto_rawDescData
}

var file_models_model_user_traffic_bill_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_traffic_bill_proto_goTypes = []any{
	(*UserTrafficBill)(nil), // 0: pb.UserTrafficBill
	(*NodeRegion)(nil),      // 1: pb.NodeRegion
}
var file_models_model_user_traffic_bill_proto_depIdxs = []int32{
	1, // 0: pb.UserTrafficBill.nodeRegion:type_name -> pb.NodeRegion
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_models_model_user_traffic_bill_proto_init() }
func file_models_model_user_traffic_bill_proto_init() {
	if File_models_model_user_traffic_bill_proto != nil {
		return
	}
	file_models_model_node_region_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_traffic_bill_proto_rawDesc), len(file_models_model_user_traffic_bill_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_traffic_bill_proto_goTypes,
		DependencyIndexes: file_models_model_user_traffic_bill_proto_depIdxs,
		MessageInfos:      file_models_model_user_traffic_bill_proto_msgTypes,
	}.Build()
	File_models_model_user_traffic_bill_proto = out.File
	file_models_model_user_traffic_bill_proto_goTypes = nil
	file_models_model_user_traffic_bill_proto_depIdxs = nil
}
