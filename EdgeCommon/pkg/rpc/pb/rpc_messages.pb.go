// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/rpc_messages.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 操作成功
type RPCSuccess struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPCSuccess) Reset() {
	*x = RPCSuccess{}
	mi := &file_models_rpc_messages_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPCSuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCSuccess) ProtoMessage() {}

func (x *RPCSuccess) ProtoReflect() protoreflect.Message {
	mi := &file_models_rpc_messages_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCSuccess.ProtoReflect.Descriptor instead.
func (*RPCSuccess) Descriptor() ([]byte, []int) {
	return file_models_rpc_messages_proto_rawDescGZIP(), []int{0}
}

// 返回数量
type RPCCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPCCountResponse) Reset() {
	*x = RPCCountResponse{}
	mi := &file_models_rpc_messages_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPCCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCCountResponse) ProtoMessage() {}

func (x *RPCCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_models_rpc_messages_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCCountResponse.ProtoReflect.Descriptor instead.
func (*RPCCountResponse) Descriptor() ([]byte, []int) {
	return file_models_rpc_messages_proto_rawDescGZIP(), []int{1}
}

func (x *RPCCountResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 是否存在
type RPCExists struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Exists        bool                   `protobuf:"varint,1,opt,name=exists,proto3" json:"exists,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPCExists) Reset() {
	*x = RPCExists{}
	mi := &file_models_rpc_messages_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPCExists) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPCExists) ProtoMessage() {}

func (x *RPCExists) ProtoReflect() protoreflect.Message {
	mi := &file_models_rpc_messages_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPCExists.ProtoReflect.Descriptor instead.
func (*RPCExists) Descriptor() ([]byte, []int) {
	return file_models_rpc_messages_proto_rawDescGZIP(), []int{2}
}

func (x *RPCExists) GetExists() bool {
	if x != nil {
		return x.Exists
	}
	return false
}

var File_models_rpc_messages_proto protoreflect.FileDescriptor

const file_models_rpc_messages_proto_rawDesc = "" +
	"\n" +
	"\x19models/rpc_messages.proto\x12\x02pb\"\f\n" +
	"\n" +
	"RPCSuccess\"(\n" +
	"\x10RPCCountResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"#\n" +
	"\tRPCExists\x12\x16\n" +
	"\x06exists\x18\x01 \x01(\bR\x06existsB\x06Z\x04./pbb\x06proto3"

var (
	file_models_rpc_messages_proto_rawDescOnce sync.Once
	file_models_rpc_messages_proto_rawDescData []byte
)

func file_models_rpc_messages_proto_rawDescGZIP() []byte {
	file_models_rpc_messages_proto_rawDescOnce.Do(func() {
		file_models_rpc_messages_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_rpc_messages_proto_rawDesc), len(file_models_rpc_messages_proto_rawDesc)))
	})
	return file_models_rpc_messages_proto_rawDescData
}

var file_models_rpc_messages_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_models_rpc_messages_proto_goTypes = []any{
	(*RPCSuccess)(nil),       // 0: pb.RPCSuccess
	(*RPCCountResponse)(nil), // 1: pb.RPCCountResponse
	(*RPCExists)(nil),        // 2: pb.RPCExists
}
var file_models_rpc_messages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_models_rpc_messages_proto_init() }
func file_models_rpc_messages_proto_init() {
	if File_models_rpc_messages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_rpc_messages_proto_rawDesc), len(file_models_rpc_messages_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_rpc_messages_proto_goTypes,
		DependencyIndexes: file_models_rpc_messages_proto_depIdxs,
		MessageInfos:      file_models_rpc_messages_proto_msgTypes,
	}.Build()
	File_models_rpc_messages_proto = out.File
	file_models_rpc_messages_proto_goTypes = nil
	file_models_rpc_messages_proto_depIdxs = nil
}
