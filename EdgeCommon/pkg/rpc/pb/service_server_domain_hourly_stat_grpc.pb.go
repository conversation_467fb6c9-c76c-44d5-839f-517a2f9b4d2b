// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.6.1
// source: service_server_domain_hourly_stat.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerDomainHourlyStatService_ListTopServerDomainStatsWithServerId_FullMethodName = "/pb.ServerDomainHourlyStatService/listTopServerDomainStatsWithServerId"
)

// ServerDomainHourlyStatServiceClient is the client API for ServerDomainHourlyStatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerDomainHourlyStatServiceClient interface {
	// 读取服务的域名排行
	ListTopServerDomainStatsWithServerId(ctx context.Context, in *ListTopServerDomainStatsWithServerIdRequest, opts ...grpc.CallOption) (*ListTopServerDomainStatsWithServerIdResponse, error)
}

type serverDomainHourlyStatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerDomainHourlyStatServiceClient(cc grpc.ClientConnInterface) ServerDomainHourlyStatServiceClient {
	return &serverDomainHourlyStatServiceClient{cc}
}

func (c *serverDomainHourlyStatServiceClient) ListTopServerDomainStatsWithServerId(ctx context.Context, in *ListTopServerDomainStatsWithServerIdRequest, opts ...grpc.CallOption) (*ListTopServerDomainStatsWithServerIdResponse, error) {
	out := new(ListTopServerDomainStatsWithServerIdResponse)
	err := c.cc.Invoke(ctx, ServerDomainHourlyStatService_ListTopServerDomainStatsWithServerId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerDomainHourlyStatServiceServer is the server API for ServerDomainHourlyStatService service.
// All implementations should embed UnimplementedServerDomainHourlyStatServiceServer
// for forward compatibility
type ServerDomainHourlyStatServiceServer interface {
	// 读取服务的域名排行
	ListTopServerDomainStatsWithServerId(context.Context, *ListTopServerDomainStatsWithServerIdRequest) (*ListTopServerDomainStatsWithServerIdResponse, error)
}

// UnimplementedServerDomainHourlyStatServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerDomainHourlyStatServiceServer struct {
}

func (UnimplementedServerDomainHourlyStatServiceServer) ListTopServerDomainStatsWithServerId(context.Context, *ListTopServerDomainStatsWithServerIdRequest) (*ListTopServerDomainStatsWithServerIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTopServerDomainStatsWithServerId not implemented")
}

// UnsafeServerDomainHourlyStatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerDomainHourlyStatServiceServer will
// result in compilation errors.
type UnsafeServerDomainHourlyStatServiceServer interface {
	mustEmbedUnimplementedServerDomainHourlyStatServiceServer()
}

func RegisterServerDomainHourlyStatServiceServer(s grpc.ServiceRegistrar, srv ServerDomainHourlyStatServiceServer) {
	s.RegisterService(&ServerDomainHourlyStatService_ServiceDesc, srv)
}

func _ServerDomainHourlyStatService_ListTopServerDomainStatsWithServerId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTopServerDomainStatsWithServerIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerDomainHourlyStatServiceServer).ListTopServerDomainStatsWithServerId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerDomainHourlyStatService_ListTopServerDomainStatsWithServerId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerDomainHourlyStatServiceServer).ListTopServerDomainStatsWithServerId(ctx, req.(*ListTopServerDomainStatsWithServerIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerDomainHourlyStatService_ServiceDesc is the grpc.ServiceDesc for ServerDomainHourlyStatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerDomainHourlyStatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerDomainHourlyStatService",
	HandlerType: (*ServerDomainHourlyStatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "listTopServerDomainStatsWithServerId",
			Handler:    _ServerDomainHourlyStatService_ListTopServerDomainStatsWithServerId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service_server_domain_hourly_stat.proto",
}
