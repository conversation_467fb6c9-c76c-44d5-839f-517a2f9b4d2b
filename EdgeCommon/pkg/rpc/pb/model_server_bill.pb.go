// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_server_bill.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerBill struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	Id                       int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId                   int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	ServerId                 int64                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`
	Amount                   float32                `protobuf:"fixed32,4,opt,name=amount,proto3" json:"amount,omitempty"`
	CreatedAt                int64                  `protobuf:"varint,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UserPlanId               int64                  `protobuf:"varint,6,opt,name=userPlanId,proto3" json:"userPlanId,omitempty"`
	PlanId                   int64                  `protobuf:"varint,7,opt,name=planId,proto3" json:"planId,omitempty"`
	TotalTrafficBytes        int64                  `protobuf:"varint,8,opt,name=totalTrafficBytes,proto3" json:"totalTrafficBytes,omitempty"`
	BandwidthPercentileBytes int64                  `protobuf:"varint,9,opt,name=bandwidthPercentileBytes,proto3" json:"bandwidthPercentileBytes,omitempty"`
	BandwidthPercentile      int32                  `protobuf:"varint,10,opt,name=bandwidthPercentile,proto3" json:"bandwidthPercentile,omitempty"`
	PriceType                string                 `protobuf:"bytes,11,opt,name=priceType,proto3" json:"priceType,omitempty"`
	UserPlan                 *UserPlan              `protobuf:"bytes,30,opt,name=userPlan,proto3" json:"userPlan,omitempty"`
	Plan                     *Plan                  `protobuf:"bytes,31,opt,name=plan,proto3" json:"plan,omitempty"`
	User                     *User                  `protobuf:"bytes,32,opt,name=user,proto3" json:"user,omitempty"`
	Server                   *Server                `protobuf:"bytes,33,opt,name=server,proto3" json:"server,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *ServerBill) Reset() {
	*x = ServerBill{}
	mi := &file_models_model_server_bill_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerBill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerBill) ProtoMessage() {}

func (x *ServerBill) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_server_bill_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerBill.ProtoReflect.Descriptor instead.
func (*ServerBill) Descriptor() ([]byte, []int) {
	return file_models_model_server_bill_proto_rawDescGZIP(), []int{0}
}

func (x *ServerBill) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServerBill) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ServerBill) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ServerBill) GetAmount() float32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ServerBill) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ServerBill) GetUserPlanId() int64 {
	if x != nil {
		return x.UserPlanId
	}
	return 0
}

func (x *ServerBill) GetPlanId() int64 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *ServerBill) GetTotalTrafficBytes() int64 {
	if x != nil {
		return x.TotalTrafficBytes
	}
	return 0
}

func (x *ServerBill) GetBandwidthPercentileBytes() int64 {
	if x != nil {
		return x.BandwidthPercentileBytes
	}
	return 0
}

func (x *ServerBill) GetBandwidthPercentile() int32 {
	if x != nil {
		return x.BandwidthPercentile
	}
	return 0
}

func (x *ServerBill) GetPriceType() string {
	if x != nil {
		return x.PriceType
	}
	return ""
}

func (x *ServerBill) GetUserPlan() *UserPlan {
	if x != nil {
		return x.UserPlan
	}
	return nil
}

func (x *ServerBill) GetPlan() *Plan {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *ServerBill) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ServerBill) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

var File_models_model_server_bill_proto protoreflect.FileDescriptor

const file_models_model_server_bill_proto_rawDesc = "" +
	"\n" +
	"\x1emodels/model_server_bill.proto\x12\x02pb\x1a\x17models/model_plan.proto\x1a\x1cmodels/model_user_plan.proto\x1a\x17models/model_user.proto\x1a\x19models/model_server.proto\"\x82\x04\n" +
	"\n" +
	"ServerBill\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x03R\bserverId\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x02R\x06amount\x12\x1c\n" +
	"\tcreatedAt\x18\x05 \x01(\x03R\tcreatedAt\x12\x1e\n" +
	"\n" +
	"userPlanId\x18\x06 \x01(\x03R\n" +
	"userPlanId\x12\x16\n" +
	"\x06planId\x18\a \x01(\x03R\x06planId\x12,\n" +
	"\x11totalTrafficBytes\x18\b \x01(\x03R\x11totalTrafficBytes\x12:\n" +
	"\x18bandwidthPercentileBytes\x18\t \x01(\x03R\x18bandwidthPercentileBytes\x120\n" +
	"\x13bandwidthPercentile\x18\n" +
	" \x01(\x05R\x13bandwidthPercentile\x12\x1c\n" +
	"\tpriceType\x18\v \x01(\tR\tpriceType\x12(\n" +
	"\buserPlan\x18\x1e \x01(\v2\f.pb.UserPlanR\buserPlan\x12\x1c\n" +
	"\x04plan\x18\x1f \x01(\v2\b.pb.PlanR\x04plan\x12\x1c\n" +
	"\x04user\x18  \x01(\v2\b.pb.UserR\x04user\x12\"\n" +
	"\x06server\x18! \x01(\v2\n" +
	".pb.ServerR\x06serverB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_server_bill_proto_rawDescOnce sync.Once
	file_models_model_server_bill_proto_rawDescData []byte
)

func file_models_model_server_bill_proto_rawDescGZIP() []byte {
	file_models_model_server_bill_proto_rawDescOnce.Do(func() {
		file_models_model_server_bill_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_server_bill_proto_rawDesc), len(file_models_model_server_bill_proto_rawDesc)))
	})
	return file_models_model_server_bill_proto_rawDescData
}

var file_models_model_server_bill_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_server_bill_proto_goTypes = []any{
	(*ServerBill)(nil), // 0: pb.ServerBill
	(*UserPlan)(nil),   // 1: pb.UserPlan
	(*Plan)(nil),       // 2: pb.Plan
	(*User)(nil),       // 3: pb.User
	(*Server)(nil),     // 4: pb.Server
}
var file_models_model_server_bill_proto_depIdxs = []int32{
	1, // 0: pb.ServerBill.userPlan:type_name -> pb.UserPlan
	2, // 1: pb.ServerBill.plan:type_name -> pb.Plan
	3, // 2: pb.ServerBill.user:type_name -> pb.User
	4, // 3: pb.ServerBill.server:type_name -> pb.Server
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_models_model_server_bill_proto_init() }
func file_models_model_server_bill_proto_init() {
	if File_models_model_server_bill_proto != nil {
		return
	}
	file_models_model_plan_proto_init()
	file_models_model_user_plan_proto_init()
	file_models_model_user_proto_init()
	file_models_model_server_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_server_bill_proto_rawDesc), len(file_models_model_server_bill_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_server_bill_proto_goTypes,
		DependencyIndexes: file_models_model_server_bill_proto_depIdxs,
		MessageInfos:      file_models_model_server_bill_proto_msgTypes,
	}.Build()
	File_models_model_server_bill_proto = out.File
	file_models_model_server_bill_proto_goTypes = nil
	file_models_model_server_bill_proto_depIdxs = nil
}
