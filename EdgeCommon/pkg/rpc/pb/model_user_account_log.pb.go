// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.6.1
// source: models/model_user_account_log.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserAccountLog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	UserAccountId int64                  `protobuf:"varint,3,opt,name=userAccountId,proto3" json:"userAccountId,omitempty"`
	Delta         float64                `protobuf:"fixed64,4,opt,name=delta,proto3" json:"delta,omitempty"`
	DeltaFrozen   float64                `protobuf:"fixed64,5,opt,name=deltaFrozen,proto3" json:"deltaFrozen,omitempty"`
	Total         float64                `protobuf:"fixed64,6,opt,name=total,proto3" json:"total,omitempty"`
	TotalFrozen   float64                `protobuf:"fixed64,7,opt,name=totalFrozen,proto3" json:"totalFrozen,omitempty"`
	EventType     string                 `protobuf:"bytes,8,opt,name=eventType,proto3" json:"eventType,omitempty"`
	Description   string                 `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,10,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	ParamsJSON    []byte                 `protobuf:"bytes,11,opt,name=paramsJSON,proto3" json:"paramsJSON,omitempty"`
	User          *User                  `protobuf:"bytes,30,opt,name=user,proto3" json:"user,omitempty"`
	UserAccount   *UserAccount           `protobuf:"bytes,31,opt,name=userAccount,proto3" json:"userAccount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAccountLog) Reset() {
	*x = UserAccountLog{}
	mi := &file_models_model_user_account_log_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccountLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccountLog) ProtoMessage() {}

func (x *UserAccountLog) ProtoReflect() protoreflect.Message {
	mi := &file_models_model_user_account_log_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccountLog.ProtoReflect.Descriptor instead.
func (*UserAccountLog) Descriptor() ([]byte, []int) {
	return file_models_model_user_account_log_proto_rawDescGZIP(), []int{0}
}

func (x *UserAccountLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAccountLog) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserAccountLog) GetUserAccountId() int64 {
	if x != nil {
		return x.UserAccountId
	}
	return 0
}

func (x *UserAccountLog) GetDelta() float64 {
	if x != nil {
		return x.Delta
	}
	return 0
}

func (x *UserAccountLog) GetDeltaFrozen() float64 {
	if x != nil {
		return x.DeltaFrozen
	}
	return 0
}

func (x *UserAccountLog) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *UserAccountLog) GetTotalFrozen() float64 {
	if x != nil {
		return x.TotalFrozen
	}
	return 0
}

func (x *UserAccountLog) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *UserAccountLog) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UserAccountLog) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserAccountLog) GetParamsJSON() []byte {
	if x != nil {
		return x.ParamsJSON
	}
	return nil
}

func (x *UserAccountLog) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UserAccountLog) GetUserAccount() *UserAccount {
	if x != nil {
		return x.UserAccount
	}
	return nil
}

var File_models_model_user_account_log_proto protoreflect.FileDescriptor

const file_models_model_user_account_log_proto_rawDesc = "" +
	"\n" +
	"#models/model_user_account_log.proto\x12\x02pb\x1a\x17models/model_user.proto\x1a\x1fmodels/model_user_account.proto\"\x9d\x03\n" +
	"\x0eUserAccountLog\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x03R\x06userId\x12$\n" +
	"\ruserAccountId\x18\x03 \x01(\x03R\ruserAccountId\x12\x14\n" +
	"\x05delta\x18\x04 \x01(\x01R\x05delta\x12 \n" +
	"\vdeltaFrozen\x18\x05 \x01(\x01R\vdeltaFrozen\x12\x14\n" +
	"\x05total\x18\x06 \x01(\x01R\x05total\x12 \n" +
	"\vtotalFrozen\x18\a \x01(\x01R\vtotalFrozen\x12\x1c\n" +
	"\teventType\x18\b \x01(\tR\teventType\x12 \n" +
	"\vdescription\x18\t \x01(\tR\vdescription\x12\x1c\n" +
	"\tcreatedAt\x18\n" +
	" \x01(\x03R\tcreatedAt\x12\x1e\n" +
	"\n" +
	"paramsJSON\x18\v \x01(\fR\n" +
	"paramsJSON\x12\x1c\n" +
	"\x04user\x18\x1e \x01(\v2\b.pb.UserR\x04user\x121\n" +
	"\vuserAccount\x18\x1f \x01(\v2\x0f.pb.UserAccountR\vuserAccountB\x06Z\x04./pbb\x06proto3"

var (
	file_models_model_user_account_log_proto_rawDescOnce sync.Once
	file_models_model_user_account_log_proto_rawDescData []byte
)

func file_models_model_user_account_log_proto_rawDescGZIP() []byte {
	file_models_model_user_account_log_proto_rawDescOnce.Do(func() {
		file_models_model_user_account_log_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_models_model_user_account_log_proto_rawDesc), len(file_models_model_user_account_log_proto_rawDesc)))
	})
	return file_models_model_user_account_log_proto_rawDescData
}

var file_models_model_user_account_log_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_models_model_user_account_log_proto_goTypes = []any{
	(*UserAccountLog)(nil), // 0: pb.UserAccountLog
	(*User)(nil),           // 1: pb.User
	(*UserAccount)(nil),    // 2: pb.UserAccount
}
var file_models_model_user_account_log_proto_depIdxs = []int32{
	1, // 0: pb.UserAccountLog.user:type_name -> pb.User
	2, // 1: pb.UserAccountLog.userAccount:type_name -> pb.UserAccount
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_models_model_user_account_log_proto_init() }
func file_models_model_user_account_log_proto_init() {
	if File_models_model_user_account_log_proto != nil {
		return
	}
	file_models_model_user_proto_init()
	file_models_model_user_account_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_models_model_user_account_log_proto_rawDesc), len(file_models_model_user_account_log_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_models_model_user_account_log_proto_goTypes,
		DependencyIndexes: file_models_model_user_account_log_proto_depIdxs,
		MessageInfos:      file_models_model_user_account_log_proto_msgTypes,
	}.Build()
	File_models_model_user_account_log_proto = out.File
	file_models_model_user_account_log_proto_goTypes = nil
	file_models_model_user_account_log_proto_depIdxs = nil
}
