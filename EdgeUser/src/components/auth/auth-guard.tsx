import { useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useAuth } from '@/context/auth-context'

interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AuthGuard({ children, fallback = null }: AuthGuardProps) {
  const navigate = useNavigate()
  const { user, accessToken, isAuthenticated, isLoading } = useAuth()

  useEffect(() => {
    // 等待加载完成后再进行重定向判断
    if (!isLoading && !isAuthenticated) {
      // 保存当前路径用于登录后重定向
      const currentPath = window.location.pathname + window.location.search + window.location.hash

      // 跳转到登录页面
      navigate({
        to: '/sign-in',
        search: {
          redirect: currentPath !== '/sign-in' ? currentPath : undefined,
        },
      })
    }
  }, [isAuthenticated, isLoading, navigate])

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return fallback || <div>正在验证登录状态...</div>
  }

  // 如果未登录，显示fallback或null
  if (!isAuthenticated) {
    return fallback
  }

  return <>{children}</>
}
