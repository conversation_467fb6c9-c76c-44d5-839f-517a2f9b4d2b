import { createContext, useContext, useEffect, ReactNode, useState } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { authService } from '@/lib/api/auth'

interface AuthContextType {
  user: any | null
  accessToken: string
  isAuthenticated: boolean
  login: (token: string, userData: any) => void
  logout: () => void
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { user, accessToken, setUser, setAccessToken, reset } = useAuthStore(
    (state) => state.auth
  )
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!accessToken

  const login = (token: string, userData: any) => {
    setAccessToken(token)
    setUser(userData)
  }

  const logout = async () => {
    try {
      // 调用后端登出接口
      await authService.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 无论后端请求是否成功，都清除本地状态
      reset()
    }
  }

  // 应用启动时验证会话状态
  useEffect(() => {
    const validateSession = async () => {
      try {
        setIsLoading(true)

        // 如果本地已有用户信息，先验证会话是否仍然有效
        const sessionResult = await authService.validateSession()

        if (sessionResult.valid && sessionResult.user_id) {
          // 会话有效，但如果本地没有用户信息，需要重建
          if (!user && sessionResult.user_id) {
            // 创建基本用户信息（可以后续通过其他API获取完整信息）
            const userData = {
              accountNo: sessionResult.user_id.toString(),
              email: '',
              username: '',
              userId: sessionResult.user_id,
              role: ['user'],
              exp: Date.now() + 24 * 60 * 60 * 1000
            }

            // 生成访问令牌
            const token = authService.generateAccessToken(sessionResult.user_id)

            setUser(userData)
            setAccessToken(token)
          }
        } else {
          // 会话无效，清除本地状态
          if (user || accessToken) {
            reset()
          }
        }
      } catch (error) {
        console.error('会话验证失败:', error)
        // 验证失败时清除本地状态
        if (user || accessToken) {
          reset()
        }
      } finally {
        setIsLoading(false)
      }
    }

    validateSession()
  }, []) // 只在组件挂载时执行一次

  const value: AuthContextType = {
    user,
    accessToken,
    isAuthenticated,
    login,
    logout,
    isLoading,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// 创建一个hook用于需要认证的组件
export function useRequireAuth() {
  const auth = useAuth()
  
  if (!auth.isAuthenticated) {
    throw new Error('This component requires authentication')
  }
  
  return auth
} 
