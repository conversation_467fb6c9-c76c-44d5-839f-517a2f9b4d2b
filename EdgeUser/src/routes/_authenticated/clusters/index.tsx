import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { IconNetwork, IconPlus, IconSettings, IconTrash, IconServer, IconChartBar } from '@tabler/icons-react'

export const Route = createFileRoute('/_authenticated/clusters/')({
  component: ClustersPage,
})

function ClustersPage() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>Cluster Management</h1>
            <p className='text-muted-foreground'>
              Manage your enterprise clusters and their configurations
            </p>
          </div>
          <Button>
            <IconPlus className='mr-2 h-4 w-4' />
            Create Cluster
          </Button>
        </div>

        <div className='grid gap-6'>
          {/* Cluster Overview Cards */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Clusters</CardTitle>
                <IconNetwork className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>12</div>
                <p className='text-xs text-muted-foreground'>
                  +2 from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Active Clusters</CardTitle>
                <IconNetwork className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>10</div>
                <p className='text-xs text-muted-foreground'>
                  83% uptime
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Nodes</CardTitle>
                <IconServer className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>156</div>
                <p className='text-xs text-muted-foreground'>
                  Across all clusters
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Resource Usage</CardTitle>
                <IconChartBar className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>67%</div>
                <p className='text-xs text-muted-foreground'>
                  Average utilization
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Cluster List */}
          <Card>
            <CardHeader>
              <CardTitle>Enterprise Clusters</CardTitle>
              <CardDescription>
                Manage and monitor your cluster infrastructure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockClusters.map((cluster) => (
                  <div
                    key={cluster.id}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='flex items-center space-x-4'>
                      <div className={`h-3 w-3 rounded-full ${
                        cluster.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <div>
                        <h3 className='font-medium'>{cluster.name}</h3>
                        <p className='text-sm text-muted-foreground'>
                          {cluster.region} • {cluster.nodes} nodes
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Button variant='outline' size='sm'>
                        <IconSettings className='h-4 w-4' />
                      </Button>
                      <Button variant='outline' size='sm'>
                        <IconTrash className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: '/clusters',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Configuration',
    href: '/clusters/config',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Monitoring',
    href: '/clusters/monitoring',
    isActive: false,
    disabled: true,
  },
]

const mockClusters = [
  {
    id: '1',
    name: 'Production Cluster',
    region: 'US-East',
    nodes: 24,
    status: 'active',
  },
  {
    id: '2',
    name: 'Development Cluster',
    region: 'US-West',
    nodes: 8,
    status: 'active',
  },
  {
    id: '3',
    name: 'Testing Cluster',
    region: 'EU-Central',
    nodes: 4,
    status: 'inactive',
  },
]
