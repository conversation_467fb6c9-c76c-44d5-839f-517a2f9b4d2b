import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { IconCreditCard, IconDownload, IconEye, IconWallet } from '@tabler/icons-react'

export const Route = createFileRoute('/_authenticated/billing/')({
  component: BillingPage,
})

function BillingPage() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>Billing Management</h1>
            <p className='text-muted-foreground'>
              Monitor your enterprise billing, usage, and payment information
            </p>
          </div>
          <Button>
            <IconDownload className='mr-2 h-4 w-4' />
            Download Invoice
          </Button>
        </div>

        <div className='grid gap-6'>
          {/* Billing Overview */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Current Balance</CardTitle>
                <IconWallet className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>$2,450.00</div>
                <p className='text-xs text-muted-foreground'>
                  Available credit
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>This Month</CardTitle>
                <IconCreditCard className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>$12,450.00</div>
                <p className='text-xs text-muted-foreground'>
                  Current usage
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Last Invoice</CardTitle>
                <IconCreditCard className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>$11,230.00</div>
                <p className='text-xs text-muted-foreground'>
                  Paid on Nov 1, 2024
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Next Payment</CardTitle>
                <IconCreditCard className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>Dec 1</div>
                <p className='text-xs text-muted-foreground'>
                  Auto-payment enabled
                </p>
              </CardContent>
            </Card>
          </div>

          <div className='grid gap-6 md:grid-cols-2'>
            {/* Account Information */}
            <Card>
              <CardHeader>
                <CardTitle>Account Information</CardTitle>
                <CardDescription>
                  Your enterprise billing account details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex justify-between'>
                    <span className='text-sm font-medium'>Account ID</span>
                    <span className='text-sm'>ENT-2024-001234</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm font-medium'>Billing Plan</span>
                    <span className='text-sm'>Enterprise Premium</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm font-medium'>Payment Method</span>
                    <span className='text-sm'>**** **** **** 4567</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm font-medium'>Billing Cycle</span>
                    <span className='text-sm'>Monthly</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm font-medium'>Currency</span>
                    <span className='text-sm'>USD</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Usage Quotas */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Quotas</CardTitle>
                <CardDescription>
                  Current usage against your plan limits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {usageQuotas.map((quota) => (
                    <div key={quota.name} className='space-y-2'>
                      <div className='flex justify-between text-sm'>
                        <span className='font-medium'>{quota.name}</span>
                        <span>{quota.used} / {quota.limit}</span>
                      </div>
                      <div className='w-full bg-gray-200 rounded-full h-2'>
                        <div
                          className={`h-2 rounded-full ${
                            quota.percentage > 90 ? 'bg-red-500' :
                            quota.percentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${quota.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Bills */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Bills</CardTitle>
              <CardDescription>
                Your recent billing history and invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {recentBills.map((bill) => (
                  <div
                    key={bill.id}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='flex items-center space-x-4'>
                      <div className={`h-3 w-3 rounded-full ${
                        bill.status === 'paid' ? 'bg-green-500' : 
                        bill.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />
                      <div>
                        <h3 className='font-medium'>Invoice #{bill.invoiceNumber}</h3>
                        <p className='text-sm text-muted-foreground'>
                          {bill.period} • {bill.status}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center space-x-4'>
                      <div className='text-right'>
                        <p className='font-medium'>${bill.amount}</p>
                        <p className='text-sm text-muted-foreground'>{bill.dueDate}</p>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <Button variant='outline' size='sm'>
                          <IconEye className='h-4 w-4' />
                        </Button>
                        <Button variant='outline' size='sm'>
                          <IconDownload className='h-4 w-4' />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Usage Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Current Month Usage</CardTitle>
              <CardDescription>
                Detailed breakdown of your current billing period usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {usageBreakdown.map((item) => (
                  <div key={item.service} className='flex items-center justify-between'>
                    <div>
                      <p className='text-sm font-medium'>{item.service}</p>
                      <p className='text-xs text-muted-foreground'>{item.description}</p>
                    </div>
                    <div className='text-right'>
                      <p className='text-sm font-bold'>${item.cost}</p>
                      <p className='text-xs text-muted-foreground'>{item.usage}</p>
                    </div>
                  </div>
                ))}
                <div className='border-t pt-4'>
                  <div className='flex items-center justify-between font-medium'>
                    <span>Total</span>
                    <span>$12,450.00</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: '/billing',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Invoices',
    href: '/billing/invoices',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Usage',
    href: '/billing/usage',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Settings',
    href: '/billing/settings',
    isActive: false,
    disabled: true,
  },
]

const usageQuotas = [
  { name: 'Bandwidth', used: '2.4 TB', limit: '5 TB', percentage: 48 },
  { name: 'Requests', used: '1.2M', limit: '2M', percentage: 60 },
  { name: 'Storage', used: '450 GB', limit: '1 TB', percentage: 45 },
  { name: 'Node Usage Rights', used: '156', limit: '200', percentage: 78 },
]

const recentBills = [
  {
    id: '1',
    invoiceNumber: 'INV-2024-11-001',
    period: 'November 2024',
    amount: '11,230.00',
    dueDate: 'Dec 1, 2024',
    status: 'paid',
  },
  {
    id: '2',
    invoiceNumber: 'INV-2024-10-001',
    period: 'October 2024',
    amount: '10,890.00',
    dueDate: 'Nov 1, 2024',
    status: 'paid',
  },
  {
    id: '3',
    invoiceNumber: 'INV-2024-09-001',
    period: 'September 2024',
    amount: '12,150.00',
    dueDate: 'Oct 1, 2024',
    status: 'paid',
  },
]

const usageBreakdown = [
  {
    service: 'CDN Bandwidth',
    description: '2.4 TB transferred',
    usage: '2.4 TB',
    cost: '7,200.00',
  },
  {
    service: 'Node Usage Rights',
    description: '156 active licenses',
    usage: '156 nodes',
    cost: '4,680.00',
  },
  {
    service: 'API Requests',
    description: '1.2M requests processed',
    usage: '1.2M requests',
    cost: '360.00',
  },
  {
    service: 'Storage',
    description: '450 GB stored',
    usage: '450 GB',
    cost: '210.00',
  },
]
