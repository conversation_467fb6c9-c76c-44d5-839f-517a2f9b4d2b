import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { IconServer, IconShoppingCart, IconEye, IconCpu } from '@tabler/icons-react'

export const Route = createFileRoute('/_authenticated/nodes/')({
  component: NodesPage,
})

function NodesPage() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>Node Management</h1>
            <p className='text-muted-foreground'>
              Purchase and manage node usage rights for your enterprise
            </p>
          </div>
          <Button>
            <IconShoppingCart className='mr-2 h-4 w-4' />
            Purchase Usage Rights
          </Button>
        </div>

        <div className='grid gap-6'>
          {/* Node Overview Cards */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Available Nodes</CardTitle>
                <IconServer className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>248</div>
                <p className='text-xs text-muted-foreground'>
                  Across all regions
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Usage Rights</CardTitle>
                <IconShoppingCart className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>156</div>
                <p className='text-xs text-muted-foreground'>
                  Active licenses
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Utilization</CardTitle>
                <IconCpu className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>73%</div>
                <p className='text-xs text-muted-foreground'>
                  Average usage
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Monthly Cost</CardTitle>
                <IconShoppingCart className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>$12,450</div>
                <p className='text-xs text-muted-foreground'>
                  Current billing period
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Available Nodes */}
          <Card>
            <CardHeader>
              <CardTitle>Available Nodes</CardTitle>
              <CardDescription>
                Browse and purchase usage rights for edge nodes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
                {mockNodes.map((node) => (
                  <div
                    key={node.id}
                    className='rounded-lg border p-4'
                  >
                    <div className='flex items-center justify-between mb-2'>
                      <div className={`h-3 w-3 rounded-full ${
                        node.status === 'available' ? 'bg-green-500' : 'bg-yellow-500'
                      }`} />
                      <span className='text-sm font-medium'>{node.region}</span>
                    </div>
                    <h3 className='font-medium mb-1'>{node.name}</h3>
                    <p className='text-sm text-muted-foreground mb-3'>
                      {node.specs}
                    </p>
                    <div className='flex items-center justify-between'>
                      <span className='text-lg font-bold'>${node.price}/month</span>
                      <div className='flex space-x-2'>
                        <Button variant='outline' size='sm'>
                          <IconEye className='h-4 w-4' />
                        </Button>
                        <Button size='sm' disabled={node.status !== 'available'}>
                          Purchase
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* My Usage Rights */}
          <Card>
            <CardHeader>
              <CardTitle>My Usage Rights</CardTitle>
              <CardDescription>
                Manage your purchased node usage rights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockUsageRights.map((right) => (
                  <div
                    key={right.id}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='flex items-center space-x-4'>
                      <div className={`h-3 w-3 rounded-full ${
                        right.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <div>
                        <h3 className='font-medium'>{right.nodeName}</h3>
                        <p className='text-sm text-muted-foreground'>
                          {right.region} • Expires: {right.expiresAt}
                        </p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className='font-medium'>${right.monthlyPrice}/month</p>
                      <p className='text-sm text-muted-foreground'>{right.status}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Available',
    href: '/nodes',
    isActive: true,
    disabled: false,
  },
  {
    title: 'My Rights',
    href: '/nodes/rights',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Usage',
    href: '/nodes/usage',
    isActive: false,
    disabled: true,
  },
]

const mockNodes = [
  {
    id: '1',
    name: 'Edge Node NYC-01',
    region: 'US-East',
    specs: '8 vCPU, 16GB RAM, 500GB SSD',
    price: 299,
    status: 'available',
  },
  {
    id: '2',
    name: 'Edge Node LAX-02',
    region: 'US-West',
    specs: '16 vCPU, 32GB RAM, 1TB SSD',
    price: 599,
    status: 'available',
  },
  {
    id: '3',
    name: 'Edge Node FRA-01',
    region: 'EU-Central',
    specs: '4 vCPU, 8GB RAM, 250GB SSD',
    price: 199,
    status: 'limited',
  },
]

const mockUsageRights = [
  {
    id: '1',
    nodeName: 'Edge Node NYC-01',
    region: 'US-East',
    monthlyPrice: 299,
    expiresAt: '2024-12-31',
    status: 'active',
  },
  {
    id: '2',
    nodeName: 'Edge Node LAX-02',
    region: 'US-West',
    monthlyPrice: 599,
    expiresAt: '2024-11-30',
    status: 'active',
  },
]
