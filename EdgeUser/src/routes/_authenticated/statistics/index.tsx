import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { IconChartBar, IconDownload, IconTrendingUp, IconActivity } from '@tabler/icons-react'

export const Route = createFileRoute('/_authenticated/statistics/')({
  component: StatisticsPage,
})

function StatisticsPage() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>Statistics & Analytics</h1>
            <p className='text-muted-foreground'>
              Monitor your enterprise CDN performance and usage metrics
            </p>
          </div>
          <Button>
            <IconDownload className='mr-2 h-4 w-4' />
            Export Report
          </Button>
        </div>

        <div className='grid gap-6'>
          {/* Key Metrics */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Traffic</CardTitle>
                <IconChartBar className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>2.4 TB</div>
                <p className='text-xs text-muted-foreground'>
                  +12% from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Bandwidth Usage</CardTitle>
                <IconTrendingUp className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>156 Mbps</div>
                <p className='text-xs text-muted-foreground'>
                  Peak: 245 Mbps
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Requests</CardTitle>
                <IconActivity className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>1.2M</div>
                <p className='text-xs text-muted-foreground'>
                  +8% from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Error Rate</CardTitle>
                <IconActivity className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>0.12%</div>
                <p className='text-xs text-muted-foreground'>
                  -0.05% from last month
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Traffic Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Traffic Analytics</CardTitle>
              <CardDescription>
                Real-time traffic patterns and geographic distribution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='h-[300px] flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg'>
                <div className='text-center'>
                  <IconChartBar className='mx-auto h-12 w-12 text-gray-400' />
                  <h3 className='mt-2 text-sm font-medium text-gray-900'>Traffic Chart</h3>
                  <p className='mt-1 text-sm text-gray-500'>
                    Interactive traffic analytics chart will be displayed here
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className='grid gap-6 md:grid-cols-2'>
            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>
                  Response times and cache hit rates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {performanceMetrics.map((metric) => (
                    <div key={metric.name} className='flex items-center justify-between'>
                      <div>
                        <p className='text-sm font-medium'>{metric.name}</p>
                        <p className='text-xs text-muted-foreground'>{metric.description}</p>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-bold'>{metric.value}</p>
                        <p className={`text-xs ${
                          metric.trend === 'up' ? 'text-green-600' : 
                          metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {metric.change}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>
                  Traffic distribution by region
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {geographicData.map((region) => (
                    <div key={region.name} className='flex items-center justify-between'>
                      <div className='flex items-center space-x-2'>
                        <div className='w-3 h-3 rounded-full' style={{ backgroundColor: region.color }} />
                        <span className='text-sm font-medium'>{region.name}</span>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-bold'>{region.percentage}%</p>
                        <p className='text-xs text-muted-foreground'>{region.traffic}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Services */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Services</CardTitle>
              <CardDescription>
                Services ranked by traffic volume and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {topServices.map((service, index) => (
                  <div key={service.name} className='flex items-center justify-between rounded-lg border p-4'>
                    <div className='flex items-center space-x-4'>
                      <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 text-sm font-bold'>
                        {index + 1}
                      </div>
                      <div>
                        <h3 className='font-medium'>{service.name}</h3>
                        <p className='text-sm text-muted-foreground'>{service.domain}</p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className='text-sm font-bold'>{service.traffic}</p>
                      <p className='text-xs text-muted-foreground'>{service.requests} requests</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: '/statistics',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Traffic',
    href: '/statistics/traffic',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Performance',
    href: '/statistics/performance',
    isActive: false,
    disabled: true,
  },
  {
    title: 'Errors',
    href: '/statistics/errors',
    isActive: false,
    disabled: true,
  },
]

const performanceMetrics = [
  {
    name: 'Average Response Time',
    description: 'Global average',
    value: '45ms',
    change: '-5ms',
    trend: 'down',
  },
  {
    name: 'Cache Hit Rate',
    description: 'Content cache efficiency',
    value: '94.2%',
    change: '+2.1%',
    trend: 'up',
  },
  {
    name: 'Origin Load',
    description: 'Backend server load',
    value: '12%',
    change: '-3%',
    trend: 'down',
  },
]

const geographicData = [
  { name: 'North America', percentage: 45, traffic: '1.08 TB', color: '#3b82f6' },
  { name: 'Europe', percentage: 32, traffic: '768 GB', color: '#10b981' },
  { name: 'Asia Pacific', percentage: 18, traffic: '432 GB', color: '#f59e0b' },
  { name: 'Others', percentage: 5, traffic: '120 GB', color: '#6b7280' },
]

const topServices = [
  {
    name: 'Main Website CDN',
    domain: 'www.example.com',
    traffic: '1.2 TB',
    requests: '450K',
  },
  {
    name: 'API Gateway CDN',
    domain: 'api.example.com',
    traffic: '680 GB',
    requests: '320K',
  },
  {
    name: 'Media Streaming',
    domain: 'media.example.com',
    traffic: '520 GB',
    requests: '180K',
  },
]
