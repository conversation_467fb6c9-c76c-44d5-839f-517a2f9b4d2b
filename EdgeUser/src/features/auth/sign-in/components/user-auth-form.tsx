import { HTMLAttributes, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate, useSearch } from '@tanstack/react-router'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/password-input'
import { useAuth } from '@/context/auth-context'
import { toast } from 'sonner'
import { authService } from '@/lib/api/auth'
import AuthUtils from '@/lib/api/auth'
import { MD5 } from 'crypto-js'

type UserAuthFormProps = HTMLAttributes<HTMLFormElement>

// 表单数据类型
type FormData = {
  username: string
  password: string
  remember: boolean
}

// 表单验证schema
const formSchema = z.object({
  username: z
    .string()
    .min(1, { message: 'Please enter your username, email or phone number' })
    .refine((value) => AuthUtils.validateUsername(value), {
      message: 'Please enter a valid username, email or phone number',
    }),
  password: z
    .string()
    .min(1, {
      message: 'Please enter your password',
    })
    .min(7, {
      message: 'Password must be at least 7 characters long',
    }),
  remember: z.boolean().default(false),
})

export function UserAuthForm({ className, ...props }: UserAuthFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const navigate = useNavigate()
  const searchParams = useSearch({ from: '/(auth)/sign-in' })
  const { login } = useAuth()

  const form = useForm<any>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
      remember: false,
    },
  })

  async function onSubmit(data: any) {
    setIsLoading(true)

    try {
      // 对密码进行 MD5 加密
      const hashedPassword = MD5(data.password).toString()

      // 使用EdgeOpenAPI登录
      const response = await authService.loginUser(
        data.username,
        hashedPassword,
        data.remember || false
      )

      // 检查登录结果
      if (response.success && response.user_id) {
        // 登录成功，创建用户信息
        const user = authService.createUserInfo(response, data.username)

        // 使用返回的访问令牌或生成一个
        const accessToken = response.access_token || authService.generateAccessToken(response.user_id)

        // 设置认证信息
        login(accessToken, user)

        toast.success('登录成功！')

        // 重定向到原页面或默认页面
        const redirectPath = (searchParams as any)?.redirect || '/dashboard'
        navigate({ to: redirectPath })

      } else {
        // 登录失败
        const errorMessage = response.message || '登录失败，请检查您的凭据'
        toast.error(errorMessage)

        // 如果是密码错误，清空密码字段
        if (errorMessage.includes('密码') || errorMessage.includes('password')) {
          form.setValue('password', '')
        }
      }

    } catch (error) {
      console.error('登录失败:', error)
      const errorMessage = error instanceof Error ? error.message : '登录失败，请稍后重试'
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }



  // 获取用户名类型以显示相应的提示
  const usernameType = form.watch('username') ? AuthUtils.getUsernameType(form.watch('username')) : null
  const getUsernamePlaceholder = () => {
    switch (usernameType) {
      case 'email':
        return '<EMAIL>'
      case 'phone':
        return '13800138000'
      case 'username':
        return 'username'
      default:
        return 'Enter username, email or phone number'
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('grid gap-3', className)}
        {...props}
      >
        <FormField
          control={form.control}
          name='username'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Username / Email / Phone
                {usernameType && (
                  <span className='text-muted-foreground ml-2 text-sm'>
                    ({usernameType})
                  </span>
                )}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={getUsernamePlaceholder()}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name='password'
          render={({ field }) => (
            <FormItem className='relative'>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput
                  placeholder='********'
                  {...field}
                />
              </FormControl>
              <FormMessage />
              <Link
                to='/forgot-password'
                className='text-muted-foreground absolute -top-0.5 right-0 text-sm font-medium hover:opacity-75'
              >
                Forgot password?
              </Link>
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name='remember'
          render={({ field }) => (
            <FormItem className='flex flex-row items-center space-x-2 space-y-0'>
              <FormControl>
                <input
                  type='checkbox'
                  checked={field.value}
                  onChange={field.onChange}
                  className='rounded border-gray-300'
                />
              </FormControl>
              <FormLabel className='text-sm font-normal'>
                Remember me
              </FormLabel>
            </FormItem>
          )}
        />

        <Button className='mt-2' disabled={isLoading}>
          {isLoading ? 'Logging in...' : 'Login'}
        </Button>

        <div className='relative my-2'>
          <div className='absolute inset-0 flex items-center'>
            <span className='w-full border-t' />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background text-muted-foreground px-2'>
              Or continue with
            </span>
          </div>
        </div>

        {/* <div className='grid grid-cols-2 gap-2'>
          <Button variant='outline' type='button' disabled={isLoading}>
            <IconBrandGithub className='h-4 w-4' /> GitHub
          </Button>
          <Button variant='outline' type='button' disabled={isLoading}>
            <IconBrandFacebook className='h-4 w-4' /> Facebook
          </Button>
        </div> */}
      </form>
    </Form>
  )
}
