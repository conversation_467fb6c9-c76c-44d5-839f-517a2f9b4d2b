package converter

import (
	"time"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
)

// User represents the JSON structure for user data in HTTP responses
type User struct {
	ID        int64     `json:"id"`
	Username  string    `json:"username"`
	Fullname  string    `json:"fullname"`
	Email     string    `json:"email"`
	Mobile    string    `json:"mobile"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateUserRequest represents the JSON structure for creating a user
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Fullname string `json:"fullname"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
	Remark   string `json:"remark"`
}

// UpdateUserRequest represents the JSON structure for updating a user
type UpdateUserRequest struct {
	Fullname string `json:"fullname"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
	Remark   string `json:"remark"`
}

// UserFromProto converts protobuf User to JSON User
func UserFromProto(pbUser *pb.User) *User {
	if pbUser == nil {
		return nil
	}

	status := "inactive"
	if pbUser.IsOn {
		status = "active"
	}

	return &User{
		ID:        pbUser.Id,
		Username:  pbUser.Username,
		Fullname:  pbUser.Fullname,
		Email:     pbUser.Email,
		Mobile:    pbUser.Mobile,
		Status:    status,
		CreatedAt: time.Unix(pbUser.CreatedAt, 0),
		UpdatedAt: time.Unix(pbUser.CreatedAt, 0), // Use CreatedAt since UpdatedAt is not available
	}
}

// CreateUserToProto converts CreateUserRequest to protobuf CreateUserRequest
func CreateUserToProto(req *CreateUserRequest) *pb.CreateUserRequest {
	return &pb.CreateUserRequest{
		Username: req.Username,
		Password: req.Password,
		Fullname: req.Fullname,
		Email:    req.Email,
		Mobile:   req.Mobile,
		Remark:   req.Remark,
		Source:   "api", // Mark as API created
	}
}

// UpdateUserToProto converts UpdateUserRequest to protobuf UpdateUserRequest
func UpdateUserToProto(userId int64, req *UpdateUserRequest) *pb.UpdateUserRequest {
	return &pb.UpdateUserRequest{
		UserId:   userId,
		Fullname: req.Fullname,
		Email:    req.Email,
		Mobile:   req.Mobile,
		Remark:   req.Remark,
	}
}

// UsersFromProto converts a slice of protobuf Users to JSON Users
func UsersFromProto(pbUsers []*pb.User) []*User {
	users := make([]*User, len(pbUsers))
	for i, pbUser := range pbUsers {
		users[i] = UserFromProto(pbUser)
	}
	return users
}

// SubUser represents the JSON structure for sub user data in HTTP responses
type SubUser struct {
	ID        int64  `json:"id"`
	UserID    int64  `json:"userId"`
	Username  string `json:"username"`
	Name      string `json:"name"`
	IsOn      bool   `json:"isOn"`
	CreatedAt int64  `json:"createdAt"`
	UpdatedAt int64  `json:"updatedAt"`
	Remark    string `json:"remark"`
}

// ConvertSubUserFromPB converts protobuf SubUser to JSON SubUser
func ConvertSubUserFromPB(pbSubUser *pb.SubUser) SubUser {
	return SubUser{
		ID:        pbSubUser.Id,
		UserID:    pbSubUser.UserId,
		Username:  pbSubUser.Username,
		Name:      pbSubUser.Name,
		IsOn:      pbSubUser.IsOn,
		CreatedAt: pbSubUser.CreatedAt,
		UpdatedAt: pbSubUser.UpdatedAt,
		Remark:    pbSubUser.Remark,
	}
}
