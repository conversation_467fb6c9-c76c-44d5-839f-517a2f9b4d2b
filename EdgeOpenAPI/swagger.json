{"swagger": "2.0", "info": {"description": "EdgeOpenAPI是EdgeAPI的HTTP REST网关，提供企业级CDN管理功能的RESTful API接口", "title": "EdgeOpenAPI", "termsOfService": "https://www.teaos.cn/terms/", "contact": {"name": "EdgeOpenAPI Support", "url": "https://www.teaos.cn/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/api/v1/users/subusers": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前用户的所有子用户列表，支持分页和搜索", "produces": ["application/json"], "tags": ["子用户管理"], "summary": "获取子用户列表", "parameters": [{"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页数量", "name": "size", "in": "query"}, {"type": "string", "description": "搜索关键词", "name": "keyword", "in": "query"}], "responses": {"200": {"description": "子用户列表", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "为当前登录用户创建一个新的子用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["子用户管理"], "summary": "创建子用户", "parameters": [{"description": "创建子用户请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_handlers.CreateSubUserRequest"}}], "responses": {"200": {"description": "创建成功", "schema": {"$ref": "#/definitions/internal_handlers.CreateSubUserResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}}}}, "/api/v1/users/subusers/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "获取指定子用户的详细信息", "produces": ["application/json"], "tags": ["子用户管理"], "summary": "获取子用户详情", "parameters": [{"type": "integer", "description": "子用户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "子用户详情", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.SubUser"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "404": {"description": "子用户不存在", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "更新指定的子用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["子用户管理"], "summary": "更新子用户", "parameters": [{"type": "integer", "description": "子用户ID", "name": "id", "in": "path", "required": true}, {"description": "更新子用户请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_handlers.UpdateSubUserRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "404": {"description": "子用户不存在", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "删除指定的子用户", "produces": ["application/json"], "tags": ["子用户管理"], "summary": "删除子用户", "parameters": [{"type": "integer", "description": "子用户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "404": {"description": "子用户不存在", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse"}}}}}}, "definitions": {"github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "error": {}, "message": {"type": "string"}, "request_id": {"type": "string"}, "timestamp": {"type": "integer"}}}, "github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.SubUser": {"type": "object", "properties": {"createdAt": {"type": "integer"}, "id": {"type": "integer"}, "isOn": {"type": "boolean"}, "name": {"type": "string"}, "remark": {"type": "string"}, "updatedAt": {"type": "integer"}, "userId": {"type": "integer"}, "username": {"type": "string"}}}, "internal_handlers.CreateSubUserRequest": {"type": "object", "required": ["name", "password", "username"], "properties": {"name": {"description": "显示名称", "type": "string", "example": "子用户001"}, "password": {"description": "密码", "type": "string", "example": "password123"}, "remark": {"description": "备注", "type": "string", "example": "这是一个测试子用户"}, "username": {"description": "用户名", "type": "string", "example": "subuser001"}}}, "internal_handlers.CreateSubUserResponse": {"type": "object", "properties": {"subUserId": {"description": "子用户ID", "type": "integer", "example": 1}}}, "internal_handlers.UpdateSubUserRequest": {"type": "object", "required": ["name", "username"], "properties": {"isOn": {"description": "是否启用", "type": "boolean", "example": true}, "name": {"description": "显示名称", "type": "string", "example": "子用户001"}, "password": {"description": "密码（可选）", "type": "string", "example": "newpassword123"}, "remark": {"description": "备注", "type": "string", "example": "这是一个更新的子用户"}, "username": {"description": "用户名", "type": "string", "example": "subuser001"}}}}, "securityDefinitions": {"BearerAuth": {"description": "在请求头中添加Bearer Token进行身份验证，格式：Bearer {token}", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}