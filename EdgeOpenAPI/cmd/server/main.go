// EdgeOpenAPI 企业级CDN管理API网关
// @title EdgeOpenAPI
// @version 1.0
// @description EdgeOpenAPI是EdgeAPI的HTTP REST网关，提供企业级CDN管理功能的RESTful API接口
// @termsOfService https://www.teaos.cn/terms/
// @contact.name EdgeOpenAPI Support
// @contact.url https://www.teaos.cn/support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host localhost:8080
// @BasePath /api/v1
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description 在请求头中添加Bearer Token进行身份验证，格式：Bearer {token}
package main

import (
	"context"
	"flag"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/middleware"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/routes"
	"github.com/gin-gonic/gin"
)

func main() {
	// Parse command line flags
	var configPath string
	flag.StringVar(&configPath, "config", "", "Path to configuration file")
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Set Gin mode
	gin.SetMode(cfg.Server.Mode)

	// Initialize EdgeAPI gRPC client with configuration
	edgeAPIClient, err := grpc.NewEdgeAPIClient(cfg.EdgeAPI.Endpoint)
	if err != nil {
		log.Fatalf("Failed to connect to EdgeAPI: %v", err)
	}
	defer edgeAPIClient.Close()

	// Initialize Gin router
	router := gin.Default()

	// Add global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())

	// Add authentication middleware (only for protected routes)
	// We'll configure this in routes setup

	// Setup routes
	routes.SetupRoutes(router, edgeAPIClient)

	// Create HTTP server
	server := &http.Server{
		Addr:    cfg.GetServerAddress(),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting EdgeOpenAPI server on %s", cfg.GetServerAddress())
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
