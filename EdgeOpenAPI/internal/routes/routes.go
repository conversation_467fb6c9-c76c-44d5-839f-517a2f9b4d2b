package routes

import (
	"net/http"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/handlers"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, edgeAPIClient *grpc.EdgeAPIClient) {
	// Health check endpoints (no auth required)
	router.GET("/health", healthCheck)
	router.GET("/ping", ping)

	// CSRF token endpoint (no auth required)
	csrfHandler := handlers.NewCSRFHandler()
	router.GET("/csrf/token", csrfHandler.GetToken)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes
		authHandler := handlers.NewAuthHandler(edgeAPIClient)
		auth := v1.Group("/auth")
		{
			// Original login endpoint (for API access keys)
			auth.POST("/login", authHandler.Login)

			// New CSRF-protected login endpoint (for User login)
			auth.POST("/login-csrf", authHandler.LoginWithCSRF)

			// Session validation endpoint (for User session check)
			auth.GET("/session", authHandler.ValidateSession)

			// Logout endpoint (for User logout)
			auth.POST("/logout", authHandler.Logout)
		}

		// User management routes
		userHandler := handlers.NewUserHandler(edgeAPIClient)
		users := v1.Group("/users")
		{
			users.GET("", userHandler.ListUsers)
			users.POST("", userHandler.CreateUser)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)

			// Sub-user management routes
			subUserHandler := handlers.NewSubUserHandler(edgeAPIClient)
			subUsers := users.Group("/subusers")
			{
				subUsers.GET("", subUserHandler.ListSubUsers)
				subUsers.POST("", subUserHandler.CreateSubUser)
				subUsers.GET("/:id", subUserHandler.GetSubUser)
				subUsers.PUT("/:id", subUserHandler.UpdateSubUser)
				subUsers.DELETE("/:id", subUserHandler.DeleteSubUser)
			}
		}

		// TODO: Add more route groups for other modules
		// - CDN services (/services)
		// - Node clusters (/clusters)
		// - Nodes (/nodes)
		// - Statistics (/stats)
		// - Billing (/billing)
	}
}

// healthCheck returns the health status of the service
func healthCheck(c *gin.Context) {
	converter.SuccessResponse(c, gin.H{
		"status":  "healthy",
		"service": "EdgeOpenAPI",
		"version": "1.0.0",
	})
}

// ping returns a simple pong response
func ping(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "pong",
	})
}
