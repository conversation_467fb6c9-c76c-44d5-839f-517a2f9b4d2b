package middleware

import (
	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

// SessionMiddleware 会话验证中间件，参考 EdgeAdmin 的实现
func SessionMiddleware(edgeAPIClient *grpc.EdgeAPIClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过不需要会话验证的端点
		if c.Request.URL.Path == "/health" ||
			c.Request.URL.Path == "/ping" ||
			c.Request.URL.Path == "/csrf/token" ||
			c.Request.URL.Path == "/api/v1/auth/login" ||
			c.Request.URL.Path == "/api/v1/auth/login-csrf" {
			c.Next()
			return
		}

		// 从 Cookie 中获取会话 ID
		sessionId, err := c.<PERSON>("geusersid")
		if err != nil || sessionId == "" {
			converter.UnauthorizedResponse(c, "未登录或会话已过期")
			c.Abort()
			return
		}

		// 验证会话
		ctx := edgeAPIClient.CreateAPIContext()
		resp, err := edgeAPIClient.LoginSessionService.FindLoginSession(ctx, &pb.FindLoginSessionRequest{
			Sid: sessionId,
		})
		if err != nil {
			converter.InternalErrorResponse(c, err)
			c.Abort()
			return
		}

		// 检查会话是否存在且有效
		if resp.LoginSession == nil || resp.LoginSession.UserId == 0 {
			converter.UnauthorizedResponse(c, "会话无效或已过期")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", resp.LoginSession.UserId)
		c.Set("session_id", sessionId)

		c.Next()
	}
}

// GetUserIDFromContext 从上下文中获取用户 ID
func GetUserIDFromContext(c *gin.Context) int64 {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(int64); ok {
			return id
		}
	}
	return 0
}

// GetSessionIDFromContext 从上下文中获取会话 ID
func GetSessionIDFromContext(c *gin.Context) string {
	if sessionID, exists := c.Get("session_id"); exists {
		if id, ok := sessionID.(string); ok {
			return id
		}
	}
	return ""
}
