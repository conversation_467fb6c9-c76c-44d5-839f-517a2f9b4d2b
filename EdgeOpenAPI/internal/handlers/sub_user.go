package handlers

import (
	"context"
	"strconv"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/middleware"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/metadata"
)

// SubUserHandler 子用户处理器
type SubUserHandler struct {
	edgeAPIClient *grpc.EdgeAPIClient
}

// NewSubUserHandler 创建子用户处理器
func NewSubUserHandler(client *grpc.EdgeAPIClient) *SubUserHandler {
	return &SubUserHandler{
		edgeAPIClient: client,
	}
}

// CreateSubUserRequest 创建子用户请求
type CreateSubUserRequest struct {
	Username string `json:"username" binding:"required" example:"subuser001"`                    // 用户名
	Password string `json:"password" binding:"required" example:"password123"`                   // 密码
	Name     string `json:"name" binding:"required" example:"子用户001"`                           // 显示名称
	Remark   string `json:"remark" example:"这是一个测试子用户"`                                        // 备注
}

// UpdateSubUserRequest 更新子用户请求
type UpdateSubUserRequest struct {
	Username string `json:"username" binding:"required" example:"subuser001"`                    // 用户名
	Password string `json:"password" example:"newpassword123"`                                   // 密码（可选）
	Name     string `json:"name" binding:"required" example:"子用户001"`                           // 显示名称
	Remark   string `json:"remark" example:"这是一个更新的子用户"`                                       // 备注
	IsOn     bool   `json:"isOn" example:"true"`                                                 // 是否启用
}

// CreateSubUserResponse 创建子用户响应
type CreateSubUserResponse struct {
	SubUserID int64 `json:"subUserId" example:"1"`                                              // 子用户ID
}

// CreateSubUser 创建子用户
// @Summary 创建子用户
// @Description 为当前登录用户创建一个新的子用户
// @Tags 子用户管理
// @Accept json
// @Produce json
// @Param request body CreateSubUserRequest true "创建子用户请求"
// @Success 200 {object} CreateSubUserResponse "创建成功"
// @Failure 400 {object} converter.APIResponse "请求参数错误"
// @Failure 401 {object} converter.APIResponse "未授权"
// @Failure 500 {object} converter.APIResponse "服务器内部错误"
// @Router /api/v1/users/subusers [post]
// @Security BearerAuth
func (h *SubUserHandler) CreateSubUser(c *gin.Context) {
	var req CreateSubUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// 创建认证上下文
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// 调用EdgeAPI创建子用户
	resp, err := h.edgeAPIClient.SubUserService.CreateSubUser(ctx, &pb.CreateSubUserRequest{
		Username: req.Username,
		Password: req.Password,
		Name:     req.Name,
		Remark:   req.Remark,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, CreateSubUserResponse{
		SubUserID: resp.SubUserId,
	})
}

// UpdateSubUser 更新子用户
// @Summary 更新子用户
// @Description 更新指定的子用户信息
// @Tags 子用户管理
// @Accept json
// @Produce json
// @Param id path int true "子用户ID"
// @Param request body UpdateSubUserRequest true "更新子用户请求"
// @Success 200 {object} converter.APIResponse "更新成功"
// @Failure 400 {object} converter.APIResponse "请求参数错误"
// @Failure 401 {object} converter.APIResponse "未授权"
// @Failure 404 {object} converter.APIResponse "子用户不存在"
// @Failure 500 {object} converter.APIResponse "服务器内部错误"
// @Router /api/v1/users/subusers/{id} [put]
// @Security BearerAuth
func (h *SubUserHandler) UpdateSubUser(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	subUserID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, "无效的子用户ID")
		return
	}

	var req UpdateSubUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// 创建认证上下文
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// 调用EdgeAPI更新子用户
	_, err = h.edgeAPIClient.SubUserService.UpdateSubUser(ctx, &pb.UpdateSubUserRequest{
		SubUserId: subUserID,
		Username:  req.Username,
		Password:  req.Password,
		Name:      req.Name,
		Remark:    req.Remark,
		IsOn:      req.IsOn,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{"message": "子用户更新成功"})
}

// DeleteSubUser 删除子用户
// @Summary 删除子用户
// @Description 删除指定的子用户
// @Tags 子用户管理
// @Produce json
// @Param id path int true "子用户ID"
// @Success 200 {object} converter.APIResponse "删除成功"
// @Failure 400 {object} converter.APIResponse "请求参数错误"
// @Failure 401 {object} converter.APIResponse "未授权"
// @Failure 404 {object} converter.APIResponse "子用户不存在"
// @Failure 500 {object} converter.APIResponse "服务器内部错误"
// @Router /api/v1/users/subusers/{id} [delete]
// @Security BearerAuth
func (h *SubUserHandler) DeleteSubUser(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	subUserID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, "无效的子用户ID")
		return
	}

	// 创建认证上下文
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// 调用EdgeAPI删除子用户
	_, err = h.edgeAPIClient.SubUserService.DeleteSubUser(ctx, &pb.DeleteSubUserRequest{
		SubUserId: subUserID,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{"message": "子用户删除成功"})
}

// GetSubUser 获取单个子用户
// @Summary 获取子用户详情
// @Description 获取指定子用户的详细信息
// @Tags 子用户管理
// @Produce json
// @Param id path int true "子用户ID"
// @Success 200 {object} converter.SubUser "子用户详情"
// @Failure 400 {object} converter.APIResponse "请求参数错误"
// @Failure 401 {object} converter.APIResponse "未授权"
// @Failure 404 {object} converter.APIResponse "子用户不存在"
// @Failure 500 {object} converter.APIResponse "服务器内部错误"
// @Router /api/v1/users/subusers/{id} [get]
// @Security BearerAuth
func (h *SubUserHandler) GetSubUser(c *gin.Context) {
	// 解析路径参数
	idStr := c.Param("id")
	subUserID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, "无效的子用户ID")
		return
	}

	// 创建认证上下文
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// 调用EdgeAPI获取子用户
	resp, err := h.edgeAPIClient.SubUserService.FindEnabledSubUser(ctx, &pb.FindEnabledSubUserRequest{
		SubUserId: subUserID,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	if resp.SubUser == nil {
		converter.NotFoundResponse(c, "子用户")
		return
	}

	// 转换数据
	subUser := converter.ConvertSubUserFromPB(resp.SubUser)
	converter.SuccessResponse(c, subUser)
}

// ListSubUsers 获取子用户列表
// @Summary 获取子用户列表
// @Description 获取当前用户的所有子用户列表，支持分页和搜索
// @Tags 子用户管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} converter.APIResponse "子用户列表"
// @Failure 400 {object} converter.APIResponse "请求参数错误"
// @Failure 401 {object} converter.APIResponse "未授权"
// @Failure 500 {object} converter.APIResponse "服务器内部错误"
// @Router /api/v1/users/subusers [get]
// @Security BearerAuth
func (h *SubUserHandler) ListSubUsers(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	size, _ := strconv.ParseInt(c.DefaultQuery("size", "20"), 10, 64)
	keyword := c.Query("keyword")

	// 创建认证上下文
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// 计算偏移量
	offset := (page - 1) * size

	// 获取总数
	countResp, err := h.edgeAPIClient.SubUserService.CountSubUsers(ctx, &pb.CountSubUsersRequest{
		Keyword: keyword,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// 获取子用户列表
	listResp, err := h.edgeAPIClient.SubUserService.ListSubUsers(ctx, &pb.ListSubUsersRequest{
		Offset:  offset,
		Size:    size,
		Keyword: keyword,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// 转换数据
	subUsers := make([]converter.SubUser, len(listResp.SubUsers))
	for i, pbSubUser := range listResp.SubUsers {
		subUsers[i] = converter.ConvertSubUserFromPB(pbSubUser)
	}

	// 计算分页信息
	pagination := &converter.PaginationInfo{
		Page:  page,
		Size:  size,
		Total: countResp.Count,
		Pages: converter.CalculatePages(countResp.Count, size),
	}

	converter.SuccessResponseWithPagination(c, subUsers, pagination)
}
