package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"net"
	"net/http"
	"strconv"
	"time"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

// Cookie 相关常量，参考 EdgeAdmin 的实现
const (
	CookieSID     = "geusersid" // 用户会话 Cookie 名称
	SessionUserId = "userId"    // Session 中的用户 ID 键
)

// setCookie 设置用户会话 Cookie，参考 EdgeAdmin 的 loginutils.SetCookie 实现
func setCookie(c *gin.Context, sessionId string, remember bool) {
	var maxAge int
	if remember {
		maxAge = 14 * 86400 // 14天
	} else {
		maxAge = 0 // 浏览器会话
	}

	cookie := &http.Cookie{
		Name:     CookieSID,
		Value:    sessionId,
		Path:     "/",
		MaxAge:   maxAge,
		HttpOnly: true,
	}

	// 如果是 HTTPS，设置安全属性
	if c.Request.TLS != nil {
		cookie.SameSite = http.SameSiteStrictMode
		cookie.Secure = true
	}

	http.SetCookie(c.Writer, cookie)
}

// unsetCookie 清除用户会话 Cookie，参考 EdgeAdmin 的 loginutils.UnsetCookie 实现
func unsetCookie(c *gin.Context) {
	cookie := &http.Cookie{
		Name:     CookieSID,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
	}

	if c.Request.TLS != nil {
		cookie.SameSite = http.SameSiteStrictMode
		cookie.Secure = true
	}

	http.SetCookie(c.Writer, cookie)
}

// generateSessionId 生成会话 ID，参考 EdgeAdmin 的实现
func generateSessionId() string {
	bytes := make([]byte, 16)
	_, err := rand.Read(bytes)
	if err != nil {
		// 如果随机数生成失败，使用时间戳作为后备
		return hex.EncodeToString([]byte(strconv.FormatInt(time.Now().UnixNano(), 16)))
	}
	return hex.EncodeToString(bytes)
}

// getClientIP 获取客户端 IP，参考 EdgeAdmin 的 loginutils.RemoteIP 实现
func getClientIP(c *gin.Context) string {
	// 首先尝试从 X-Forwarded-For 头获取
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		// 取第一个 IP
		if ip := net.ParseIP(xff); ip != nil {
			return xff
		}
	}

	// 尝试从 X-Real-IP 头获取
	if xri := c.GetHeader("X-Real-IP"); xri != "" {
		if ip := net.ParseIP(xri); ip != nil {
			return xri
		}
	}

	// 最后从 RemoteAddr 获取
	ip, _, _ := net.SplitHostPort(c.Request.RemoteAddr)
	return ip
}

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	edgeAPIClient *grpc.EdgeAPIClient
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(edgeAPIClient *grpc.EdgeAPIClient) *AuthHandler {
	return &AuthHandler{
		edgeAPIClient: edgeAPIClient,
	}
}

// Login handles POST /api/v1/auth/login
func (h *AuthHandler) Login(c *gin.Context) {
	var req converter.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Validate input
	if len(req.Username) == 0 || len(req.Password) == 0 {
		converter.ValidationErrorResponse(c, gin.H{
			"username": "Username is required",
			"password": "Password is required",
		})
		return
	}

	// Create API context for gRPC call (requires API node credentials)
	ctx := h.edgeAPIClient.CreateAPIContext()

	// Convert to protobuf request
	pbReq := converter.LoginUserToProto(&req)

	// Call EdgeAPI to validate user credentials
	resp, err := h.edgeAPIClient.UserService.LoginUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Convert response
	loginResp := converter.LoginResponseFromProto(resp)

	if !loginResp.Success {
		// Login failed
		converter.ErrorResponse(c, http.StatusUnauthorized, "Login failed", gin.H{
			"type":    "authentication_error",
			"details": loginResp.Message,
		})
		return
	}

	// Login successful - now we need to provide access credentials
	// For now, we'll return the user ID and suggest creating access keys
	loginResp.Message = "Login successful. Please create access keys for API access."
	loginResp.TokenType = "Bearer"

	converter.SuccessResponse(c, loginResp)
}

// LoginWithCSRF handles POST /api/v1/auth/login-csrf (User login with CSRF protection)
func (h *AuthHandler) LoginWithCSRF(c *gin.Context) {
	var req converter.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Validate input
	if len(req.Username) == 0 || len(req.Password) == 0 {
		converter.ValidationErrorResponse(c, gin.H{
			"username": "Username is required",
			"password": "Password is required",
		})
		return
	}

	// Validate CSRF token
	if req.CSRFToken == "" {
		converter.ErrorResponse(c, http.StatusForbidden, "缺少CSRF token", gin.H{
			"type": "csrf_error",
			"code": "missing_token",
		})
		return
	}

	// Validate CSRF token
	if !ValidateCSRFToken(req.CSRFToken) {
		converter.ErrorResponse(c, http.StatusForbidden, "CSRF token无效或已过期，请刷新页面后重试", gin.H{
			"type": "csrf_error",
			"code": "invalid_token",
		})
		return
	}

	// Create API context for gRPC call (requires API node credentials)
	ctx := h.edgeAPIClient.CreateAPIContext()

	// Convert to protobuf request
	pbReq := converter.LoginUserToProto(&req)

	// Call EdgeAPI to validate user credentials
	resp, err := h.edgeAPIClient.UserService.LoginUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Convert response
	loginResp := converter.LoginResponseFromProto(resp)

	if !loginResp.Success {
		// Login failed
		converter.ErrorResponse(c, http.StatusUnauthorized, "登录失败", gin.H{
			"type":    "authentication_error",
			"details": loginResp.Message,
		})
		return
	}

	// Login successful - 创建会话并设置 Cookie
	if loginResp.UserID > 0 {
		// 创建会话 ID（类似 EdgeAdmin 的实现）
		sessionId := generateSessionId()

		// 写入用户 ID 到会话（这会自动创建会话记录）
		_, err = h.edgeAPIClient.LoginSessionService.WriteLoginSessionValue(ctx, &pb.WriteLoginSessionValueRequest{
			Sid:   sessionId,
			Key:   SessionUserId,
			Value: strconv.FormatInt(loginResp.UserID, 10),
		})
		if err != nil {
			converter.InternalErrorResponse(c, err)
			return
		}

		// 写入客户端 IP 到会话
		_, err = h.edgeAPIClient.LoginSessionService.WriteLoginSessionValue(ctx, &pb.WriteLoginSessionValueRequest{
			Sid:   sessionId,
			Key:   "@ip",
			Value: getClientIP(c),
		})
		if err != nil {
			converter.InternalErrorResponse(c, err)
			return
		}

		// 设置 Cookie
		setCookie(c, sessionId, req.Remember)
	}

	loginResp.Message = "登录成功"
	loginResp.TokenType = "Bearer"

	converter.SuccessResponse(c, loginResp)
}

// ValidateSession handles GET /api/v1/auth/session (会话验证端点)
func (h *AuthHandler) ValidateSession(c *gin.Context) {
	// 从 Cookie 中获取会话 ID
	sessionId, err := c.Cookie(CookieSID)
	if err != nil || sessionId == "" {
		converter.UnauthorizedResponse(c, "未登录或会话已过期")
		return
	}

	// 验证会话
	ctx := h.edgeAPIClient.CreateAPIContext()
	resp, err := h.edgeAPIClient.LoginSessionService.FindLoginSession(ctx, &pb.FindLoginSessionRequest{
		Sid: sessionId,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// 检查会话是否存在且有效
	if resp.LoginSession == nil || resp.LoginSession.UserId == 0 {
		converter.UnauthorizedResponse(c, "会话无效或已过期")
		return
	}

	// 返回用户信息
	converter.SuccessResponse(c, gin.H{
		"valid":   true,
		"user_id": resp.LoginSession.UserId,
		"message": "会话有效",
	})
}

// Logout handles POST /api/v1/auth/logout (用户登出)
func (h *AuthHandler) Logout(c *gin.Context) {
	// 从 Cookie 中获取会话 ID
	sessionId, err := c.Cookie(CookieSID)
	if err != nil || sessionId == "" {
		// 即使没有会话也返回成功，因为目标是清除登录状态
		unsetCookie(c)
		converter.SuccessResponse(c, gin.H{
			"message": "登出成功",
		})
		return
	}

	// 删除会话记录
	ctx := h.edgeAPIClient.CreateAPIContext()
	_, err = h.edgeAPIClient.LoginSessionService.DeleteLoginSession(ctx, &pb.DeleteLoginSessionRequest{
		Sid: sessionId,
	})
	if err != nil {
		// 即使删除失败也要清除 Cookie
		unsetCookie(c)
		converter.SuccessResponse(c, gin.H{
			"message": "登出成功",
		})
		return
	}

	// 清除 Cookie
	unsetCookie(c)
	converter.SuccessResponse(c, gin.H{
		"message": "登出成功",
	})
}
