package grpc

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"strconv"
	"time"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/encrypt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
)

// EdgeAPIClient wraps all EdgeAPI gRPC service clients
type EdgeAPIClient struct {
	conn                  *grpc.ClientConn
	APIAccessTokenService pb.APIAccessTokenServiceClient
	UserService           pb.UserServiceClient
	SubUserService        pb.SubUserServiceClient
	ServerService         pb.ServerServiceClient
	NodeService           pb.NodeServiceClient
	NodeClusterService    pb.NodeClusterServiceClient
	UserBillService       pb.UserBillServiceClient
	UserAccessKeyService  pb.UserAccessKeyServiceClient
	LoginSessionService   pb.LoginSessionServiceClient
	// Note: StatService doesn't exist in EdgeCommon, will use specific stat services as needed
}

// NewEdgeAPIClient creates a new EdgeAPI client with connection to the specified endpoint
func NewEdgeAPIClient(endpoint string) (*EdgeAPIClient, error) {
	// Configure gRPC connection with keepalive and retry settings
	conn, err := grpc.Dial(endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                10 * time.Second, // Send keepalive pings every 10 seconds
			Timeout:             time.Second,      // Wait 1 second for ping ack before considering the connection dead
			PermitWithoutStream: true,             // Send pings even without active streams
		}),
	)
	if err != nil {
		return nil, err
	}

	return &EdgeAPIClient{
		conn:                  conn,
		APIAccessTokenService: pb.NewAPIAccessTokenServiceClient(conn),
		UserService:           pb.NewUserServiceClient(conn),
		SubUserService:        pb.NewSubUserServiceClient(conn),
		ServerService:         pb.NewServerServiceClient(conn),
		NodeService:           pb.NewNodeServiceClient(conn),
		NodeClusterService:    pb.NewNodeClusterServiceClient(conn),
		UserBillService:       pb.NewUserBillServiceClient(conn),
		UserAccessKeyService:  pb.NewUserAccessKeyServiceClient(conn),
		LoginSessionService:   pb.NewLoginSessionServiceClient(conn),
	}, nil
}

// Close closes the gRPC connection
func (c *EdgeAPIClient) Close() error {
	return c.conn.Close()
}

// GetAPIAccessToken is a convenience method to get access token
func (c *EdgeAPIClient) GetAPIAccessToken(ctx context.Context, accessKeyId, accessKey string) (*pb.GetAPIAccessTokenResponse, error) {
	return c.APIAccessTokenService.GetAPIAccessToken(ctx, &pb.GetAPIAccessTokenRequest{
		Type:        "user",
		AccessKeyId: accessKeyId,
		AccessKey:   accessKey,
	})
}

// CreateAPIContext creates a context with API node credentials for gRPC calls
func (c *EdgeAPIClient) CreateAPIContext() context.Context {
	// Load configuration
	cfg := config.GetConfig()
	if cfg == nil {
		// Fallback to default values if config is not loaded
		nodeId := "EdgeOpenAPI_Node_001"
		timestamp := time.Now().Unix()
		tokenBytes := []byte(`{"timestamp":` + strconv.FormatInt(timestamp, 10) + `,"type":"user","userId":0}`)
		token := base64.StdEncoding.EncodeToString(tokenBytes)

		ctx := context.Background()
		ctx = metadata.AppendToOutgoingContext(ctx, "nodeid", nodeId, "token", token)
		return ctx
	}

	// Use configuration values
	nodeId := cfg.APINode.NodeID
	secret := cfg.APINode.Secret

	// Create token data with proper structure
	timestamp := time.Now().Unix()
	tokenData := map[string]interface{}{
		"timestamp": timestamp,
		"type":      "user",
		"userId":    0,
	}

	// Encrypt token using EdgeAPI's encryption method
	token, err := createEncryptedToken(nodeId, secret, tokenData)
	if err != nil {
		// Fallback to simple base64 encoding if encryption fails
		tokenBytes, _ := json.Marshal(tokenData)
		token = base64.StdEncoding.EncodeToString(tokenBytes)
	}

	// Create context with metadata
	ctx := context.Background()
	ctx = metadata.AppendToOutgoingContext(ctx, "nodeid", nodeId, "token", token)
	return ctx
}

// createEncryptedToken 创建加密的token
func createEncryptedToken(nodeId, secret string, data map[string]interface{}) (string, error) {
	// 将数据序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	cfg := config.GetConfig()
	// 创建加密实例
	method, err := encrypt.NewMethodInstance(cfg.Security.EncryptMethod, secret, nodeId)
	if err != nil {
		return "", err
	}

	// 加密数据
	encryptedData, err := method.Encrypt(jsonData)
	if err != nil {
		return "", err
	}

	// Base64编码
	token := base64.StdEncoding.EncodeToString(encryptedData)
	return token, nil
}
