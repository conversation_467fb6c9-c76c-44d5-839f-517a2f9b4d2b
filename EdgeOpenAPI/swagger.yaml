basePath: /api/v1
definitions:
  github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse:
    properties:
      code:
        type: integer
      data: {}
      error: {}
      message:
        type: string
      request_id:
        type: string
      timestamp:
        type: integer
    type: object
  github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.SubUser:
    properties:
      createdAt:
        type: integer
      id:
        type: integer
      isOn:
        type: boolean
      name:
        type: string
      remark:
        type: string
      updatedAt:
        type: integer
      userId:
        type: integer
      username:
        type: string
    type: object
  internal_handlers.CreateSubUserRequest:
    properties:
      name:
        description: 显示名称
        example: 子用户001
        type: string
      password:
        description: 密码
        example: password123
        type: string
      remark:
        description: 备注
        example: 这是一个测试子用户
        type: string
      username:
        description: 用户名
        example: subuser001
        type: string
    required:
    - name
    - password
    - username
    type: object
  internal_handlers.CreateSubUserResponse:
    properties:
      subUserId:
        description: 子用户ID
        example: 1
        type: integer
    type: object
  internal_handlers.UpdateSubUserRequest:
    properties:
      isOn:
        description: 是否启用
        example: true
        type: boolean
      name:
        description: 显示名称
        example: 子用户001
        type: string
      password:
        description: 密码（可选）
        example: newpassword123
        type: string
      remark:
        description: 备注
        example: 这是一个更新的子用户
        type: string
      username:
        description: 用户名
        example: subuser001
        type: string
    required:
    - name
    - username
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: EdgeOpenAPI Support
    url: https://www.teaos.cn/support
  description: EdgeOpenAPI是EdgeAPI的HTTP REST网关，提供企业级CDN管理功能的RESTful API接口
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://www.teaos.cn/terms/
  title: EdgeOpenAPI
  version: "1.0"
paths:
  /api/v1/users/subusers:
    get:
      description: 获取当前用户的所有子用户列表，支持分页和搜索
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: size
        type: integer
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 子用户列表
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
      security:
      - BearerAuth: []
      summary: 获取子用户列表
      tags:
      - 子用户管理
    post:
      consumes:
      - application/json
      description: 为当前登录用户创建一个新的子用户
      parameters:
      - description: 创建子用户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/internal_handlers.CreateSubUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/internal_handlers.CreateSubUserResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
      security:
      - BearerAuth: []
      summary: 创建子用户
      tags:
      - 子用户管理
  /api/v1/users/subusers/{id}:
    delete:
      description: 删除指定的子用户
      parameters:
      - description: 子用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "404":
          description: 子用户不存在
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
      security:
      - BearerAuth: []
      summary: 删除子用户
      tags:
      - 子用户管理
    get:
      description: 获取指定子用户的详细信息
      parameters:
      - description: 子用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 子用户详情
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.SubUser'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "404":
          description: 子用户不存在
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
      security:
      - BearerAuth: []
      summary: 获取子用户详情
      tags:
      - 子用户管理
    put:
      consumes:
      - application/json
      description: 更新指定的子用户信息
      parameters:
      - description: 子用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新子用户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/internal_handlers.UpdateSubUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "404":
          description: 子用户不存在
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/github_com_TeaOSLab_EdgeOpenAPI_pkg_converter.APIResponse'
      security:
      - BearerAuth: []
      summary: 更新子用户
      tags:
      - 子用户管理
securityDefinitions:
  BearerAuth:
    description: 在请求头中添加Bearer Token进行身份验证，格式：Bearer {token}
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
