package models

import (
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/iwind/TeaGo/Tea"
	"github.com/iwind/TeaGo/dbs"
	"github.com/iwind/TeaGo/types"
)

const (
	SubUserStateEnabled  = 1 // 已启用
	SubUserStateDisabled = 0 // 已禁用
)

type SubUserDAO dbs.DAO

func NewSubUserDAO() *SubUserDAO {
	return dbs.NewDAO(&SubUserDAO{
		DAOObject: dbs.DAOObject{
			DB:     Tea.Env,
			Table:  "edgeSubUsers",
			Model:  new(SubUser),
			PkName: "id",
		},
	}).(*SubUserDAO)
}

var SharedSubUserDAO *SubUserDAO

func init() {
	dbs.OnReady(func() {
		SharedSubUserDAO = NewSubUserDAO()
	})
}

// 启用条目
func (this *SubUserDAO) EnableSubUser(tx *dbs.Tx, id uint32) error {
	_, err := this.Query(tx).
		Pk(id).
		Set("state", SubUserStateEnabled).
		Update()
	return err
}

// 禁用条目
func (this *SubUserDAO) DisableSubUser(tx *dbs.Tx, id uint32) error {
	_, err := this.Query(tx).
		Pk(id).
		Set("state", SubUserStateDisabled).
		Update()
	return err
}

// 查找启用中的条目
func (this *SubUserDAO) FindEnabledSubUser(tx *dbs.Tx, id uint32) (*SubUser, error) {
	result, err := this.Query(tx).
		Pk(id).
		Attr("state", SubUserStateEnabled).
		Find()
	if result == nil {
		return nil, err
	}
	return result.(*SubUser), err
}

// 根据主键查找名称
func (this *SubUserDAO) FindSubUserName(tx *dbs.Tx, id uint32) (string, error) {
	return this.Query(tx).
		Pk(id).
		Result("name").
		FindStringCol("")
}

// CreateSubUser 创建子用户
func (this *SubUserDAO) CreateSubUser(tx *dbs.Tx, userId uint32, username string, password string, name string, remark string) (uint32, error) {
	var op = NewSubUserOperator()
	op.UserId = userId
	op.Username = username
	op.Password = password
	op.Name = name
	op.Remark = remark
	op.IsOn = true
	op.CreatedAt = uint64(time.Now().Unix())
	op.UpdatedAt = uint64(time.Now().Unix())
	op.State = SubUserStateEnabled

	err := this.Save(tx, op)
	if err != nil {
		return 0, err
	}

	return types.Uint32(op.Id), nil
}

// UpdateSubUser 更新子用户
func (this *SubUserDAO) UpdateSubUser(tx *dbs.Tx, subUserId uint32, username string, password string, name string, remark string, isOn bool) error {
	var op = NewSubUserOperator()
	op.Id = subUserId
	op.Username = username
	if len(password) > 0 {
		op.Password = password
	}
	op.Name = name
	op.Remark = remark
	op.IsOn = isOn
	op.UpdatedAt = uint64(time.Now().Unix())

	return this.Save(tx, op)
}

// DeleteSubUser 删除子用户
func (this *SubUserDAO) DeleteSubUser(tx *dbs.Tx, subUserId uint32) error {
	_, err := this.Query(tx).
		Pk(subUserId).
		Set("state", SubUserStateDisabled).
		Update()
	return err
}

// CountSubUsers 计算子用户数量
func (this *SubUserDAO) CountSubUsers(tx *dbs.Tx, userId uint32, keyword string) (int64, error) {
	query := this.Query(tx).
		Attr("userId", userId).
		Attr("state", SubUserStateEnabled)

	if len(keyword) > 0 {
		query.Where("(username LIKE ? OR name LIKE ?)", "%"+keyword+"%", "%"+keyword+"%")
	}

	return query.Count()
}

// ListSubUsers 列出子用户
func (this *SubUserDAO) ListSubUsers(tx *dbs.Tx, userId uint32, keyword string, offset int64, size int64) ([]*SubUser, error) {
	query := this.Query(tx).
		Attr("userId", userId).
		Attr("state", SubUserStateEnabled).
		Offset(offset).
		Limit(size).
		Desc("id")

	if len(keyword) > 0 {
		query.Where("(username LIKE ? OR name LIKE ?)", "%"+keyword+"%", "%"+keyword+"%")
	}

	ones, err := query.FindAll()
	if err != nil {
		return nil, err
	}

	result := make([]*SubUser, len(ones))
	for i, one := range ones {
		result[i] = one.(*SubUser)
	}
	return result, nil
}

// CheckSubUserUsername 检查子用户名是否存在
func (this *SubUserDAO) CheckSubUserUsername(tx *dbs.Tx, userId uint32, username string, excludeSubUserId uint32) (bool, error) {
	query := this.Query(tx).
		Attr("userId", userId).
		Attr("username", username).
		Attr("state", SubUserStateEnabled)

	if excludeSubUserId > 0 {
		query.Where("id != :excludeSubUserId").
			Param("excludeSubUserId", excludeSubUserId)
	}

	return query.Exist()
}

// FindSubUserWithUserId 根据用户ID和子用户ID查找子用户（用于权限验证）
func (this *SubUserDAO) FindSubUserWithUserId(tx *dbs.Tx, userId uint32, subUserId uint32) (*SubUser, error) {
	result, err := this.Query(tx).
		Attr("userId", userId).
		Pk(subUserId).
		Attr("state", SubUserStateEnabled).
		Find()
	if result == nil {
		return nil, err
	}
	return result.(*SubUser), err
}
