package services

import (
	"context"
	"errors"

	"github.com/TeaOSLab/EdgeAPI/internal/db/models"
	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/iwind/TeaGo/types"
	stringutil "github.com/iwind/TeaGo/utils/string"
)

// SubUserService 子用户服务
type SubUserService struct {
	BaseService
}

// CreateSubUser 创建子用户
func (this *SubUserService) CreateSubUser(ctx context.Context, req *pb.CreateSubUserRequest) (*pb.CreateSubUserResponse, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	// 验证输入参数
	if len(req.Username) == 0 {
		return nil, errors.New("username is required")
	}
	if len(req.Password) == 0 {
		return nil, errors.New("password is required")
	}
	if len(req.Name) == 0 {
		return nil, errors.New("name is required")
	}

	var tx = this.NullTx()

	// 检查用户名是否已存在
	exists, err := models.SharedSubUserDAO.CheckSubUserUsername(tx, types.Uint32(userId), req.Username, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("username already exists")
	}

	// 密码加密
	hashedPassword := stringutil.Md5(req.Password)

	// 创建子用户
	subUserId, err := models.SharedSubUserDAO.CreateSubUser(tx, types.Uint32(userId), req.Username, hashedPassword, req.Name, req.Remark)
	if err != nil {
		return nil, err
	}

	return &pb.CreateSubUserResponse{
		SubUserId: int64(subUserId),
	}, nil
}

// UpdateSubUser 更新子用户
func (this *SubUserService) UpdateSubUser(ctx context.Context, req *pb.UpdateSubUserRequest) (*pb.RPCSuccess, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	if req.SubUserId <= 0 {
		return nil, errors.New("invalid subUserId")
	}

	var tx = this.NullTx()

	// 验证子用户是否属于当前用户
	subUser, err := models.SharedSubUserDAO.FindSubUserWithUserId(tx, types.Uint32(userId), types.Uint32(req.SubUserId))
	if err != nil {
		return nil, err
	}
	if subUser == nil {
		return nil, errors.New("sub user not found or access denied")
	}

	// 检查用户名是否已存在（排除当前子用户）
	if len(req.Username) > 0 {
		exists, err := models.SharedSubUserDAO.CheckSubUserUsername(tx, types.Uint32(userId), req.Username, types.Uint32(req.SubUserId))
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, errors.New("username already exists")
		}
	}

	// 密码加密（如果提供了密码）
	var hashedPassword string
	if len(req.Password) > 0 {
		hashedPassword = stringutil.Md5(req.Password)
	}

	// 更新子用户
	err = models.SharedSubUserDAO.UpdateSubUser(tx, types.Uint32(req.SubUserId), req.Username, hashedPassword, req.Name, req.Remark, req.IsOn)
	if err != nil {
		return nil, err
	}

	return this.Success()
}

// DeleteSubUser 删除子用户
func (this *SubUserService) DeleteSubUser(ctx context.Context, req *pb.DeleteSubUserRequest) (*pb.RPCSuccess, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	if req.SubUserId <= 0 {
		return nil, errors.New("invalid subUserId")
	}

	var tx = this.NullTx()

	// 验证子用户是否属于当前用户
	subUser, err := models.SharedSubUserDAO.FindSubUserWithUserId(tx, types.Uint32(userId), types.Uint32(req.SubUserId))
	if err != nil {
		return nil, err
	}
	if subUser == nil {
		return nil, errors.New("sub user not found or access denied")
	}

	// 删除子用户
	err = models.SharedSubUserDAO.DeleteSubUser(tx, types.Uint32(req.SubUserId))
	if err != nil {
		return nil, err
	}

	return this.Success()
}

// CountSubUsers 计算子用户数量
func (this *SubUserService) CountSubUsers(ctx context.Context, req *pb.CountSubUsersRequest) (*pb.RPCCountResponse, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	var tx = this.NullTx()

	count, err := models.SharedSubUserDAO.CountSubUsers(tx, types.Uint32(userId), req.Keyword)
	if err != nil {
		return nil, err
	}

	return this.SuccessCount(count)
}

// ListSubUsers 列出子用户
func (this *SubUserService) ListSubUsers(ctx context.Context, req *pb.ListSubUsersRequest) (*pb.ListSubUsersResponse, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	var tx = this.NullTx()

	subUsers, err := models.SharedSubUserDAO.ListSubUsers(tx, types.Uint32(userId), req.Keyword, req.Offset, req.Size)
	if err != nil {
		return nil, err
	}

	result := make([]*pb.SubUser, len(subUsers))
	for i, subUser := range subUsers {
		result[i] = &pb.SubUser{
			Id:        int64(subUser.Id),
			UserId:    int64(subUser.UserId),
			Username:  subUser.Username,
			Name:      subUser.Name,
			IsOn:      subUser.IsOn,
			CreatedAt: int64(subUser.CreatedAt),
			UpdatedAt: int64(subUser.UpdatedAt),
			Remark:    subUser.Remark,
		}
	}

	return &pb.ListSubUsersResponse{
		SubUsers: result,
	}, nil
}

// FindEnabledSubUser 查找单个子用户
func (this *SubUserService) FindEnabledSubUser(ctx context.Context, req *pb.FindEnabledSubUserRequest) (*pb.FindEnabledSubUserResponse, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	if req.SubUserId <= 0 {
		return nil, errors.New("invalid subUserId")
	}

	var tx = this.NullTx()

	// 验证子用户是否属于当前用户
	subUser, err := models.SharedSubUserDAO.FindSubUserWithUserId(tx, types.Uint32(userId), types.Uint32(req.SubUserId))
	if err != nil {
		return nil, err
	}

	if subUser == nil {
		return &pb.FindEnabledSubUserResponse{SubUser: nil}, nil
	}

	return &pb.FindEnabledSubUserResponse{
		SubUser: &pb.SubUser{
			Id:        int64(subUser.Id),
			UserId:    int64(subUser.UserId),
			Username:  subUser.Username,
			Name:      subUser.Name,
			IsOn:      subUser.IsOn,
			CreatedAt: int64(subUser.CreatedAt),
			UpdatedAt: int64(subUser.UpdatedAt),
			Remark:    subUser.Remark,
		},
	}, nil
}

// CheckSubUserUsername 检查子用户名是否存在
func (this *SubUserService) CheckSubUserUsername(ctx context.Context, req *pb.CheckSubUserUsernameRequest) (*pb.CheckSubUserUsernameResponse, error) {
	// 验证用户权限
	_, userId, err := this.ValidateAdminAndUser(ctx, true)
	if err != nil {
		return nil, err
	}

	if len(req.Username) == 0 {
		return nil, errors.New("username is required")
	}

	var tx = this.NullTx()

	exists, err := models.SharedSubUserDAO.CheckSubUserUsername(tx, types.Uint32(userId), req.Username, types.Uint32(req.ExcludeSubUserId))
	if err != nil {
		return nil, err
	}

	return &pb.CheckSubUserUsernameResponse{
		Exists: exists,
	}, nil
}
